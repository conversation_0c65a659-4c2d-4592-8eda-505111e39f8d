using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using MotoKingPro.Core;
using MotoKingPro.UI;

namespace MotoKingPro.Platforms
{
    /// <summary>
    /// Qualcomm platform page (EE97B6A8 -> QualcommPage)
    /// Handles Qualcomm Snapdragon devices with EDL mode support
    /// </summary>
    public partial class QualcommPage : UserControl
    {
        #region Events
        public event Action<bool, string> OperationStateChanged;
        public event Action<string, LogLevel, bool, bool> LogMessage;
        #endregion

        #region Fields
        private MainForm parentForm;
        private QualcommManager qualcommManager;
        private LogRichTextBox textOutput;
        private List<PartitionInfo> partitions;
        private bool isOperationRunning = false;
        #endregion

        #region UI Controls
        private Panel mainPanel;
        private GroupBox deviceInfoGroup;
        private GroupBox operationsGroup;
        private GroupBox partitionsGroup;
        
        private Button connectButton;
        private Button disconnectButton;
        private Button readInfoButton;
        private Button unlockBootloaderButton;
        private Button lockBootloaderButton;
        private Button rebootButton;
        private Button rebootEDLButton;
        private Button rebootFastbootButton;
        
        private ComboBox rebootModeCombo;
        private ListView partitionsList;
        private Button readPartitionButton;
        private Button writePartitionButton;
        private Button erasePartitionButton;
        
        private Label deviceModelLabel;
        private Label chipsetLabel;
        private Label bootloaderStatusLabel;
        private Label connectionStatusLabel;
        #endregion

        #region Constructor
        public QualcommPage()
        {
            InitializeComponent();
            InitializeCustomComponents();
            SetupEventHandlers();
        }
        #endregion

        #region Initialization
        private void InitializeComponent()
        {
            // Initialize UI components
            mainPanel = new Panel { Dock = DockStyle.Fill, BackColor = Color.FromArgb(45, 45, 48) };
            
            // Device info group
            deviceInfoGroup = new GroupBox 
            { 
                Text = "Device Information", 
                ForeColor = Color.White,
                Size = new Size(300, 120),
                Location = new Point(10, 10)
            };
            
            deviceModelLabel = new Label { Text = "Model: Not Connected", ForeColor = Color.White, Location = new Point(10, 20) };
            chipsetLabel = new Label { Text = "Chipset: Unknown", ForeColor = Color.White, Location = new Point(10, 40) };
            bootloaderStatusLabel = new Label { Text = "Bootloader: Unknown", ForeColor = Color.White, Location = new Point(10, 60) };
            connectionStatusLabel = new Label { Text = "Status: Disconnected", ForeColor = Color.Red, Location = new Point(10, 80) };
            
            deviceInfoGroup.Controls.AddRange(new Control[] { deviceModelLabel, chipsetLabel, bootloaderStatusLabel, connectionStatusLabel });
            
            // Operations group
            operationsGroup = new GroupBox 
            { 
                Text = "Operations", 
                ForeColor = Color.White,
                Size = new Size(300, 200),
                Location = new Point(10, 140)
            };
            
            connectButton = new Button { Text = "Connect Device", Size = new Size(120, 30), Location = new Point(10, 20) };
            disconnectButton = new Button { Text = "Disconnect", Size = new Size(120, 30), Location = new Point(140, 20), Enabled = false };
            readInfoButton = new Button { Text = "Read Device Info", Size = new Size(120, 30), Location = new Point(10, 60), Enabled = false };
            
            unlockBootloaderButton = new Button { Text = "Unlock Bootloader", Size = new Size(120, 30), Location = new Point(10, 100), Enabled = false };
            lockBootloaderButton = new Button { Text = "Lock Bootloader", Size = new Size(120, 30), Location = new Point(140, 100), Enabled = false };
            
            rebootModeCombo = new ComboBox 
            { 
                Size = new Size(120, 25), 
                Location = new Point(10, 140),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Enabled = false
            };
            rebootModeCombo.Items.AddRange(new[] { "Normal", "Bootloader", "Recovery", "EDL" });
            rebootModeCombo.SelectedIndex = 0;
            
            rebootButton = new Button { Text = "Reboot", Size = new Size(120, 30), Location = new Point(140, 140), Enabled = false };
            
            operationsGroup.Controls.AddRange(new Control[] 
            { 
                connectButton, disconnectButton, readInfoButton, 
                unlockBootloaderButton, lockBootloaderButton,
                rebootModeCombo, rebootButton
            });
            
            // Partitions group
            partitionsGroup = new GroupBox 
            { 
                Text = "Partitions", 
                ForeColor = Color.White,
                Size = new Size(300, 200),
                Location = new Point(320, 10)
            };
            
            partitionsList = new ListView 
            { 
                Size = new Size(280, 120), 
                Location = new Point(10, 20),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                BackColor = Color.FromArgb(30, 30, 30),
                ForeColor = Color.White
            };
            partitionsList.Columns.Add("Name", 100);
            partitionsList.Columns.Add("Size", 80);
            partitionsList.Columns.Add("Type", 80);
            
            readPartitionButton = new Button { Text = "Read", Size = new Size(80, 30), Location = new Point(10, 150), Enabled = false };
            writePartitionButton = new Button { Text = "Write", Size = new Size(80, 30), Location = new Point(100, 150), Enabled = false };
            erasePartitionButton = new Button { Text = "Erase", Size = new Size(80, 30), Location = new Point(190, 150), Enabled = false };
            
            partitionsGroup.Controls.AddRange(new Control[] { partitionsList, readPartitionButton, writePartitionButton, erasePartitionButton });
            
            // Text output
            textOutput = new LogRichTextBox 
            { 
                Size = new Size(610, 200), 
                Location = new Point(10, 350),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };
            
            mainPanel.Controls.AddRange(new Control[] { deviceInfoGroup, operationsGroup, partitionsGroup, textOutput });
            Controls.Add(mainPanel);
        }

        private void InitializeCustomComponents()
        {
            qualcommManager = new QualcommManager();
            partitions = new List<PartitionInfo>();
            
            // Log initial messages
            LogMessage("Qualcomm Snapdragon Platform", LogLevel.Word, true, true);
            LogMessage("Version: " + ApplicationInfo.GetVersionString(), LogLevel.Success);
            LogMessage("Selected Tab: Qualcomm", LogLevel.Success, true, true);
            LogMessage("Ready for device connection...", LogLevel.Info);
        }

        private void SetupEventHandlers()
        {
            connectButton.Click += ConnectButton_Click;
            disconnectButton.Click += DisconnectButton_Click;
            readInfoButton.Click += ReadInfoButton_Click;
            unlockBootloaderButton.Click += UnlockBootloaderButton_Click;
            lockBootloaderButton.Click += LockBootloaderButton_Click;
            rebootButton.Click += RebootButton_Click;
            
            readPartitionButton.Click += ReadPartitionButton_Click;
            writePartitionButton.Click += WritePartitionButton_Click;
            erasePartitionButton.Click += ErasePartitionButton_Click;
            
            qualcommManager.OperationStateChanged += OnOperationStateChanged;
            qualcommManager.LogMessage += OnLogMessage;
        }
        #endregion

        #region Public Methods
        public void SetParentForm(MainForm form)
        {
            parentForm = form;
        }

        public void FocusTextOutput()
        {
            textOutput?.Focus();
        }

        public void ClearLog()
        {
            textOutput?.ClearLog();
        }

        public List<LogItem> GetLogItems()
        {
            // Return log items for history
            return new List<LogItem>();
        }

        public void SetOperationState(bool isRunning)
        {
            isOperationRunning = isRunning;
            UpdateUIState();
        }
        #endregion

        #region Event Handlers
        private void ConnectButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Connect Qualcomm Device");
            
            bool success = qualcommManager.ConnectDevice();
            if (success)
            {
                connectionStatusLabel.Text = "Status: Connected";
                connectionStatusLabel.ForeColor = Color.Green;
                EnableConnectedControls(true);
                LogMessage("Device connected successfully", LogLevel.Success);
            }
            else
            {
                LogMessage("Failed to connect device", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Connect Qualcomm Device");
        }

        private void DisconnectButton_Click(object sender, EventArgs e)
        {
            qualcommManager.DisconnectDevice();
            connectionStatusLabel.Text = "Status: Disconnected";
            connectionStatusLabel.ForeColor = Color.Red;
            EnableConnectedControls(false);
            LogMessage("Device disconnected", LogLevel.Warning);
        }

        private void ReadInfoButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Read Device Information");
            
            var deviceInfo = qualcommManager.ReadDeviceInfo();
            if (deviceInfo != null)
            {
                deviceModelLabel.Text = $"Model: {deviceInfo.Model}";
                chipsetLabel.Text = $"Chipset: {deviceInfo.Chipset}";
                LogMessage($"Device Model: {deviceInfo.Model}", LogLevel.Success);
                LogMessage($"Chipset: {deviceInfo.Chipset}", LogLevel.Success);
                LogMessage($"Android Version: {deviceInfo.AndroidVersion}", LogLevel.Success);
            }
            
            OnOperationStateChanged(false, "Read Device Information");
        }

        private void UnlockBootloaderButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Unlock Bootloader");
            
            bool success = qualcommManager.UnlockBootloader();
            if (success)
            {
                bootloaderStatusLabel.Text = "Bootloader: Unlocked";
                LogMessage("Bootloader unlocked successfully", LogLevel.Success);
            }
            else
            {
                LogMessage("Failed to unlock bootloader", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Unlock Bootloader");
        }

        private void LockBootloaderButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Lock Bootloader");
            
            bool success = qualcommManager.LockBootloader();
            if (success)
            {
                bootloaderStatusLabel.Text = "Bootloader: Locked";
                LogMessage("Bootloader locked successfully", LogLevel.Success);
            }
            else
            {
                LogMessage("Failed to lock bootloader", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Lock Bootloader");
        }

        private void RebootButton_Click(object sender, EventArgs e)
        {
            string mode = rebootModeCombo.SelectedItem.ToString();
            OnOperationStateChanged(true, $"Reboot to {mode}");
            
            bool success = qualcommManager.RebootDevice(mode);
            if (success)
            {
                LogMessage($"Device rebooted to {mode} mode", LogLevel.Success);
            }
            else
            {
                LogMessage($"Failed to reboot to {mode} mode", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, $"Reboot to {mode}");
        }

        private void ReadPartitionButton_Click(object sender, EventArgs e)
        {
            if (partitionsList.SelectedItems.Count > 0)
            {
                string partitionName = partitionsList.SelectedItems[0].Text;
                OnOperationStateChanged(true, $"Read Partition: {partitionName}");
                
                bool success = qualcommManager.ReadPartition(partitionName);
                LogMessage(success ? $"Partition {partitionName} read successfully" : $"Failed to read partition {partitionName}", 
                    success ? LogLevel.Success : LogLevel.Error);
                
                OnOperationStateChanged(false, $"Read Partition: {partitionName}");
            }
        }

        private void WritePartitionButton_Click(object sender, EventArgs e)
        {
            if (partitionsList.SelectedItems.Count > 0)
            {
                string partitionName = partitionsList.SelectedItems[0].Text;
                
                using (OpenFileDialog ofd = new OpenFileDialog())
                {
                    ofd.Filter = "Image Files (*.img)|*.img|All Files (*.*)|*.*";
                    if (ofd.ShowDialog() == DialogResult.OK)
                    {
                        OnOperationStateChanged(true, $"Write Partition: {partitionName}");
                        
                        bool success = qualcommManager.WritePartition(partitionName, ofd.FileName);
                        LogMessage(success ? $"Partition {partitionName} written successfully" : $"Failed to write partition {partitionName}", 
                            success ? LogLevel.Success : LogLevel.Error);
                        
                        OnOperationStateChanged(false, $"Write Partition: {partitionName}");
                    }
                }
            }
        }

        private void ErasePartitionButton_Click(object sender, EventArgs e)
        {
            if (partitionsList.SelectedItems.Count > 0)
            {
                string partitionName = partitionsList.SelectedItems[0].Text;
                OnOperationStateChanged(true, $"Erase Partition: {partitionName}");
                
                bool success = qualcommManager.ErasePartition(partitionName);
                LogMessage(success ? $"Partition {partitionName} erased successfully" : $"Failed to erase partition {partitionName}", 
                    success ? LogLevel.Success : LogLevel.Error);
                
                OnOperationStateChanged(false, $"Erase Partition: {partitionName}");
            }
        }

        private void OnOperationStateChanged(bool isRunning, string operationName)
        {
            OperationStateChanged?.Invoke(isRunning, operationName);
        }

        private void OnLogMessage(string message, LogLevel level, bool newLine, bool bold)
        {
            LogMessage?.Invoke(message, level, newLine, bold);
            textOutput.LogMessage(message, level, newLine, bold);
        }
        #endregion

        #region Helper Methods
        private void EnableConnectedControls(bool enabled)
        {
            disconnectButton.Enabled = enabled;
            readInfoButton.Enabled = enabled;
            unlockBootloaderButton.Enabled = enabled;
            lockBootloaderButton.Enabled = enabled;
            rebootModeCombo.Enabled = enabled;
            rebootButton.Enabled = enabled;
            readPartitionButton.Enabled = enabled;
            writePartitionButton.Enabled = enabled;
            erasePartitionButton.Enabled = enabled;
            
            connectButton.Enabled = !enabled;
        }

        private void UpdateUIState()
        {
            // Update UI based on operation state
        }

        private void LogMessage(string message, LogLevel level = LogLevel.Info, bool newLine = true, bool bold = false)
        {
            textOutput?.LogMessage(message, level, newLine, bold);
        }
        #endregion
    }

    /// <summary>
    /// Qualcomm device manager for EDL operations
    /// </summary>
    public class QualcommManager : BasePlatformManager
    {
        private DeviceConnectionState connectionState = DeviceConnectionState.Disconnected;
        private DeviceInfo currentDevice;

        public override bool ConnectDevice()
        {
            try
            {
                OnLogMessage("Attempting to connect Qualcomm device...", LogLevel.Info);

                // Simulate device detection and connection
                System.Threading.Thread.Sleep(1000);

                connectionState = DeviceConnectionState.EDL;
                currentDevice = new DeviceInfo
                {
                    Model = "Qualcomm Device",
                    Manufacturer = "Qualcomm",
                    Chipset = "Snapdragon 855",
                    Platform = PlatformType.Qualcomm,
                    SerialNumber = "QC123456789"
                };

                OnLogMessage("Qualcomm device connected in EDL mode", LogLevel.Success);
                return true;
            }
            catch (Exception ex)
            {
                OnLogMessage($"Connection failed: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        public override bool DisconnectDevice()
        {
            connectionState = DeviceConnectionState.Disconnected;
            currentDevice = null;
            OnLogMessage("Device disconnected", LogLevel.Warning);
            return true;
        }

        public override DeviceConnectionState GetConnectionState()
        {
            return connectionState;
        }

        public DeviceInfo ReadDeviceInfo()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return null;

            OnLogMessage("Reading device information...", LogLevel.Info);

            // Simulate reading device info
            System.Threading.Thread.Sleep(500);

            return currentDevice;
        }

        public bool UnlockBootloader()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage("Unlocking bootloader...", LogLevel.Info);
            System.Threading.Thread.Sleep(2000);
            OnLogMessage("Bootloader unlock completed", LogLevel.Success);
            return true;
        }

        public bool LockBootloader()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage("Locking bootloader...", LogLevel.Info);
            System.Threading.Thread.Sleep(2000);
            OnLogMessage("Bootloader lock completed", LogLevel.Success);
            return true;
        }

        public bool RebootDevice(string mode)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage($"Rebooting device to {mode} mode...", LogLevel.Info);
            System.Threading.Thread.Sleep(1000);
            OnLogMessage($"Device reboot to {mode} initiated", LogLevel.Success);
            return true;
        }

        public bool ReadPartition(string partitionName)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage($"Reading partition: {partitionName}", LogLevel.Info);
            System.Threading.Thread.Sleep(3000);
            OnLogMessage($"Partition {partitionName} read completed", LogLevel.Success);
            return true;
        }

        public bool WritePartition(string partitionName, string imagePath)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage($"Writing partition: {partitionName} from {imagePath}", LogLevel.Info);
            System.Threading.Thread.Sleep(5000);
            OnLogMessage($"Partition {partitionName} write completed", LogLevel.Success);
            return true;
        }

        public bool ErasePartition(string partitionName)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage($"Erasing partition: {partitionName}", LogLevel.Info);
            System.Threading.Thread.Sleep(2000);
            OnLogMessage($"Partition {partitionName} erase completed", LogLevel.Success);
            return true;
        }
    }
}
