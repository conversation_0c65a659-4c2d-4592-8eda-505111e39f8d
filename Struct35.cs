internal struct Struct35
{
	private uint uint_0;

	public uint method_0(Class76 C78D560C)
	{
		uint num = (C78D560C.uint_1 >> 11) * uint_0;
		if (C78D560C.uint_0 < num)
		{
			C78D560C.uint_1 = num;
			uint_0 += 2048 - uint_0 >> 5;
			if (C78D560C.uint_1 < 16777216)
			{
				C78D560C.uint_0 = (C78D560C.uint_0 << 8) | (byte)C78D560C.B31968A8.ReadByte();
				C78D560C.uint_1 <<= 8;
			}
			return 0u;
		}
		C78D560C.uint_1 -= num;
		C78D560C.uint_0 -= num;
		uint_0 -= uint_0 >> 5;
		if (C78D560C.uint_1 < 16777216)
		{
			C78D560C.uint_0 = (C78D560C.uint_0 << 8) | (byte)C78D560C.B31968A8.ReadByte();
			C78D560C.uint_1 <<= 8;
		}
		return 1u;
	}

	public void F13A0D25()
	{
		uint_0 = 1024u;
	}
}
