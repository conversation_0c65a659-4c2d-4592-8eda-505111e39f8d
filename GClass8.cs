using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading;

public class GClass8
{
	public E5964FB0 A68CE121;

	public GClass59 gclass59_0;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private GClass112.E39CAE0B e39CAE0B_0;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private GDelegate25 gdelegate25_0;

	public bool bool_0;

	public GClass7 gclass7_0;

	private GClass51.GStruct49 gstruct49_0;

	public GClass51.AFBD65B0 DE28AC3C;

	private uint uint_0;

	public Dictionary<string, byte[]> AB26F3BB;

	public event GClass112.E39CAE0B Event_0
	{
		[CompilerGenerated]
		add
		{
			GClass112.E39CAE0B e39CAE0B = e39CAE0B_0;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[752](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref e39CAE0B_0, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
		[CompilerGenerated]
		remove
		{
			GClass112.E39CAE0B e39CAE0B = e39CAE0B_0;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[629](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref e39CAE0B_0, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
	}

	public event GDelegate25 Event_1
	{
		[CompilerGenerated]
		add
		{
			GDelegate25 gDelegate = gdelegate25_0;
			GDelegate25 gDelegate2;
			do
			{
				gDelegate2 = gDelegate;
				GDelegate25 value2 = (GDelegate25)Class607.B630A78B.object_0[752](gDelegate2, value);
				gDelegate = Interlocked.CompareExchange(ref gdelegate25_0, value2, gDelegate2);
			}
			while ((object)gDelegate != gDelegate2);
		}
		[CompilerGenerated]
		remove
		{
			GDelegate25 gDelegate = gdelegate25_0;
			GDelegate25 gDelegate2;
			do
			{
				gDelegate2 = gDelegate;
				GDelegate25 value2 = (GDelegate25)Class607.B630A78B.object_0[629](gDelegate2, value);
				gDelegate = Interlocked.CompareExchange(ref gdelegate25_0, value2, gDelegate2);
			}
			while ((object)gDelegate != gDelegate2);
		}
	}

	public List<uint> BDAA3A27(uint B9B77283, int A5108980 = 1)
	{
		if (A68CE121.A18A06B0 == GClass34.AB881C92.const_0)
		{
			return A68CE121.A335BB39.method_4(B9B77283, A5108980);
		}
		if (A68CE121.A18A06B0 == GClass34.AB881C92.const_1)
		{
			return A68CE121.gclass51_0.EE83B4B7(B9B77283, A5108980);
		}
		List<uint> list = new List<uint>();
		if (A5108980 < 32)
		{
			using IEnumerator<int> enumerator = GClass112.smethod_30(0, A5108980).GetEnumerator();
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				byte[] array = method_0(Class607.B630A78B.object_0[21](B9B77283 + current * 4));
				if (array != null)
				{
					object bBB4E2A = GClass111.C3B9331C("<I", array)[0];
					if (A5108980 != 1)
					{
						list.Add(Class607.B630A78B.object_0[40](bBB4E2A));
						continue;
					}
					list.Add(Class607.B630A78B.object_0[40](bBB4E2A));
					return list;
				}
				return list;
			}
		}
		else
		{
			byte[] aA23FA = method_1(B9B77283, Class607.B630A78B.object_0[836](A5108980 * 4));
			using IEnumerator<int> enumerator2 = GClass112.smethod_30(0, A5108980, 4).GetEnumerator();
			while (Class607.B630A78B.object_0[212](enumerator2))
			{
				int current2 = enumerator2.Current;
				list.Add(Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(aA23FA, current2, 4))[0]));
			}
		}
		return list;
	}

	public byte[] method_0(uint A88A4AA6)
	{
		string object_ = GClass5.smethod_11("CUSTOMREGR");
		if (method_12(object_))
		{
			string string_ = method_5();
			if (Class607.B630A78B.object_0[787](string_, "OK"))
			{
				method_12(A88A4AA6);
				string string_2 = method_5(bool_1: true);
				string_ = method_5();
				FBB13E94();
				string_ = method_5();
				FBB13E94();
				return GClass112.FF06B0AD(string_2);
			}
		}
		return null;
	}

	public byte[] method_1(uint B7AFFB39, uint F4087D20)
	{
		string object_ = GClass5.smethod_11("CUSTOMMEMR");
		if (method_12(object_))
		{
			string string_ = method_5();
			if (Class607.B630A78B.object_0[787](string_, "Ok"))
			{
				method_12(B7AFFB39, 1, bool_1: true);
				method_12(F4087D20);
				string string_2 = method_5(bool_1: true);
				string_ = method_5();
				FBB13E94();
				string_ = method_5();
				FBB13E94();
				return GClass112.FF06B0AD(string_2);
			}
		}
		return null;
	}

	public bool method_2(uint B18F0FAC, object object_0)
	{
		if (A68CE121.A18A06B0 == GClass34.AB881C92.const_0)
		{
			return A68CE121.A335BB39.method_2(B18F0FAC, object_0);
		}
		if (A68CE121.A18A06B0 == GClass34.AB881C92.const_1)
		{
			return A68CE121.gclass51_0.method_2(B18F0FAC, object_0);
		}
		List<uint> list = new List<uint>();
		if (GClass112.smethod_19(object_0))
		{
			list.Add(Class607.B630A78B.object_0[40](object_0));
		}
		else
		{
			list = (List<uint>)object_0;
		}
		int num = 0;
		if (list.Count < 32)
		{
			foreach (uint item in list)
			{
				if (E8A898B6(Class607.B630A78B.object_0[21](B18F0FAC + num), item))
				{
					num += 4;
					continue;
				}
				return false;
			}
		}
		else
		{
			List<byte> list2 = new List<byte>();
			foreach (uint item2 in list)
			{
				list2.AddRange(GClass111.smethod_2("<I", new object[1] { item2 }));
			}
			C5066B06(B18F0FAC, list2.ToArray());
		}
		return true;
	}

	public bool E8A898B6(uint D284BFBD, uint FE9A32A1)
	{
		string object_ = GClass5.smethod_11("CUSTOMREGW");
		if (method_12(object_))
		{
			string string_ = method_5();
			if (Class607.B630A78B.object_0[787](string_, "OK"))
			{
				method_12(D284BFBD, 1, bool_1: true);
				method_12(FE9A32A1);
				string_ = method_5();
				FBB13E94();
				string_ = method_5();
				FBB13E94();
				return true;
			}
		}
		return false;
	}

	public bool C5066B06(uint D338AFBB, byte[] byte_0)
	{
		string object_ = GClass5.smethod_11("CUSTOMMEMR");
		if (method_12(object_))
		{
			string string_ = method_5();
			if (Class607.B630A78B.object_0[787](string_, "OK"))
			{
				method_12(D338AFBB, 1, bool_1: true);
				method_12(byte_0.Length);
				method_12(byte_0);
				string_ = method_5();
				FBB13E94();
				string_ = method_5();
				FBB13E94();
				return true;
			}
		}
		return false;
	}

	public bool method_3(bool bool_1)
	{
		string text = "unlock";
		if (!bool_1)
		{
			text = "lock";
		}
		e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[1217]("Perform functions related to {0} bootloader", text));
		e39CAE0B_0?.Invoke("Reading Partition Information tables from device :");
		gclass59_0.D60E1983(new D905A51F(0u, 0u, 0u)).Deconstruct(out var _, out var item2);
		GClass37 gClass = item2;
		GClass37.B6BA8237 b6BA = default(GClass37.B6BA8237);
		byte[] array = null;
		foreach (GClass37.B6BA8237 item3 in gClass.list_0)
		{
			if (Class607.B630A78B.object_0[787](item3.string_2, "seccfg"))
			{
				b6BA = item3;
				array = method_6(b6BA.D78F1C10 * A68CE121.CF001212.uint_0, b6BA.ulong_1 * A68CE121.CF001212.uint_0, "", "user").byte_0;
				break;
			}
		}
		if (array == null)
		{
			throw Class607.B630A78B.object_0[778]("Couldn't detect existing seccfg partition. Aborting unlock.");
		}
		e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
		e39CAE0B_0?.Invoke("initializing seccfg :");
		if (!array.Take(4).SequenceEqual(GClass111.smethod_2("<I", new object[1] { 1296911693 })))
		{
			throw Class607.B630A78B.object_0[778]("Unknown seccfg partition header. Aborting unlock.");
		}
		B42DC004 b42DC = new B42DC004(A68CE121);
		if (array.Take(12).SequenceEqual(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[47](), "AND_SECCFG_v")))
		{
			e39CAE0B_0?.Invoke("v3", EF1F389C.Success);
			GClass63 gClass2 = new GClass63(b42DC);
			e39CAE0B_0?.Invoke("excuting aes key arguments :");
			if (!gClass2.BABD1F1C(array))
			{
				throw Class607.B630A78B.object_0[778]("Device has either already unlocked or the algorithm is unknown. Aborting.");
			}
			byte[] array2 = gClass2.method_0(text);
			if (method_4(b6BA.D78F1C10 * A68CE121.CF001212.uint_0, Class607.B630A78B.object_0[715](array2.Length), null, 0, "user", array2))
			{
				e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
				return true;
			}
			throw Class607.B630A78B.object_0[778]("Error on writing seccfg config to flash.");
		}
		IEnumerable<byte> first = array.Take(4);
		byte[] array3 = new byte[4];
		DEBEDD9C.smethod_0(array3, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		if (first.SequenceEqual(array3))
		{
			e39CAE0B_0?.Invoke("v4", EF1F389C.Success);
			GClass64 gClass3 = new GClass64(b42DC);
			e39CAE0B_0?.Invoke("excuting aes key arguments :");
			if (!gClass3.method_0(array))
			{
				throw Class607.B630A78B.object_0[778]("Device has either already unlocked or the algorithm is unknown. Aborting.");
			}
			byte[] array4 = gClass3.method_1(text);
			if (method_4(b6BA.D78F1C10 * A68CE121.CF001212.uint_0, Class607.B630A78B.object_0[715](array4.Length), null, 0, "user", array4))
			{
				e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
				return true;
			}
			throw Class607.B630A78B.object_0[778]("Error on writing seccfg config to flash.");
		}
		throw Class607.B630A78B.object_0[778]("Unknown lockstate or no lockstate");
	}

	public bool method_4(ulong ulong_0, ulong ulong_1, string string_0 = "", int int_0 = 0, string string_1 = null, byte[] DD30B99D = null)
	{
		return (bool)new GClass128().DFB12B0F(new object[7] { this, ulong_0, ulong_1, string_0, int_0, string_1, DD30B99D }, 22970865);
	}

	public bool F79521B0(object C82E46A8, Stream stream_0, bool bool_1 = true, bool bool_2 = false)
	{
		if (C82E46A8 is EF3DC1AF)
		{
			EF3DC1AF eF3DC1AF = (EF3DC1AF)C82E46A8;
			string string_ = eF3DC1AF.String_1;
			ulong c8976E = eF3DC1AF.C8976E00;
			string object_ = Class538.smethod_0(string_, new char[1] { ':' })[2];
			ulong num = Class607.B630A78B.object_0[756](Class607.B630A78B.object_0[1211](object_, 2), NumberStyles.HexNumber);
			FD997205(num);
			string string_2 = method_5();
			if (Class607.B630A78B.object_0[787](string_2, "OK"))
			{
				ulong num2 = 0uL;
				for (ulong num3 = 0uL; num3 < num; num3 += c8976E)
				{
					method_11(0);
					string_2 = method_5();
					if (Class607.B630A78B.object_0[1240](string_2, "OK"))
					{
						byte[] array = new byte[c8976E];
						int num4 = Class607.B630A78B.object_0[53](stream_0, array, 0, array.Length);
						if (num4 == 0)
						{
							break;
						}
						if (array.Length != num4)
						{
							array = GClass112.smethod_14(array, 0, num4);
						}
						method_12(array);
						string_2 = method_5();
						if (Class607.B630A78B.object_0[1240](string_2, "OK"))
						{
							num2 += Class607.B630A78B.object_0[715](num4);
							gdelegate25_0?.Invoke(num, num2);
							continue;
						}
						e39CAE0B_0?.Invoke("Error: ");
						e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[1217]("Error on writing stage2 at pos {0:X}", num3), EF1F389C.Error);
						return false;
					}
					string b8ABF2AA = method_10(string_2, "message");
					e39CAE0B_0?.Invoke("Error: ");
					e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[1217]("Error on writing stage2 ACK0 at pos {0:X}", num3), EF1F389C.Error);
					e39CAE0B_0?.Invoke("msg: ");
					e39CAE0B_0?.Invoke(b8ABF2AA, EF1F389C.Error);
					return false;
				}
				if (bool_2)
				{
					FBB13E94();
				}
				C784AC8C().Deconstruct(out var item, out var item2);
				string string_3 = item;
				object obj = item2;
				FBB13E94();
				if (Class607.B630A78B.object_0[787](string_3, "CMD:END") && Class607.B630A78B.object_0[787]((string)obj, "OK"))
				{
					C784AC8C().Deconstruct(out item, out item2);
					string_3 = item;
					obj = item2;
					if (Class607.B630A78B.object_0[787](string_3, "CMD:START"))
					{
						return true;
					}
				}
				else
				{
					C784AC8C().Deconstruct(out item, out item2);
					string_3 = item;
					obj = item2;
					e39CAE0B_0?.Invoke("Error: ");
					e39CAE0B_0?.Invoke((string)obj, EF1F389C.Error);
				}
			}
			return false;
		}
		e39CAE0B_0?.Invoke("Error: ");
		e39CAE0B_0?.Invoke("No upload data received. Aborting.", EF1F389C.Error);
		return false;
	}

	public bool F5028197(ulong DEA45F98, ulong ulong_0, string BD153D24 = "", bool bool_1 = true)
	{
		return (bool)new GClass128().method_323(new object[5] { this, DEA45F98, ulong_0, BD153D24, bool_1 }, 22970147);
	}

	public GClass8(E5964FB0 E192A50C)
	{
		Class607.B630A78B.object_0[571](this);
		A68CE121 = E192A50C;
		gclass59_0 = new GClass59(E192A50C, method_6, EB2DBFAC);
	}

	public Tuple<byte[], GClass37> EB2DBFAC()
	{
		EC3D9B11(GClass5.smethod_1(), bool_1: true);
		C784AC8C().Deconstruct(out var item, out var item2);
		object obj = item2;
		if (!(obj is GClass6))
		{
			return null;
		}
		byte[] array = method_21((GClass6)obj);
		C784AC8C().Deconstruct(out item, out item2);
		object obj2 = item2;
		FBB13E94();
		if (Class607.B630A78B.object_0[787]((string)obj2, "OK"))
		{
			C784AC8C().Deconstruct(out item, out item2);
			object obj3 = item2;
			if (Class607.B630A78B.object_0[787]((string)obj3, "START"))
			{
				GClass37 gClass = new GClass37();
				gClass.list_0 = new List<GClass37.B6BA8237>();
				string e505FF2C = Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[1124](), array);
				string[] array2 = Class607.B630A78B.object_0[699](e505FF2C, "<pt>".ToArray());
				foreach (string string_ in array2)
				{
					string text = method_10(string_, "name");
					if (!Class607.B630A78B.object_0[1205](text))
					{
						string string_2 = method_10(string_, "start");
						string string_3 = method_10(string_, "size");
						if (!Class607.B630A78B.object_0[1205](string_3))
						{
							ulong num = Class607.B630A78B.object_0[756](string_3, NumberStyles.HexNumber);
							ulong num2 = Class607.B630A78B.object_0[756](string_2, NumberStyles.HexNumber);
							gClass.list_0.Add(new GClass37.B6BA8237
							{
								string_2 = text,
								F9937622 = num / A68CE121.CF001212.uint_0,
								ulong_2 = num2 / A68CE121.CF001212.uint_0
							});
						}
					}
				}
				return Tuple.Create(array, gClass);
			}
		}
		return null;
	}

	public string method_5(bool bool_1 = false)
	{
		byte[] byte_ = A68CE121.f7020D24_0.method_6(12).byte_0;
		if (byte_.Length == 12 && Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(byte_, 0, 4))[0]) == 4277071599u && Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(byte_, 4, 4))[0]) == 1)
		{
			int num = Class607.B630A78B.object_0[84](GClass111.C3B9331C("<I", GClass112.smethod_14(byte_, 8, 4))[0]);
			byte[] byte_2 = A68CE121.f7020D24_0.method_6(num).byte_0;
			if (byte_2.Length == num)
			{
				if (bool_1)
				{
					return Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[1058](byte_2), "-", "");
				}
				return Class607.B630A78B.object_0[647](Class607.B630A78B.object_0[920](Encoding.UTF8, byte_2.smethod_1(0)), new char[1]);
			}
		}
		return "";
	}

	public GClass51.GStruct57 method_6(ulong ulong_0, ulong D3103100, string string_0, string E8B8D688 = "")
	{
		GClass51.GStruct57 result = default(GClass51.GStruct57);
		if (Class607.B630A78B.object_0[1205](E8B8D688))
		{
			if (Class607.B630A78B.object_0[787](A68CE121.CF001212.string_1, "emmc"))
			{
				E8B8D688 = "user";
			}
			else if (Class607.B630A78B.object_0[787](A68CE121.CF001212.string_1, "ufs"))
			{
				E8B8D688 = "lu3";
			}
		}
		method_8(E8B8D688, D3103100).Deconstruct(out var _, out var item2, out var item3);
		string string_1 = item2;
		ulong ulong_1 = item3;
		EC3D9B11(GClass5.smethod_6(string_1, ulong_0, ulong_1), bool_1: true);
		C784AC8C().Deconstruct(out item2, out var item4);
		object obj = item4;
		if (obj is GClass6)
		{
			return result;
		}
		GClass51.GStruct57 result2 = method_7(obj, string_0);
		C784AC8C().Deconstruct(out item2, out item4);
		object obj2 = item4;
		if (Class607.B630A78B.object_0[787]((string)obj2, "START"))
		{
			return result2;
		}
		return default(GClass51.GStruct57);
	}

	public GClass51.GStruct57 method_7(object B78A5D86, string F72E5424 = "", bool bool_1 = false)
	{
		GClass51.GStruct57 result = default(GClass51.GStruct57);
		if (B78A5D86 is GClass6)
		{
			string object_ = method_5();
			if (Class607.B630A78B.object_0[1240](object_, "OK@"))
			{
				string object_2 = Class538.smethod_0(object_, new char[1] { '@' })[1];
				ulong num = Class607.B630A78B.object_0[1171](Class607.B630A78B.object_0[1211](object_2, 2), 16);
				FBB13E94();
				string object_3 = method_5();
				if (Class607.B630A78B.object_0[1240](object_3, "OK"))
				{
					FBB13E94();
					int num2 = 0;
					ulong num3 = num;
					Stream stream = ((!Class607.B630A78B.object_0[1205](F72E5424)) ? ((Stream)Class607.B630A78B.object_0[835](F72E5424, FileMode.OpenOrCreate)) : ((Stream)Class607.B630A78B.object_0[939]()));
					using (stream)
					{
						bool flag = false;
						while (num3 > 0L)
						{
							byte[] array = A3BBEDBB();
							num3 -= Class607.B630A78B.object_0[715](array.Length);
							num2 += array.Length;
							Class607.B630A78B.object_0[132](stream, array, 0, array.Length);
							gdelegate25_0?.Invoke(num, num3);
							FBB13E94();
							object_3 = method_5();
							if (Class607.B630A78B.object_0[1240](object_3, "OK"))
							{
								FBB13E94();
								continue;
							}
							flag = true;
							break;
						}
						if (flag)
						{
							return result;
						}
						if (!Class607.B630A78B.object_0[1205](F72E5424))
						{
							result.BD147F02 = true;
						}
						else
						{
							result.BD147F02 = true;
							result.byte_0 = Class607.B630A78B.object_0[1126]((MemoryStream)stream);
						}
					}
				}
			}
		}
		else
		{
			e39CAE0B_0?.Invoke("Error: ");
			e39CAE0B_0?.Invoke("No download data received. Aborting.", EF1F389C.Error);
		}
		return result;
	}

	public Tuple<int, string, ulong> method_8(string string_0, ulong A8925B00)
	{
		string string_1 = A68CE121.CF001212.string_1;
		string string_2 = string_1;
		int int_;
		if (!Class607.B630A78B.object_0[787](string_2, "nor"))
		{
			if (!Class607.B630A78B.object_0[787](string_2, "nand"))
			{
				if (!Class607.B630A78B.object_0[787](string_2, "ufs"))
				{
					int_ = ((!Class607.B630A78B.object_0[787](string_2, "sdc")) ? 1 : 2);
				}
				else
				{
					int_ = 48;
					if (string_0.F02F0138() && Class607.B630A78B.object_0[754](string_0) == 8)
					{
						string_0 = Class607.B630A78B.object_0[924](3);
					}
				}
			}
			else
			{
				int_ = 16;
			}
		}
		else
		{
			int_ = 32;
		}
		return method_9(int_, string_0, A8925B00);
	}

	public Tuple<int, string, ulong> method_9(int int_0, string string_0 = null, ulong ulong_0 = 0uL)
	{
		if (ulong_0 < 131072L)
		{
			ulong_0 = 131072uL;
		}
		string text2;
		char c;
		switch (int_0)
		{
		case 16:
		case 17:
		case 18:
		case 19:
		case 20:
		case 21:
			string_0 = "NAND-WHOLE";
			break;
		case 1:
		case 2:
		{
			int_0 = 1;
			string text3 = string_0;
			string text4 = text3;
			if (text4 != null && (text4 == null || Class607.B630A78B.object_0[343](text4) != 0) && !Class607.B630A78B.object_0[787](text4, "user"))
			{
				if (!Class607.B630A78B.object_0[787](text4, "boot1"))
				{
					if (!Class607.B630A78B.object_0[787](text4, "boot2"))
					{
						if (!Class607.B630A78B.object_0[787](text4, "gp1"))
						{
							e39CAE0B_0?.Invoke("error: ");
							e39CAE0B_0?.Invoke("Unknown parttype. Known parttypes are \"boot1\",\"boot2\",\"gp1\",\"gp2\",\"gp3\",\"gp4\",\"rpmb\"", EF1F389C.Error);
							return null;
						}
						string_0 = "EMMC-GP1";
						if (Class607.B630A78B.object_0[787](A68CE121.CF001212.string_1, "emmc"))
						{
							ulong_0 = Class607.B630A78B.object_0[743](ulong_0, gstruct49_0.ulong_3);
						}
					}
					else
					{
						string_0 = "EMMC-BOOT2";
						if (Class607.B630A78B.object_0[787](A68CE121.CF001212.string_1, "emmc"))
						{
							ulong_0 = Class607.B630A78B.object_0[743](ulong_0, gstruct49_0.B61F2413);
						}
					}
				}
				else
				{
					string_0 = "EMMC-BOOT1";
					if (Class607.B630A78B.object_0[787](A68CE121.CF001212.string_1, "emmc"))
					{
						ulong_0 = Class607.B630A78B.object_0[743](ulong_0, gstruct49_0.ulong_1);
					}
				}
			}
			else
			{
				string_0 = "EMMC-USER";
			}
			break;
		}
		case 48:
		{
			string text = string_0;
			text2 = text;
			if (!Class607.B630A78B.object_0[787](text2, "") && text2 != null)
			{
				if (text2 != null)
				{
					switch (Class607.B630A78B.object_0[343](text2))
					{
					case 3:
						break;
					case 4:
						goto IL_033c;
					case 5:
						goto IL_03c9;
					default:
						goto IL_0472;
					}
					switch (Class607.B630A78B.object_0[1091](text2, 2))
					{
					case '1':
						break;
					case '2':
						goto IL_02dc;
					case '3':
						goto IL_02fd;
					case '4':
						goto IL_031e;
					default:
						goto IL_0472;
					}
					if (Class607.B630A78B.object_0[787](text2, "lu1"))
					{
						goto IL_044b;
					}
				}
				goto IL_0472;
			}
			goto IL_04a9;
		}
		case 32:
		case 33:
		case 34:
			{
				string_0 = "NOR-WHOLE";
				break;
			}
			IL_031e:
			if (Class607.B630A78B.object_0[787](text2, "lu4"))
			{
				goto IL_039f;
			}
			goto IL_0472;
			IL_039f:
			string_0 = "UFS-LUA3";
			ulong_0 = Class607.B630A78B.object_0[743](ulong_0, DE28AC3C.B8BA8BB6);
			break;
			IL_02fd:
			if (!Class607.B630A78B.object_0[787](text2, "lu3"))
			{
				goto IL_0472;
			}
			goto IL_04a9;
			IL_03c9:
			c = Class607.B630A78B.object_0[1091](text2, 4);
			if (c != '1')
			{
				if (c == '2' && Class607.B630A78B.object_0[787](text2, "boot2"))
				{
					goto IL_0408;
				}
			}
			else if (Class607.B630A78B.object_0[787](text2, "boot1"))
			{
				goto IL_044b;
			}
			goto IL_0472;
			IL_0408:
			string_0 = "UFS-LUA1";
			ulong_0 = Class607.B630A78B.object_0[743](ulong_0, DE28AC3C.ulong_1);
			break;
			IL_0472:
			e39CAE0B_0?.Invoke("Error: ");
			e39CAE0B_0?.Invoke("Unknown parttype. Known parttypes are \"lu1\",\"lu2\",\"lu3\",\"lu4\"", EF1F389C.Error);
			return null;
			IL_044b:
			string_0 = "UFS-LUA0";
			ulong_0 = Class607.B630A78B.object_0[743](ulong_0, DE28AC3C.ulong_0);
			break;
			IL_033c:
			c = Class607.B630A78B.object_0[1091](text2, 0);
			if (c != 'r')
			{
				if (c == 'u' && Class607.B630A78B.object_0[787](text2, "user"))
				{
					goto IL_04a9;
				}
			}
			else if (Class607.B630A78B.object_0[787](text2, "rpmb"))
			{
				goto IL_039f;
			}
			goto IL_0472;
			IL_04a9:
			string_0 = "UFS-LUA2";
			ulong_0 = Class607.B630A78B.object_0[743](ulong_0, DE28AC3C.ulong_2);
			break;
			IL_02dc:
			if (Class607.B630A78B.object_0[787](text2, "lu2"))
			{
				goto IL_0408;
			}
			goto IL_0472;
		}
		return new Tuple<int, string, ulong>(int_0, string_0, ulong_0);
	}

	public string method_10(string string_0, string D019A18C)
	{
		if (GClass112.smethod_3(string_0))
		{
			string_0 = Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[1124](), GClass112.FF06B0AD(string_0));
		}
		string text = string_0;
		int num = Class607.B630A78B.object_0[1145](text, Class607.B630A78B.object_0[1140]("<", D019A18C, ">"));
		if (num != -1)
		{
			int num2 = Class607.B630A78B.object_0[1119](text, Class607.B630A78B.object_0[1140]("</", D019A18C, ">"), num + Class607.B630A78B.object_0[342](D019A18C) + 2);
			if (num != -1 && num2 != -1)
			{
				return Class607.B630A78B.object_0[31](text, num + Class607.B630A78B.object_0[342](D019A18C) + 2, num2 - (num + Class607.B630A78B.object_0[342](D019A18C) + 2));
			}
		}
		return "";
	}

	public bool FBB13E94()
	{
		return method_12("OK");
	}

	public bool FD997205(ulong C4A3F001)
	{
		return method_12(Class607.B630A78B.object_0[1217]("OK@{0:X}", C4A3F001));
	}

	public bool method_11(int int_0)
	{
		return method_12(Class607.B630A78B.object_0[1217]("OK@{0:X}", int_0));
	}

	public bool DC396D83(string string_0)
	{
		return method_12(Class607.B630A78B.object_0[720]("OK@", string_0));
	}

	public bool method_12(object object_0, int int_0 = 1, bool bool_1 = false)
	{
		int num;
		if (!GClass112.smethod_19(object_0))
		{
			num = ((!(object_0 is string)) ? ((byte[])object_0).Length : (Class607.B630A78B.object_0[342]((string)object_0) + 1));
		}
		else if (bool_1)
		{
			object_0 = GClass111.smethod_2("<Q", new object[1] { object_0 });
			num = 8;
		}
		else
		{
			object_0 = GClass111.smethod_2("<I", new object[1] { object_0 });
			num = 4;
		}
		byte[] object_1 = GClass111.smethod_2("<III", new object[3] { 4277071599u, int_0, num });
		if (A68CE121.f7020D24_0.method_1(object_1))
		{
			if (object_0 is string)
			{
				List<byte> list = new List<byte>();
				list.AddRange(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1124](), (string)object_0));
				list.Add(0);
				return A68CE121.f7020D24_0.method_1(list.ToArray());
			}
			return A68CE121.f7020D24_0.method_1((byte[])object_0);
		}
		return false;
	}

	public byte[] A3BBEDBB()
	{
		byte[] byte_ = A68CE121.f7020D24_0.method_6(12).byte_0;
		if (byte_.Length == 12 && Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(byte_, 0, 4))[0]) == 4277071599u && Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(byte_, 4, 4))[0]) == 1)
		{
			int num = Class607.B630A78B.object_0[84](GClass111.C3B9331C("<I", GClass112.smethod_14(byte_, 8, 4))[0]);
			int int_ = A68CE121.f7020D24_0.FD86C6B3();
			byte[] array = new byte[num];
			byte[] byte_2;
			for (int i = 0; i < num; i += byte_2.Length)
			{
				int int_2 = Class607.B630A78B.object_0[78](int_, num - i);
				byte_2 = A68CE121.f7020D24_0.method_6(int_2).byte_0;
				Class607.B630A78B.object_0[1144](byte_2, 0, array, i, byte_2.Length);
			}
			return array;
		}
		return new byte[0];
	}

	public Tuple<string, object> C784AC8C()
	{
		string text = method_5();
		string text2 = method_10(text, "command");
		string text3 = "";
		if (Class607.B630A78B.object_0[787](text2, "") && Class607.B630A78B.object_0[1240](text, "OK@"))
		{
			string object_ = Class538.smethod_0(text, new char[1] { '@' })[1];
			int num = Class607.B630A78B.object_0[633](Class607.B630A78B.object_0[1211](object_, 2), NumberStyles.HexNumber);
			FBB13E94();
			string object_2 = method_5();
			if (Class607.B630A78B.object_0[1240](object_2, "OK"))
			{
				FBB13E94();
				byte[] array = new byte[0];
				int num2 = 0;
				int num3 = num;
				while (num3 > 0)
				{
					byte[] array2 = A3BBEDBB();
					num3 -= array2.Length;
					num2 += array2.Length;
					array = array.Concat(array2).ToArray();
				}
				FBB13E94();
				return new Tuple<string, object>(text2, Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[1058](array), "-", ""));
			}
		}
		if (Class607.B630A78B.object_0[787](text2, "CMD:PROGRESS-REPORT"))
		{
			FBB13E94();
			while (!Class607.B630A78B.object_0[1007](text, "OK!EOT"))
			{
				text = method_5();
				FBB13E94();
			}
			text = method_5();
			text2 = method_10(text, "command");
		}
		if (Class607.B630A78B.object_0[787](text2, "CMD:START"))
		{
			FBB13E94();
			return new Tuple<string, object>(text2, "START");
		}
		if (Class607.B630A78B.object_0[787](text2, "CMD:DOWNLOAD-FILE"))
		{
			string string_ = method_10(text, "checksum");
			string string_2 = method_10(text, "info");
			string eD25F1B = method_10(text, "source_file");
			ulong ulong_ = Class607.B630A78B.object_0[756](method_10(text, "packet_length"), NumberStyles.HexNumber);
			FBB13E94();
			return new Tuple<string, object>(text2, new EF3DC1AF(string_, string_2, eD25F1B, ulong_));
		}
		if (Class607.B630A78B.object_0[787](text2, "CMD:UPLOAD-FILE"))
		{
			string string_3 = method_10(text, "checksum");
			string string_4 = method_10(text, "info");
			string string_5 = method_10(text, "target_file");
			string e43AB = method_10(text, "packet_length");
			FBB13E94();
			return new Tuple<string, object>(text2, new GClass6(string_3, string_4, string_5, e43AB));
		}
		if (Class607.B630A78B.object_0[787](text2, "CMD:FILE-SYS-OPERATION"))
		{
			string c9126C = method_10(text, "key");
			string string_6 = method_10(text, "file_path");
			FBB13E94();
			return new Tuple<string, object>(text2, new EBB8EB1D(c9126C, string_6));
		}
		if (Class607.B630A78B.object_0[787](text2, "CMD:END"))
		{
			text3 = method_10(text, "result");
			if (Class607.B630A78B.object_0[1240](text, "message") && Class607.B630A78B.object_0[121](text3, "OK"))
			{
				string item = method_10(text, "message");
				return new Tuple<string, object>(text2, item);
			}
		}
		return new Tuple<string, object>(text2, text3);
	}

	public object CBB7AE06()
	{
		return EC3D9B11(GClass5.smethod_12());
	}

	public object EC3D9B11(string D5A0D886, bool bool_1 = false)
	{
		if (method_12(D5A0D886))
		{
			string text = method_5();
			string item;
			object item2;
			if (Class607.B630A78B.object_0[787](text, "OK"))
			{
				if (bool_1)
				{
					return true;
				}
				C784AC8C().Deconstruct(out item, out item2);
				string string_ = item;
				object result = item2;
				if (!Class607.B630A78B.object_0[787](string_, "CMD:END"))
				{
					return result;
				}
				FBB13E94();
				C784AC8C().Deconstruct(out item, out item2);
				string string_2 = item;
				object obj = item2;
				if (Class607.B630A78B.object_0[787](string_2, "CMD:START"))
				{
					if (Class607.B630A78B.object_0[787]((string)obj, "OK"))
					{
						return true;
					}
					e39CAE0B_0?.Invoke("Error: ");
					e39CAE0B_0?.Invoke((string)obj, EF1F389C.Error);
					return false;
				}
			}
			else if (Class607.B630A78B.object_0[787](text, "ERR!UNSUPPORTED"))
			{
				C784AC8C().Deconstruct(out item, out item2);
				object result2 = item2;
				FBB13E94();
				C784AC8C().Deconstruct(out item, out item2);
				string string_3 = item;
				if (Class607.B630A78B.object_0[787](string_3, "CMD:START"))
				{
					return result2;
				}
			}
			else if (Class607.B630A78B.object_0[1240](text, "ERR!"))
			{
				return text;
			}
		}
		return false;
	}

	public bool method_13()
	{
		return (bool)new GClass128().AD84C1A2(new object[1] { this }, 407576);
	}

	public object method_14(string DE28C11E = "")
	{
		return EC3D9B11(GClass5.smethod_10(DE28C11E));
	}

	public byte[] method_15(byte[] byte_0)
	{
		return byte_0;
	}

	public static int smethod_0(byte[] byte_0, byte[] byte_1, int BB9E72B6 = 0)
	{
		byte[][] array = GClass112.smethod_10(byte_1, new byte[1] { 46 });
		int num = 0;
		List<int> list = new List<int>();
		while (true)
		{
			if (num != -1)
			{
				num = GClass112.smethod_24(GClass112.smethod_14(byte_0, BB9E72B6, byte_0.Length - BB9E72B6), array[0], num);
				if (num == -1)
				{
					if (list.Count <= 0)
					{
						break;
					}
					foreach (int item in list)
					{
						int num2 = 0;
						int num3 = item + array[0].Length;
						for (int i = 1; i < array.Length; i++)
						{
							if (array[i].Length == 0)
							{
								num3++;
								continue;
							}
							num3++;
							if (GClass112.smethod_24(GClass112.smethod_14(byte_0, num3, byte_0.Length - num3), array[i], 0) == 0)
							{
								num3 += array[i].Length;
								continue;
							}
							num2 = 1;
							break;
						}
						if (num2 == 0)
						{
							return item + BB9E72B6;
						}
					}
				}
				else
				{
					list.Add(num);
					num++;
				}
				continue;
			}
			return -1;
		}
		return -1;
	}

	public static (uint, uint) C810090F(uint F61C5E17, uint uint_1, uint C0B004B2)
	{
		uint num = F61C5E17 + C0B004B2;
		uint num2 = ((((num & 0xFFFF) >> 12) & 0xF) << 16) | (uint_1 << 14) | (num & 0xFFF);
		num >>= 16;
		uint num3 = ((((num & 0xFFFF) >> 12) & 0xF) << 16) | (uint_1 << 14) | (num & 0xFFF) | 0x400000;
		uint item = 3808428032u + num2;
		uint item2 = 3808428032u + num3;
		return (item, item2);
	}

	public static uint? E6395BB3(uint FEABD31D, uint uint_1, uint uint_2)
	{
		uint num = (FEABD31D & 0xF000) >> 12;
		uint num2 = (uint_1 & 0xF000) >> 12;
		uint num3 = (FEABD31D & 0xF00000) >> 20;
		uint num4 = (uint_1 & 0xF00000) >> 20;
		uint num5 = (((uint_1 & 0xF0000) >> 4) | (uint_1 & 0xFFF)) << (int)(num4 * 4);
		uint num6 = (((FEABD31D & 0xF0000) >> 4) | (FEABD31D & 0xFFF)) << (int)(num3 * 4);
		if (num == num2 && num2 == uint_2)
		{
			return num5 | num6;
		}
		return null;
	}

	public byte[] method_16(byte[] byte_0)
	{
		uint_0 = A68CE121.gclass51_0.AA329715.********.list_0[2].uint_1;
		byte[] array = (byte[])Class607.B630A78B.object_0[302](byte_0);
		byte[] array2 = new byte[19];
		DEBEDD9C.smethod_0(array2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		byte[] array3 = array2;
		int num = Array.IndexOf(array, array3[0], 0, array.Length);
		while (num != -1 && GClass112.smethod_24(array, array3, num) != -1)
		{
			num = Array.IndexOf(array, array3[0], num + 1);
		}
		uint num2 = uint_0;
		if (num != -1)
		{
			(uint, uint) tuple = C810090F((uint)(num + 1), 0u, num2);
			uint item = tuple.Item1;
			uint item2 = tuple.Item2;
			byte[] array4 = Class607.B630A78B.object_0[190](item);
			byte[] array5 = Class607.B630A78B.object_0[190](item2);
			int num3 = Array.IndexOf(array, array4[0], 0, array.Length);
			while (num3 != -1 && GClass112.smethod_24(array, array4, num3) != -1)
			{
				num3 = Array.IndexOf(array, array4[0], num3 + 1);
			}
			int num4 = Array.IndexOf(array, array5[0], num3, array.Length - num3);
			while (num4 != -1 && GClass112.smethod_24(array, array4, num3) != -1)
			{
				num4 = Array.IndexOf(array, array5[0], num4 + 1);
			}
			if (num3 + 8 == num4)
			{
				uint fEABD31D = Class607.B630A78B.object_0[1251](array, num3 + 4);
				uint uint_ = Class607.B630A78B.object_0[1251](array, num4 + 4);
				uint? num5 = E6395BB3(fEABD31D, uint_, 2u);
				if (num5.HasValue)
				{
					num5 -= num2;
					byte[] array6 = GClass112.FF06B0AD("704c2de910b08de20080a0e100000fe3000846e30410a0e3002098e532ff2fe100000fe3000846e3000000e3000846e3002098e532ff2fe1000000e3000846e330ff2fe1708cbde8");
					Class607.B630A78B.object_0[242](array6, 0, array, (int)num5.Value, array6.Length);
					byte[] array7 = new byte[11];
					DEBEDD9C.smethod_0(array7, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
					byte[] array8 = array7;
					Class607.B630A78B.object_0[242](array8, 0, array, num + 1, array8.Length);
					return array;
				}
			}
		}
		return byte_0;
	}

	public byte[] BDB99982(byte[] CA14EBB1)
	{
		e39CAE0B_0?.Invoke("Patching da2 ...");
		byte[] array = (byte[])Class607.B630A78B.object_0[302](CA14EBB1);
		int num = 0;
		int num2;
		while (true)
		{
			byte[] array2 = new byte[20];
			DEBEDD9C.smethod_0(array2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			if ((num2 = smethod_0(CA14EBB1, array2, num)) == -1)
			{
				break;
			}
			int num3 = Class607.B630A78B.object_0[41](array, num2 + 12) - 1;
			byte[] array3 = new byte[16];
			DEBEDD9C.smethod_0(array3, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			byte[] array_ = array3.Concat(Class607.B630A78B.object_0[266]((ushort)num3)).Concat(new byte[2] { 0, 235 }).ToArray();
			Class607.B630A78B.object_0[242](array_, 0, array, num2, 20);
			num += num2 + 20;
		}
		array = method_16(CA14EBB1);
		byte[] byte_ = array;
		byte[] array4 = new byte[25];
		DEBEDD9C.smethod_0(array4, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		num2 = smethod_0(byte_, array4);
		if (num2 != -1)
		{
			byte[] array5 = new byte[24];
			DEBEDD9C.smethod_0(array5, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			byte[] array_2 = array5;
			Class607.B630A78B.object_0[242](array_2, 0, array, num2 - 1, 24);
		}
		byte[] byte_2 = array;
		byte[] array6 = new byte[8];
		DEBEDD9C.smethod_0(array6, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		num2 = smethod_0(byte_2, array6);
		if (num2 != -1)
		{
			byte[] array7 = new byte[8];
			DEBEDD9C.smethod_0(array7, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			byte[] array_3 = array7;
			Class607.B630A78B.object_0[242](array_3, 0, array, num2, 8);
		}
		byte[] byte_3 = array;
		byte[] array8 = new byte[16];
		DEBEDD9C.smethod_0(array8, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int? num4 = smethod_0(byte_3, array8);
		if (num4.HasValue)
		{
			byte[] array9 = new byte[8];
			DEBEDD9C.smethod_0(array9, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			byte[] array_4 = array9;
			Class607.B630A78B.object_0[242](array_4, 0, array, num4.Value, 8);
		}
		else
		{
			byte[] byte_4 = array;
			byte[] array10 = new byte[16];
			DEBEDD9C.smethod_0(array10, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			num4 = smethod_0(byte_4, array10);
			if (num4.HasValue)
			{
				byte[] array11 = new byte[8];
				DEBEDD9C.smethod_0(array11, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				byte[] array_5 = array11;
				Class607.B630A78B.object_0[242](array_5, 0, array, num4.Value, 8);
				byte[] byte_5 = array;
				byte[] array12 = new byte[16];
				DEBEDD9C.smethod_0(array12, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				int? num5 = smethod_0(byte_5, array12);
				if (num5.HasValue)
				{
					array_5 = new byte[4] { 255, 0, 0, 0 };
					Class607.B630A78B.object_0[242](array_5, 0, array, num5.Value, 4);
				}
			}
			else
			{
				byte[] byte_6 = array;
				byte[] array13 = new byte[28];
				DEBEDD9C.smethod_0(array13, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num4 = smethod_0(byte_6, array13);
				if (num4.HasValue)
				{
					byte[] array14 = new byte[8];
					DEBEDD9C.smethod_0(array14, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
					byte[] array_6 = array14;
					Class607.B630A78B.object_0[242](array_6, 0, array, num4.Value, 8);
				}
				else
				{
					for (num2 = Array.IndexOf(array, (byte)68, 0, array.Length); num2 != -1; num2 = Array.IndexOf(array, (byte)68, num2 + 1))
					{
						byte[] byte_7 = array;
						byte[] array15 = new byte[14];
						DEBEDD9C.smethod_0(array15, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
						if (smethod_0(byte_7, array15, num2) == -1)
						{
							break;
						}
					}
					if (num2 != -1)
					{
						byte[] array16 = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[47](), "DA.SLA\0DISABLE");
						Class607.B630A78B.object_0[242](array16, 0, array, num2, array16.Length);
					}
				}
			}
		}
		return array;
	}

	public Tuple<byte[], byte[]> A918B297(byte[] byte_0, byte[] B807C7AE)
	{
		int a08677A = Class607.B630A78B.object_0[1262](A68CE121.gclass51_0.AA329715.********.list_0[1].uint_3);
		int num = Class607.B630A78B.object_0[1262](A68CE121.gclass51_0.AA329715.********.list_0[2].uint_3);
		var (num5, int_, num6) = A68CE121.gclass51_0.method_12(byte_0, B807C7AE, a08677A, num, A68CE121.gclass51_0.AA329715.A08FA093);
		if (num5 != -1)
		{
			byte_0 = method_15(byte_0);
			B807C7AE = BDB99982(B807C7AE);
			byte_0 = GClass67.A826F023(byte_0, B807C7AE, num5, int_, num6);
			A68CE121.gclass51_0.bool_1 = true;
			A68CE121.gclass51_0.AA329715.byte_0 = B807C7AE.Take(num6).ToArray();
		}
		else
		{
			A68CE121.gclass51_0.bool_1 = false;
			A68CE121.gclass51_0.AA329715.byte_0 = B807C7AE.Take(B807C7AE.Length - num).ToArray();
		}
		return Tuple.Create(byte_0, B807C7AE);
	}

	public bool method_17()
	{
		return (bool)new GClass128().DFB12B0F(new object[1] { this }, 68052);
	}

	public bool method_18(uint uint_1, byte[] byte_0, bool FD96B590 = true, double A3B44C0C = 0.5)
	{
		object obj = EC3D9B11(GClass5.smethod_9(uint_1, uint_1, 140635416404044L, byte_0.Length));
		if (obj is EF3DC1AF)
		{
			e39CAE0B_0?.Invoke("Uploading stage 2: ");
			if (method_19((EF3DC1AF)obj, byte_0))
			{
				e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
				return true;
			}
		}
		else
		{
			e39CAE0B_0?.Invoke("Error: ");
			e39CAE0B_0?.Invoke("Wrong boot_to response :(", EF1F389C.Error);
		}
		return false;
	}

	public bool method_19(EF3DC1AF E7B2AA31, byte[] byte_0, bool bool_1 = true, bool bool_2 = false)
	{
		if (E7B2AA31 != null)
		{
			string string_ = E7B2AA31.String_1;
			ulong c8976E = E7B2AA31.C8976E00;
			string object_ = Class538.smethod_0(string_, new char[1] { ':' })[2];
			ulong num = Class607.B630A78B.object_0[756](Class607.B630A78B.object_0[1211](object_, 2), NumberStyles.HexNumber);
			FD997205(num);
			if (!bool_1)
			{
			}
			string string_2 = method_5();
			int num2 = 0;
			if (Class607.B630A78B.object_0[787](string_2, "OK"))
			{
				for (ulong num3 = 0uL; num3 < num; num3 += c8976E)
				{
					method_11(0);
					string_2 = method_5();
					if (Class607.B630A78B.object_0[1240](string_2, "OK"))
					{
						byte[] array = new byte[Class607.B630A78B.object_0[743](c8976E, num - num3)];
						Class607.B630A78B.object_0[1144](byte_0, Class607.B630A78B.object_0[704](num3), array, 0, array.Length);
						method_12(array);
						string_2 = method_5();
						if (Class607.B630A78B.object_0[1240](string_2, "OK"))
						{
							num2 += array.Length;
							if (!bool_1)
							{
							}
							continue;
						}
						e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[1217]("Error on writing stage2 at pos {0:X}", num3));
						return false;
					}
					string b8ABF2AA = method_10(string_2, "message");
					e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[1217]("Error on writing stage2 ACK0 at pos {0:X} : ", num3));
					e39CAE0B_0?.Invoke(b8ABF2AA, EF1F389C.Error);
					return false;
				}
				if (bool_2)
				{
					FBB13E94();
				}
				C784AC8C().Deconstruct(out var item, out var item2);
				string string_3 = item;
				object obj = item2;
				FBB13E94();
				if (Class607.B630A78B.object_0[787](string_3, "CMD:END") && Class607.B630A78B.object_0[787]((string)obj, "OK"))
				{
					C784AC8C().Deconstruct(out item, out item2);
					string_3 = item;
					obj = item2;
					if (Class607.B630A78B.object_0[787](string_3, "CMD:START"))
					{
						return true;
					}
				}
				else
				{
					C784AC8C().Deconstruct(out item, out item2);
					string_3 = item;
					obj = item2;
					e39CAE0B_0?.Invoke("Error: ");
					e39CAE0B_0?.Invoke((string)obj, EF1F389C.Error);
				}
			}
			return false;
		}
		e39CAE0B_0?.Invoke("Error: ");
		e39CAE0B_0?.Invoke("No upload data received. Aborting.", EF1F389C.Error);
		return false;
	}

	public bool C728DE1B()
	{
		object obj = EC3D9B11(GClass5.smethod_8());
		if (obj is string && Class607.B630A78B.object_0[1240]((string)obj, "Unsupported"))
		{
			return false;
		}
		return true;
	}

	public bool method_20()
	{
		byte[] f0AED = method_22();
		string object_ = Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[1124](), f0AED);
		if (Class607.B630A78B.object_0[1240](object_, "item key="))
		{
			int int_ = Class607.B630A78B.object_0[1145](object_, "item key=") + 8;
			string object_2 = Class607.B630A78B.object_0[1211](object_, int_);
			string string_ = Class607.B630A78B.object_0[31](object_2, Class607.B630A78B.object_0[1145](object_2, ">") + 1, Class607.B630A78B.object_0[1145](object_2, "<") - Class607.B630A78B.object_0[1145](object_2, ">") - 1);
			if (Class607.B630A78B.object_0[787](string_, "DISABLED"))
			{
				return false;
			}
			return true;
		}
		e39CAE0B_0?.Invoke("Couldn't find item key");
		return false;
	}

	public byte[] method_21(GClass6 gclass6_0)
	{
		if (gclass6_0 != null)
		{
			string text = method_5();
			if (Class607.B630A78B.object_0[1240](text, "OK@"))
			{
				string object_ = Class538.smethod_0(text, new char[1] { '@' })[1];
				int num = Class607.B630A78B.object_0[259](Class607.B630A78B.object_0[1211](object_, 2), 16);
				FBB13E94();
				string object_2 = method_5();
				if (Class607.B630A78B.object_0[1240](object_2, "OK"))
				{
					FBB13E94();
					byte[] array = new byte[0];
					int num2 = 0;
					int num3 = num;
					while (num3 > 0)
					{
						byte[] array2 = A3BBEDBB();
						num3 -= array2.Length;
						num2 += array2.Length;
						array = array.Concat(array2).ToArray();
					}
					FBB13E94();
					return array;
				}
				e39CAE0B_0?.Invoke("Error on downloading data:");
				e39CAE0B_0?.Invoke(text, EF1F389C.Error);
				return null;
			}
			e39CAE0B_0?.Invoke("No download data received. Aborting.");
			return null;
		}
		e39CAE0B_0?.Invoke("Invalid data type for download. Aborting.");
		return null;
	}

	public byte[] method_22(string D3BBE2BC = "DA.SLA", int CA3F221F = 2097152)
	{
		EC3D9B11(GClass5.C0865F21(D3BBE2BC, CA3F221F), bool_1: true);
		C784AC8C().Deconstruct(out var item, out var item2);
		object obj = item2;
		if (!(obj is GClass6))
		{
			return null;
		}
		byte[] result = method_21((GClass6)obj);
		C784AC8C().Deconstruct(out item, out item2);
		object obj2 = item2;
		FBB13E94();
		if (Class607.B630A78B.object_0[787]((string)obj2, "OK"))
		{
			C784AC8C().Deconstruct(out item, out item2);
			object obj3 = item2;
			if (Class607.B630A78B.object_0[787]((string)obj3, "START"))
			{
				return result;
			}
		}
		return null;
	}

	public GClass7 method_23()
	{
		EC3D9B11(GClass5.A80E0304(), bool_1: true);
		C784AC8C().Deconstruct(out var item, out var item2);
		object obj = item2;
		if (!(obj is GClass6))
		{
			return null;
		}
		byte[] f0AED = method_21((GClass6)obj);
		C784AC8C().Deconstruct(out item, out item2);
		object obj2 = item2;
		FBB13E94();
		if (Class607.B630A78B.object_0[787]((string)obj2, "OK"))
		{
			C784AC8C().Deconstruct(out item, out item2);
			object obj3 = item2;
			if (Class607.B630A78B.object_0[787]((string)obj3, "START"))
			{
				string string_ = method_10(Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[1096](), f0AED), "storage");
				return new GClass7(this, e39CAE0B_0, string_, Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[1096](), f0AED));
			}
		}
		return null;
	}

	public void A721FE2D(bool A5022E3C = false)
	{
		new GClass128().method_68(new object[2] { this, A5022E3C }, 116008);
	}

	public bool method_24()
	{
		EC3D9B11(GClass5.smethod_7("LIFE-CYCLE-STATUS"), bool_1: true);
		Tuple<string, object> tuple = C784AC8C();
		if (!(tuple.Item2 is GClass6))
		{
			if (Class607.B630A78B.object_0[787](tuple.Item1, "CMD:END"))
			{
				FBB13E94();
				tuple = C784AC8C();
			}
			return false;
		}
		byte[] first = method_21((GClass6)tuple.Item2);
		Tuple<string, object> tuple2 = C784AC8C();
		FBB13E94();
		if (Class607.B630A78B.object_0[787]((string)tuple2.Item2, "OK"))
		{
			Tuple<string, object> tuple3 = C784AC8C();
			if (Class607.B630A78B.object_0[787]((string)tuple3.Item2, "START"))
			{
				if (first.SequenceEqual(new byte[3] { 79, 75, 0 }))
				{
					return true;
				}
				return false;
			}
		}
		return false;
	}

	public Dictionary<string, byte[]> method_25()
	{
		EC3D9B11(GClass5.smethod_0(), bool_1: true);
		C784AC8C().Deconstruct(out var item, out var item2);
		object obj = item2;
		if (!(obj is GClass6))
		{
			return null;
		}
		string text = Class607.B630A78B.object_0[920](Encoding.UTF8, method_21((GClass6)obj));
		C784AC8C().Deconstruct(out item, out item2);
		object obj2 = item2;
		FBB13E94();
		if (Class607.B630A78B.object_0[787]((string)obj2, "OK"))
		{
			Dictionary<string, byte[]> dictionary = new Dictionary<string, byte[]>();
			if (Class607.B630A78B.object_0[1240](text, "rnd"))
			{
				dictionary["rnd"] = GClass112.FF06B0AD(method_10(text, "rnd"));
			}
			if (Class607.B630A78B.object_0[1240](text, "hrid"))
			{
				dictionary["hrid"] = GClass112.FF06B0AD(method_10(text, "hrid"));
			}
			if (Class607.B630A78B.object_0[1240](text, "socid"))
			{
				dictionary["socid"] = GClass112.FF06B0AD(method_10(text, "socid"));
			}
			C784AC8C().Deconstruct(out item, out item2);
			object obj3 = item2;
			if (Class607.B630A78B.object_0[787]((string)obj3, "START"))
			{
				return dictionary;
			}
		}
		return null;
	}

	public bool method_26(byte[] byte_0)
	{
		return (bool)new GClass128().EF8D5E3B(new object[2] { this, byte_0 }, 22519369);
	}

	public bool EB3DBF1D()
	{
		string object_ = GClass5.smethod_11("CUSTOMACK");
		if (method_12(object_))
		{
			method_5();
			string string_ = method_5(bool_1: true);
			method_5();
			FBB13E94();
			method_5();
			FBB13E94();
			byte[] first = GClass112.FF06B0AD(string_);
			byte[] array = new byte[4];
			DEBEDD9C.smethod_0(array, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			if (first.SequenceEqual(array))
			{
				return true;
			}
		}
		return false;
	}

	public byte[] EA92B43F(byte[] F4B5A199, RSAParameters rsaparameters_0)
	{
		using RSA rSA = Class607.B630A78B.object_0[122]();
		Class607.B630A78B.object_0[43](rSA, rsaparameters_0);
		return Class607.B630A78B.object_0[341](rSA, F4B5A199, Class607.B630A78B.object_0[105]());
	}

	public bool method_27()
	{
		return (bool)new GClass128().method_323(new object[1] { this }, 261156);
	}
}
