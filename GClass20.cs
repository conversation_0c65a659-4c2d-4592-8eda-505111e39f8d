public class GClass20
{
	public GClass12 gclass12_0;

	public GClass18 gclass18_0;

	public C51E6F3B c51E6F3B_0;

	public GClass9 gclass9_0;

	public int BA37A0B1;

	public uint D3296C01;

	public uint E085BB8F;

	public GClass11 FC0F1FAC;

	public GDelegate1 gdelegate1_0;

	public GDelegate2 gdelegate2_0;

	public GClass20(GClass11 FC0F1FAC)
	{
		Class607.B630A78B.object_0[571](this);
		gclass12_0 = new GClass12(FC0F1FAC);
		gclass18_0 = new GClass18(FC0F1FAC);
		c51E6F3B_0 = new C51E6F3B(FC0F1FAC);
		gclass9_0 = new GClass9(FC0F1FAC);
		BA37A0B1 = FC0F1FAC.int_0;
		D3296C01 = FC0F1FAC.uint_4;
		E085BB8F = FC0F1FAC.F126D59A;
		this.FC0F1FAC = FC0F1FAC;
	}

	public object method_0(byte[] byte_0, byte[] F6BE0F28 = null, bool EBAF300C = true, byte[] AAB2D8B2 = null, string D9B17FA0 = "cbc", string EDBB180F = "sej")
	{
		if (AAB2D8B2 == null)
		{
			AAB2D8B2 = new byte[32];
		}
		if (Class607.B630A78B.object_0[787](EDBB180F, "sej"))
		{
			if (EBAF300C)
			{
				if (Class607.B630A78B.object_0[787](D9B17FA0, "cbc"))
				{
					return c51E6F3B_0.method_13(byte_0, E8AF9033: true);
				}
			}
			else if (Class607.B630A78B.object_0[787](D9B17FA0, "cbc"))
			{
				return c51E6F3B_0.method_13(byte_0, E8AF9033: false);
			}
			if (Class607.B630A78B.object_0[787](D9B17FA0, "rpmb"))
			{
				return c51E6F3B_0.method_17(byte_0, AAB2D8B2);
			}
			if (Class607.B630A78B.object_0[787](D9B17FA0, "mtee"))
			{
				return c51E6F3B_0.method_14(AAB2D8B2);
			}
		}
		else if (Class607.B630A78B.object_0[787](EDBB180F, "gcpu"))
		{
			uint eDB8851A = GClass112.C78DEB29.A8054FBA.EDB8851A;
			if (Class607.B630A78B.object_0[787](D9B17FA0, "ecb"))
			{
				return gclass18_0.method_2(byte_0, EBAF300C);
			}
			if (Class607.B630A78B.object_0[787](D9B17FA0, "cbc") && gclass18_0.F68A5C27(eDB8851A, byte_0, F6BE0F28, EBAF300C))
			{
				return gclass18_0.method_0(eDB8851A, EBAF300C);
			}
		}
		else if (Class607.B630A78B.object_0[787](EDBB180F, "dxcc"))
		{
			if (Class607.B630A78B.object_0[787](D9B17FA0, "fde"))
			{
				return gclass12_0.method_7(1);
			}
			if (Class607.B630A78B.object_0[787](D9B17FA0, "rpmb2"))
			{
				return gclass12_0.method_7(2);
			}
			if (Class607.B630A78B.object_0[787](D9B17FA0, "rpmb"))
			{
				return gclass12_0.method_7(0);
			}
			if (Class607.B630A78B.object_0[787](D9B17FA0, "itrustee"))
			{
				return gclass12_0.method_6();
			}
			if (Class607.B630A78B.object_0[787](D9B17FA0, "prov"))
			{
				return gclass12_0.method_5();
			}
			if (Class607.B630A78B.object_0[787](D9B17FA0, "sha256"))
			{
				return gclass12_0.method_0(byte_0);
			}
		}
		return null;
	}
}
