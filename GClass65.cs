using System.Collections.Generic;
using System.Linq;

public class GClass65
{
	public int int_0 = 0;

	public byte[] F597CA95;

	public GClass65(byte[] F597CA95, int int_1 = 0)
	{
		Class607.B630A78B.object_0[571](this);
		int_0 = int_1;
		this.F597CA95 = F597CA95;
	}

	public uint method_0(bool bool_0 = false)
	{
		string string_ = (bool_0 ? ">" : "<");
		object bBB4E2A = GClass111.C3B9331C(Class607.B630A78B.object_0[720](string_, "I"), GClass112.smethod_14(F597CA95, int_0, 4))[0];
		int_0 += 4;
		return Class607.B630A78B.object_0[40](bBB4E2A);
	}

	public ushort method_1(bool C9A971BB = false)
	{
		string string_ = (C9A971BB ? ">" : "<");
		object object_ = GClass111.C3B9331C(Class607.B630A78B.object_0[720](string_, "H"), GClass112.smethod_14(F597CA95, int_0, 2))[0];
		int_0 += 2;
		return Class607.B630A78B.object_0[504](object_);
	}

	public List<ushort> method_2(int int_1, bool bool_0 = false)
	{
		string string_ = (bool_0 ? ">" : "<");
		object[] source = GClass111.C3B9331C(Class607.B630A78B.object_0[720](string_, Class607.B630A78B.object_0[275]('H', int_1)), GClass112.smethod_14(F597CA95, int_0, 2 * int_1));
		int_0 += 2 * int_1;
		return source.Cast<ushort>().ToList();
	}

	public ulong BD305808(bool bool_0 = false)
	{
		string string_ = (bool_0 ? ">" : "<");
		object object_ = GClass111.C3B9331C(Class607.B630A78B.object_0[720](string_, "Q"), GClass112.smethod_14(F597CA95, int_0, 8))[0];
		int_0 += 8;
		return Class607.B630A78B.object_0[750](object_);
	}

	public byte[] method_3(int C1269A19 = 1)
	{
		byte[] result = GClass112.smethod_14(F597CA95, int_0, C1269A19);
		int_0 += C1269A19;
		return result;
	}

	public List<ulong> F09F2117(int int_1 = 1, bool bool_0 = false)
	{
		string string_ = (bool_0 ? ">" : "<");
		object[] source = GClass111.C3B9331C(Class607.B630A78B.object_0[720](string_, Class607.B630A78B.object_0[275]('Q', int_1)), GClass112.smethod_14(F597CA95, int_0, 8 * int_1));
		int_0 += 8 * int_1;
		return source.Cast<ulong>().ToList();
	}

	public List<uint> method_4(int int_1 = 1)
	{
		object[] source = GClass111.C3B9331C(Class607.B630A78B.object_0[720]("<", Class607.B630A78B.object_0[275]('I', int_1)), GClass112.smethod_14(F597CA95, int_0, 4 * int_1));
		int_0 += 4 * int_1;
		return source.Cast<uint>().ToList();
	}

	public byte[] method_5(int int_1 = 1)
	{
		byte[] result = GClass112.smethod_14(F597CA95, int_0, int_1);
		int_0 += int_1;
		return result;
	}

	public byte[] D122DA0F(int B2B8C31A = 1)
	{
		byte[] result = GClass112.smethod_14(F597CA95, int_0, B2B8C31A);
		int_0 += B2B8C31A;
		return result;
	}

	public int method_6()
	{
		return int_0;
	}

	public void method_7(int BBA573B4)
	{
		int_0 = BBA573B4;
	}
}
