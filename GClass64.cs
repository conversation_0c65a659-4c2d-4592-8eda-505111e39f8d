using System.Collections.Generic;

public class GClass64
{
	public B42DC004 b42DC004_0;

	public uint F73629A9;

	public uint A1233B32;

	public uint uint_0;

	public uint uint_1;

	public uint uint_2;

	public uint uint_3;

	public uint FC303314;

	public byte[] byte_0;

	public string string_0;

	public GClass64(B42DC004 b42DC004_1)
	{
		Class607.B630A78B.object_0[571](this);
		b42DC004_0 = b42DC004_1;
		F73629A9 = 1296911693u;
		FC303314 = 1162167621u;
	}

	public bool method_0(byte[] byte_1)
	{
		GClass65 gClass = new GClass65(byte_1);
		F73629A9 = gClass.method_0();
		A1233B32 = gClass.method_0();
		uint_0 = gClass.method_0();
		uint_1 = gClass.method_0();
		uint_2 = gClass.method_0();
		uint_3 = gClass.method_0();
		FC303314 = gClass.method_0();
		gClass.method_7(Class607.B630A78B.object_0[1262](uint_0 - 32));
		byte_0 = gClass.method_5(32);
		if ((F73629A9 != 1296911693) | (FC303314 != 1162167621))
		{
			throw Class607.B630A78B.object_0[778]("Unknown V4 seccfg structure !");
		}
		byte[] array = GClass111.smethod_2("<IIIIIII", new object[7] { F73629A9, A1233B32, uint_0, uint_1, uint_2, uint_3, 1162167621 });
		byte[] byte_2 = GClass112.DB37D01D(array);
		byte[] array2 = b42DC004_0.GClass27_0.method_8(byte_0, DA31A1BE: false);
		if (GClass112.smethod_24(array2, byte_2, 0) != -1)
		{
			string_0 = "SW";
		}
		else
		{
			array2 = b42DC004_0.GClass27_0.F911B912(byte_0, E63DE020: false);
			if (GClass112.smethod_24(array2, byte_2, 0) != -1)
			{
				string_0 = "V2";
			}
			else
			{
				array2 = b42DC004_0.GClass27_0.method_10(byte_0, B1B013AD: false);
				if (GClass112.smethod_24(array2, byte_2, 0) == -1)
				{
					array2 = b42DC004_0.GClass27_0.method_10(byte_0, B1B013AD: false, bool_1: true);
					if (GClass112.smethod_24(array2, byte_2, 0) != -1)
					{
						string_0 = "V4";
					}
					return false;
				}
				string_0 = "V3";
			}
		}
		return true;
	}

	public byte[] method_1(string string_1 = "unlock")
	{
		if (Class607.B630A78B.object_0[787](string_1, "lock") && uint_1 == 1)
		{
			throw Class607.B630A78B.object_0[778]("Device is already locked");
		}
		if (Class607.B630A78B.object_0[787](string_1, "unlock") && uint_1 == 3)
		{
			throw Class607.B630A78B.object_0[778]("Device is already unlocked");
		}
		if (Class607.B630A78B.object_0[787](string_1, "unlock"))
		{
			uint_1 = 3u;
			uint_2 = 1u;
		}
		else if (Class607.B630A78B.object_0[787](string_1, "lock"))
		{
			uint_1 = 1u;
			uint_2 = 0u;
		}
		E5964FB0.GStruct43 gStruct = new E5964FB0.GStruct43
		{
			uint_0 = 1296911693u,
			uint_3 = 1162167621u,
			uint_1 = 4u,
			uint_2 = 60u,
			genum21_0 = E5964FB0.GEnum21.FF24FC28,
			genum22_0 = E5964FB0.GEnum22.C4AD7AB5,
			e4380E8D_0 = E5964FB0.E4380E8D.const_3
		};
		if (Class607.B630A78B.object_0[787](string_1, "lock"))
		{
			gStruct.e4380E8D_0 = E5964FB0.E4380E8D.const_4;
		}
		gStruct.byte_0 = new byte[32];
		byte[] collection = GClass112.smethod_16(gStruct, 28);
		byte[] array = GClass112.DB37D01D(collection);
		byte[] collection2 = new byte[0];
		if (Class607.B630A78B.object_0[787](string_0, "SW"))
		{
			collection2 = b42DC004_0.GClass27_0.method_8(array);
		}
		else if (Class607.B630A78B.object_0[787](string_0, "V2"))
		{
			collection2 = b42DC004_0.GClass27_0.F911B912(array, E63DE020: true);
		}
		else if (Class607.B630A78B.object_0[787](string_0, "V3"))
		{
			collection2 = b42DC004_0.GClass27_0.method_10(array);
		}
		else if (Class607.B630A78B.object_0[787](string_0, "V4"))
		{
			collection2 = b42DC004_0.GClass27_0.method_10(array, B1B013AD: true, bool_1: true);
		}
		List<byte> list = new List<byte>();
		list.AddRange(collection);
		list.AddRange(collection2);
		while (list.Count % 512 != 0)
		{
			list.Add(0);
		}
		return list.ToArray();
	}
}
