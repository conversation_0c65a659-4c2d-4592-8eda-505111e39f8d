using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using LibUsbDotNet;
using LibUsbDotNet.Main;

public class F7020D24
{
	public struct GStruct60
	{
		public int E69B549E;

		public string string_0;

		public byte[] byte_0;
	}

	public struct GStruct61
	{
		public bool bool_0;

		public bool bool_1;
	}

	public GClass66 gclass66_0;

	public string string_0;

	public string string_1;

	private GStruct96 gstruct96_0;

	public GClass66 GClass66_0
	{
		get
		{
			GClass66 gClass = new GClass66();
			if (!Class607.B630A78B.object_0[1205](gstruct96_0.string_0))
			{
				gClass.String_0 = gstruct96_0.string_0;
			}
			return gClass;
		}
	}

	public F7020D24()
	{
		Class607.B630A78B.object_0[571](this);
		F792CA24();
	}

	public void F792CA24()
	{
		if (gclass66_0 != null)
		{
			DC1596BA();
		}
		gclass66_0 = GClass66_0;
	}

	public void method_0(byte[] byte_0, int int_0)
	{
		DEBD952A(byte_0, int_0);
	}

	private void DEBD952A(byte[] EDB4FC3E, int int_0)
	{
		gclass66_0.method_6();
		gclass66_0.method_4(EDB4FC3E, int_0);
	}

	public byte[] ED2CC8BB(byte[] C5218490, int int_0 = 0, bool bool_0 = false)
	{
		try
		{
			int int_1 = C5218490.Length;
			if (method_1(C5218490))
			{
				if (bool_0)
				{
					return method_6(int_0).byte_0;
				}
				if (method_6(int_1).byte_0[0] != C5218490[0])
				{
					return null;
				}
				if (int_0 > 0)
				{
					return method_6(int_0).byte_0;
				}
			}
		}
		catch
		{
		}
		return null;
	}

	public bool method_1(object object_0, int int_0 = -1)
	{
		try
		{
			if (GClass112.E31AABAB)
			{
				throw Class607.B630A78B.object_0[778]("Stopped By User");
			}
			byte[] array = new byte[0];
			if (object_0 is string)
			{
				if (!Class607.B630A78B.object_0[1205]((string)object_0))
				{
					array = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1124](), (string)object_0);
				}
			}
			else if (Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[910](object_0.GetType()), "Byte[]"))
			{
				array = (byte[])object_0;
			}
			if (int_0 == -1)
			{
				int_0 = array.Length;
			}
			method_0(array, int_0);
			if (GClass112.E31AABAB)
			{
				throw Class607.B630A78B.object_0[778]("Stopped By User");
			}
			return true;
		}
		catch (IOException ex)
		{
			throw ex;
		}
		catch (Exception ex2)
		{
			throw ex2;
		}
		finally
		{
			GClass112.E31AABAB = false;
		}
	}

	public bool B8295B87(byte byte_0)
	{
		try
		{
			if (GClass112.E31AABAB)
			{
				throw Class607.B630A78B.object_0[778]("Stopped By User");
			}
			byte[] byte_1 = new byte[1] { byte_0 };
			method_0(byte_1, 1);
			if (GClass112.E31AABAB)
			{
				throw Class607.B630A78B.object_0[778]("Stopped By User");
			}
			return true;
		}
		catch (IOException ex)
		{
			throw ex;
		}
		catch (Exception ex2)
		{
			throw ex2;
		}
		finally
		{
			GClass112.E31AABAB = false;
		}
	}

	public bool method_2(object object_0)
	{
		byte[] array = ((!GClass112.smethod_19(object_0)) ? ((byte[])object_0) : GClass111.smethod_2(">I", new object[1] { object_0 }));
		method_1(array);
		GStruct60 gStruct = method_6(array.Length);
		if (gStruct.byte_0 == null || !gStruct.byte_0.SequenceEqual(array))
		{
			return false;
		}
		return true;
	}

	public List<ushort> BAB212A4(int int_0 = 1, bool B5932F9B = false)
	{
		string text = "<";
		if (!B5932F9B)
		{
			text = ">";
		}
		List<ushort> list = new List<ushort>();
		GClass112.smethod_28(100);
		using (IEnumerator<int> enumerator = Class607.B630A78B.object_0[159](0, int_0).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				_ = enumerator.Current;
				GStruct60 gStruct = method_6(2);
				if (gStruct.E69B549E != 0)
				{
					list.Add(Class607.B630A78B.object_0[504](GClass111.C3B9331C(Class607.B630A78B.object_0[720](text, "H"), gStruct.byte_0)[0]));
					continue;
				}
				return list;
			}
		}
		return list;
	}

	public List<uint> method_3(int int_0 = 1, bool bool_0 = false)
	{
		string text = (bool_0 ? "<" : ">");
		GStruct60 gStruct = method_6(4 * int_0);
		object[] array = GClass111.C3B9331C(Class607.B630A78B.object_0[720](text, Class607.B630A78B.object_0[275]('I', int_0)), gStruct.byte_0);
		if (int_0 == 1)
		{
			return new List<uint> { (uint)array[0] };
		}
		return array.Cast<uint>().ToList();
	}

	public byte[] B2BCD19C(int int_0 = 1)
	{
		return method_6(int_0, 2000).byte_0;
	}

	public GStruct60 method_4(int int_0 = 0, int int_1 = 30000)
	{
		GStruct60 result = default(GStruct60);
		if (int_0 != 0)
		{
			byte[] byte_ = new byte[int_0];
			result.E69B549E = gclass66_0.method_5(byte_, int_0);
			if (result.E69B549E > 0)
			{
				result.byte_0 = byte_;
				return result;
			}
		}
		else
		{
			int int32_ = gclass66_0.Int32_0;
			if (int32_ > 0)
			{
				byte[] byte_2 = new byte[int32_];
				result.E69B549E = gclass66_0.method_5(byte_2, int32_);
				if (result.E69B549E > 0)
				{
					result.byte_0 = byte_2;
				}
			}
		}
		return result;
	}

	public int FD86C6B3()
	{
		return 512;
	}

	public int method_5()
	{
		return 512;
	}

	public GStruct60 method_6(int int_0 = 0, int int_1 = 30000)
	{
		return method_4(int_0);
	}

	public bool FAB40F1A()
	{
		return true;
	}

	public void DC1596BA()
	{
		try
		{
			if (gclass66_0 != null && gclass66_0.C1B6CD1E)
			{
				gclass66_0.E61BA82A();
			}
		}
		catch
		{
		}
	}

	public void method_7(GStruct96 gstruct96_1)
	{
		gstruct96_0 = gstruct96_1;
		try
		{
			gclass66_0.E61BA82A();
		}
		catch
		{
		}
		gclass66_0.String_0 = gstruct96_1.string_0;
		string_0 = gstruct96_1.string_3;
		string_1 = gstruct96_1.string_3;
	}

	public GStruct61 method_8(byte C7B8D80F, byte byte_0, int int_0, int int_1, object object_0)
	{
		GStruct61 result = new GStruct61
		{
			bool_1 = false,
			bool_0 = false
		};
		UsbDevice val = GClass114.E09D98AE(gstruct96_0, 1000);
		if (val == null)
		{
			GClass33.smethod_0(gstruct96_0.string_3, gstruct96_0.string_2);
			val = GClass114.E09D98AE(gstruct96_0, 1000);
		}
		try
		{
			if (val == null)
			{
				throw Class607.B630A78B.object_0[778]("Device not found , please install libusb driver with lisbusb win32 and try again");
			}
			result.bool_0 = true;
			IUsbDevice val2 = (IUsbDevice)(object)((val is IUsbDevice) ? val : null);
			if (!Class607.B630A78B.object_0[406](val2))
			{
				Class607.B630A78B.object_0[862](val2);
			}
			byte[] array = null;
			if (object_0 != null)
			{
				array = ((!GClass112.smethod_19(object_0)) ? ((byte[])object_0) : new byte[Class607.B630A78B.object_0[84](object_0)]);
			}
			UsbSetupPacket usbSetupPacket_ = default(UsbSetupPacket);
			D7B58B09.smethod_0(ref usbSetupPacket_, C7B8D80F, byte_0, int_0, int_1, (array != null) ? array.Length : 0);
			int int_2 = default(int);
			Class309.AC94242A(val2, ref usbSetupPacket_, array, (array != null) ? array.Length : 0, ref int_2);
			result.bool_1 = true;
		}
		finally
		{
			if (val != null)
			{
				if (Class607.B630A78B.object_0[1035](val))
				{
					IUsbDevice val3 = (IUsbDevice)(object)((val is IUsbDevice) ? val : null);
					if (val3 != null)
					{
						Class607.B630A78B.object_0[1025](val3, 2);
						Class607.B630A78B.object_0[1046](val3);
					}
					Class607.B630A78B.object_0[555](val);
				}
				Class607.B630A78B.object_0[746]();
			}
		}
		return result;
	}

	public bool method_9(byte byte_0, byte byte_1, int int_0, int int_1, object FE83470D, int int_2 = 0)
	{
		GStruct61 gStruct = new GStruct61
		{
			bool_1 = false,
			bool_0 = false
		};
		UsbDevice val = GClass114.E09D98AE(gstruct96_0, 1000);
		if (val == null)
		{
			GClass33.smethod_0(gstruct96_0.string_3, gstruct96_0.string_2);
			val = GClass114.E09D98AE(gstruct96_0, 1000);
		}
		try
		{
			if (val == null)
			{
				throw Class607.B630A78B.object_0[778]("Device not found , please install libusb driver with lisbusb win32 and try again");
			}
			gStruct.bool_0 = true;
			IUsbDevice val2 = (IUsbDevice)(object)((val is IUsbDevice) ? val : null);
			if (!Class607.B630A78B.object_0[406](val2))
			{
				Class607.B630A78B.object_0[862](val2);
			}
			byte[] object_ = null;
			if (FE83470D != null)
			{
				object_ = ((!GClass112.smethod_19(FE83470D)) ? ((byte[])FE83470D) : new byte[Class607.B630A78B.object_0[84](FE83470D)]);
			}
			UsbSetupPacket usbSetupPacket_ = default(UsbSetupPacket);
			Class607.B630A78B.object_0[1256](ref usbSetupPacket_, byte_0, byte_1, int_0, int_1, int_2);
			int FE1F12A = default(int);
			Class607.B630A78B.object_0[358](val2, ref usbSetupPacket_, object_, int_2, ref FE1F12A);
			gStruct.bool_1 = true;
		}
		finally
		{
			if (val != null)
			{
				if (Class607.B630A78B.object_0[1035](val))
				{
					IUsbDevice val3 = (IUsbDevice)(object)((val is IUsbDevice) ? val : null);
					if (val3 != null)
					{
						Class607.B630A78B.object_0[1025](val3, 2);
						Class607.B630A78B.object_0[1046](val3);
					}
					Class607.B630A78B.object_0[555](val);
				}
				Class607.B630A78B.object_0[746]();
			}
		}
		return gStruct.bool_1;
	}
}
