using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Numerics;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Threading;
using System.Windows.Forms;

public class GClass51
{
	public class GClass52
	{
		public const uint uint_0 = 4277071599u;

		public const uint uint_1 = 1129208147u;

		public const uint uint_2 = 65536u;

		public const uint uint_3 = 65537u;

		public const uint uint_4 = 65538u;

		public const uint uint_5 = 65539u;

		public const uint uint_6 = 65540u;

		public const uint uint_7 = 65541u;

		public const uint C3035B28 = 65542u;

		public const uint uint_8 = 65543u;

		public const uint uint_9 = 65544u;

		public const uint uint_10 = 65545u;

		public const uint F19DD5A5 = 65546u;

		public const uint uint_11 = 65547u;

		public const uint uint_12 = 65548u;

		public const uint uint_13 = 65549u;

		public const uint uint_14 = 65550u;

		public const uint uint_15 = 65551u;

		public const uint uint_16 = 65552u;

		public const uint DA276231 = 65792u;

		public const uint BF27AC84 = 65793u;

		public const uint uint_17 = 131073u;

		public const uint B098940E = 131074u;

		public const uint uint_18 = 131075u;

		public const uint uint_19 = 262166u;

		public const uint uint_20 = 131076u;

		public const uint uint_21 = 131077u;

		public const uint uint_22 = 131078u;

		public const uint uint_23 = 131079u;

		public const uint A319830B = 131080u;

		public const uint B50FF118 = 131081u;

		public const uint BC2C37BB = 131082u;

		public const uint D414A0A2 = 131083u;

		public const uint uint_24 = 131084u;

		public const uint uint_25 = 131085u;

		public const uint uint_26 = 131088u;

		public const uint uint_27 = 131089u;

		public const uint uint_28 = 262145u;

		public const uint uint_29 = 262146u;

		public const int DAAE0334 = 262147;

		public const int int_0 = 262148;

		public const int DDAA0794 = 262149;

		public const int int_1 = 262150;

		public const int int_2 = 262151;

		public const int FDB2793B = 262152;

		public const int C5961925 = 262153;

		public const int int_3 = 262154;

		public const int D3310818 = 262155;

		public const int int_4 = 262156;

		public const int int_5 = 262157;

		public const int int_6 = 262158;

		public const int int_7 = 262159;

		public const int E028FE37 = 262160;

		public const int int_8 = 262161;

		public const int int_9 = 262162;

		public const int int_10 = 262163;

		public const int F09D3A35 = 262164;

		public const int int_11 = 262165;

		public const int int_12 = 524289;

		public const int B339A189 = 524290;

		public const int int_13 = 524291;

		public const int int_14 = 524292;

		public const int int_15 = 8388613;

		public const int C3B69B15 = 524295;

		public const int A484F298 = 917504;

		public const int BD842A8E = 917505;

		public const int int_16 = 917506;

		public const int E82799B2 = 917507;

		public GClass52()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass53
	{
		public const uint DE1E6835 = 983040u;

		public const uint uint_0 = 983041u;

		public const uint E3857792 = 983042u;

		public const uint uint_1 = 983043u;

		public const uint uint_2 = 983044u;

		public const uint uint_3 = 983045u;

		public const uint FFA25781 = 983045u;

		public const uint uint_4 = 983046u;

		public const uint AA141227 = 983047u;

		public const uint uint_5 = 983048u;

		public const uint uint_6 = 983049u;

		public const uint B21FE419 = 983050u;

		public const uint uint_7 = 983051u;

		public GClass53()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass54
	{
		public const int F21952A1 = 1;

		public const int int_0 = 2;

		public GClass54()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass55
	{
		public const int BF864223 = 0;

		public const int int_0 = 1;

		public GClass55()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class C514C28E
	{
		public const byte byte_0 = 1;

		public const byte byte_1 = 2;

		public const byte C009C537 = 48;

		public const byte byte_2 = 16;

		public const byte D7A1FC1C = 17;

		public const byte byte_3 = 18;

		public const byte byte_4 = 19;

		public const byte byte_5 = 20;

		public const byte byte_6 = 21;

		public const byte D82939A7 = 32;

		public const byte C2A1D715 = 33;

		public const byte byte_7 = 34;

		public C514C28E()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass56
	{
		public const int int_0 = 1;

		public const int EBAD5611 = 2;

		public const int int_1 = 3;

		public const int int_2 = 4;

		public const int int_3 = 5;

		public const int int_4 = 6;

		public const int int_5 = 7;

		public const int int_6 = 8;

		public const int A939AAB8 = 9;

		public const int int_7 = 10;

		public GClass56()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass57
	{
		public const int int_0 = 0;

		public const int int_1 = 1;

		public const int int_2 = 2;

		public const int int_3 = 3;

		public const int int_4 = 4;

		public const int DA14BEB2 = 5;

		public const int int_5 = 6;

		public const int int_6 = 7;

		public const int int_7 = 8;

		public GClass57()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public struct GStruct49
	{
		public uint BC3C9900;

		public ulong ulong_0;

		public ulong ulong_1;

		public ulong B61F2413;

		public ulong ulong_2;

		public ulong ulong_3;

		public ulong ulong_4;

		public ulong ulong_5;

		public ulong DFA560AD;

		public ulong ulong_6;

		public byte[] byte_0;

		public ulong D99D8B2C;

		public byte[] byte_1;
	}

	public struct AFBD65B0
	{
		public uint uint_0;

		public uint uint_1;

		public ulong B81255AA;

		public ulong ulong_0;

		public ulong ulong_1;

		public ulong ulong_2;

		public ulong B8BA8BB6;

		public byte[] A73A4294;

		public uint uint_2;
	}

	public struct GStruct50
	{
		public ulong ulong_0;

		public ulong ulong_1;

		public ulong ulong_2;

		public ulong ulong_3;

		public ulong AB3DF7A5;

		public ulong ulong_4;
	}

	public struct GStruct51
	{
		public ushort ushort_0;

		public ushort F3BAF920;

		public ushort E0A32624;

		public ushort ushort_1;

		public uint B73022B2;
	}

	public class GClass58
	{
		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private string string_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private List<int> list_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private string string_1;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private BigInteger E5A78F18;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private BigInteger bigInteger_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private BigInteger D1B3E881;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private RSAParameters rsaparameters_0;

		public string String_0
		{
			[CompilerGenerated]
			get
			{
				return string_0;
			}
			[CompilerGenerated]
			set
			{
				string_0 = value;
			}
		}

		public List<int> A690FD24
		{
			[CompilerGenerated]
			get
			{
				return list_0;
			}
			[CompilerGenerated]
			set
			{
				list_0 = value;
			}
		}

		public string E6190089
		{
			[CompilerGenerated]
			get
			{
				return string_1;
			}
			[CompilerGenerated]
			set
			{
				string_1 = value;
			}
		}

		public BigInteger D599713F
		{
			[CompilerGenerated]
			get
			{
				return E5A78F18;
			}
			[CompilerGenerated]
			set
			{
				E5A78F18 = value;
			}
		}

		public BigInteger C1BA701A
		{
			[CompilerGenerated]
			get
			{
				return bigInteger_0;
			}
			[CompilerGenerated]
			set
			{
				bigInteger_0 = value;
			}
		}

		public BigInteger CD9D0411
		{
			[CompilerGenerated]
			get
			{
				return D1B3E881;
			}
			[CompilerGenerated]
			set
			{
				D1B3E881 = value;
			}
		}

		public RSAParameters RSAParameters_0
		{
			[CompilerGenerated]
			get
			{
				return rsaparameters_0;
			}
			[CompilerGenerated]
			private set
			{
				rsaparameters_0 = value;
			}
		}

		public GClass58(string BE3A7404, List<int> list_1, string string_2, string string_3, string C0151083, string string_4)
		{
			Class607.B630A78B.object_0[571](this);
			String_0 = BE3A7404;
			A690FD24 = list_1;
			E6190089 = string_2;
			D599713F = BEBAA1A6(string_3);
			C1BA701A = BEBAA1A6(C0151083);
			CD9D0411 = BEBAA1A6(string_4);
			RSAParameters_0 = new RSAParameters
			{
				Modulus = C1BA701A.smethod_6(),
				Exponent = CD9D0411.smethod_6(),
				D = D599713F.smethod_6()
			};
		}

		private static BigInteger BEBAA1A6(string string_2)
		{
			BigInteger result = default(BigInteger);
			if (Class607.B630A78B.object_0[731](string_2, ref result))
			{
				return result;
			}
			return Class607.B630A78B.object_0[79](Class607.B630A78B.object_0[720]("0", string_2), NumberStyles.HexNumber);
		}

		public RSA C82B070A(BigInteger A2899096, BigInteger bigInteger_1, BigInteger bigInteger_2)
		{
			RSAParameters rSAParameters = new RSAParameters
			{
				Modulus = A2899096.smethod_6(),
				D = bigInteger_1.smethod_6(),
				Exponent = bigInteger_2.smethod_6()
			};
			RSA rSA = Class607.B630A78B.object_0[122]();
			Class607.B630A78B.object_0[43](rSA, rSAParameters);
			return rSA;
		}
	}

	public struct GStruct52
	{
		public uint uint_0;

		public uint uint_1;

		public uint uint_2;

		public uint uint_3;

		public ulong D223FF1A;

		public ulong ulong_0;

		public uint uint_4;

		public object[] object_0;
	}

	public struct GStruct53
	{
		public int int_0;

		public int F7196F10;
	}

	public struct GStruct54
	{
		public int E51B2AA5;

		public int int_0;

		public int int_1;

		public int int_2;

		public int int_3;

		public int int_4;

		public int B31CDAA9;
	}

	public struct GStruct55
	{
		public uint uint_0;

		public uint uint_1;

		public ulong FE06E83F;
	}

	public struct GStruct56
	{
		public ushort FEA7E1B1;

		public ushort A2BCA7B6;

		public ushort ushort_0;

		public ushort CF1614A1;

		public ushort ushort_1;
	}

	public struct GStruct57
	{
		public bool BD147F02;

		public byte[] byte_0;
	}

	public class E8BC80B8
	{
		public const int int_0 = 786432;

		public const int D0BD7830 = 786433;

		public const int int_1 = 786434;

		public const int DA0C751D = 786435;

		public const int CA2F72B1 = 786436;

		public const int int_2 = 786437;

		public const uint uint_0 = 786438u;

		public const uint F095F01B = 786439u;

		public const int CE24DB04 = 786440;

		public const int int_3 = 2310144;

		public E8BC80B8()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class D3A49627
	{
		public const int A4352BA4 = 192;

		public const int int_0 = 1;

		public const int F71642B8 = 4;

		public const int int_1 = 8;

		public const int BE92302C = 0;

		public const int A5BFB4B2 = 255;

		public const int EB3B31AE = 12;

		public const int int_2 = 1;

		public const int int_3 = 2;

		public const int int_4 = 4;

		public const int D91F0313 = 8;

		public const int int_5 = 16;

		public const int B882B9BD = 32;

		public const int int_6 = 64;

		public const int BB862431 = 128;

		public const int B82E508D = 176;

		public const int int_7 = 177;

		public const int int_8 = 178;

		public const int int_9 = 179;

		public const int int_10 = 180;

		public const int DF863703 = 182;

		public const int int_11 = 183;

		public const int B524E109 = 184;

		public const int int_12 = 185;

		public const int int_13 = 186;

		public const int int_14 = 196;

		public const int F101F129 = 197;

		public const int CFBE9CBE = 198;

		public const int int_15 = 199;

		public const int int_16 = 208;

		public const int EBAFB787 = 209;

		public const int CCA8058C = 210;

		public const int EDB19102 = 211;

		public const int int_17 = 212;

		public const int int_18 = 213;

		public const int E601CB01 = 214;

		public const int int_19 = 215;

		public const int D087461A = 216;

		public const int int_20 = 217;

		public const int int_21 = 217;

		public const int int_22 = 224;

		public const int int_23 = 225;

		public const int B612B014 = 226;

		public const int D1BC4A0C = 227;

		public const int int_24 = 231;

		public const int int_25 = 223;

		public const int int_26 = 219;

		public const int B60E28A9 = 218;

		public const int FE09DA95 = 252;

		public const int int_27 = 253;

		public const int C88BEE03 = 255;

		public const int int_28 = 1297559601;

		public const int int_29 = 1296379953;

		public const int int_30 = 1296379954;

		public const int int_31 = 1296379955;

		public const int CC10BE2F = 112;

		public const int int_32 = 113;

		public const int E7A70F96 = 183;

		public const int CB9DB3BE = 184;

		public const int EBB1A512 = 191;

		public const int int_33 = 254;

		public const int int_34 = 0;

		public const int A0898BA6 = 1;

		public const int B8B14E02 = 2;

		public D3A49627()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public enum A4B06010
	{
		NORMAL,
		HOME_SCREEN,
		FASTBOOT
	}

	public struct GStruct58
	{
		public bool bool_0;

		public long long_0;
	}

	public struct GStruct59
	{
		public bool A4105F86;

		public byte[] byte_0;
	}

	[Serializable]
	[CompilerGenerated]
	private sealed class F8B88704
	{
		public static readonly F8B88704 _003C_003E9 = new F8B88704();

		public static Func<byte, int> _003C_003E9__46_0;

		public static Func<byte, int> _003C_003E9__47_0;

		public F8B88704()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal int method_0(byte byte_0)
		{
			return byte_0;
		}

		internal int method_1(byte byte_0)
		{
			return byte_0;
		}
	}

	public bool bool_0;

	public byte[] byte_0;

	public GStruct56 B7BB46B5;

	public GStruct49 B2A384A8;

	public GStruct52 BC9B8291;

	public GStruct55 gstruct55_0;

	public AFBD65B0 afbd65B0_0;

	public GStruct50 gstruct50_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private GClass112.E39CAE0B e39CAE0B_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private GDelegate25 gdelegate25_0;

	public E5964FB0 e5964FB0_0;

	public GClass70 AA329715;

	public bool bool_1;

	public GClass59 gclass59_0;

	public List<GClass58> list_0;

	public List<GClass58> F83FD0B8;

	public event GClass112.E39CAE0B Event_0
	{
		[CompilerGenerated]
		add
		{
			GClass112.E39CAE0B e39CAE0B = e39CAE0B_0;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[752](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref e39CAE0B_0, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
		[CompilerGenerated]
		remove
		{
			GClass112.E39CAE0B e39CAE0B = e39CAE0B_0;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[629](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref e39CAE0B_0, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
	}

	public event GDelegate25 Event_1
	{
		[CompilerGenerated]
		add
		{
			GDelegate25 gDelegate = gdelegate25_0;
			GDelegate25 gDelegate2;
			do
			{
				gDelegate2 = gDelegate;
				GDelegate25 value2 = (GDelegate25)Class607.B630A78B.object_0[752](gDelegate2, value);
				gDelegate = Interlocked.CompareExchange(ref gdelegate25_0, value2, gDelegate2);
			}
			while ((object)gDelegate != gDelegate2);
		}
		[CompilerGenerated]
		remove
		{
			GDelegate25 gDelegate = gdelegate25_0;
			GDelegate25 gDelegate2;
			do
			{
				gDelegate2 = gDelegate;
				GDelegate25 value2 = (GDelegate25)Class607.B630A78B.object_0[629](gDelegate2, value);
				gDelegate = Interlocked.CompareExchange(ref gdelegate25_0, value2, gDelegate2);
			}
			while ((object)gDelegate != gDelegate2);
		}
	}

	public bool A41BB51D(bool bool_2 = true)
	{
		return (bool)new GClass128().B4154402(new object[2] { this, bool_2 }, 391591);
	}

	public static byte[] smethod_0(byte[] byte_1, BigInteger bigInteger_0, BigInteger bigInteger_1)
	{
		for (int i = 0; i < byte_1.Length; i += 2)
		{
			ref byte reference = ref byte_1[i];
			ref byte reference2 = ref byte_1[i + 1];
			byte b = byte_1[i + 1];
			byte b2 = byte_1[i];
			reference = b;
			reference2 = b2;
		}
		byte[] array = smethod_1(bigInteger_0, bigInteger_1, byte_1);
		for (int j = 0; j < array.Length; j += 2)
		{
			ref byte reference = ref array[j];
			ref byte reference3 = ref array[j + 1];
			byte b2 = array[j + 1];
			byte b = array[j];
			reference = b2;
			reference3 = b;
		}
		return array;
	}

	private static byte[] smethod_1(BigInteger bigInteger_0, BigInteger DA943C02, byte[] DC236EA4)
	{
		int num = bigInteger_0.smethod_0();
		int num2 = (num + 7) / 8;
		byte[] second = Enumerable.Repeat(byte.MaxValue, num2 - DC236EA4.Length - 3).ToArray();
		byte[] source = new byte[2] { 0, 1 }.Concat(second).Concat(new byte[1]).Concat(DC236EA4)
			.ToArray();
		BigInteger bigInteger_1 = default(BigInteger);
		Class607.B630A78B.object_0[57](ref bigInteger_1, source.Reverse().ToArray());
		BigInteger F026B = Class607.B630A78B.object_0[462](bigInteger_1, DA943C02, bigInteger_0);
		byte[] array = Class607.B630A78B.object_0[369](ref F026B).Reverse().ToArray();
		if (array.Length < num2)
		{
			byte[] array2 = new byte[num2];
			Class607.B630A78B.object_0[242](array, 0, array2, num2 - array.Length, array.Length);
			array = array2;
		}
		return array;
	}

	public GClass51(E5964FB0 e5964FB0_1)
	{
		Class607.B630A78B.object_0[571](this);
		e5964FB0_0 = e5964FB0_1;
		gclass59_0 = new GClass59(e5964FB0_1, method_33, null);
		list_0 = new List<GClass58>
		{
			new GClass58("Generic", new List<int>(), "IMG_AUTH_KEY.ini", "4BD992E9A2230CD2ABEF49E4F6A7E11D7E2ADD24847787B320239829C560D5EAB94B8304317C938E9358E94758AE60D9B13F2913DD1A749A9941FACAFAB574D70EBBFBCC0133A4BE2134CBA3CE7EE18A6D3CC98D33DAB06AEEE512F405A3248EA316ABC31A2758D4C5A7B9DFCC02C2508A492EF3760A0D4CDA827CFFCADD11ED", "5FFF0B70D5DE3FC5BF41CB824B4BFD14820571CE57EDD3E7C668CC570E718DB07DCC7A6CACD0E80DADC38AA33DB37816839D97980DF3E577A6E0B1169D708071E17DD259CFE538DBDA804A2FC07D795841F2F59DEE023A9919360D0A3F4647FDF5657D9FC5944C8BFA2802336BA23AFDCDE8D546E8806EB532AA7F95A01D8DD1", "010001"),
			new GClass58("Generic", new List<int>(), "ROWAN / 0_2048_key.pem / CHIP_TEST_KEY.ini / lk/files/pbp/keys/toolauth/sla_prvk.pem", "09976537029B4362591C5B13873F223DE5525D55DF52DDE283E52AFA67F6C9DBF1408D2FB586A624EFC93426F5F3BE981F80E861DDD975A1E5E662DB84F5164804A3AE717605D7F15866DF9ED1497C38FDD6197243163EF22F958D7B822C57317203E9A1E7D18DAD01F15054FACDBDDB9261A1272638DA661FE4F9F0714ECF00E6541CC435AFB1FD75A27D34B17AD400E9474BA850DAFCE266799CAFF32A058FF71E4C2DAACAF8BA709E9CA4DC87584A7FFE8AA9A0A160ED069C3970B7DAE3987DED71BD0BC824356987BD74363D46682C71913C3EDBDB2A911F701F23AEE3F8DD98180B5A138FD5AD74743682D2D2D1BB3D92786710248F316DD8391178EA81", "D16403466C530EF9BB53C1E8A96A61A4E332E17DC0F55BB46D207AC305BAE9354EAAC2CB3077B33740D275036B822DB268200DE17DA3DB7266B27686B8970B85737050F084F8D576904E74CD6C53B31F0BB0CD60686BF67C60DA0EC20F563EEA715CEBDBF76D1C5C10E982AB2955D833DE553C9CDAFD7EA2388C02823CFE7DD9AC83FA2A8EB0685ABDAB56A92DF1A7805E8AC0BD10C0F3DCB1770A9E6BBC3418C5F84A48B7CB2316B2C8F64972F391B116A58C9395A9CE9E743569A367086D7771D39FEC8EBBBA3DD2B519785A76A9F589D36D637AF884543FD65BAC75BE823C0C50AA16D58187B97223625C54C66B5A5E4DBAEAB7BE89A4E340A2E241B09B2F", "010001"),
			new GClass58("Generic", new List<int>(), "SetRsaKey in libsla_challenge.so, secure_chip_tools/keys/toolauth/sla_prvk.pem V5", "8E02CDB389BBC52D5383EBB5949C895B0850E633CF7DD3B5F7B5B8911B0DDF2A80387B46FAF67D22BC2748978A0183B5B420BA579B6D847082EA0BD14AB21B6CCCA175C66586FCE93756C2F426C85D7DF07629A47236265D1963B8354CB229AFA2E560B7B3641DDB8A0A839ED8F39BA8C7CDB94104650E8C7790305E2FF6D18206F49B7290B1ADB7B4C523E10EBF53630D438EF49C877402EA3C1BD6DD903892FD662FBDF1DFF5D7B095712E58E728BD7F6A8B5621175F4C08EBD6143CDACD65D9284DFFECAB64F70FD63182E4981551522727A2EE9873D0DB78180C26553AD0EE1CAAA21BCEBC5A8C0B331FE7FD8710F905A7456AF675A04AF1118CE71E36C9", "C43469A95B143CDC63CE318FE32BAD35B9554A136244FA74D13947425A32949EE6DC808CDEBF4121687A570B83C51E657303C925EC280B420C757E5A63AD3EC6980AAD5B6CA6D1BBDC50DB793D2FDDC0D0361C06163CFF9757C07F96559A2186322F7ABF1FFC7765F396673A48A4E8E3296427BC5510D0F97F54E5CA1BD7A93ADE3F6A625056426BDFE77B3B502C68A18F08B470DA23B0A2FAE13B8D4DB3746255371F43306582C74794D1491E97FDE504F0B1ECAC9DDEF282D674B817B7FFA8522672CF6281790910378FEBFA7DC6C2B0AF9DA03A58509D60AA1AD6F9BFDC84537CD0959B8735FE0BB9B471104B458A38DF846366926993097222F90628528F", "010001"),
			new GClass58("Generic", new List<int>(), "bootloader/preloader/platform/mt6781/flash/custom/oemkey.h V6", "607C8892D0DE8CE0CA116914C8BD277B821E784D298D00D3473EDE236399435F8541009525C2786CB3ED3D7530D47C9163692B0D588209E7E0E8D06F4A69725498B979599DC576303B5D8D96F874687A310D32E8C86E965B844BC2ACE51DC5E06859EA087BD536C39DCB8E1262FDEAF6DA20035F14D3592AB2C1B58734C5C62AC86FE44F98C602BABAB60A6C8D09A199D2170E373D9B9A5D9B6DE852E859DEB1BDF33034DCD91EC4EEBFDDBECA88E29724391BB928F40EFD945299DFFC4595BB8D45F426AC15EC8B1C68A19EB51BEB2CC6611072AE5637DF0ABA89ED1E9CB8C9AC1EB05B1F01734DB303C23BE1869C9013561B9F6EA65BD2516DE950F08B2E81", "B243F6694336D527C5B3ED569DDD0386D309C6592841E4C033DCB461EEA7B6F8535FC4939E403060646A970DD81DE367CF003848146F19D259F50A385015AF6309EAA71BFED6B098C7A24D4871B4B82AAD7DC6E2856C301BE7CDB46DC10795C0D30A68DD8432B5EE5DA42BA22124796512FCA21D811D50B34C2F672E25BCC2594D9C012B34D473EE222D1E56B90E7D697CEA97E8DD4CCC6BED5FDAECE1A43F96495335F322CCE32612DAB462B024281841F553FF7FF33E0103A7904037F8FE5D9BE293ACD7485CDB50957DB11CA6DB28AF6393C3E78D9FBCD4567DEBCA2601622F0F2EB19DA9192372F9EA3B28B1079409C0A09E3D51D64A4C4CE026FAD24CD7", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_U91", "8553E31D7A73F6C9294E961815C23F31F2B5EA1116E3C613AE12B26CF285E4C5CA0E2DC8E17D52F96B30CEF6AD544E43205933F20AD17EB8712097AAA23116C68EB6328980B8BA26706105656FA65315688B8232758607B8936D0ABC27DBC97D94E95B4F1957FD1965082E5849C4185EBBA8AFC7D558D4F5F001AC5363423AC1", "9A97C44B0768424B6BBB0B6AA987A2D373448C6FEE1F61FB81F8CF53D70856F0F77E76C06A6901DE90ED3B4D9AD4B9E04EAED42E5657BF2FCCF390FE9F5ABE1ABE8575F07916DA69ACEF95D38874223EC51CB501148A1FEEA2BE2B8CCDA08672AA423A4099203C6AA4777FED7353C57696B8E0D4020BD6930B828B9846A454CD", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_OTMINI", "6BC0E84B4F38415BC575DD0D5248C2D182EC55E2BA7A11DFE86815155C709A25BBE34FAFA6A9C19344ADCFB32EB3D2ECA465C2DC0FD7528A00CC268C6657CDFF0B0DA1B2AC6A95B94865FACB7E1494CEDF44358E29EC7E8F091172E4EF29856D1F45032AA644EFC273F141C10CB8281A12CEBE202B65F176E1A145C326D75841", "BC7B5107BCF46C2CD7758F4BD4D4E9F06B731D9CFF383DFFE48156D1AD91FF74A7925FA3027669766B3D4C6E28C1C9310194C34A59E672C8CED38588E998D7B162889DCF06668345F93E4EFCA34B5FEE5BB57DFC38D7623A48F31B382DE2DB656EC1F3B5267A9A8F5E441C61448A283E4717ACE6983D01B163E34F959C9972CD", "010001"),
			new GClass58("Alcatel", new List<int>(), "ST513", "9EA0F7256BCCA9099E5DB80757A5F3DDEB3292475C01D2E6EAFF8DA905D9537A5875E874D26872A8C04B552DD310F194EF5A5EA445A50D5C1E6670E5126EF01E5FB1AF24A67D07B5A9F72197BC66D5743FAAB54759FBEDCF1FD8AC1AABFDE2C6FB29601B4734334DB92A92FC25F7ED8700D307B74A2C435C9CE5B5CABA4B3801", "DF836C16BC8E129DAC8E6EFCD3F41636981687C29C465B481CBE874FFB14D592DE024B70F4FA20AD96C96E4E3EDED3625F314DEDB4D8635782F6D668D04AB1167982229E03EDE17A7857A22CBF72444A6BEE2BBA54F32099E0EABE654C3DA4933926DB4D97DCAEB68236DF4B3E51BD3C4BFA8B2D47C2534405E4F1C1D43E1069", "010001"),
			new GClass58("Alcatel", new List<int>(), "MPK_U7", "C5829B5BC34253F090DB831F5085CD5A6F88DA7F6F90E3A3CB6FFF6E53218C5A616719971B3F64EF02DE526719A7B709978BF1ED48C821981B32EA77C9E536BBDA206FAD74946D02A20D17120F89419B0DAEE2D8A47275768930AD53C876AFEBFFB6805483C1DDCF6C19F3566F0DE494838AFB51B18080BEFF66364DE5294581", "DB0B6E89FABDC24E6E7379D25B0C402686537AB6375D8B2407BEAF44CBBEF27E04E90B556801BBCE5EEE2A7EC636AC825667DAE3578EB7BBB66701BC62EE86F28FC14D57E8637A2DDCEE00CC3AB87DFF4155250C2DBDE9AE62F3D7A9D5E4A265FB0A8B23C082BE263D7788E44D59780B47A31B25DC588F81902BE419F917933B", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_U8", "76CA90A16BCF7552DB2B716B8531FE5617BFE86635627647E3D27291FDF47E67BA8F953AC362DBBCE2977F05A9F24AFF4250F8F3A14D3EF09B7B99C9384AAD0C53104F87B47D7DAEA3CA725BEB233D127EC342CE0619B16BD3D5E44371CFFCE9F23178FF48DD42FC4450CCDB3E2D63437EF9DFC0296B12840AE85D472CF0135D", "9BC517A0DFA87A7E240000C5F42CF31905AB93D4BCB95694DEE85282867D5C83270AEA0B0948D66EB39D8500AA6C8B1069B8EE784F75948958F7BBF627D6ED5F286FD3BD4DF60A6C9490CB319448B22765ABA9329820EEC50F62F1CA0B6B3322AA27747B26855A1F1719CF0C4060C9F5A6A3A60EC60FE6E04E7B044E5DA994E9", "010001"),
			new GClass58("Alcatel", new List<int>(), "MPK_U91", "8553E31D7A73F6C9294E961815C23F31F2B5EA1116E3C613AE12B26CF285E4C5CA0E2DC8E17D52F96B30CEF6AD544E43205933F20AD17EB8712097AAA23116C68EB6328980B8BA26706105656FA65315688B8232758607B8936D0ABC27DBC97D94E95B4F1957FD1965082E5849C4185EBBA8AFC7D558D4F5F001AC5363423AC1", "9A97C44B0768424B6BBB0B6AA987A2D373448C6FEE1F61FB81F8CF53D70856F0F77E76C06A6901DE90ED3B4D9AD4B9E04EAED42E5657BF2FCCF390FE9F5ABE1ABE8575F07916DA69ACEF95D38874223EC51CB501148A1FEEA2BE2B8CCDA08672AA423A4099203C6AA4777FED7353C57696B8E0D4020BD6930B828B9846A454CD", "010001"),
			new GClass58("Alcatel", new List<int> { 25975 }, "MTK_6577_HUIZHOU", "3D6FF33AE0EC1D029DB4A6FB9CA3E41890F5CB5A53BFC0AB3CB2053D85243C7715A07EBFAD719BEA67C252A223AD0FE65074A5D26EA14BA63FF8D92E553E879B6CE51E065F05B23E5D27DEED116EC751C9556EA0CEC11E80F3BD206DA9E9072FBE1695B19A8A9FCB576F00F7A268DF8D6D262127AB3F3246941004F25534AC8D2F418815D15F4A5A663A2F1383115CB3E8BD263EBCD92C5BD1B92644497E15A1B41E77E648CAC179182D83C496728FB52B9A1C600954AD0C3EAC5D4633D519C88DAF775FE090C2F2568C7C91A8938A2859245F100FCE764033147D84D79075A81331ECDD170D2541832AB9161DC473CADC1DFBC17DF2BE89FA6D6C13D9DB3611", "AC2A2C19BF4BEEF4272DF8899CB648F90453E53FAA1DD8143327978620EC74E6068A8FD051FAC856A59FF0A2F3051B7512F55FCD6EEA57262A5A24E141B2A9C105509B79976B952A4CFA0367535AA1DB83290F18F62E2F604BFD5FEE3FB6FA863CA5546E359E0348937E5B62E47F645E9552EBD2E7E516C13A192A6075C55351192DD545DD90C34FA28C695D6643A2449C0C7ACC9D003B9BB4F9D249BC19BEB8FFDC2D6115260499156461EEA896361AAC9A24ACE3BF6C81DB3E8C32FD6D74D876882382618C7AE920CE63B0C33A3ED6A59642ACDCDCCD68F2E84F6B1DFE8E4DD33FD78208C750F877A8EDDBF32B7F6CD28BC7F62A79E1281CAD49B29EA1AEEB", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_S_2019", "68D01875EE507057075DD8CF2E3007AEBEAF767F350C130684911C483EB918A5E235AB71C2EAEC62AA7BBEECDAC518CB8962272E83A2943CB0E486B66DA8E244FBF3E3D8E4A065198032FDB045F011784127CDFD63D285F7F20DCC37B0EBBDC8B49020B9A16333F196E8E3E8246835B1E76615985BA6E221241D096CC5BDD7336D8B22704DC1576AE0AC252FEA8DAB129756A609F347D60E25D8D085CF0C8775631D3C0E54E50FC67DFF2C55148B4E78CF36987FEBB23E14FFC1DA9CB0ADFC139D509826AA98F6FE0E25EC6AB6442E5A7CEBBE6454FF06B897467512CDD8F0460201125D0BC9CC2BAE259840722AE56D16B06F9E0515A2D128A23B5B0A1896E1", "E4607E6CF78F5E4857BDAEA441F8DDD35A7576F552B4AD2C8B4EE7F578C0590D747B049BB5014E06F8350DC6B78D5E0DDF1B4BB8AF695E4A338A154596555738CCCBE6B58EB43AE221DF9BABFE9DDA6CA770C25AB42FF986F946756B46EC553DAF7616F2843DCD6A48F48D9011C050E7ED11C99F61624F057695D622088F868BF6A3966F25BD8AD58DB81623FD63F2B91F3DED1A5BE0EFB69A64BB40D8BBFC251D9C32FBF0A1BAD516751E9E04439392C59BA6F856B5C0BEBE0DCC67D7D4F25DA5342ABA94680583ED76D94823C6F62E5E7484F7E2D2A467D167AD3F5647F958DBBA3EB66F756C851A55138D1CE465333592969470FA8652DF2E38BC380FF4F", "010001"),
			new GClass58("Alcatel", new List<int> { 25975 }, "MTK_6577_SHENZHEN", "6D209285B39EE78C7CFA17A34473855463C8A42D7B494FF0D6885C16D672AED0219193EF388B5AAFB3AB10BEF394D6FB7831B122CE47564ABB084F68F3F7BE113BCFC4E8AD3774FBC8EAA8A6FE030E96A56022CD0891F59EB2564FFA2700056E50A8CCE72357D3F7AC7EF7B4FDAA69E0CEAE1AB3D0F5B90E00414A3CD7BD17AFC3B6463EF43BFD22788B68FCFCC2964421B1B622907D8C75E8D83193A579E50C26B0BEB93E53E2888CFDDAFEFA03C368C68E6D357087F1BF0800E1BB4F0FC97C092A7E7098CB60CAD71E292B506C0CD1F428AFF3192DA6818351A780AA1B4CCE0DCCD15ADAD815B610F445A6571D3C65D2C44DA9057B5C8970CDED0DFC3072C1", "A29274E3085C260DE63F571646CD2C69737BA5A0BF604AD31CF6A86D6A9E08DC931ECDDB7404F4C9255C72B5DEBDB69114146BDD7EDF6B38505B19C4D18EB0E71516D4FAA871CBE1D2E24E15C1877B33587A8BDD1E7DFE1B17235D1AC431C27CAE07804014C287FBF2479E6B4B80665898F7CBAA7EDCF23DAA8DD95F63039FE7EB641AD7C05E221D29ADC62CF84893FFC6ACFD44A9D2CD60D5E0F94D1C29D317BBDDB3F5A324648069C72857CFC708FC9BD8A3F7A98051FA9835AF1F9C71D80236334EA51CBD52E57E5A7950BEB394D9C97BCC32591D9700106B0ABFE1DD2DB9617FB7DD2EAA3885630C3CE1DFCF087C814B480F30C411F3071F12AEE4077", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_S_2022", "3BE4C4D89124E53D12CDC922C0C6571224E8925FBA160186068855C5032DE6655BE49233899432008FAEF8BA5037F1A0B237E169F6F9F05BE2694BF53D04B44507FCEB1480007D2F49C8191CED7528E6B4FB06070851C85F2025CCB60271631DEF9F831822B351ED17CA9A165AAE97516A6C3940971D17E927F3BEFB43432C1B689CC660A896237F090D7B311D9E39AA1EEE5A4E3AF00843C965C30CA9AA5DD7767809D27D4F66777661779D2A1FB90B014329A1973E67B8989DE924E8AC98673667E4F734382F87F0DD0300D360142AFA772D5BECA2EF248E90A7BD32240C4A5B5F41AED3F4B63F90642F138186FE17AFD713A3242EEA7B2DD0F32B06B67681", "AF823063550D6E5ADCCA01A1AE1FE357F73D7E5C60CFA25E4BEAC24304B70623654FD13547DE869899BE532F45F3C5FF26B50292DCCD112DDA1478721C05304445058499BD00F6B104E16FCF2D0AF55781BE147787227EFF54A25DCA42B9D6F1FC8F4B821C099F483C402ADDD178330167AA9B1021DAE121BB2BDCB0127AC47AE866A1579F2399C70E69293DDD3B0BACEC2DF9DC518AA0C58C2D7561C5783AC32E57B91D16D6C57764755894963733B85F19F9A3BCBF624199CDD1B31CBECC5448B132C3799E2D0E569F0BA61245796DB5876820EF125F4A230039C5CD16B2414855BF3A3B565F81787A4E9B264C9BC855B4FE7AC17CACA1BC5F070594A9C175", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_B3G", "12FF6A160CDA225DDC898CC6EF7DD3C69D05DC24D23B7A0334568DC85191F3B63D278AB1C8449507DEA8533496E04C77225A12A27B7ABCF34D10C3CD67B1B41D7C19C44114E344A74396541D998D7B76CA06D0322BF3333684652528DF22021C190BC38ACDAC2A3BE6E2D0BCE7F1E3C77A71750FF17895CFF9C6225275A3CE81", "CF8243D13128ED39FADAD9CA97C15585D634F4D9B38DD59E4EEC4B0B93E4EB2FD2D96C425855E69706D5C11021A8C2E08BFF87B424BED2DC3EFA9360BF1BCF80C96CD4BA9C39EB79BFA2BF9D4EFC5A56798CCD9C6599EDE595AEA644086605FBE55B2F7719FCCBAFE0C95956FCFFB0CE77A9637C9ED66E067165CBE901EB041B", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_E8", "95B32D61A10E6C2A54FA4E5E020D590F6BF0F295FA87FA03B3D00DCDC4982DC997AD5C7FF872255141EC1B77F714C14587FFB87C985531C937B245062EE03514AA796AD79698C40C49A8B3C54EC66FC20DEB874A8BBCE87239C414F541367A350D525FA6BDEA77E4CD3078CF7DDF22A8AEFB0C595A6C76285D837008C0A77E29", "BDEF438901DFA726CFC2CCA59D12F009108B8E1FD7DD9B91A5CC71FA7B1E36C8783F9DE5850050E6505FC715C50BDDD59A3064B05214C4365360CB98D080CC38658A94695184B564E8E8DCC28F70EB0122A4BB7662E3A1F34C057EA523819ED02ED46BAE0CD9530B0536CBE7A1BA3F33A45FEB2F92FF5104DC32EBE94F249EED", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_C3G", "4375BE875664FAD432CB6476F1C7AEECAEA3166A51EADEAA32E96D0D79DD159B6287F4CD42685330FC15391EB4EE83DC6FD22A913C5FD5023D8FD6B71AF8B530209B5355ACF1CC6E6397AA6E5D2DC92B7D37635D391CD22A3AA337D8FC0A274CDD7D6630395D13517E32C91DAAB2F5378ED7A1BE86C81C2E775C249201F2C221", "C04E6A1BE49C5A57ACCAEBC837099B40890180FC046C3DCA58745749D0979CADB63B8B4573FAFC129C2F89EBB64C4EC81339E862F5638AE145E2C8BC291097E6B90434FF3F3A1E620FA77DCB6D963F53B79ABAF4EEFB8A5D4378CDF4AB3060A9901909FD455CF850AE5ADBDF035CB3CBCF572AC4DCE4BC1321562273A461DDF1", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_K6", "4F65CDA0C3AC66753C58D748DB46BFB8CB8DBD1F849C7444AFCF37DC6BB218904C5A2FE08808680D2A6E7587681256A6ED9751046FA42CE44874BF2061F40DCA4953C345C2F156E8EE7E2F497EBC59B3DDCCDA98584DFC999D213D6782F2B0FAFF59A9671CEE801DEFEB5A51178A7B95C487AA735B463E8B1321B6EBE58C7401", "A7E3089840BB7A9A7A972E8C88D7C464FE40DC4771A2DF0DA981079CC800F5D3CD45ED9EB34EFAC6BF7D2AA6DBC1266285F50D7E86E6E0E5DC6D062BC8FE871672139904E5FFE64C6FFB4FF00817FFC0AD4C18787A253BA5F7F7BD8412E5F46E2C264CEDF174ED5163943331A658B434C59EC9E11B269E829AB638C80C4EBE51", "010001"),
			new GClass58("Alcatel", new List<int>(), "OPK_VLE5", "4BEFE0EB0C424D83CD2DACB59740CDDEC599AB3C8833DEE354717425993D12BA5441056297153BB3D2667C3E9C76CAABC349A07CFAB60EFA9E5E7B35E971FE7EEDAC090A1A5A7D8A2CD59DE84762F09CACFFECB65BF70ED504243721FD0E094C3F216FBB85778AD82829658232A2F472919E992060394E79F2AADA9E8A42CE21", "CB676A5DE86E2C7D75A17F10FB2E3F81A473E5D2D088833D8C1928CE78CAF1000AAA607C83F55B57DC07FAC7A9ECAD1600DF5D033986C02C003884620661A9674042A835B99CF8A024C27A10410EB379AC69E72D6F5A9CF72C185262331C98879CBC225DE835D864983D2BD085F1DF99341D3CBB0BA3B0A50491C8EE98D691B5", "010001"),
			new GClass58("Alcatel", new List<int>(), "OPK_U7_1", "18E2FA361F4E7FC86574D9A93F2113A4D99D272710F303E29E07EBF71444335CE789DBF9816D472B27935AD49202379E44023071706BD0058E2BAE45ACE0938E75610579240EC87086D27FC0844BA25BBA09214AE43037CB902801A58915CE58C6F805FB3AD6CF7996F25E0CF0A94C13E04EB4370ED6B93C39BA2136F8CFD101", "DE15DC10818E30C363BD0A87D5F8D89B832329FA25B8388709D94E9B0EE4EFDD3E24EED3D931F01EA1B0E2B76265D7DC270EA8012545BB7245C286761210BF46C6DD1FADEFC257FABEbba29BBBD86E8336460E5D21888A319156E8BA529E4B6A200136AE4ABA447FB37A357028142D8B16D79A421D513ECD9B9EC0D908BA8217", "010001"),
			new GClass58("Alcatel", new List<int>(), "MPK_U7_1", "88A4477997B57337CB144D0656BD2D5F0EF59D6B574B631A79AC8015A4C20D454E1DF85682AD25ECCC7FB92BE373259FFFE58741B5A85E50CAA68B9FE84F6E295D2176B96C20FF819E8BB889702C474EFFE1A77710FF3B93E896FA488F1717C75E46A1B0F5898FCACFA35943F1ABF80EBB665BA7FDE59C4BAA61DD2F6C5EC001", "B6A33B825B0CF6ABD3C9D39D1C8BDCE50A41F9BD5CA2DE52C4C447AFA9943F5C1365D2E9CB7961FFD877FD38696B4479A8BB7EB8DA15BD8D59A1CD7E5EE517D1A20F29BC66974F87796A11F7537529F8F46AC57861484808BFCE9EE6CD6527F7FE3BFD57B4A7FD46F8DC047D6C8370DE6507620C2B9A3BF864E8EE4C4D2ABDA1", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_S_AT_META", "8294E45929B8F95A380C59FE715DA5225FD518920A85FDC9A8B2ADE6675B7680293C21539FA4466907CB3601B072D8ADEBB0481ECF069BAAEE00D0F5CB4396F4FFEA11DFD41F3C62FDEB312EE9B4BE2026BC40AACD9FF928130FA7AF0305228DD5E47C551C2A701653DD6841B9566099DE99E2731194AE617CA8D9DF99A47C49D9F514620EA1E3742DA8DC7DEC6756403631A274DC226C6121863E4A571A120B63C38D134853DF5B986FAC1565E1F3BD8A02D239462967E9C71CEDD9AE0C0EEC330018CA553CC7CC2FBC73D6BA37BE2FE360644FF69AB7C734264675C057417857DF4CA206DFDAC9A5621F9D8E45DD2E58DC8B4198667DE3EFD1D5BD7CE007A1", "D1EEE63F19D148C904076C507AA8D4F6C7E931A65476FE5231C06036FEA2ECBcb8c811882c4f70e6e3523be73f5c7a83570f3a40bd894399a5ee9f903e8e745ec4e4e034495175b167192535843f06241d6477e3ce1ad5270e590db9cb905404c01aa407433fa2c2ca1f8366c1623fa45bd5ee68e3145a57f9af3e6e68fce41b8c682c0e07f3c48f4b377951b23b467fea0d4ee0e67c0235d0e83ae27e40ad1c060063ceb966835a0ac1eb68066f8b55775ceb7b444ffaeec19548a42247ebe687f881a0c8e5277beec22241e2ddae1c21cec8046eb005302812b7ef42ac153cab317bbeaad73f7ccaced38c433530b7e0ad464150026025a9a3ff5d45e025db", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_B7", "776B1DEB8C3E943B3DAE67CF2B597BA55C439DC1FA10E4E9EA530DF96BD0815CC3EC3EF0267F89A699C5CB64BDB91E5E9AE4C7AF03CBCFBFB4755CDA55E3A31D510F96A102B5AED90731788A426E371F8AE24F660403377CC0836A06B2A8E159BD177F4CF68E36D447E4B52CA63611CD8416C1EFCAC52143106C272F7474387D", "E3E3166F47177DE4915E915A9D555D980AFAD96BE22CBB8A02516AC8FE69657BD10BC6D072046DCD33E4476E24F128CB7AC613DF140CEFE71ABF080A74D27C114635D3954C55299F6F81C2A0AA14C4C678307F4B3BBF0C64F0006051EA7573B5B0CC290201C76C4D272C981B1BB19BD0A0A0AC046E6E63B0F4CF88D2C98A5C91", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_BACHATA", "09FE029A23FF7E37C749386FCC9A640450546B95E5127489D364D380393C99F5C10DA6D7CF0ED955F4A5F3D8D90D97CC7C49069D394206F9B59C11568FFE66163EAE377447ABB103CD5D4256885CF7984B28CFF8A096DC479B9196D66CD534CBDFECE7A61DE04110BB14A3BA5E0F20AE0BB4D82E18FBFF0335904DC09B829E91", "94CC529BB1AF0AB8043F09E3CA612D787CC19485D3769546E750F6EDB844979BA8F1F9AFB8B93B521330B74713831A78A584F7B24780F92DD00E5D56CE8DEFA3CD39D01752E514A4C2BA7499F334729622049491B1AECEE6C9E1C867E996C294B10F5D62EA4504E333424B280162087296C300C01FDF75F47D874DF40DBDB94F", "010001"),
			new GClass58("Alcatel", new List<int>(), "MPK_VLE5", "7005D1BF5BE81DB7B17C9B16B1D407B308B42E3490E75A93E9D00FD6C812D1D8DB2F1041A342964808A037F315A448ECAF0502A5215C58F0DE709C5BD87E3A65E0291A1A23547C76CF437EF1D9B434B70DBB417049A31DE9EE7BECF218A5BB63B05FB84FF49D1E6AAA4B9B4376F47417435ECD85CCDA63BE9070E7892ECD4A41", "A62BF756A70657B6B560588E85E662E181B6A61AE466AC3D0D2E971F160E88216792CEDFB1979B3D6B665068EEE8A8699888CD74EC9482C61AE7EAE3571E50BECCDCB336477C26040D09B46DBD93EFC0FECE4ADDE2E00C1CBEDAFD6AD7C43BD621675A6A46425C5CF6182FC5602BE443A372FB4EAD4531E64285CE29BE913285", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_C7", "31D28ED040A8ACE0D56FC94B4A7D29DBB135D62C7905621818D657499FD6FF6FE7417592CECECDD3F3D37EC0A361228DA34D3E7A2724B7832CED00008FB4AE500357FC3D285C64FBF7EFD4BD1EE48ED40190296171ACC3C2D0C69E89DA5A8FDE7E0BA7048AEC6BEF1BB19646F883FE9D77D8D263545E7C00E8604BE38210D065", "C1D6C392828F4620E455C138840AB448CFCD4ACA821663335DBA9C51DEC9B8198301CA6B069ADEfcd1887f1cec31c15674ab264daeeb82398b419f08b4236904203c48a7db8724f1773d04a6b8c88cb38907a00bc53e86cdb2bbf479a68b8241382bdc5ac6105270efc2da4cb91a36459ccf6a2a87dd56ec4c331dd419ba5931", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_MARTELL", "985E549FD42C0B4955D3DB8C3EE601F65E10A3DB08F957FAB4016DBAD0F60C7E09E8B7A782404CB0FC7C805DFD67FED814765ED58B7A146ED2C1D31B80E3F845A45B6CCDA5A0344247BE404C23DEBF027C7B5082373372B49BF78D9058CAA66C57D3BE829088C3610034FAF1EA9F24A21110BBB3865182747CA1779E83C6983C189B3F19F3DF49E5F9CDFA57F4F69DFAE53E19EE0B1EC30986D59AD11F52BDC022A9499DFA89F8546D266F6026AA307501CA5A619F5413A45EF38F139C3EA8B52F02FBC8983AA878052D9108668ECFC8605057A298355D2F680C34630E224C57DD4C4F2DC0D51766EF7070DADDFA3C885A3F94D76C943C6C1054D338E2323B99", "A800D061E4A42E4C3453A17CC8DAA974E23BFAA403B4A60FAD6D3516D8EC035C1EBABDCD60009D9B8C639954E616C6CB6CF821E31E58772FFC366E6FFB7314657567B12279A34DD69E46B8A4A628DC2DFABC68FA1D89388D2058A97D2E31520B4FB04BC2F963E110E8541EEFD22D90A03ECA806B3C6A20C6BD1A7468E61EA1AB283ED1BC462DFAE189EB5FB451F802FB868CDA9A7409AA52E42B18882E79F4F1C2377829FAFD9760468BD1DB823BD9080378CF46EF405D91636CAFC03ACAD9FADA6B0446DBAF51E9D533887E4A3A8F62114063E0B8920684C28BFBE256AAB26E98751166358C201347BA6C3B36D49AAB6302FC248EEA3C254E15A08429FD2149", "010001"),
			new GClass58("Alcatel", new List<int>(), "OPK_U7", "61ED86791440C26491B763730F483C18C32FCD77BDB6F9E9E3E11CDFB9716D22C392C68556219E2B6C1ADA57649CE2DE559C239A9FF8F33252480421E4A2649DF8E3EE0095C9BEC361F25A5EC67D0B4D96C73404FF8A115FECF1173A6568845480FD4423B5DBA2E5111335655F3BC2F3FEC65510648571992E010BA0AAF243E1", "9F94E8F1171FED4B427629C928E807B2220F109AC70A3D5B1B8CBD295BC3FA3226D3903298CD81319B9B08A6F8E77EFAD0B04139B686EE0D1586175913AD6F65D6CF21BBC7F769885381BA6D840414B26FE7B9B3E393ACACB3453E3A0CB79CA21CB38A42685A03462244FDB2A5F1D8B9E20745FB3206E799655C47146310911B", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_S_2020", "B86887499D157D3B1FEB1041B9D2E94065732B41D22FEEBCE317676321D66D1BABCC7A53544E35A714C207811E62D134291D616417295E5B0C4AA3D65E40B41A352822263C22CBB4041A1883C76B97A8C925CB428A7B2300622DDAEC62209D8DC0C60159F6C7CCFC26768BC469DEEC22BCD62F49F4C2CA1B2CF0BE49D6E5EC563279CDEE79C92800C6C965200D316C79285551A54359B37EC4173EADF4C0506D857DDCA4831ADE7EA8F13097B4E2B630A2D3EB9C57ABCC65F84D693C55E361763D8D37BB40CD6E2520684AE05EDC62A36CDA6747509600F4605B7ED924EE1AD49E66ECA1176A20794600173DBB42FCED2F1FA0CCCC0AF3B56D58453BEE420099", "E4CC4967BEC817BB348468024C3084B15FD4F7810C8A9D078A4F51CF9974D2E3BAE8D5A19A85C0A73BEFD0675100E642A3F425E3192DFB1DE56928C37F45FC142ADAA65798ADA863B84D4B5F22C3F79B95CC201AC7C292C99475453A62B7B7E06E84833DFCA7A0DF931084932A80129E543C6D24A13C5F2CBA6EF5FFED9EFD4DBC20496F5194F0D1AAD9D789F32577F8846DF9A14778504CCB5DD7507114C148C1937FB99DA15F9596D4FA052CDAF1F66D7E5E0C0793628752BF9AF3C4AC67E21C21D170AD448160761BDDF586A4900FBB7DCC44467F1550D15DB774D7CACFE3105B465321A5F95FEC22C2011D616A5C0E22F0535DD1F969202BE56EC015F891", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_U82", "3F5D99A61561D70C6C335A30D9A11FA8A3AD70FBECF46C9E233D57AA827CCCBB137C060A47E693E234BA1B532851053E17446D5582B9FEE205C0D12C7613378C8B8C8C0184CDBA90D56A308014AAC0458C5572699D599A15BA36146B6F2E230034708CF67D31AB837B7BD8E5967FD9A7BF413B7D9314302B18E48962D01CF6F1", "ED2055A7B95DB86F7E3101196AE6218015D70D03DF6FD5787DE150E82927443097A90485757743447E2F4641AFCF510ACF585F73E79C45B2908D5DE8835221A76D93E48CA465FFBE0DD76CDAA98550AB2E7B84A6470D48595742FB54A204442CE67BAB989C69ADF86457E313EB24C87D80AA7D635449FAB0D97B6B08C5F7C86F", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_JADE", "69FD6B9E25BA604E204EC90E8E0769B28417E6B52DDA7AC53DEB712C549F398A48EA8AD20BF065A093AC85F336F92F1221D3413F3793BC8C7C6057A091828C04F6FB695F43747D0D22DE100BCCCE70AC7A8F9D092AFAA7D44FCDA99B12454F8C887E383C69E7E21AD15203EAAE51D803CF35DA09C8D536139C658BEBFDDCCF01", "E8490DCBD3488278442F78EC5634CCDB8BEFEE081ED0D19071480A10C299416AB8D0E9EB19E8975CAC260606463C51BB62875AB24690D07905B9C48FE60086DA12899BCE3DBED91E0157CFF76F27A1C09B37E837E7ACB71DA3C0E30564223AE20216FBCB3DE5E93C2D7F98827D61441B988E57497C1DDACB87CEC1E73139BF67", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_B82", "88A4477997B57337CB144D0656BD2D5F0EF59D6B574B631A79AC8015A4C20D454E1DF85682AD25ECCC7FB92BE373259FFFE58741B5A85E50CAA68B9FE84F6E295D2176B96C20FF819E8BB889702C474EFFE1A77710FF3B93E896FA488F1717C75E46A1B0F5898FCACFA35943F1ABF80EBB665BA7FDE59C4BAA61DD2F6C5EC001", "B6A33B825B0CF6ABD3C9D39D1C8BDCE50A41F9BD5CA2DE52C4C447AFA9943F5C1365D2E9CB7961FFD877FD38696B4479A8BB7EB8DA15BD8D59A1CD7E5EE517D1A20F29BC66974F87796A11F7537529F8F46AC57861484808BFCE9EE6CD6527F7FE3BFD57B4A7FD46F8DC047D6C8370DE6507620C2B9A3BF864E8EE4C4D2ABDA1", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_S_2021", "CF553C03AC3CF21FDB4097D4A97F35FC6C305A2E30DFBEBB7667BA2ADFDEC99D3277BCCD314281C592ADE680B42849FDE6122659A68CD7E525B764520D612C7C6C141BC4B2594BC88732D4CA0A97E464D7C1ECF4FC2788F1920CB030C1B2B3EA84E8D6191D5E53D56C5FC495051A1D0FDBEA947D58A9D773A68152D157D4BF57F2B4FBA8182F96EA4C9B798018361054F95B251089C786BE542C7881C49B077AD52AF25A359BB26257170706217F66533CF4B8379A1FB7A30C955C8ED4C1C6DCA905CE6E7E5E92EC7E1BDA1DB44CD187A9E5137FE44A37CFDBEE173A49654994926CB2FDD7857DFC8978D9DE73E899E18F5DFE33A64E6414FC5D93738F8C5591", "DF85F4C4AE8C98E78142D403D002276A5BF9EDD17870CAA848FC45720E8B4BE94F6F9A47181417840A5B7D4FC36575129AFD6A848A0DE3F62FAC5B5F687A2219CAB8CDF2E7527D6AF3C6BE84EB99BF519B0B210960FC8F5223C9BC38E8F20D0267642153CF370312B955143E10490C6A207868AC7AC314BBE10F6063A1BA606E28D248A1EE3E7000D12E9C4EBD47AE483B625156B82026FCFDC36118198CAC1463AEB56BDFE260EFA38AC1D4123C13FE59E0FB0F2F895609C117F7A39FB9F27C356D4748CF7AF41E15EA68C6C7C64C4D0A1ACB4632965E0260D9B08DE9FD81B82050C9929B79EE865F89272483B6FED8A409D6A1AF2429D24FD358A4B4DA4E77", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_MINIQ2", "A475BA952A9F2F9E58D6EC91B41A03158354EA1E451656D83D15691C07ECA3410E7A2401283462F66A0EBF1F91682A80AE61168B2260F4368F93E197A9DB65F4139523EF5449A6FA77568A9FFE90E0A34A37F99B7C1EC6ED1683A574D9045993679EF73299991CD43B96FDBA6673AE4318F2F635A816F8559D325F9EBE428A01", "BA1D10A245E60471E8D3138611615170F213CAE5B895C8AF35EB720E2671915F07DD6CCB5384D7580200D18F430C89405DCB0BE6A5E91CFF0FDA970E292D5F0704720473BC61E19590539B1BB08CE2B306755DB1F70CF1193933802CA44281FE01699F75E56FB7660FCE0342CCC284D497A17AD7D3D15EAA20AD4C67BD92DE61", "010001"),
			new GClass58("Alcatel", new List<int>(), "MTK_HIGHWAY", "5407571C851F5B877A2255C6887C5D832369698B481C81DB8AC07062DFABC7229D4B00F95956665743F7DEEEDBF54A17C9A404C97433F46D983BD0C5F49FA4B013B9D86E5F1377F563D8299675C0EA2B81F51C33AD74A265184DF9389EEFB8E72D2F0585E4A41826B8846B0EE6DA5EF8CCE471536109FE4C658735247EBBC301", "BECA753FD31EF104BBB01B0A7C560C7BC040D30EA18F216B64B7DE416B695AF2B3350ECC02FA5224B412793F876A7BDBD8CBE7FECD754AA8214A27BFE7ECECD8CAA16959DF83BDAEED524880A820F8DFE601DC70F164FF1921BAAA06EFD8C584C22269A109D16287356FD30E7EB02A1365CA93FCB8088278F119A2C7298306A9", "010001"),
			new GClass58("Motorola", new List<int>(), "G13", "AEAC47CD11A5DD6C5EEEC43D8F2C536A2917CEF95AD02F5A7C978E88C35702B590F7A72A2AF28AEB9B5F5B2D8056D03F916595D189C9B6927AC0874980537178AACE8E1831DD654E0B72FF2F44670196A57A43C340355CAF828B331A5715AED4E06D5D18896BCF25B201A0DC9760B0B2EF1CFB4EAB6940D7F8E2EBD86DC1E678AA69F6B0BBF55C688BF72C2123CF42E367F789E2592CE281C7C4752E14F6FD00D54610977DEF753E3890F12F704688537E860D81142805750B805E7CAE3AACDE1CD7A272D227E9F8CCAADCB4D06489664627BAC46CAF5DA0F0740CEEDEBC7ED1C1D1EB1E37C6A8A9E6A0454F742B3248448B20C93D5FF6E5C789907A862C90A1", "DA61964924F441559A1F8B5264CEB01DACE8E417413BBA4657F4556811D07B85074FD6987F315A7492E003D03C57FC83D3B889F2D4F136D0989E515A08628A7B16A300217162DC35C340B1127046AA86649B763AF97F7C9871964483DE6695CDA2E8CCE82E1F6A0F701AF8BE767BB16927489524F8FC9A2C280F5692E850E4C4E2606436CF2E253147AFAB32E6B92A19FA180C43CF480619B71B3D6A7863C7CC376C0A36BCF8BA3DA89CBF3E6DAA4691DCD769C0AE4535E502A9966AFF3F123C7A0EDA2DF04593B0E1FC60DC688F2BA7617DFE67D31854443ED95D2645323728C594CA49DAA9351A572E3182D0A1B3146C92CEF87380CBD2DEFFEBC4E8F420D3", "010001")
		};
		F83FD0B8 = new List<GClass58>
		{
			new GClass58("Tecno/Infinix", new List<int> { 4616, 2311 }, "", "499890AEF768030B56AD8BF7355262A57643A8C3DF318647B72E0B72416D91D25882D0077FEBE9E8275E9F42C3F89FFF5E99E0163487461B7CC3E97C1CC4F4E2D587AB7D8DB80335AA8E3254F318AAC42136FF1043649918685A0F5CF5548318090C26D951A2B93F79FB0D9B5E1DB1C928091E1908A5DA7D3B9194A22B18B78C6EF88932EFB3D88DB8BA7117257063DE165FA8534D50C6B35F6F7C0AF5F4A96BB89756BB7AC94110E8D1B5868A5DAADF815FE8FA38960A039D681F319C7B7ED7A55649C2F2F75B26C27807AFED8BC4EF57619FF9DA152E337FE379E1B1B0020C1EF2ABDCE4A66AAF3802EE8AB105ACA6F3388EA4C20184E572778C8EB3D30B51", "BA4C5178EE5477B7A30FC99F9FE78E7C011E58B5DC05832591AADEE0D26A2667738FFE851782016CAE7DB8DC1958C627BC60E3A96D46E225536DCC76CBFADFE6CA2CBE4BE982AB46CBB0F1C5CB2CB13D7BBAC96E467D76AF819DCA28FB90A03AF92DDC05B34506E24F02A049D73B8143A1CF4B081372D49F4BCA21DA9EE8A19679669C71C836FC5B8180BD98256BA2AE82B702DDC61EDFD9C47871D027297D6C72A984046081953D141656F0DC4BC4E2D4006147BE096D7E855AA7057460EAC7C57660F1F28BE278C38700BAC5EBADB39F6508CF2F1E4044421664A856623FFB2002C1204CED786D576E3DDAA7C185FD698B1185A98A03B875F0A93831270557", "010001"),
			new GClass58("KaiOS", new List<int>(), "", "928d2a63d56bc42c3c02e836c025f6db39f06d57480dc705f3eaf238120a2b0d8fc00ec3cdf209615ca41bf73ae499dd9accb09b99fbb4046087008aab48d96f437292c160092dfc4ae33f94acee374e584b9d70d90ca46664d31cf72bba302e6eafab68e13a7f1ed9fa0ddc2604f3164bc97bc8c1b0be9db1d76d1d970ea36f4af8ad2ce0ab5b7ca6b4f99c133fa3f8ff9c92da874394d0e8f1bdbb83c8e811a344a5b5a7251c9b4fb4ad0ac494a2c50a1a79fa9b3992d749535b691dc2f016cee493e41cd2033190c4c0497f689e48bae5d3ad28cb0e8d17dae7f4e8c034ab60ab330f678fbfdd2c9716bfa6a617339d248b46df1593d5317b3af44a864641", "9BB734774443D77557A76E24B10733787750D90D09C869CD606D54F28978EA6220DC9948B3C9E89284F8551D6166F3754B6A3B890AC9CDA9E37DFAA0C1317E351CE5107C4273795949C6CCE638314AB1A345385D7642CB8D055A1F410C7D7E24A6F0A2AAB8184E773D21B3754A947541680F2C1A8D6BA5BEFD3B6E1FC28EC0B61D55B1454383F2C3E8BD27170A25978608F6788B90A2FC34F0CE35056BF7520795C8C60232CBBC0B0399367AF937869CA45CF737A8A066127893E93166C433298DD6FD009E6790E743B3392ACA8EA99F61DFC77BD99416DDA4B8A9D7E4DA24217427F3584119A4932016F1735CC63B12650FDDDA73C8FCFBC79E058F36219D3D", "010001"),
			new GClass58("Rowan", new List<int>(), "", "09976537029b4362591c5b13873f223de5525d55df52dde283e52afa67f6c9dbf1408d2fb586a624efc93426f5f3be981f80e861ddd975a1e5e662db84f5164804a3ae717605d7f15866df9ed1497c38fdd6197243163ef22f958d7b822c57317203e9a1e7d18dad01f15054facdbddb9261a1272638da661fe4f9f0714ecf00e6541cc435afb1fd75a27d34b17ad400e9474ba850dafce266799caff32a058ff71e4c2daacaf8ba709e9ca4dc87584a7ffe8aa9a0a160ed069c3970b7dae3987ded71bd0bc824356987bd74363d46682c71913c3edbdb2a911f701f23aee3f8dd98180b5a138fd5ad74743682d2d2d1bb3d92786710248f316dd8391178ea81", "D16403466C530EF9BB53C1E8A96A61A4E332E17DC0F55BB46D207AC305BAE9354EAAC2CB3077B33740D275036B822DB268200DE17DA3DB7266B27686B8970B85737050F084F8D576904E74CD6C53B31F0BB0CD60686BF67C60DA0EC20F563EEA715CEBDBF76D1C5C10E982AB2955D833DE553C9CDAFD7EA2388C02823CFE7DD9AC83FA2A8EB0685ABDAB56A92DF1A7805E8AC0BD10C0F3DCB1770A9E6BBC3418C5F84A48B7CB2316B2C8F64972F391B116A58C9395A9CE9E743569A367086D7771D39FEC8EBBBA3DD2B519785A76A9F589D36D637AF884543FD65BAC75BE823C0C50AA16D58187B97223625C54C66B5A5E4DBAEAB7BE89A4E340A2E241B09B2F", "010001"),
			new GClass58("Generic", new List<int>(), "CodeSigKey", "00D57BCF5934CE1A035F4598B42DAD4BC8AE7577FB6D81FA232317E8EE7C4FBB33772EFE378DF7DFE5369BB9ACAB1008FA1CFBA737890012A883B372B15932335B689B46A32F1B383B75F0DE2E1B5B0B9F4E1C3E780C2AB0CD3671EB4E34F30BB4C630A60D168CA124810D0F91A1ACC8EFDCEE52D4762BB35813BCC93878E1D15B750561B78006B4C13A8F76B5F10C941E5776C21192357A9B9D7E02C0FC812D2B154671863DE97CE3ED07F90624A0CADD04079E145168A3558A64786820192F5B638354DA69520288B976296961C337FB18A90120F2B6B365C0E1A57CE4119AE8BC718E08FDA33F1F42AA1C91AED090EC6B5656A66C246F89FDE5FD41A76671", "01C6B6A1DDF05E818E3DFE16101C5DF65939F352EAB8AACA91CB5BFEC15A1989DD7553343683BC30BB38E45F15BF17BCCAB16A41D695A4318F26504675FE83E92EE21C991C0FBA705395B4A34C331842D8A6F69846B58CC67306E3DE27B05666A6C4372E3FC0D92F314805EDD5B1CB7D25BF3CF9CA9C33C36D97B0B37DA8A44A7A1CA651679D8D680557740C7C1CA25D84BDD12136C2930432808F28265D1E33E667389E4806D865F3CC06329534F7A11861EB688545DCCCEF0B04E96735A08368FA31A1F3260073B31299B216192E620B8D1EA468925ADBD627C49EFC3623658F3CF8AD6D8556272E48FA7711E650287DA19196610F036B6C0D394E42C121D1", "010001"),
			new GClass58("Generic", new List<int>(), "SecureROSigKey", "040AB412E994921780E7D3AC4E665B5018BAD2221D93A236FCD3D4245BB14EF5E715B687254BFC5D5C058FF5C33AF644E6B03748A6ABFDFAFF808265B9C12B42C2164826B3A8CD5D6B3295E025618AED68D33E02D75FB8C69FDE6753AF454EAA92F448961C5D11DFD8D2D5125E54C71DE5792EAD4B4AD2A47ED2F144C664A2EC2B5C527D4C4570162EBADF6FA6AA19D86C927257BDA4AC4B471AD94AC16C8A97E9201101EF268E35E66835FE9831F3D18495BA15B3FCD9089B4569F770896674173647EFED86F4570CE6118E48A7EDD6CE2A2ED72A2E6FD615A323708F0881443B6C6DEF3800B392385E060AAC6CE086DDBA9227027F80B0DFDF691A1ED8A601", "041EDF18B2702830A0D1D6D2C0C9833C46D55636929ABC8CBF62D03A8E352697F536DDF250E6CA5F1153A9227FEA4F6C4A91CF118FD821ABA9020FFC6AA05D4C361177AA384ACEE201E1326D5A0E1D566E74DA51CD735841FB5638E03F4E9A5AC58D0123F8E057686541E424F83508D1CEADDE7A893F57B852ECFF27953F53030953859EA265C0C7155F6B730E902BC23FEB58077E8D439606B164635D5AA0C53657BF2143EEE86F06781573BED22DD3E792591A263F2357CC42AEF4B8DB585987A311A022C4442A4DFA1C4891D8ADA1B231A92096E16D9C718FB09DFC0A5B008BB8243BF32C537A6B19542E37311085197B6BE8DA54EE1D6BC28BA94E0079CB", "010001"),
			new GClass58("Motorola", new List<int>(), "Moto G24", "21285935122662264864057010353628894046000309676608514009863068975210666417913992390203952016184103585209090567456567655640013653254389492000330698369108534400952734580691061611305270427006293274003837251061472898178055883452926583157069288429704943815305237986749864472793502417868363277703857993800596854717455227000386605194799101756888463582260204208854094963305819533909836121398593369779835885060955127693070927806187215287927106022661033162137977188155754455157501898266271848816663792930331047190853544282318635289788145216933852870788132800493768471635534748961829891319455548129601994556070502373744923475337", "27716841114500344765566038575545425862643744318131811064043948092197282887898697011320983157192537336111902575339275485241294128734436513028266336433137947170436497709458387570684339829820815050334574753587565304243810940252616667269969799046662551962481559713449481789622057339608569750866816501553907454007165785963469328609654526247959103207964930235031378798604098615371009080202164164858220545960117335763391558057794040598744405105128408738639336292387025499581025311622360049763406211499993117726494678511901460658618003333913774822703760038655009487487524407468957728357988965151989430864537201197625439572663", "010001")
		};
	}

	public bool BE2E2DAC(uint A505AB04, uint uint_0)
	{
		if (method_9(983044u))
		{
			method_53(A505AB04);
			method_53(uint_0);
			long num = method_55();
			if (num == 0L)
			{
				return true;
			}
		}
		return false;
	}

	public bool method_0(uint uint_0, byte[] byte_1)
	{
		if (method_9(983043u))
		{
			method_53(uint_0);
			method_53(byte_1.Length);
			method_53(byte_1);
			long num = method_55();
			if (num == 0L)
			{
				return true;
			}
		}
		return false;
	}

	public bool method_1(uint uint_0, byte[] byte_1)
	{
		if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.const_0)
		{
			return e5964FB0_0.A335BB39.method_1(uint_0, byte_1);
		}
		using (IEnumerator<int> enumerator = GClass112.smethod_30(0, byte_1.Length, 4).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				List<byte> list = GClass112.smethod_14(byte_1, current, 4).ToList();
				while (list.Count < 4)
				{
					list.Add(0);
				}
				method_2(Class607.B630A78B.object_0[21](uint_0 + current), GClass111.C3B9331C("<I", list.ToArray()).Cast<uint>().ToList());
			}
		}
		return true;
	}

	public bool FF33F413(uint F2151B96, uint[] uint_0)
	{
		if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.const_0)
		{
			return e5964FB0_0.A335BB39.CA184E06(F2151B96, uint_0);
		}
		for (int i = 0; i < uint_0.Length; i++)
		{
			method_2(Class607.B630A78B.object_0[21](F2151B96 + i * 4), uint_0[i]);
		}
		return true;
	}

	public bool method_2(uint D7144A2A, object AC903998)
	{
		if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.const_0)
		{
			return e5964FB0_0.A335BB39.method_2(D7144A2A, AC903998);
		}
		if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.D03BC921)
		{
			return e5964FB0_0.gclass8_0.method_2(D7144A2A, AC903998);
		}
		List<uint> list = new List<uint>();
		if (GClass112.smethod_19(AC903998))
		{
			list.Add(Class607.B630A78B.object_0[40](AC903998));
		}
		else
		{
			list = (List<uint>)AC903998;
		}
		int num = 0;
		if (list.Count < 32)
		{
			foreach (uint item in list)
			{
				if (BE2E2DAC(Class607.B630A78B.object_0[21](D7144A2A + num), item))
				{
					num += 4;
					continue;
				}
				return false;
			}
		}
		else
		{
			List<byte> list2 = new List<byte>();
			foreach (uint item2 in list)
			{
				list2.AddRange(GClass111.smethod_2("<I", new object[1] { item2 }));
			}
			method_0(D7144A2A, list2.ToArray());
		}
		return true;
	}

	public bool AB28960B(ulong B79A5A15, ulong ulong_0, string string_0 = "", int int_0 = 0, string B638110E = null, byte[] byte_1 = null)
	{
		return (bool)new GClass128().E6A33692(new object[7] { this, B79A5A15, ulong_0, string_0, int_0, B638110E, byte_1 }, 22530837);
	}

	public bool method_3(ulong ulong_0, byte[] byte_1)
	{
		method_34();
		if (method_32("PGPT", "").BD147F02)
		{
			method_50(131077u);
			method_50(524289u);
			method_34();
			if (method_32("SGPT", "").BD147F02)
			{
				string[] array = new string[2] { "preloader", "preloader_backup" };
				try
				{
					string[] array2 = array;
					int num = 0;
					while (true)
					{
						if (num < array2.Length)
						{
							string a6166DA = array2[num];
							D73D8A10();
							if (!method_4(a6166DA, ulong_0))
							{
								break;
							}
							int num2 = byte_1.Sum((byte result) => result) & 0xFFFF;
							if (method_53(GClass111.smethod_2("<I", new object[1] { 0 })))
							{
								if (method_53(GClass111.smethod_2("<I", new object[1] { num2 })))
								{
									if (method_53(byte_1))
									{
										gdelegate25_0?.Invoke(ulong_0, ulong_0);
										long num3 = method_55();
										if (num3 == 0L)
										{
											gdelegate25_0?.Invoke(ulong_0, ulong_0);
											num++;
											continue;
										}
										return false;
									}
									return false;
								}
								return false;
							}
							return false;
						}
						method_55();
						method_50(8388613u, null, null, bool_2: true);
						gdelegate25_0?.Invoke(ulong_0, ulong_0);
						return true;
					}
					return false;
				}
				catch
				{
					return false;
				}
			}
		}
		return false;
	}

	public bool method_4(string A6166DA0, ulong E11A3F97)
	{
		if (method_53(65537u))
		{
			long num = method_55();
			if (num == 0L)
			{
				List<byte[]> list = new List<byte[]>();
				list.Add(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), A6166DA0));
				list.Add(GClass111.smethod_2("<Q", new object[1] { E11A3F97 }));
				return method_54(list);
			}
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
		}
		return false;
	}

	public bool method_5(ulong ulong_0, ulong ulong_1, int int_0 = 1, int int_1 = 8)
	{
		if (method_53(65540u))
		{
			long num = method_55();
			if (num == 0L)
			{
				GStruct54 gStruct = default(GStruct54);
				List<byte> list = GClass111.smethod_2("<IIQQ", new object[4] { int_0, int_1, ulong_0, ulong_1 }).ToList();
				list.AddRange(GClass111.smethod_2("<IIIIIIII", new object[8] { gStruct.E51B2AA5, gStruct.int_0, gStruct.int_1, gStruct.int_2, gStruct.int_3, gStruct.int_4, gStruct.B31CDAA9, 0 }));
				return method_54(list.ToArray());
			}
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
		}
		return false;
	}

	public List<uint> EE83B4B7(uint uint_0, int int_0 = 1)
	{
		if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.const_0)
		{
			return e5964FB0_0.A335BB39.method_4(uint_0, int_0);
		}
		if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.D03BC921)
		{
			return e5964FB0_0.gclass8_0.BDAA3A27(uint_0, int_0);
		}
		List<uint> list = new List<uint>();
		if (int_0 < 32)
		{
			using IEnumerator<int> enumerator = GClass112.smethod_30(0, int_0).GetEnumerator();
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				byte[] array = method_7(Class607.B630A78B.object_0[21](uint_0 + current * 4));
				if (array != null)
				{
					object bBB4E2A = GClass111.C3B9331C("<I", array)[0];
					if (int_0 != 1)
					{
						list.Add(Class607.B630A78B.object_0[40](bBB4E2A));
						continue;
					}
					list.Add(Class607.B630A78B.object_0[40](bBB4E2A));
					return list;
				}
				return list;
			}
		}
		else
		{
			byte[] aA23FA = method_6(uint_0, Class607.B630A78B.object_0[836](int_0 * 4));
			using IEnumerator<int> enumerator2 = GClass112.smethod_30(0, int_0, 4).GetEnumerator();
			while (Class607.B630A78B.object_0[212](enumerator2))
			{
				int current2 = enumerator2.Current;
				list.Add(Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(aA23FA, current2, 4))[0]));
			}
		}
		return list;
	}

	public byte[] method_6(uint B522A3AB, uint uint_0)
	{
		if (method_9(983041u))
		{
			method_53(B522A3AB);
			method_53(uint_0);
			byte[] result = method_51();
			long num = method_55();
			if (num == 0L)
			{
				return result;
			}
		}
		return null;
	}

	public byte[] method_7(uint uint_0)
	{
		if (method_9(983042u))
		{
			method_53(uint_0);
			byte[] result = method_51();
			long num = method_55();
			if (num == 0L)
			{
				return result;
			}
		}
		return null;
	}

	public bool method_8(uint AB9C5BA2)
	{
		if (method_53(65545u))
		{
			long num = method_55();
			if (num == 0L && method_53(AB9C5BA2))
			{
				return true;
			}
		}
		return false;
	}

	public bool method_9(uint uint_0)
	{
		if (method_53(65545u))
		{
			long num = method_55();
			if (num == 0L && method_53(uint_0))
			{
				num = method_55();
				if (num == 0L)
				{
					return true;
				}
				e39CAE0B_0?.Invoke("Error: ");
				e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
			}
		}
		return false;
	}

	public Tuple<byte[], byte[]> method_10(byte[] FE37F427, byte[] CD13C102)
	{
		int a08677A = Class607.B630A78B.object_0[1262](AA329715.E1105331.list_0[1].uint_3);
		int num = Class607.B630A78B.object_0[1262](AA329715.E1105331.list_0[2].uint_3);
		var (num5, int_, num6) = method_12(FE37F427, CD13C102, a08677A, num, AA329715.A08FA093);
		if (num5 != -1)
		{
			FE37F427 = GClass67.smethod_5(FE37F427);
			CD13C102 = GClass67.BB93B517(CD13C102, AA329715.E1105331);
			FE37F427 = GClass67.A826F023(FE37F427, CD13C102, num5, int_, num6);
			bool_1 = true;
			AA329715.byte_0 = CD13C102.Take(num6).ToArray();
		}
		else
		{
			bool_1 = false;
			AA329715.byte_0 = CD13C102.Take(CD13C102.Length - num).ToArray();
		}
		return Tuple.Create(FE37F427, CD13C102);
	}

	public Tuple<int, int> method_11(byte[] byte_1, int F8954A8D)
	{
		int num = byte_1.Length - F8954A8D - 48;
		byte[] array = byte_1.Skip(num).Take(48).ToArray();
		if (array.Skip(array.Length - 4).Take(4).SequenceEqual(new byte[4]))
		{
			return Tuple.Create(num, 2);
		}
		return Tuple.Create(-1, -1);
	}

	public Tuple<int, int> C614B385(byte[] byte_1)
	{
		int num = GClass112.smethod_24(byte_1, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[47](), "MMU MAP: VA"), 0);
		if (num != -1)
		{
			byte[] array = byte_1.Skip(num - 48).Take(48).ToArray();
			if (array.Skip(array.Length - 4).Take(4).SequenceEqual(new byte[4]))
			{
				return Tuple.Create(num - 48, 2);
			}
			return Tuple.Create(num - 20, 1);
		}
		return Tuple.Create(-1, -1);
	}

	public Tuple<int, int, int> method_12(byte[] A8898A2F, byte[] byte_1, int A08677A2, int int_0, bool bool_2)
	{
		int num = byte_1.Length - int_0;
		DD9B12B9(A8898A2F, byte_1.Take(num).ToArray()).Deconstruct(out var item, out var item2);
		int item3 = item;
		int num2 = item2;
		if (num2 == -1)
		{
			num = byte_1.Length;
			DD9B12B9(A8898A2F, byte_1).Deconstruct(out item2, out item);
			item3 = item2;
			num2 = item;
			if (num2 == -1 && !bool_2)
			{
				C614B385(A8898A2F).Deconstruct(out item, out item2);
				num2 = item;
				item3 = item2;
			}
			else if (num2 == -1 && bool_2)
			{
				method_11(A8898A2F, A08677A2).Deconstruct(out item2, out item);
				num2 = item2;
				item3 = item;
				if (num2 == -1)
				{
					return Tuple.Create(-1, -1, -1);
				}
			}
			return Tuple.Create(num2, item3, num);
		}
		return Tuple.Create(num2, item3, num);
	}

	public Tuple<int, int> DD9B12B9(byte[] byte_1, byte[] byte_2)
	{
		using SHA1 b02A = Class607.B630A78B.object_0[287]();
		byte[] byte_3 = Class607.B630A78B.object_0[455](b02A, byte_2);
		using SHA256 b02A2 = Class607.B630A78B.object_0[286]();
		byte[] byte_4 = Class607.B630A78B.object_0[455](b02A2, byte_2);
		int num = GClass112.smethod_24(byte_1, byte_3, 0);
		int item = 1;
		if (num == -1)
		{
			num = GClass112.smethod_24(byte_1, byte_4, 0);
			item = 2;
		}
		return Tuple.Create(item, num);
	}

	public Tuple<int, int, ulong> method_13(string D0B042A6, ulong C201D293)
	{
		string string_ = AA329715.string_1;
		string string_2 = string_;
		int int_;
		if (!Class607.B630A78B.object_0[787](string_2, "nor"))
		{
			if (!Class607.B630A78B.object_0[787](string_2, "nand"))
			{
				if (!Class607.B630A78B.object_0[787](string_2, "ufs"))
				{
					int_ = ((!Class607.B630A78B.object_0[787](string_2, "sdc")) ? 1 : 2);
				}
				else
				{
					int_ = 48;
					if (D0B042A6.F02F0138() && Class607.B630A78B.object_0[754](D0B042A6) == 8)
					{
						D0B042A6 = Class607.B630A78B.object_0[924](3);
					}
				}
			}
			else
			{
				int_ = 16;
			}
		}
		else
		{
			int_ = 32;
		}
		return method_14(int_, D0B042A6, C201D293);
	}

	public Tuple<int, int, ulong> method_14(int int_0, string C806012E = null, ulong E12A0187 = 0uL)
	{
		int item = 0;
		char c;
		switch (int_0)
		{
		case 16:
		case 17:
		case 18:
		case 19:
		case 20:
		case 21:
			item = 8;
			E12A0187 = Class607.B630A78B.object_0[743](E12A0187, BC9B8291.D223FF1A);
			break;
		case 1:
		case 2:
			if (C806012E != null)
			{
				if (C806012E != null)
				{
					switch (Class607.B630A78B.object_0[343](C806012E))
					{
					case 3:
						break;
					case 4:
						goto IL_0253;
					case 5:
						goto IL_02fd;
					default:
						goto IL_03e9;
					case 0:
						goto IL_03f1;
					}
					switch (Class607.B630A78B.object_0[1091](C806012E, 2))
					{
					case '1':
						break;
					case '2':
						goto IL_0124;
					case '3':
						goto IL_0189;
					case '4':
						goto IL_01ee;
					default:
						goto IL_03e9;
					}
					if (Class607.B630A78B.object_0[787](C806012E, "gp1"))
					{
						item = 4;
						if (Class607.B630A78B.object_0[787](AA329715.string_1, "emmc"))
						{
							E12A0187 = Class607.B630A78B.object_0[743](E12A0187, B2A384A8.ulong_3);
						}
						break;
					}
				}
				goto IL_03e9;
			}
			goto IL_03f1;
		case 48:
			if (!Class607.B630A78B.object_0[787](C806012E, "") && C806012E != null)
			{
				if (C806012E != null)
				{
					switch (Class607.B630A78B.object_0[343](C806012E))
					{
					case 3:
						break;
					case 4:
						goto IL_0520;
					case 5:
						goto IL_05a8;
					default:
						goto IL_0644;
					}
					switch (Class607.B630A78B.object_0[1091](C806012E, 2))
					{
					case '1':
						break;
					case '2':
						goto IL_04c0;
					case '3':
						goto IL_04e1;
					case '4':
						goto IL_0502;
					default:
						goto IL_0644;
					}
					if (Class607.B630A78B.object_0[787](C806012E, "lu1"))
					{
						goto IL_0622;
					}
				}
				goto IL_0644;
			}
			goto IL_0649;
		case 32:
		case 33:
		case 34:
			{
				item = 8;
				E12A0187 = Class607.B630A78B.object_0[743](E12A0187, gstruct55_0.FE06E83F);
				break;
			}
			IL_0644:
			return null;
			IL_0649:
			item = 3;
			E12A0187 = Class607.B630A78B.object_0[743](E12A0187, afbd65B0_0.B81255AA);
			break;
			IL_05a8:
			c = Class607.B630A78B.object_0[1091](C806012E, 4);
			if (c != '1')
			{
				if (c == '2' && Class607.B630A78B.object_0[787](C806012E, "boot2"))
				{
					goto IL_05e4;
				}
			}
			else if (Class607.B630A78B.object_0[787](C806012E, "boot1"))
			{
				goto IL_0622;
			}
			goto IL_0644;
			IL_02fd:
			c = Class607.B630A78B.object_0[1091](C806012E, 4);
			if (c != '1')
			{
				if (c == '2' && Class607.B630A78B.object_0[787](C806012E, "boot2"))
				{
					item = 2;
					if (Class607.B630A78B.object_0[787](AA329715.string_1, "emmc"))
					{
						E12A0187 = Class607.B630A78B.object_0[743](E12A0187, B2A384A8.B61F2413);
					}
					break;
				}
			}
			else if (Class607.B630A78B.object_0[787](C806012E, "boot1"))
			{
				item = 1;
				if (Class607.B630A78B.object_0[787](AA329715.string_1, "emmc"))
				{
					E12A0187 = Class607.B630A78B.object_0[743](E12A0187, B2A384A8.ulong_1);
				}
				break;
			}
			goto IL_03e9;
			IL_01ee:
			if (Class607.B630A78B.object_0[787](C806012E, "gp4"))
			{
				item = 7;
				if (Class607.B630A78B.object_0[787](AA329715.string_1, "emmc"))
				{
					E12A0187 = Class607.B630A78B.object_0[743](E12A0187, B2A384A8.DFA560AD);
				}
				break;
			}
			goto IL_03e9;
			IL_0189:
			if (Class607.B630A78B.object_0[787](C806012E, "gp3"))
			{
				item = 6;
				if (Class607.B630A78B.object_0[787](AA329715.string_1, "emmc"))
				{
					E12A0187 = Class607.B630A78B.object_0[743](E12A0187, B2A384A8.ulong_5);
				}
				break;
			}
			goto IL_03e9;
			IL_0520:
			c = Class607.B630A78B.object_0[1091](C806012E, 0);
			if (c != 'r')
			{
				if (c == 'u' && Class607.B630A78B.object_0[787](C806012E, "user"))
				{
					goto IL_0649;
				}
			}
			else if (Class607.B630A78B.object_0[787](C806012E, "rpmb"))
			{
				goto IL_0583;
			}
			goto IL_0644;
			IL_0124:
			if (Class607.B630A78B.object_0[787](C806012E, "gp2"))
			{
				item = 5;
				if (Class607.B630A78B.object_0[787](AA329715.string_1, "emmc"))
				{
					E12A0187 = Class607.B630A78B.object_0[743](E12A0187, B2A384A8.ulong_4);
				}
				break;
			}
			goto IL_03e9;
			IL_0253:
			c = Class607.B630A78B.object_0[1091](C806012E, 0);
			if (c != 'r')
			{
				if (c == 'u' && Class607.B630A78B.object_0[787](C806012E, "user"))
				{
					goto IL_03f1;
				}
			}
			else if (Class607.B630A78B.object_0[787](C806012E, "rpmb"))
			{
				item = 3;
				if (Class607.B630A78B.object_0[787](AA329715.string_1, "emmc"))
				{
					E12A0187 = Class607.B630A78B.object_0[743](E12A0187, B2A384A8.ulong_2);
				}
				break;
			}
			goto IL_03e9;
			IL_03f1:
			item = 8;
			break;
			IL_0502:
			if (Class607.B630A78B.object_0[787](C806012E, "lu4"))
			{
				goto IL_0583;
			}
			goto IL_0644;
			IL_0583:
			item = 4;
			E12A0187 = Class607.B630A78B.object_0[743](E12A0187, afbd65B0_0.ulong_1);
			break;
			IL_04e1:
			if (!Class607.B630A78B.object_0[787](C806012E, "lu3"))
			{
				goto IL_0644;
			}
			goto IL_0649;
			IL_04c0:
			if (Class607.B630A78B.object_0[787](C806012E, "lu2"))
			{
				goto IL_05e4;
			}
			goto IL_0644;
			IL_05e4:
			item = 2;
			E12A0187 = Class607.B630A78B.object_0[743](E12A0187, afbd65B0_0.ulong_1);
			break;
			IL_03e9:
			return null;
			IL_0622:
			item = 1;
			E12A0187 = Class607.B630A78B.object_0[743](E12A0187, afbd65B0_0.ulong_0);
			break;
		}
		return new Tuple<int, int, ulong>(int_0, item, E12A0187);
	}

	public bool method_15()
	{
		ulong num = 0uL;
		bool e690DC = false;
		if (B2A384A8.ulong_2 > 0L)
		{
			num = B2A384A8.ulong_2 / 256L;
		}
		else if (afbd65B0_0.uint_1 != 0)
		{
			num = 65536uL;
			e690DC = true;
		}
		Tuple<bool, string> tuple = method_24();
		if (tuple.Item1 && num > 0L)
		{
			byte[] byte_ = new byte[256];
			using (IEnumerator<int> enumerator = GClass112.smethod_30(0, Class607.B630A78B.object_0[704](num)).GetEnumerator())
			{
				while (Class607.B630A78B.object_0[212](enumerator))
				{
					int current = enumerator.Current;
					if (method_23(current, byte_, e690DC))
					{
						gdelegate25_0?.Invoke(num, Class607.B630A78B.object_0[715](current));
						continue;
					}
					return false;
				}
			}
			return true;
		}
		return false;
	}

	public bool method_16(int int_0, int int_1)
	{
		bool e690DC = false;
		if (B2A384A8.ulong_2 > 0L)
		{
			e690DC = false;
		}
		else if (afbd65B0_0.uint_1 != 0)
		{
			e690DC = true;
		}
		Tuple<bool, string> tuple = method_25();
		if (tuple.Item1 && int_1 > 0)
		{
			byte[] byte_ = new byte[256];
			int num = int_0;
			while (true)
			{
				if (num <= int_1)
				{
					if (!method_23(num, byte_, e690DC))
					{
						break;
					}
					gdelegate25_0?.Invoke((ulong)int_1, Class607.B630A78B.object_0[715](num));
					num++;
					continue;
				}
				return true;
			}
			return false;
		}
		return false;
	}

	public bool method_17(int int_0, int int_1)
	{
		bool e690DC = false;
		if (B2A384A8.ulong_2 > 0L)
		{
			e690DC = false;
		}
		else if (afbd65B0_0.uint_1 != 0)
		{
			e690DC = true;
		}
		Tuple<bool, string> tuple = method_26();
		if (tuple.Item1 && int_1 > 0)
		{
			byte[] byte_ = new byte[256];
			int num = int_0;
			while (true)
			{
				if (num <= int_1)
				{
					if (!method_23(num, byte_, e690DC))
					{
						break;
					}
					gdelegate25_0?.Invoke((ulong)int_1, Class607.B630A78B.object_0[715](num));
					num++;
					continue;
				}
				return true;
			}
			return false;
		}
		return false;
	}

	public Tuple<bool, string> method_18()
	{
		return method_25();
	}

	public long method_19()
	{
		uint num = 0u;
		try
		{
			object[] array = GClass111.C3B9331C("<III", e5964FB0_0.f7020D24_0.method_6(12).byte_0);
			uint num2 = Class607.B630A78B.object_0[40](array[0]);
			Class607.B630A78B.object_0[40](array[1]);
			Class607.B630A78B.object_0[40](array[2]);
			if (num2 != 4277071599u)
			{
				return -1L;
			}
			F7020D24.GStruct60 gStruct = e5964FB0_0.f7020D24_0.method_6(4);
			if (gStruct.byte_0.Length < 4)
			{
				return -1L;
			}
			num = Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", gStruct.byte_0)[0]);
			if (num == 0)
			{
				return 0L;
			}
			return num;
		}
		catch
		{
			return num;
		}
	}

	public bool method_20()
	{
		return (bool)new GClass128().DFB12B0F(new object[1] { this }, 66648);
	}

	public bool method_21()
	{
		uint num = 0u;
		if (B2A384A8.ulong_2 > 0L)
		{
			num = 786434u;
		}
		else if (afbd65B0_0.uint_1 != 0)
		{
			num = 786435u;
		}
		if (method_53(num))
		{
			method_51();
			long num2 = method_55();
			if ((ulong)num2 > 0uL)
			{
				byte[] array = e5964FB0_0.f7020D24_0.B2BCD19C();
				string b8ABF2AA = Class607.B630A78B.object_0[720]("RPMB Key data block found at 0x000000", Class607.B630A78B.object_0[1273](ref array[0], "X"));
				e39CAE0B_0?.Invoke(b8ABF2AA);
				return true;
			}
		}
		return false;
	}

	public bool ACAD4EBB(string string_0)
	{
		ulong num = 0uL;
		bool eF = false;
		if (B2A384A8.ulong_2 > 0L)
		{
			num = B2A384A8.ulong_2;
		}
		else if (afbd65B0_0.uint_1 != 0)
		{
			num = 16777216uL;
			eF = true;
		}
		num -= 256L;
		e39CAE0B_0?.Invoke("RPMB Init: ");
		if (method_20())
		{
			e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
			e39CAE0B_0?.Invoke("Writing RPMB On device :");
			using (FileStream object_ = Class607.B630A78B.object_0[123](string_0))
			{
				using IEnumerator<int> enumerator = GClass112.smethod_30(0, Class607.B630A78B.object_0[704](num)).GetEnumerator();
				while (Class607.B630A78B.object_0[212](enumerator))
				{
					int current = enumerator.Current;
					byte[] aA23FA = new byte[256];
					int int_ = Class607.B630A78B.object_0[53](object_, aA23FA, 0, 256);
					aA23FA = GClass112.smethod_14(aA23FA, 0, int_);
					if (CE866E86(current, aA23FA, eF))
					{
						gdelegate25_0?.Invoke(num, Class607.B630A78B.object_0[715](current));
						continue;
					}
					Class607.B630A78B.object_0[587](object_);
					return false;
				}
			}
			return true;
		}
		e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
		return false;
	}

	public bool CE866E86(int AB9C38BD, byte[] byte_1, bool EF123808 = false)
	{
		uint num = (EF123808 ? 786439u : 786438u);
		if (method_53(num))
		{
			long num2 = method_55();
			if (num2 == 0L && method_53(AB9C38BD) && method_53(byte_1))
			{
				num2 = method_55();
				return num2 == 0L;
			}
		}
		return false;
	}

	public bool method_22(string string_0)
	{
		if (!Class607.B630A78B.object_0[695](string_0))
		{
			return false;
		}
		ulong num = 0uL;
		bool e690DC = false;
		if (B2A384A8.ulong_2 > 0L)
		{
			num = B2A384A8.ulong_2 / 256L;
		}
		else if (afbd65B0_0.uint_1 != 0)
		{
			num = 65536uL;
			e690DC = true;
		}
		Tuple<bool, string> tuple = method_24();
		if (tuple.Item1)
		{
			e39CAE0B_0?.Invoke("Writing RPMB On device :");
			if (num > 0L)
			{
				using (FileStream object_ = Class607.B630A78B.object_0[123](string_0))
				{
					using IEnumerator<int> enumerator = GClass112.smethod_30(0, Class607.B630A78B.object_0[704](num)).GetEnumerator();
					while (Class607.B630A78B.object_0[212](enumerator))
					{
						int current = enumerator.Current;
						byte[] aA23FA = new byte[256];
						int int_ = Class607.B630A78B.object_0[53](object_, aA23FA, 0, 256);
						aA23FA = GClass112.smethod_14(aA23FA, 0, int_);
						if (method_23(current, aA23FA, e690DC))
						{
							gdelegate25_0?.Invoke(num, Class607.B630A78B.object_0[715](current));
							continue;
						}
						Class607.B630A78B.object_0[587](object_);
						return false;
					}
				}
				return true;
			}
		}
		return false;
	}

	public bool method_23(int int_0, byte[] byte_1, bool E690DC32 = false)
	{
		if (byte_1.Length != 256)
		{
			return false;
		}
		uint uint_ = 983047u;
		if (E690DC32)
		{
			uint_ = 983050u;
		}
		if (method_9(uint_))
		{
			method_53(int_0);
			method_53(GClass112.smethod_14(byte_1, 0, 256));
			ushort num = Class607.B630A78B.object_0[504](GClass111.C3B9331C("<H", method_51())[0]);
			long num2 = method_55();
			if (num2 == 0L && num == 0)
			{
				return true;
			}
		}
		return false;
	}

	public Tuple<bool, string> method_24()
	{
		e39CAE0B_0?.Invoke("RPMB Reinit: ");
		bool flag = false;
		if (B2A384A8.ulong_2 > 0L)
		{
			flag = false;
		}
		else if (afbd65B0_0.uint_1 != 0)
		{
			flag = true;
		}
		uint uint_ = 983045u;
		if (flag)
		{
			uint_ = 983048u;
		}
		if (method_9(uint_))
		{
			long num = method_55();
			if (num == 0L)
			{
				e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
				return Tuple.Create(item1: true, "");
			}
		}
		return Tuple.Create(item1: false, "");
	}

	public Tuple<bool, string> method_25()
	{
		B42DC004 b42DC = new B42DC004(e5964FB0_0);
		if (GClass112.C78DEB29.A8054FBA.EC16A503 != 0)
		{
			byte[] array = e5964FB0_0.E68B8FBC;
			if (array == null || array.Length == 0)
			{
				array = method_6(GClass112.C78DEB29.A8054FBA.EC16A503, 16u);
			}
			byte[] byte_ = e5964FB0_0.CF001212.byte_2;
			if (array != null && array.Length == 16)
			{
				e39CAE0B_0?.Invoke("Generating sej rpmbkey : ");
				e5964FB0_0.E68B8FBC = array;
				byte[] array2 = b42DC.F838CF88(array, null, bool_0: true, byte_, "rpmb");
				if (array2 != null && method_9(983051u) && method_53(array2))
				{
					byte[] array3 = method_51();
					if (method_55() == 0L && array3 != null && array3.SequenceEqual(array2))
					{
						e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
					}
				}
			}
		}
		e39CAE0B_0?.Invoke("RPMB Reinit: ");
		bool flag = false;
		if (B2A384A8.ulong_2 > 0L)
		{
			flag = false;
		}
		else if (afbd65B0_0.uint_1 != 0)
		{
			flag = true;
		}
		uint uint_ = 983045u;
		if (flag)
		{
			uint_ = 983048u;
		}
		if (method_9(uint_))
		{
			byte[] array4 = method_51();
			long num = method_55();
			if (num == 0L)
			{
				e39CAE0B_0?.Invoke("RPMB Key : ");
				string item = GClass112.smethod_27(array4);
				e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
				return Tuple.Create(item1: true, item);
			}
		}
		return Tuple.Create(item1: false, "");
	}

	public GClass20 EAAA42A0()
	{
		GClass11 gClass = new GClass11();
		gClass.list_0 = GClass112.C78DEB29.A8054FBA.B3927213;
		gClass.uint_1 = GClass112.C78DEB29.A8054FBA.AB96C4A7;
		gClass.uint_0 = GClass112.C78DEB29.A8054FBA.uint_4;
		gClass.uint_2 = GClass112.C78DEB29.A8054FBA.EDB8851A;
		gClass.uint_3 = GClass112.C78DEB29.A8054FBA.uint_3;
		gClass.int_0 = e5964FB0_0.int_1;
		gClass.F2B1FB97 = method_2;
		gClass.F41AE714 = method_1;
		gClass.gdelegate1_0 = EE83B4B7;
		return new GClass20(gClass);
	}

	public Tuple<bool, string> method_26()
	{
		GClass20 gClass = EAAA42A0();
		if (GClass112.C78DEB29.A8054FBA.EC16A503 != 0)
		{
			byte[] array = method_6(GClass112.C78DEB29.A8054FBA.EC16A503, 16u);
			if (array != null || array.Length != 0)
			{
				e39CAE0B_0?.Invoke("Initializing RPMB : ");
				e5964FB0_0.E68B8FBC = array;
				method_39(gClass);
				object obj = gClass.method_0(array, null, EBAF300C: true, null, "rpmb");
				if (obj != null && method_9(983051u) && method_53((byte[])obj))
				{
					byte[] first = method_51();
					if (method_55() == 0L && first.SequenceEqual((byte[])obj))
					{
						e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
					}
				}
			}
		}
		bool flag = false;
		if (B2A384A8.ulong_2 > 0L)
		{
			flag = false;
		}
		else if (afbd65B0_0.uint_1 != 0)
		{
			flag = true;
		}
		uint uint_ = 983045u;
		if (flag)
		{
			uint_ = 983048u;
		}
		if (method_9(uint_))
		{
			byte[] array2 = method_51();
			long num = method_55();
			if (num == 0L)
			{
				e39CAE0B_0?.Invoke("RPMB Key : ");
				string text = GClass112.smethod_27(array2);
				e39CAE0B_0?.Invoke(text, EF1F389C.Success);
				return Tuple.Create(item1: true, text);
			}
		}
		return Tuple.Create(item1: false, "");
	}

	public bool method_27(string string_0)
	{
		ulong num = 0uL;
		bool bool_ = false;
		if (B2A384A8.ulong_2 > 0L)
		{
			num = B2A384A8.ulong_2 / 256L;
		}
		else if (afbd65B0_0.uint_1 != 0)
		{
			num = 65536uL;
			bool_ = true;
		}
		gdelegate25_0?.Invoke(0uL, 0uL);
		GClass112.E209C304(Class607.B630A78B.object_0[321](string_0));
		if (num > 0L)
		{
			using (FileStream object_ = Class607.B630A78B.object_0[187](string_0))
			{
				using IEnumerator<int> enumerator = GClass112.smethod_30(0, Class607.B630A78B.object_0[704](num)).GetEnumerator();
				while (Class607.B630A78B.object_0[212](enumerator))
				{
					int current = enumerator.Current;
					byte[] array = method_29(current, bool_);
					if (array != null)
					{
						gdelegate25_0?.Invoke(num, Class607.B630A78B.object_0[715](current));
						Class607.B630A78B.object_0[132](object_, array, 0, array.Length);
						continue;
					}
					Class607.B630A78B.object_0[587](object_);
					return false;
				}
			}
			return true;
		}
		return false;
	}

	public bool method_28(string BB0AE5A3, int int_0, ulong BB941C9E)
	{
		bool bool_ = false;
		if (B2A384A8.ulong_2 <= 0L && afbd65B0_0.uint_1 != 0)
		{
			bool_ = true;
		}
		gdelegate25_0?.Invoke(0uL, 0uL);
		GClass112.E209C304(Class607.B630A78B.object_0[321](BB0AE5A3));
		int num = (int)BB941C9E;
		if (BB941C9E > 0L)
		{
			using (FileStream object_ = Class607.B630A78B.object_0[187](BB0AE5A3))
			{
				for (int i = int_0; i <= num; i++)
				{
					byte[] array = method_29(i, bool_);
					if (array != null)
					{
						gdelegate25_0?.Invoke(BB941C9E, Class607.B630A78B.object_0[715](i));
						Class607.B630A78B.object_0[132](object_, array, 0, array.Length);
						continue;
					}
					Class607.B630A78B.object_0[587](object_);
					return false;
				}
			}
			return true;
		}
		return false;
	}

	public byte[] method_29(int int_0, bool bool_2 = false)
	{
		uint uint_ = 983046u;
		if (bool_2)
		{
			uint_ = 983049u;
		}
		if (method_9(uint_))
		{
			method_53(int_0);
			byte[] f81EF19F = method_51();
			if (Class607.B630A78B.object_0[504](GClass111.C3B9331C("<H", f81EF19F)[0]) == 0)
			{
				byte[] result = method_51();
				long num = method_55();
				if (num == 0L)
				{
					return result;
				}
			}
		}
		return null;
	}

	public bool method_30(ulong ulong_0, ulong ulong_1, string string_0 = "", bool bool_2 = true)
	{
		return (bool)new GClass128().DFB12B0F(new object[5] { this, ulong_0, ulong_1, string_0, bool_2 }, 232256);
	}

	public bool method_31(A4B06010 CAA67FBA = A4B06010.NORMAL)
	{
		e39CAE0B_0?.Invoke("shutdown device :");
		if (method_53(65543u))
		{
			long num = method_55();
			if (num == 0L)
			{
				int num2 = 0;
				if (CAA67FBA != A4B06010.NORMAL)
				{
					num2 = 1;
				}
				int num3 = 0;
				int num4 = 0;
				int num5 = 0;
				int num6 = 0;
				int num7 = 0;
				e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
				byte[] eEA = GClass111.smethod_2("<IIIIIIII", new object[8]
				{
					num2,
					num5,
					num3,
					Class607.B630A78B.object_0[84](CAA67FBA),
					num4,
					num6,
					num7,
					0
				});
				if (method_53(eEA))
				{
					num = method_55();
					if (num == 0L)
					{
						e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
						return true;
					}
				}
				return true;
			}
			e39CAE0B_0?.Invoke("Failed", EF1F389C.Success);
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
		}
		return false;
	}

	public GStruct57 method_32(string string_0, string string_1)
	{
		GStruct57 result = new GStruct57
		{
			BD147F02 = false
		};
		D73D8A10();
		if (C68DE811(string_0))
		{
			byte[] f81EF19F = method_51();
			object[] array = GClass111.C3B9331C("<Q", f81EF19F);
			ulong num = Class607.B630A78B.object_0[750](array[0]);
			ulong num2 = Class607.B630A78B.object_0[750](array[0]);
			long num3 = method_55();
			if (num3 == 0L)
			{
				Stream stream = Class607.B630A78B.object_0[939]();
				try
				{
					if (!Class607.B630A78B.object_0[1205](string_1))
					{
						stream = Class607.B630A78B.object_0[792](string_1, FileMode.OpenOrCreate, FileAccess.ReadWrite);
					}
					byte[] object_ = GClass111.smethod_2("<III", new object[3] { 4277071599u, 1, 4 });
					byte[] object_2 = GClass111.smethod_2("<I", new object[1] { 0 });
					while (num > 0L)
					{
						object[] array2 = GClass111.C3B9331C("<III", e5964FB0_0.f7020D24_0.method_6(12).byte_0);
						uint num4 = Class607.B630A78B.object_0[40](array2[0]);
						Class607.B630A78B.object_0[40](array2[1]);
						uint num5 = Class607.B630A78B.object_0[40](array2[2]);
						F7020D24.GStruct60 gStruct = default(F7020D24.GStruct60);
						if (num4 == 4277071599u)
						{
							gStruct = e5964FB0_0.f7020D24_0.method_6(Class607.B630A78B.object_0[1262](num5));
						}
						if (gStruct.byte_0 != null)
						{
							switch (num5)
							{
							default:
								Class607.B630A78B.object_0[132](stream, gStruct.byte_0, 0, gStruct.byte_0.Length);
								e5964FB0_0.f7020D24_0.method_1(object_);
								e5964FB0_0.f7020D24_0.method_1(object_2);
								num -= Class607.B630A78B.object_0[715](gStruct.byte_0.Length);
								gdelegate25_0?.Invoke(num2, num2 - num);
								break;
							case 4u:
								if (Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", gStruct.byte_0)[0]) != 0)
								{
									goto end_IL_0277;
								}
								break;
							case 0u:
							case 1u:
							case 2u:
							case 3u:
								break;
							}
							continue;
						}
						throw Class607.B630A78B.object_0[778]("Failed to Read data from device.");
						continue;
						end_IL_0277:
						break;
					}
				}
				finally
				{
					result.BD147F02 = num == 0L;
					result.byte_0 = (Class607.B630A78B.object_0[1205](string_1) ? Class607.B630A78B.object_0[1126]((MemoryStream)stream) : null);
					Class607.B630A78B.object_0[587](stream);
					Class607.B630A78B.object_0[469](stream);
				}
			}
		}
		return result;
	}

	public GStruct57 method_33(ulong ulong_0, ulong F3AEA790, string D887E216, string string_0 = "")
	{
		GStruct57 result = new GStruct57
		{
			BD147F02 = false
		};
		Tuple<int, int, ulong> tuple = method_13(string_0, F3AEA790);
		if (tuple == null)
		{
			return result;
		}
		int item = tuple.Item1;
		int item2 = tuple.Item2;
		ulong num = tuple.Item3;
		D73D8A10();
		if (method_35(ulong_0, num, item, item2))
		{
			ulong num2 = Class607.B630A78B.object_0[405](num);
			if (!Class607.B630A78B.object_0[1205](D887E216))
			{
				GClass112.E209C304(Class607.B630A78B.object_0[321](D887E216));
				using (FileStream object_ = Class607.B630A78B.object_0[792](D887E216, FileMode.OpenOrCreate, FileAccess.ReadWrite))
				{
					byte[] object_2 = GClass111.smethod_2("<III", new object[3] { 4277071599u, 1, 4 });
					byte[] object_3 = GClass111.smethod_2("<I", new object[1] { 0 });
					while (num2 > 0L)
					{
						object[] array = GClass111.C3B9331C("<III", e5964FB0_0.f7020D24_0.method_6(12).byte_0);
						uint num3 = Class607.B630A78B.object_0[40](array[0]);
						Class607.B630A78B.object_0[40](array[1]);
						uint num4 = Class607.B630A78B.object_0[40](array[2]);
						F7020D24.GStruct60 gStruct = default(F7020D24.GStruct60);
						if (num3 == 4277071599u)
						{
							gStruct = e5964FB0_0.f7020D24_0.method_6(Class607.B630A78B.object_0[1262](num4));
						}
						switch (num4)
						{
						default:
							if (gStruct.byte_0 != null)
							{
								Class607.B630A78B.object_0[132](object_, gStruct.byte_0, 0, gStruct.byte_0.Length);
								e5964FB0_0.f7020D24_0.method_1(object_2);
								e5964FB0_0.f7020D24_0.method_1(object_3);
								num2 -= Class607.B630A78B.object_0[715](gStruct.byte_0.Length);
								gdelegate25_0?.Invoke(num, num - num2);
								continue;
							}
							throw Class607.B630A78B.object_0[778]("Failed to Read data from device.");
						case 4u:
							break;
						case 0u:
						case 1u:
						case 2u:
						case 3u:
							continue;
						}
						if (Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", gStruct.byte_0)[0]) != 0)
						{
							break;
						}
					}
					object[] array2 = GClass111.C3B9331C("<III", e5964FB0_0.f7020D24_0.method_6(12).byte_0);
					uint num5 = Class607.B630A78B.object_0[40](array2[0]);
					Class607.B630A78B.object_0[84](array2[1]);
					int num6 = Class607.B630A78B.object_0[84](array2[2]);
					if (num5 == 4277071599u)
					{
						F7020D24.GStruct60 gStruct2 = e5964FB0_0.f7020D24_0.method_6(num6);
						if (num6 == 4 && Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", gStruct2.byte_0)[0]) == 0)
						{
							result.BD147F02 = true;
							return result;
						}
					}
				}
				return result;
			}
			List<byte> list = new List<byte>();
			ulong num7 = 0uL;
			while (num > 0L)
			{
				byte[] array3 = method_51();
				if (array3 != null)
				{
					list.AddRange(array3);
					if (!EC33849D().bool_0)
					{
						break;
					}
					num7 += Class607.B630A78B.object_0[715](array3.Length);
					gdelegate25_0?.Invoke(num2, num7);
					num -= Class607.B630A78B.object_0[715](array3.Length);
					continue;
				}
				throw Class607.B630A78B.object_0[778]("Failed To read");
			}
			result.BD147F02 = true;
			result.byte_0 = list.ToArray();
		}
		return result;
	}

	public GStruct58 EC33849D(bool bool_2 = true)
	{
		GStruct58 result = new GStruct58
		{
			long_0 = -1L,
			bool_0 = false
		};
		try
		{
			byte[] object_ = GClass111.smethod_2("<III", new object[3] { 4277071599u, 1, 4 });
			byte[] object_2 = GClass111.smethod_2("<I", new object[1] { 0 });
			e5964FB0_0.f7020D24_0.method_1(object_);
			e5964FB0_0.f7020D24_0.method_1(object_2);
			if (bool_2)
			{
				result.long_0 = method_55();
				result.bool_0 = result.long_0 == 0L;
				return result;
			}
			result.bool_0 = true;
		}
		catch
		{
		}
		return result;
	}

	public int method_34()
	{
		GStruct59 gStruct = method_50(262153u);
		if (gStruct.A4105F86)
		{
			long num = method_55();
			if (num == 0L)
			{
				object[] array = GClass111.C3B9331C("<I", gStruct.byte_0);
				return Class607.B630A78B.object_0[84](array[0]);
			}
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
		}
		return 0;
	}

	public GStruct53 D73D8A10()
	{
		GStruct53 result = default(GStruct53);
		GStruct59 gStruct = method_50(262151u);
		if (gStruct.A4105F86)
		{
			long num = method_55();
			if (num == 0L)
			{
				object[] array = GClass111.C3B9331C("<II", gStruct.byte_0);
				result.int_0 = Class607.B630A78B.object_0[84](array[0]);
				result.F7196F10 = Class607.B630A78B.object_0[84](array[1]);
			}
			else
			{
				e39CAE0B_0?.Invoke("Error : ");
				e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
			}
		}
		return result;
	}

	public bool C68DE811(string string_0)
	{
		if (method_53(65538u))
		{
			long num = method_55();
			if (num == 0L && method_53(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), string_0)))
			{
				num = method_55();
				return num == 0L;
			}
		}
		return false;
	}

	public bool method_35(ulong ulong_0, ulong E4113E8B, int int_0 = 1, int int_1 = 8)
	{
		if (method_53(65541u))
		{
			long num = method_55();
			if (num == 0L)
			{
				GStruct54 gStruct = new GStruct54
				{
					int_0 = 0,
					int_1 = 0,
					E51B2AA5 = 0,
					int_2 = 0,
					B31CDAA9 = 0,
					int_3 = 0,
					int_4 = 0
				};
				List<byte> list = GClass111.smethod_2("<IIQQ", new object[4] { int_0, int_1, ulong_0, E4113E8B }).ToList();
				list.AddRange(GClass111.smethod_2("<IIIIIIII", new object[8] { gStruct.E51B2AA5, gStruct.int_0, gStruct.int_1, gStruct.int_2, gStruct.int_3, gStruct.int_4, gStruct.B31CDAA9, 0 }));
				method_54(list.ToArray());
				num = method_55();
				if (num == 0L)
				{
					return true;
				}
			}
			else
			{
				e39CAE0B_0?.Invoke("Error : ");
				e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
			}
		}
		return false;
	}

	public void method_36()
	{
		for (int i = 0; i < 5; i++)
		{
			e5964FB0_0.f7020D24_0.method_8(161, 33, 0, 0, new byte[7]);
		}
		byte[] object_ = GClass111.smethod_2("<Q", new object[1] { 2251799813800448L });
		e5964FB0_0.f7020D24_0.method_8(33, 32, 0, 0, object_);
		for (int j = 0; j < 2; j++)
		{
			e5964FB0_0.f7020D24_0.method_8(33, 34, 3, 0, 0);
		}
		e5964FB0_0.f7020D24_0.method_8(161, 33, 0, 0, new byte[7]);
		e5964FB0_0.f7020D24_0.method_8(33, 32, 0, 0, 0);
		e5964FB0_0.f7020D24_0.method_8(33, 35, 0, 0, 0);
		for (int k = 0; k < 2; k++)
		{
			e5964FB0_0.f7020D24_0.method_8(33, 34, 3, 0, 0);
		}
	}

	public bool method_37(GClass70 gclass70_0, bool E185D997 = false)
	{
		return (bool)new GClass128().F3BD1601(new object[3] { this, gclass70_0, E185D997 }, 118900);
	}

	public void method_38()
	{
		new GClass128().C5017C25(new object[1] { this }, 22065058);
	}

	public void method_39(GClass20 gclass20_0)
	{
		byte[] array = null;
		if (AA329715.D10D9B3F == null)
		{
		}
		if (array == null)
		{
			array = new byte[32];
		}
		gclass20_0.c51E6F3B_0.method_0(array);
	}

	public void BCB82094(B42DC004 E7B8E630)
	{
		byte[] array = null;
		if (AA329715.D10D9B3F == null)
		{
		}
		if (array == null)
		{
			array = new byte[32];
		}
		E7B8E630.GClass27_0.A027F09F(array);
	}

	public bool method_40()
	{
		method_53(65543u);
		method_51();
		B70DB6A1(GClass112.FF06B0AD("010000000000000000000000000000000000000000000000"));
		method_51();
		return true;
	}

	public bool method_41(bool bool_2)
	{
		string text = "unlock";
		if (!bool_2)
		{
			text = "lock";
		}
		e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[1217]("Perform functions related to {0} bootloader", text));
		e39CAE0B_0?.Invoke("Reading Partition Information tables from device :");
		Tuple<byte[], GClass37> tuple = gclass59_0.D60E1983(new D905A51F(0u, 0u, 0u));
		if (tuple != null)
		{
			e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
			_ = tuple.Item1;
			GClass37 item = tuple.Item2;
			byte[] array = null;
			GClass37.B6BA8237 b6BA = default(GClass37.B6BA8237);
			e39CAE0B_0?.Invoke("Count of partition finded :");
			GClass112.E39CAE0B e39CAE0B = e39CAE0B_0;
			if (e39CAE0B != null)
			{
				int int_ = item.list_0.Count;
				e39CAE0B(Class607.B630A78B.object_0[1070](ref int_), EF1F389C.Success);
			}
			foreach (GClass37.B6BA8237 item2 in item.list_0)
			{
				string string_ = item2.string_2;
				string string_2 = string_;
				if (Class607.B630A78B.object_0[787](string_2, "seccfg"))
				{
					e39CAE0B_0?.Invoke("Reading seccfg partition :");
					b6BA = item2;
					array = method_33(b6BA.D78F1C10 * AA329715.uint_0, b6BA.ulong_1 * AA329715.uint_0, "", "user").byte_0;
					if (array != null)
					{
						e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
					}
				}
			}
			if (array == null)
			{
				e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
				return false;
			}
			e39CAE0B_0?.Invoke("initializing seccfg :");
			if (!array.Take(4).SequenceEqual(GClass111.smethod_2("<I", new object[1] { 1296911693 })))
			{
				throw Class607.B630A78B.object_0[778]("Unknown seccfg partition header. Aborting unlock.");
			}
			B42DC004 b42DC = new B42DC004(e5964FB0_0);
			if (array.Take(12).SequenceEqual(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "AND_SECCFG_v")))
			{
				e39CAE0B_0?.Invoke("v3", EF1F389C.Success);
				e39CAE0B_0?.Invoke("excuting aes key arguments :");
				GClass63 gClass = new GClass63(b42DC);
				if (!gClass.BABD1F1C(array))
				{
					throw Class607.B630A78B.object_0[778]("Device has either already unlocked or the algorithm is unknown. Aborting.");
				}
				byte[] array2 = gClass.method_0(text);
				if (AB28960B(b6BA.D78F1C10 * e5964FB0_0.CF001212.uint_0, Class607.B630A78B.object_0[715](array2.Length), null, 0, "user", array2))
				{
					e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
					if (bool_2)
					{
						DialogResult dialogResult = GClass110.C2AB1F9F("for completing unlock bootloader operation, some device required factory reset, do u want i do clear userdata?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
						if (dialogResult == DialogResult.Yes)
						{
							foreach (GClass37.B6BA8237 item3 in item.list_0)
							{
								string string_3 = item3.string_2;
								string string_4 = string_3;
								if (Class607.B630A78B.object_0[787](string_4, "metadata") || Class607.B630A78B.object_0[787](string_4, "md_udc") || Class607.B630A78B.object_0[787](string_4, "userdata"))
								{
									if (!method_30(item3.D78F1C10 * AA329715.uint_0, item3.ulong_1 * AA329715.uint_0))
									{
										e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
										return false;
									}
									e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
								}
							}
						}
					}
					return true;
				}
				throw Class607.B630A78B.object_0[778]("Error on writing seccfg config to flash.");
			}
			IEnumerable<byte> first = array.Take(4);
			byte[] array3 = new byte[4];
			DEBEDD9C.smethod_0(array3, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			if (first.SequenceEqual(array3))
			{
				e39CAE0B_0?.Invoke("v4", EF1F389C.Success);
				GClass64 gClass2 = new GClass64(b42DC);
				e39CAE0B_0?.Invoke("excuting aes key arguments :");
				if (!gClass2.method_0(array))
				{
					throw Class607.B630A78B.object_0[778]("Device has either already unlocked or the algorithm is unknown. Aborting.");
				}
				byte[] array4 = gClass2.method_1(text);
				if (AB28960B(b6BA.D78F1C10 * e5964FB0_0.CF001212.uint_0, Class607.B630A78B.object_0[715](array4.Length), null, 0, "user", array4))
				{
					e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
					if (bool_2)
					{
						DialogResult dialogResult2 = GClass110.C2AB1F9F("for completing unlock bootloader operation, some device required factory reset, do u want i do clear userdata?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
						if (dialogResult2 == DialogResult.Yes)
						{
							foreach (GClass37.B6BA8237 item4 in item.list_0)
							{
								string string_5 = item4.string_2;
								string string_6 = string_5;
								if (Class607.B630A78B.object_0[787](string_6, "metadata") || Class607.B630A78B.object_0[787](string_6, "md_udc") || Class607.B630A78B.object_0[787](string_6, "userdata"))
								{
									if (!method_30(item4.D78F1C10 * AA329715.uint_0, item4.ulong_1 * AA329715.uint_0))
									{
										e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
										return false;
									}
									e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
								}
							}
						}
					}
					return true;
				}
				throw Class607.B630A78B.object_0[778]("Error on writing seccfg config to flash.");
			}
			throw Class607.B630A78B.object_0[778]("Unknown lockstate or no lockstate");
		}
		e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
		return false;
	}

	public Tuple<byte[], List<GClass37.B6BA8237>> FDA59913()
	{
		return new Tuple<byte[], List<GClass37.B6BA8237>>(null, null);
	}

	public bool DC099AA9()
	{
		if (method_50(65547u).byte_0 != null)
		{
			long num = method_55();
			if (num == 0L)
			{
				if (method_53(GClass111.smethod_2("<I", new object[1] { 244129793 })))
				{
					num = method_55();
					if (num == 0L)
					{
						return true;
					}
				}
			}
			else
			{
				e39CAE0B_0?.Invoke("Error : ");
				e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
			}
		}
		return false;
	}

	public byte[] method_42()
	{
		GStruct59 gStruct = method_50(262155u);
		if (gStruct.byte_0 != null)
		{
			long num = method_55();
			if (num == 0L)
			{
				return gStruct.byte_0;
			}
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
		}
		return null;
	}

	public byte[] F9BD758E()
	{
		GStruct59 gStruct = method_50(262152u);
		if (gStruct.byte_0 != null)
		{
			long num = method_55();
			if (num == 0L)
			{
				return gStruct.byte_0;
			}
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
		}
		return null;
	}

	public GStruct56 D4399F3C()
	{
		GStruct56 result = default(GStruct56);
		GStruct59 gStruct = method_50(262157u);
		if (gStruct.byte_0 != null)
		{
			long num = method_55();
			if (num == 0L)
			{
				object[] array = GClass111.C3B9331C(">HHHHH", GClass112.smethod_14(gStruct.byte_0, 0, 10));
				result.FEA7E1B1 = Class607.B630A78B.object_0[504](array[0]);
				result.A2BCA7B6 = Class607.B630A78B.object_0[504](array[1]);
				result.ushort_0 = Class607.B630A78B.object_0[504](array[2]);
				result.CF1614A1 = Class607.B630A78B.object_0[504](array[3]);
				result.ushort_1 = Class607.B630A78B.object_0[504](array[4]);
			}
			else
			{
				e39CAE0B_0?.Invoke("Error : ");
				e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
			}
		}
		return result;
	}

	public GStruct55 method_43()
	{
		GStruct55 result = default(GStruct55);
		GStruct59 gStruct = method_50(262147u);
		if (gStruct.byte_0 != null)
		{
			long num = method_55();
			if (num == 0L)
			{
				result.uint_0 = 1u;
				object[] array = GClass111.C3B9331C("<IIQ", GClass112.smethod_14(gStruct.byte_0, 0, 16));
				result.uint_0 = Class607.B630A78B.object_0[40](array[0]);
				result.uint_1 = Class607.B630A78B.object_0[40](array[1]);
				result.FE06E83F = Class607.B630A78B.object_0[750](array[2]);
			}
			else
			{
				e39CAE0B_0?.Invoke("Error : ");
				e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
			}
		}
		return result;
	}

	public GStruct52 method_44()
	{
		GStruct52 result = default(GStruct52);
		GStruct59 gStruct = method_50(262146u);
		if (gStruct.byte_0 != null)
		{
			long num = method_55();
			if (num == 0L)
			{
				result.uint_2 = 512u;
				result.uint_0 = 1u;
				object[] array = GClass111.C3B9331C("<IIII", GClass112.smethod_14(gStruct.byte_0, 0, 16));
				result.uint_0 = Class607.B630A78B.object_0[40](array[0]);
				result.uint_1 = Class607.B630A78B.object_0[40](array[1]);
				result.uint_2 = Class607.B630A78B.object_0[40](array[2]);
				result.uint_3 = Class607.B630A78B.object_0[40](array[3]);
				array = GClass111.C3B9331C("<QQ", GClass112.smethod_14(gStruct.byte_0, 16, 16));
				result.D223FF1A = Class607.B630A78B.object_0[750](array[0]);
				result.ulong_0 = Class607.B630A78B.object_0[750](array[1]);
				result.uint_4 = GClass112.smethod_14(gStruct.byte_0, 32, 1)[0];
				result.object_0 = GClass111.C3B9331C("<BBBBBBBBBBBB", GClass112.smethod_14(gStruct.byte_0, 33, 12));
			}
			else
			{
				e39CAE0B_0?.Invoke("Error : ");
				e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
			}
		}
		return result;
	}

	public GStruct50 A03566B7()
	{
		GStruct50 result = default(GStruct50);
		GStruct59 gStruct = method_50(262156u);
		if (gStruct.byte_0 != null)
		{
			long num = method_55();
			if (num == 0L)
			{
				if (gStruct.byte_0.Length == 24)
				{
					object[] array = GClass111.C3B9331C("<IIIIII", gStruct.byte_0);
					result.ulong_0 = Class607.B630A78B.object_0[40](array[0]);
					result.ulong_2 = Class607.B630A78B.object_0[40](array[1]);
					result.ulong_4 = Class607.B630A78B.object_0[40](array[2]);
					result.ulong_1 = Class607.B630A78B.object_0[40](array[3]);
					result.ulong_3 = Class607.B630A78B.object_0[40](array[4]);
					result.AB3DF7A5 = Class607.B630A78B.object_0[40](array[5]);
				}
				else if (gStruct.byte_0.Length == 48)
				{
					object[] array2 = GClass111.C3B9331C("<QQQQQQ", gStruct.byte_0);
					result.ulong_0 = Class607.B630A78B.object_0[750](array2[0]);
					result.ulong_2 = Class607.B630A78B.object_0[750](array2[1]);
					result.ulong_4 = Class607.B630A78B.object_0[750](array2[2]);
					result.ulong_1 = Class607.B630A78B.object_0[750](array2[3]);
					result.ulong_3 = Class607.B630A78B.object_0[750](array2[4]);
					result.AB3DF7A5 = Class607.B630A78B.object_0[750](array2[5]);
				}
			}
			else
			{
				e39CAE0B_0?.Invoke("Error : ");
				e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
			}
		}
		return result;
	}

	public byte[] E3B38303()
	{
		GStruct59 gStruct = method_50(262163u);
		if (gStruct.byte_0 != null)
		{
			long num = method_55();
			if (num == 0L)
			{
				return gStruct.byte_0;
			}
		}
		return null;
	}

	public bool F4837D1B(byte[] B70D1824)
	{
		return (bool)new GClass128().E8AA1C3C(new object[2] { this, B70D1824 }, 126340);
	}

	public bool FDB05A0E(byte[] C62EBAAD)
	{
		return method_50(131083u, C62EBAAD).A4105F86;
	}

	public byte[] method_45(byte[] byte_1, RSAParameters E928E8A7)
	{
		using RSA rSA = Class607.B630A78B.object_0[122]();
		Class607.B630A78B.object_0[43](rSA, E928E8A7);
		return Class607.B630A78B.object_0[341](rSA, byte_1, Class607.B630A78B.object_0[105]());
	}

	public int method_46()
	{
		GStruct59 gStruct = method_50(262166u);
		if (gStruct.byte_0 != null)
		{
			long num = method_55();
			if (num == 0L)
			{
				return Class607.B630A78B.object_0[84](GClass111.C3B9331C("<I", gStruct.byte_0)[0]);
			}
		}
		return -1;
	}

	public bool E2946995(ulong ulong_0, byte[] byte_1)
	{
		if (method_53(65544u) && method_55() == 0L)
		{
			byte[] array = GClass111.smethod_2("<QQ", new object[2]
			{
				ulong_0,
				Class607.B630A78B.object_0[715](byte_1.Length)
			});
			byte[] object_ = GClass111.smethod_2("<III", new object[3] { 4277071599u, 1, array.Length });
			if (e5964FB0_0.f7020D24_0.method_1(object_) && e5964FB0_0.f7020D24_0.method_1(array) && B70DB6A1(byte_1))
			{
				long num = method_55();
				if (num == 1129208147L || num == 0L)
				{
					return true;
				}
				e39CAE0B_0?.Invoke("Error : ");
				e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
			}
		}
		return false;
	}

	public bool B70DB6A1(byte[] byte_1)
	{
		byte[] object_ = GClass111.smethod_2("<III", new object[3] { 4277071599u, 1, byte_1.Length });
		if (e5964FB0_0.f7020D24_0.method_1(object_))
		{
			int num = byte_1.Length;
			int num2 = 0;
			e5964FB0_0.f7020D24_0.gclass66_0.method_7();
			e5964FB0_0.f7020D24_0.gclass66_0.method_8();
			while (num > 0)
			{
				int num3 = Class607.B630A78B.object_0[78](num, 64);
				byte[] object_2 = GClass112.smethod_14(byte_1, num2, num3);
				if (e5964FB0_0.f7020D24_0.method_1(object_2))
				{
					num2 += num3;
					num -= num3;
				}
				gdelegate25_0?.Invoke(Class607.B630A78B.object_0[715](byte_1.Length), Class607.B630A78B.object_0[715](num2));
			}
			long num4 = method_55();
			if (num4 == 0L)
			{
				return true;
			}
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num4)), EF1F389C.Error);
			return false;
		}
		return false;
	}

	public bool method_47(byte[] byte_1)
	{
		if (method_53(65546u))
		{
			long num = method_55();
			if (num == 0L)
			{
				if (method_53(GClass111.smethod_2("<I", new object[1] { byte_1.Length })))
				{
					return method_54(byte_1);
				}
			}
			else
			{
				e39CAE0B_0?.Invoke("Error : ");
				e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
			}
		}
		return false;
	}

	public AFBD65B0 C2898737()
	{
		AFBD65B0 result = default(AFBD65B0);
		GStruct59 gStruct = method_50(262148u);
		if (gStruct.byte_0 == null)
		{
			return result;
		}
		long num = method_55();
		if (num == 0L)
		{
			object[] array = GClass111.C3B9331C("<IIQQQ", GClass112.smethod_14(gStruct.byte_0, 0, 32));
			result.uint_0 = Class607.B630A78B.object_0[40](array[0]);
			result.uint_1 = Class607.B630A78B.object_0[40](array[1]);
			result.ulong_1 = Class607.B630A78B.object_0[750](array[2]);
			result.ulong_0 = Class607.B630A78B.object_0[750](array[3]);
			result.B81255AA = Class607.B630A78B.object_0[750](array[4]);
			result.A73A4294 = GClass112.smethod_14(gStruct.byte_0, 32, 16);
			result.uint_2 = Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(gStruct.byte_0, 48, 4))[0]);
			if (result.uint_0 != 0)
			{
				e5964FB0_0.CF001212.uint_0 = result.uint_1;
				if (AA329715 != null)
				{
					AA329715.uint_0 = result.uint_1;
				}
			}
		}
		else
		{
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
		}
		return result;
	}

	public GStruct49 DB3C9E0E()
	{
		GStruct49 result = default(GStruct49);
		GStruct59 gStruct = method_50(262145u);
		if (gStruct.byte_0 == null)
		{
			return result;
		}
		long num = method_55();
		if (num == 0L)
		{
			result.BC3C9900 = 1u;
			result.ulong_0 = 512uL;
			object[] array = GClass111.C3B9331C("<II", GClass112.smethod_14(gStruct.byte_0, 0, 8));
			result.BC3C9900 = Class607.B630A78B.object_0[40](array[0]);
			result.ulong_0 = Class607.B630A78B.object_0[40](array[1]);
			object[] array2 = GClass111.C3B9331C("<QQQQQQQQ", GClass112.smethod_14(gStruct.byte_0, 8, 64));
			result.ulong_1 = Class607.B630A78B.object_0[750](array2[0]);
			result.B61F2413 = Class607.B630A78B.object_0[750](array2[1]);
			result.ulong_2 = Class607.B630A78B.object_0[750](array2[2]);
			result.ulong_3 = Class607.B630A78B.object_0[750](array2[3]);
			result.ulong_4 = Class607.B630A78B.object_0[750](array2[4]);
			result.ulong_5 = Class607.B630A78B.object_0[750](array2[5]);
			result.DFA560AD = Class607.B630A78B.object_0[750](array2[6]);
			result.ulong_6 = Class607.B630A78B.object_0[750](array2[7]);
			result.byte_0 = GClass112.smethod_14(gStruct.byte_0, 72, 16);
			result.D99D8B2C = Class607.B630A78B.object_0[750](GClass111.C3B9331C("<Q", GClass112.smethod_14(gStruct.byte_0, 88, 8))[0]);
			result.byte_1 = GClass112.smethod_14(gStruct.byte_0, 96, gStruct.byte_0.Length - 96);
		}
		else
		{
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
		}
		return result;
	}

	public byte[] B3274D3B()
	{
		GStruct59 gStruct = method_50(262154u);
		if (gStruct.byte_0 != null)
		{
			long num = method_55();
			if (num == 0L)
			{
				return gStruct.byte_0;
			}
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
		}
		return null;
	}

	public bool method_48(int int_0 = 0)
	{
		byte[] byte_ = GClass111.smethod_2("<I", new object[1] { int_0 });
		return method_50(131075u, byte_).A4105F86;
	}

	public bool CE38BE0D(int int_0 = 104)
	{
		byte[] byte_ = GClass111.smethod_2("<I", new object[1] { int_0 });
		return method_50(131076u, byte_).A4105F86;
	}

	public byte[] method_49()
	{
		GStruct59 gStruct = method_50(262161u);
		if (gStruct.A4105F86)
		{
			long num = method_55();
			if (num == 0L)
			{
				return gStruct.byte_0;
			}
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num)), EF1F389C.Error);
		}
		return null;
	}

	public bool A210A33B(bool bool_2 = false)
	{
		if (method_9(983045u))
		{
			if (bool_2)
			{
				method_53(Class607.B630A78B.object_0[241](1));
			}
			else
			{
				method_53(Class607.B630A78B.object_0[241](0));
			}
			long num = method_55();
			if (num == 0L)
			{
				return true;
			}
		}
		return false;
	}

	public GStruct59 method_50(uint uint_0, byte[] byte_1 = null, List<long> list_1 = null, bool bool_2 = false)
	{
		GStruct59 result = new GStruct59
		{
			A4105F86 = false
		};
		if (list_1 == null)
		{
			list_1 = new List<long> { 0L };
		}
		if (method_53(65545u))
		{
			list_1[0] = method_55();
			if (list_1[0] == 0L && method_53(uint_0))
			{
				list_1[0] = method_55();
				if (list_1[0] == 0L)
				{
					if (byte_1 == null)
					{
						result.byte_0 = method_51();
						result.A4105F86 = true;
						return result;
					}
					result.A4105F86 = method_54(byte_1);
					return result;
				}
			}
		}
		if (list_1[0] != 3221291012L)
		{
			e39CAE0B_0?.Invoke("Error : ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](list_1[0])), EF1F389C.Error);
		}
		return result;
	}

	public byte[] method_51()
	{
		try
		{
			object[] array = GClass111.C3B9331C("<III", e5964FB0_0.f7020D24_0.method_6(12, 3000).byte_0);
			uint num = Class607.B630A78B.object_0[40](array[0]);
			Class607.B630A78B.object_0[40](array[1]);
			int int_ = Class607.B630A78B.object_0[84](array[2]);
			if (num != 4277071599u)
			{
				return null;
			}
			return e5964FB0_0.f7020D24_0.method_6(int_).byte_0;
		}
		catch
		{
			return null;
		}
	}

	public bool method_52()
	{
		return (bool)new GClass128().F3BD1601(new object[1] { this }, 298635);
	}

	public bool CEA76029()
	{
		if (method_53(65792u))
		{
			byte[] object_ = GClass111.smethod_2("<IIIII", new object[5] { 2, 1, 0, 0, 0 });
			return method_54(object_);
		}
		return false;
	}

	public bool D4ACD22F()
	{
		if (method_53(65792u))
		{
			byte[] object_ = GClass111.smethod_2("<IIIII", new object[5] { 2, 1, 1, 0, 1 });
			return method_54(object_);
		}
		return false;
	}

	public bool E6A1BE1D()
	{
		return method_53(1129208147u);
	}

	public bool method_53(object EEA76926, int int_0 = 1, bool DA3A891D = false)
	{
		byte[] array;
		int num;
		if (GClass112.smethod_19(EEA76926))
		{
			if (DA3A891D)
			{
				array = GClass111.smethod_2("<Q", new object[1] { EEA76926 });
				num = 4;
			}
			else
			{
				array = GClass111.smethod_2("<I", new object[1] { EEA76926 });
				num = 4;
			}
		}
		else
		{
			array = (byte[])EEA76926;
			num = array.Length;
		}
		byte[] object_ = GClass111.smethod_2("<III", new object[3] { 4277071599u, int_0, num });
		if (e5964FB0_0.f7020D24_0.method_1(object_))
		{
			return e5964FB0_0.f7020D24_0.method_1(array);
		}
		return false;
	}

	public bool method_54(object object_0)
	{
		List<byte[]> list = new List<byte[]>();
		if (Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[910](object_0.GetType()), "Byte[]"))
		{
			list.Add((byte[])object_0);
		}
		else
		{
			list = (List<byte[]>)object_0;
		}
		foreach (byte[] item in list)
		{
			byte[] object_1 = GClass111.smethod_2("<III", new object[3] { 4277071599u, 1, item.Length });
			if (!e5964FB0_0.f7020D24_0.method_1(object_1))
			{
				continue;
			}
			int num = item.Length;
			int num2 = 0;
			while (num > 0)
			{
				int num3 = Class607.B630A78B.object_0[78](num, 512);
				byte[] object_2 = GClass112.smethod_14(item, num2, num3);
				if (!e5964FB0_0.f7020D24_0.method_1(object_2))
				{
					break;
				}
				num2 += num3;
				num -= num3;
			}
		}
		long num4 = method_55();
		switch (num4)
		{
		case 0L:
			return true;
		default:
			e39CAE0B_0?.Invoke("Error: ");
			e39CAE0B_0?.Invoke(GClass4.D40D06B0(Class607.B630A78B.object_0[21](num4)), EF1F389C.Error);
			break;
		case 3221487696L:
			break;
		}
		return false;
	}

	public long method_55()
	{
		uint num = 0u;
		try
		{
			object[] array = GClass111.C3B9331C("<III", e5964FB0_0.f7020D24_0.method_6(12).byte_0);
			uint num2 = Class607.B630A78B.object_0[40](array[0]);
			Class607.B630A78B.object_0[40](array[1]);
			uint num3 = Class607.B630A78B.object_0[40](array[2]);
			if (num2 != 4277071599u)
			{
				return -1L;
			}
			F7020D24.GStruct60 gStruct = e5964FB0_0.f7020D24_0.method_6(Class607.B630A78B.object_0[1262](num3));
			if (gStruct.byte_0.Length < num3)
			{
				return -1L;
			}
			if (num3 == 2)
			{
				num = Class607.B630A78B.object_0[40](GClass111.C3B9331C("<H", gStruct.byte_0)[0]);
				if (num == 0)
				{
					return 0L;
				}
			}
			else
			{
				if (num3 != 4)
				{
					string e7A = Class607.B630A78B.object_0[720]("<", Class607.B630A78B.object_0[275]('I', Class607.B630A78B.object_0[1262](num3 / 4)));
					num = Class607.B630A78B.object_0[40](GClass111.C3B9331C(e7A, gStruct.byte_0)[0]);
					return num;
				}
				num = Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", gStruct.byte_0)[0]);
				if (num == 4277071599u)
				{
					return 0L;
				}
			}
			return num;
		}
		catch
		{
			return num;
		}
	}

	public void CC116908()
	{
		new GClass128().method_68(new object[1] { this }, 35040);
	}

	public bool method_56()
	{
		return (bool)new GClass128().C5017C25(new object[1] { this }, 11022328);
	}
}
