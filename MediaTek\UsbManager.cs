using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using Microsoft.Win32.SafeHandles;
using System.ComponentModel;

namespace MotoKingPro.MediaTek
{
    /// <summary>
    /// USB device manager for MediaTek devices using native Windows APIs
    /// </summary>
    public class UsbManager : IDisposable
    {
        #region MediaTek USB VID/PID Constants
        public static readonly Dictionary<int, string> MediaTekVendorIds = new()
        {
            { 0x0E8D, "MediaTek Inc." },
            { 0x1004, "LG Electronics (MediaTek)" },
            { 0x22B8, "Motorola (MediaTek)" },
            { 0x19D2, "ZTE (MediaTek)" },
            { 0x2717, "<PERSON><PERSON> (MediaTek)" }
        };

        public static readonly Dictionary<int, string> MediaTekProductIds = new()
        {
            // BROM Mode PIDs
            { 0x0003, "MT6516 BROM" },
            { 0x1000, "MT6516 Preloader" },
            { 0x2000, "MT6516 Download Agent" },
            { 0x3000, "MT6516 Debug Agent" },
            
            // MT65xx Series
            { 0x6516, "MT6516" },
            { 0x6573, "MT6573" },
            { 0x6575, "MT6575" },
            { 0x6577, "MT6577" },
            { 0x6582, "MT6582" },
            { 0x6589, "MT6589" },
            { 0x6592, "MT6592" },
            
            // MT67xx Series
            { 0x6735, "MT6735" },
            { 0x6737, "MT6737" },
            { 0x6739, "MT6739" },
            { 0x6750, "MT6750" },
            { 0x6753, "MT6753" },
            { 0x6755, "MT6755" },
            { 0x6757, "MT6757" },
            { 0x6758, "MT6758" },
            { 0x6759, "MT6759" },
            { 0x6761, "MT6761" },
            { 0x6762, "MT6762" },
            { 0x6763, "MT6763" },
            { 0x6765, "MT6765" },
            { 0x6768, "MT6768" },
            { 0x6771, "MT6771" },
            { 0x6779, "MT6779" },
            { 0x6785, "MT6785" },
            { 0x6795, "MT6795" },
            { 0x6797, "MT6797" },
            
            // MT68xx Series
            { 0x6833, "MT6833" },
            { 0x6853, "MT6853" },
            { 0x6873, "MT6873" },
            { 0x6877, "MT6877" },
            { 0x6885, "MT6885" },
            { 0x6889, "MT6889" },
            { 0x6891, "MT6891" },
            { 0x6893, "MT6893" }
        };

        public static readonly Dictionary<int, string> BootModes = new()
        {
            { 0x0003, "BROM Mode" },
            { 0x1000, "Preloader Mode" },
            { 0x2000, "Download Agent Mode" },
            { 0x3000, "Debug Agent Mode" }
        };
        #endregion

        #region LibUSB and Windows API Declarations

        // LibUSB0 declarations (using the available libusb0.dll)
        [DllImport("Libusb/x64/libusb0.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern IntPtr usb_init();

        [DllImport("Libusb/x64/libusb0.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int usb_find_busses();

        [DllImport("Libusb/x64/libusb0.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int usb_find_devices();

        [DllImport("Libusb/x64/libusb0.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern IntPtr usb_get_busses();

        [DllImport("Libusb/x64/libusb0.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern IntPtr usb_open(IntPtr device);

        [DllImport("Libusb/x64/libusb0.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int usb_close(IntPtr handle);

        [DllImport("Libusb/x64/libusb0.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int usb_bulk_write(IntPtr handle, int endpoint, byte[] buffer, int size, int timeout);

        [DllImport("Libusb/x64/libusb0.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int usb_bulk_read(IntPtr handle, int endpoint, byte[] buffer, int size, int timeout);

        [DllImport("Libusb/x64/libusb0.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int usb_claim_interface(IntPtr handle, int interface_number);

        [DllImport("Libusb/x64/libusb0.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int usb_release_interface(IntPtr handle, int interface_number);

        [DllImport("Libusb/x64/libusb0.dll", CallingConvention = CallingConvention.StdCall)]
        private static extern int usb_set_configuration(IntPtr handle, int configuration);

        // Windows SetupAPI for device enumeration
        [DllImport("setupapi.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern IntPtr SetupDiGetClassDevs(
            ref Guid classGuid,
            string enumerator,
            IntPtr hwndParent,
            uint flags);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiEnumDeviceInfo(
            IntPtr deviceInfoSet,
            uint memberIndex,
            ref SP_DEVINFO_DATA deviceInfoData);

        [DllImport("setupapi.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern bool SetupDiGetDeviceRegistryProperty(
            IntPtr deviceInfoSet,
            ref SP_DEVINFO_DATA deviceInfoData,
            uint property,
            out uint propertyRegDataType,
            byte[] propertyBuffer,
            uint propertyBufferSize,
            out uint requiredSize);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiDestroyDeviceInfoList(IntPtr deviceInfoSet);

        [DllImport("kernel32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern SafeFileHandle CreateFile(
            string fileName,
            uint desiredAccess,
            uint shareMode,
            IntPtr securityAttributes,
            uint creationDisposition,
            uint flagsAndAttributes,
            IntPtr templateFile);

        [DllImport("winusb.dll", SetLastError = true)]
        private static extern bool WinUsb_Initialize(
            SafeFileHandle deviceHandle,
            out IntPtr interfaceHandle);

        [DllImport("winusb.dll", SetLastError = true)]
        private static extern bool WinUsb_Free(IntPtr interfaceHandle);

        [DllImport("winusb.dll", SetLastError = true)]
        private static extern bool WinUsb_GetDescriptor(
            IntPtr interfaceHandle,
            byte descriptorType,
            byte index,
            ushort languageID,
            byte[] buffer,
            uint bufferLength,
            out uint lengthTransferred);

        [DllImport("winusb.dll", SetLastError = true)]
        private static extern bool WinUsb_WritePipe(
            IntPtr interfaceHandle,
            byte pipeID,
            byte[] buffer,
            uint bufferLength,
            out uint lengthTransferred,
            IntPtr overlapped);

        [DllImport("winusb.dll", SetLastError = true)]
        private static extern bool WinUsb_ReadPipe(
            IntPtr interfaceHandle,
            byte pipeID,
            byte[] buffer,
            uint bufferLength,
            out uint lengthTransferred,
            IntPtr overlapped);

        // Constants
        private const uint DIGCF_PRESENT = 0x00000002;
        private const uint DIGCF_DEVICEINTERFACE = 0x00000010;
        private const uint SPDRP_HARDWAREID = 0x00000001;
        private const uint SPDRP_DEVICEDESC = 0x00000000;
        private const uint GENERIC_READ = 0x80000000;
        private const uint GENERIC_WRITE = 0x40000000;
        private const uint FILE_SHARE_READ = 0x00000001;
        private const uint FILE_SHARE_WRITE = 0x00000002;
        private const uint OPEN_EXISTING = 3;
        private const uint FILE_ATTRIBUTE_NORMAL = 0x80;
        private const uint FILE_FLAG_OVERLAPPED = 0x40000000;

        // USB Class GUID for WinUSB devices
        private static readonly Guid GUID_DEVINTERFACE_USB_DEVICE = new Guid("A5DCBF10-6530-11D2-901F-00C04FB951ED");

        [StructLayout(LayoutKind.Sequential)]
        private struct SP_DEVINFO_DATA
        {
            public uint cbSize;
            public Guid classGuid;
            public uint devInst;
            public IntPtr reserved;
        }

        // LibUSB structures
        [StructLayout(LayoutKind.Sequential)]
        private struct usb_device_descriptor
        {
            public byte bLength;
            public byte bDescriptorType;
            public ushort bcdUSB;
            public byte bDeviceClass;
            public byte bDeviceSubClass;
            public byte bDeviceProtocol;
            public byte bMaxPacketSize0;
            public ushort idVendor;
            public ushort idProduct;
            public ushort bcdDevice;
            public byte iManufacturer;
            public byte iProduct;
            public byte iSerialNumber;
            public byte bNumConfigurations;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct usb_device
        {
            public IntPtr next;
            public IntPtr prev;
            public IntPtr filename;
            public IntPtr bus;
            public usb_device_descriptor descriptor;
            public IntPtr config;
            public IntPtr dev;
            public byte devnum;
            public byte num_children;
            public IntPtr children;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct usb_bus
        {
            public IntPtr next;
            public IntPtr prev;
            public IntPtr dirname;
            public IntPtr devices;
            public uint location;
            public IntPtr root_dev;
        }

        #endregion

        #region Fields
        private List<MediaTekDevice> connectedDevices;
        private bool disposed = false;
        #endregion

        #region Events
        public event EventHandler<MediaTekDevice> DeviceConnected;
        public event EventHandler<MediaTekDevice> DeviceDisconnected;
        public event EventHandler<string> LogMessage;
        #endregion

        #region Constructor/Destructor
        public UsbManager()
        {
            InitializeUsbManager();
        }

        ~UsbManager()
        {
            Dispose(false);
        }
        #endregion

        #region Initialization
        private void InitializeUsbManager()
        {
            try
            {
                connectedDevices = new List<MediaTekDevice>();

                // Initialize libusb
                usb_init();
                LogMessage?.Invoke(this, "LibUSB initialized successfully");
                LogMessage?.Invoke(this, "USB Manager initialized successfully");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Failed to initialize USB Manager: {ex.Message}");
            }
        }
        #endregion

        #region Device Detection
        public List<MediaTekDevice> GetConnectedMediaTekDevices()
        {
            var devices = new List<MediaTekDevice>();

            try
            {
                // Use libusb to scan for devices
                usb_find_busses();
                usb_find_devices();

                IntPtr busses = usb_get_busses();
                IntPtr currentBus = busses;

                while (currentBus != IntPtr.Zero)
                {
                    var bus = Marshal.PtrToStructure<usb_bus>(currentBus);
                    IntPtr currentDevice = bus.devices;

                    while (currentDevice != IntPtr.Zero)
                    {
                        var device = Marshal.PtrToStructure<usb_device>(currentDevice);

                        // Check if it's a MediaTek device
                        if (IsMediaTekDevice(device.descriptor.idVendor, device.descriptor.idProduct))
                        {
                            var mtkDevice = CreateMediaTekDeviceFromLibUsb(device, currentDevice);
                            if (mtkDevice != null)
                            {
                                devices.Add(mtkDevice);
                            }
                        }

                        currentDevice = device.next;
                    }

                    currentBus = bus.next;
                }

                LogMessage?.Invoke(this, $"Found {devices.Count} MediaTek device(s)");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Error scanning for devices: {ex.Message}");
            }

            return devices;
        }

        private MediaTekDevice CreateMediaTekDeviceFromLibUsb(usb_device device, IntPtr devicePtr)
        {
            try
            {
                var mtkDevice = new MediaTekDevice
                {
                    VendorId = device.descriptor.idVendor,
                    ProductId = device.descriptor.idProduct,
                    DevicePath = devicePtr.ToString(),
                    Manufacturer = GetVendorName(device.descriptor.idVendor),
                    ProductName = GetProductName(device.descriptor.idProduct),
                    BootMode = GetBootMode(device.descriptor.idProduct),
                    SerialNumber = "Unknown",
                    IsConnected = true,
                    DeviceHandle = null,
                    WinUsbHandle = IntPtr.Zero,
                    LibUsbDevicePtr = devicePtr
                };

                return mtkDevice;
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Error creating MediaTek device: {ex.Message}");
                return null;
            }
        }

        private bool IsMediaTekDevice(int vid, int pid)
        {
            return MediaTekVendorIds.ContainsKey(vid) || MediaTekProductIds.ContainsKey(pid);
        }

        private MediaTekDevice CreateMediaTekDeviceFromInfo(IntPtr deviceInfoSet, SP_DEVINFO_DATA deviceInfoData)
        {
            try
            {
                // Get hardware ID
                string hardwareId = GetDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_HARDWAREID);
                if (string.IsNullOrEmpty(hardwareId))
                    return null;

                // Parse VID/PID from hardware ID (format: USB\VID_xxxx&PID_xxxx)
                if (!ParseVidPid(hardwareId, out int vid, out int pid))
                    return null;

                // Check if it's a MediaTek device
                if (!IsMediaTekDevice(vid, pid))
                    return null;

                // Get device description
                string description = GetDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_DEVICEDESC);

                var mtkDevice = new MediaTekDevice
                {
                    VendorId = vid,
                    ProductId = pid,
                    DevicePath = hardwareId,
                    Manufacturer = GetVendorName(vid),
                    ProductName = GetProductName(pid),
                    BootMode = GetBootMode(pid),
                    SerialNumber = "Unknown",
                    IsConnected = true,
                    DeviceHandle = null,
                    WinUsbHandle = IntPtr.Zero
                };

                return mtkDevice;
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Error creating MediaTek device: {ex.Message}");
                return null;
            }
        }

        private string GetDeviceProperty(IntPtr deviceInfoSet, SP_DEVINFO_DATA deviceInfoData, uint property)
        {
            try
            {
                uint requiredSize;
                SetupDiGetDeviceRegistryProperty(deviceInfoSet, ref deviceInfoData, property, out _, null, 0, out requiredSize);

                if (requiredSize > 0)
                {
                    byte[] buffer = new byte[requiredSize];
                    if (SetupDiGetDeviceRegistryProperty(deviceInfoSet, ref deviceInfoData, property, out _, buffer, requiredSize, out _))
                    {
                        return Encoding.Unicode.GetString(buffer).TrimEnd('\0');
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Error getting device property: {ex.Message}");
            }
            return string.Empty;
        }

        private bool ParseVidPid(string hardwareId, out int vid, out int pid)
        {
            vid = 0;
            pid = 0;

            try
            {
                // Parse format: USB\VID_xxxx&PID_xxxx
                if (hardwareId.StartsWith("USB\\VID_", StringComparison.OrdinalIgnoreCase))
                {
                    int vidStart = hardwareId.IndexOf("VID_") + 4;
                    int vidEnd = hardwareId.IndexOf("&", vidStart);
                    if (vidEnd > vidStart)
                    {
                        string vidStr = hardwareId.Substring(vidStart, vidEnd - vidStart);
                        vid = Convert.ToInt32(vidStr, 16);
                    }

                    int pidStart = hardwareId.IndexOf("PID_") + 4;
                    int pidEnd = hardwareId.IndexOf("&", pidStart);
                    if (pidEnd == -1) pidEnd = hardwareId.Length;
                    if (pidEnd > pidStart)
                    {
                        string pidStr = hardwareId.Substring(pidStart, pidEnd - pidStart);
                        pid = Convert.ToInt32(pidStr, 16);
                    }

                    return vid > 0 && pid > 0;
                }
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Error parsing VID/PID: {ex.Message}");
            }
            return false;
        }

        private string GetVendorName(int vid)
        {
            return MediaTekVendorIds.TryGetValue(vid, out string vendor) ? vendor : $"Unknown Vendor (0x{vid:X4})";
        }

        private string GetProductName(int pid)
        {
            return MediaTekProductIds.TryGetValue(pid, out string product) ? product : $"Unknown Product (0x{pid:X4})";
        }

        private string GetBootMode(int pid)
        {
            return BootModes.TryGetValue(pid, out string mode) ? mode : "Unknown Mode";
        }
        #endregion

        #region Device Communication
        public bool OpenDevice(MediaTekDevice device)
        {
            try
            {
                if (device == null || device.LibUsbDevicePtr == IntPtr.Zero)
                    return false;

                // Open device using libusb
                device.LibUsbHandle = usb_open(device.LibUsbDevicePtr);

                if (device.LibUsbHandle == IntPtr.Zero)
                {
                    LogMessage?.Invoke(this, "Failed to open device with libusb");
                    return false;
                }

                // Set configuration
                int result = usb_set_configuration(device.LibUsbHandle, 1);
                if (result < 0)
                {
                    LogMessage?.Invoke(this, $"Failed to set configuration: {result}");
                    usb_close(device.LibUsbHandle);
                    device.LibUsbHandle = IntPtr.Zero;
                    return false;
                }

                // Claim interface
                result = usb_claim_interface(device.LibUsbHandle, 0);
                if (result < 0)
                {
                    LogMessage?.Invoke(this, $"Failed to claim interface: {result}");
                    usb_close(device.LibUsbHandle);
                    device.LibUsbHandle = IntPtr.Zero;
                    return false;
                }

                device.IsOpen = true;
                LogMessage?.Invoke(this, $"Device opened: {device.ProductName}");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Failed to open device: {ex.Message}");
                return false;
            }
        }

        public bool CloseDevice(MediaTekDevice device)
        {
            try
            {
                if (device == null)
                    return false;

                if (device.LibUsbHandle != IntPtr.Zero)
                {
                    // Release interface
                    usb_release_interface(device.LibUsbHandle, 0);

                    // Close device
                    usb_close(device.LibUsbHandle);
                    device.LibUsbHandle = IntPtr.Zero;
                }

                device.IsOpen = false;
                LogMessage?.Invoke(this, $"Device closed: {device.ProductName}");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Failed to close device: {ex.Message}");
                return false;
            }
        }

        public int SendCommand(MediaTekDevice device, byte[] command, byte[] response, int timeout = 5000)
        {
            try
            {
                if (device?.LibUsbHandle == IntPtr.Zero || !device.IsOpen)
                    return -1;

                // Send command (endpoint 0x01 for output)
                int bytesWritten = usb_bulk_write(device.LibUsbHandle, 0x01, command, command.Length, timeout);
                if (bytesWritten < 0)
                {
                    LogMessage?.Invoke(this, $"Write error: {bytesWritten}");
                    return -1;
                }

                // Read response (endpoint 0x81 for input)
                int bytesRead = usb_bulk_read(device.LibUsbHandle, 0x81, response, response.Length, timeout);
                if (bytesRead < 0)
                {
                    LogMessage?.Invoke(this, $"Read error: {bytesRead}");
                    return -1;
                }

                return bytesRead;
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Communication error: {ex.Message}");
                return -1;
            }
        }
        #endregion

        #region Event Handlers
        public void RefreshDevices()
        {
            try
            {
                var newDevices = GetConnectedMediaTekDevices();

                // Check for newly connected devices
                foreach (var device in newDevices)
                {
                    if (!connectedDevices.Any(d => d.VendorId == device.VendorId && d.ProductId == device.ProductId))
                    {
                        connectedDevices.Add(device);
                        DeviceConnected?.Invoke(this, device);
                        LogMessage?.Invoke(this, $"Device connected: {device}");
                    }
                }

                // Check for disconnected devices
                var disconnectedDevices = connectedDevices.Where(d =>
                    !newDevices.Any(nd => nd.VendorId == d.VendorId && nd.ProductId == d.ProductId)).ToList();

                foreach (var device in disconnectedDevices)
                {
                    connectedDevices.Remove(device);
                    DeviceDisconnected?.Invoke(this, device);
                    LogMessage?.Invoke(this, $"Device disconnected: {device}");
                }
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Device refresh error: {ex.Message}");
            }
        }
        #endregion

        #region Dispose
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    try
                    {
                        // Close all open devices
                        foreach (var device in connectedDevices.Where(d => d.IsOpen))
                        {
                            CloseDevice(device);
                        }

                        connectedDevices.Clear();
                    }
                    catch (Exception ex)
                    {
                        LogMessage?.Invoke(this, $"Dispose error: {ex.Message}");
                    }
                }
                disposed = true;
            }
        }
        #endregion
    }

    /// <summary>
    /// MediaTek device information
    /// </summary>
    public class MediaTekDevice
    {
        public int VendorId { get; set; }
        public int ProductId { get; set; }
        public string DevicePath { get; set; }
        public string Manufacturer { get; set; }
        public string ProductName { get; set; }
        public string BootMode { get; set; }
        public string SerialNumber { get; set; }
        public bool IsConnected { get; set; }
        public bool IsOpen { get; set; }
        public SafeFileHandle DeviceHandle { get; set; }
        public IntPtr WinUsbHandle { get; set; }
        public IntPtr LibUsbDevicePtr { get; set; }
        public IntPtr LibUsbHandle { get; set; }

        public override string ToString()
        {
            return $"{ProductName} ({BootMode}) - VID: 0x{VendorId:X4}, PID: 0x{ProductId:X4}";
        }
    }
}
