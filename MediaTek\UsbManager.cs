using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using LibUsbDotNet;
using LibUsbDotNet.Main;
using LibUsbDotNet.DeviceNotify;

namespace MotoKingPro.MediaTek
{
    /// <summary>
    /// USB device manager for MediaTek devices using LibUsbDotNet
    /// </summary>
    public class UsbManager : IDisposable
    {
        #region MediaTek USB VID/PID Constants
        public static readonly Dictionary<int, string> MediaTekVendorIds = new()
        {
            { 0x0E8D, "MediaTek Inc." },
            { 0x1004, "LG Electronics (MediaTek)" },
            { 0x22B8, "Motorola (MediaTek)" },
            { 0x19D2, "ZTE (MediaTek)" },
            { 0x2717, "Xiaomi (MediaTek)" }
        };

        public static readonly Dictionary<int, string> MediaTekProductIds = new()
        {
            // BROM Mode PIDs
            { 0x0003, "MT6516 BROM" },
            { 0x1000, "MT6516 Preloader" },
            { 0x2000, "MT6516 Download Agent" },
            { 0x3000, "MT6516 Debug Agent" },
            
            // MT65xx Series
            { 0x6516, "MT6516" },
            { 0x6573, "MT6573" },
            { 0x6575, "MT6575" },
            { 0x6577, "MT6577" },
            { 0x6582, "MT6582" },
            { 0x6589, "MT6589" },
            { 0x6592, "MT6592" },
            
            // MT67xx Series
            { 0x6735, "MT6735" },
            { 0x6737, "MT6737" },
            { 0x6739, "MT6739" },
            { 0x6750, "MT6750" },
            { 0x6753, "MT6753" },
            { 0x6755, "MT6755" },
            { 0x6757, "MT6757" },
            { 0x6758, "MT6758" },
            { 0x6759, "MT6759" },
            { 0x6761, "MT6761" },
            { 0x6762, "MT6762" },
            { 0x6763, "MT6763" },
            { 0x6765, "MT6765" },
            { 0x6768, "MT6768" },
            { 0x6771, "MT6771" },
            { 0x6779, "MT6779" },
            { 0x6785, "MT6785" },
            { 0x6795, "MT6795" },
            { 0x6797, "MT6797" },
            
            // MT68xx Series
            { 0x6833, "MT6833" },
            { 0x6853, "MT6853" },
            { 0x6873, "MT6873" },
            { 0x6877, "MT6877" },
            { 0x6885, "MT6885" },
            { 0x6889, "MT6889" },
            { 0x6891, "MT6891" },
            { 0x6893, "MT6893" }
        };

        public static readonly Dictionary<int, string> BootModes = new()
        {
            { 0x0003, "BROM Mode" },
            { 0x1000, "Preloader Mode" },
            { 0x2000, "Download Agent Mode" },
            { 0x3000, "Debug Agent Mode" }
        };
        #endregion

        #region Fields
        private UsbDeviceCollection deviceCollection;
        private IDeviceNotifier deviceNotifier;
        private bool disposed = false;
        #endregion

        #region Events
        public event EventHandler<UsbDevice> DeviceConnected;
        public event EventHandler<UsbDevice> DeviceDisconnected;
        public event EventHandler<string> LogMessage;
        #endregion

        #region Constructor/Destructor
        public UsbManager()
        {
            InitializeUsbManager();
        }

        ~UsbManager()
        {
            Dispose(false);
        }
        #endregion

        #region Initialization
        private void InitializeUsbManager()
        {
            try
            {
                // Initialize LibUsbDotNet
                UsbDevice.UsbErrorEvent += OnUsbError;
                
                // Setup device notification
                deviceNotifier = DeviceNotifier.OpenDeviceNotifier();
                deviceNotifier.OnDeviceNotify += OnDeviceNotify;
                
                LogMessage?.Invoke(this, "USB Manager initialized successfully");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Failed to initialize USB Manager: {ex.Message}");
            }
        }
        #endregion

        #region Device Detection
        public List<MediaTekDevice> GetConnectedMediaTekDevices()
        {
            var devices = new List<MediaTekDevice>();
            
            try
            {
                deviceCollection = UsbDevice.AllDevices;
                
                foreach (UsbRegistry regDevice in deviceCollection)
                {
                    if (IsMediaTekDevice(regDevice.Vid, regDevice.Pid))
                    {
                        var device = CreateMediaTekDevice(regDevice);
                        if (device != null)
                        {
                            devices.Add(device);
                        }
                    }
                }
                
                LogMessage?.Invoke(this, $"Found {devices.Count} MediaTek device(s)");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Error scanning for devices: {ex.Message}");
            }
            
            return devices;
        }

        private bool IsMediaTekDevice(int vid, int pid)
        {
            return MediaTekVendorIds.ContainsKey(vid) || MediaTekProductIds.ContainsKey(pid);
        }

        private MediaTekDevice CreateMediaTekDevice(UsbRegistry regDevice)
        {
            try
            {
                UsbDevice device;
                if (!regDevice.Open(out device))
                {
                    return null;
                }

                var mtkDevice = new MediaTekDevice
                {
                    UsbDevice = device,
                    VendorId = regDevice.Vid,
                    ProductId = regDevice.Pid,
                    DevicePath = regDevice.DeviceInterfaceGuids?.FirstOrDefault()?.ToString(),
                    Manufacturer = GetVendorName(regDevice.Vid),
                    ProductName = GetProductName(regDevice.Pid),
                    BootMode = GetBootMode(regDevice.Pid),
                    SerialNumber = device.Info.SerialString,
                    IsConnected = true
                };

                return mtkDevice;
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Error creating MediaTek device: {ex.Message}");
                return null;
            }
        }

        private string GetVendorName(int vid)
        {
            return MediaTekVendorIds.TryGetValue(vid, out string vendor) ? vendor : $"Unknown Vendor (0x{vid:X4})";
        }

        private string GetProductName(int pid)
        {
            return MediaTekProductIds.TryGetValue(pid, out string product) ? product : $"Unknown Product (0x{pid:X4})";
        }

        private string GetBootMode(int pid)
        {
            return BootModes.TryGetValue(pid, out string mode) ? mode : "Unknown Mode";
        }
        #endregion

        #region Device Communication
        public bool OpenDevice(MediaTekDevice device)
        {
            try
            {
                if (device?.UsbDevice == null)
                    return false;

                // For LibUsbDotNet on Windows, we need to use WinUSB driver
                if (device.UsbDevice is IUsbDevice wholeUsbDevice)
                {
                    // Select configuration #1
                    wholeUsbDevice.SetConfiguration(1);
                    
                    // Claim interface #0
                    wholeUsbDevice.ClaimInterface(0);
                }

                device.IsOpen = true;
                LogMessage?.Invoke(this, $"Device opened: {device.ProductName}");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Failed to open device: {ex.Message}");
                return false;
            }
        }

        public bool CloseDevice(MediaTekDevice device)
        {
            try
            {
                if (device?.UsbDevice == null)
                    return false;

                if (device.UsbDevice is IUsbDevice wholeUsbDevice)
                {
                    // Release interface #0
                    wholeUsbDevice.ReleaseInterface(0);
                }

                device.UsbDevice.Close();
                device.IsOpen = false;
                LogMessage?.Invoke(this, $"Device closed: {device.ProductName}");
                return true;
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Failed to close device: {ex.Message}");
                return false;
            }
        }

        public int SendCommand(MediaTekDevice device, byte[] command, byte[] response, int timeout = 5000)
        {
            try
            {
                if (device?.UsbDevice == null || !device.IsOpen)
                    return -1;

                UsbEndpointWriter writer = device.UsbDevice.OpenEndpointWriter(WriteEndpointID.Ep01);
                UsbEndpointReader reader = device.UsbDevice.OpenEndpointReader(ReadEndpointID.Ep01);

                // Send command
                int bytesWritten;
                ErrorCode writeResult = writer.Write(command, timeout, out bytesWritten);
                
                if (writeResult != ErrorCode.None)
                {
                    LogMessage?.Invoke(this, $"Write error: {writeResult}");
                    return -1;
                }

                // Read response
                int bytesRead;
                ErrorCode readResult = reader.Read(response, timeout, out bytesRead);
                
                if (readResult != ErrorCode.None)
                {
                    LogMessage?.Invoke(this, $"Read error: {readResult}");
                    return -1;
                }

                return bytesRead;
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Communication error: {ex.Message}");
                return -1;
            }
        }
        #endregion

        #region Event Handlers
        private void OnUsbError(object sender, UsbError e)
        {
            LogMessage?.Invoke(this, $"USB Error: {e.Description}");
        }

        private void OnDeviceNotify(object sender, DeviceNotifyEventArgs e)
        {
            try
            {
                if (e.EventType == EventType.DeviceArrival)
                {
                    LogMessage?.Invoke(this, "Device connected");
                    // Refresh device list
                    var devices = GetConnectedMediaTekDevices();
                    foreach (var device in devices)
                    {
                        DeviceConnected?.Invoke(this, device.UsbDevice);
                    }
                }
                else if (e.EventType == EventType.DeviceRemoveComplete)
                {
                    LogMessage?.Invoke(this, "Device disconnected");
                    DeviceDisconnected?.Invoke(this, null);
                }
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Device notification error: {ex.Message}");
            }
        }
        #endregion

        #region Dispose
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    try
                    {
                        deviceNotifier?.Enabled = false;
                        deviceNotifier?.Dispose();
                        
                        if (deviceCollection != null)
                        {
                            foreach (UsbRegistry regDevice in deviceCollection)
                            {
                                regDevice?.Device?.Close();
                            }
                        }
                        
                        UsbDevice.Exit();
                    }
                    catch (Exception ex)
                    {
                        LogMessage?.Invoke(this, $"Dispose error: {ex.Message}");
                    }
                }
                disposed = true;
            }
        }
        #endregion
    }

    /// <summary>
    /// MediaTek device information
    /// </summary>
    public class MediaTekDevice
    {
        public UsbDevice UsbDevice { get; set; }
        public int VendorId { get; set; }
        public int ProductId { get; set; }
        public string DevicePath { get; set; }
        public string Manufacturer { get; set; }
        public string ProductName { get; set; }
        public string BootMode { get; set; }
        public string SerialNumber { get; set; }
        public bool IsConnected { get; set; }
        public bool IsOpen { get; set; }

        public override string ToString()
        {
            return $"{ProductName} ({BootMode}) - VID: 0x{VendorId:X4}, PID: 0x{ProductId:X4}";
        }
    }
}
