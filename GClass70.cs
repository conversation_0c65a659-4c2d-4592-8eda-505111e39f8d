using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

public class GClass70
{
	[StructLayout(LayoutKind.Sequential, Pack = 1)]
	public struct GStruct63
	{
		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
		public string D820C63A;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
		public string string_0;

		public uint uint_0;

		public uint uint_1;

		public int int_0;
	}

	public static class GClass71
	{
		public const int A400FFA0 = 0;

		public const int int_0 = 1;

		public const int int_1 = 2;

		public const int D10A1B14 = 3;

		public const int D13CD4AC = 4;
	}

	public static class F5A45A14
	{
		public const int int_0 = 1;

		public const int E20AA61D = 2;

		public const int CD8C391E = 48;

		public const int int_1 = 16;

		public const int B921BF31 = 17;

		public const int int_2 = 18;

		public const int int_3 = 19;

		public const int C73896BF = 20;

		public const int CE32461C = 21;

		public const int int_4 = 32;

		public const int int_5 = 33;

		public const int B1AEA3AA = 34;
	}

	public static class GClass72
	{
		public const byte byte_0 = 1;

		public const byte byte_1 = 2;

		public const byte D7BD5B28 = 3;

		public const byte byte_2 = 4;

		public const byte byte_3 = 5;

		public const byte byte_4 = 6;

		public const byte byte_5 = 7;

		public const byte byte_6 = 8;

		public const byte DC282486 = 9;

		public const byte byte_7 = 10;
	}

	public static class GClass73
	{
		public const int int_0 = 0;

		public const int int_1 = 1;

		public const int int_2 = 2;

		public const int D70F8B1E = 3;

		public const int int_3 = 4;

		public const int int_4 = 5;

		public const int DAB69705 = 6;

		public const int A0BB9E1A = 7;

		public const int int_5 = 8;
	}

	public static class C9A2C1AE
	{
		public const int BA31E797 = 1;

		public const int int_0 = 2;

		public const int int_1 = 3;
	}

	public static class GClass74
	{
		public const int E80D4503 = 0;

		public const int E21F0EAB = 1;

		public const int int_0 = 2;

		public const int int_1 = 3;

		public const int int_2 = 4;

		public const int int_3 = 5;

		public const int int_4 = 6;

		public const int int_5 = 7;
	}

	public string string_0;

	public ushort ushort_0;

	public ulong ulong_0;

	public string string_1;

	public uint DB09DC88;

	public ulong ulong_1;

	public uint uint_0 = 512u;

	public C10B6585 E1105331;

	public byte[] byte_0;

	public byte[] byte_1;

	public byte[] byte_2;

	public int int_0;

	public byte[] D10D9B3F;

	public string B0357487;

	public bool A08FA093;

	public Dictionary<uint, List<C10B6585>> dictionary_0;

	internal ulong ulong_2;

	internal ulong EF89F58D;

	internal ulong ulong_3;

	public GClass70(string[] DE81FFAF = null)
	{
		Class607.B630A78B.object_0[571](this);
		string[] source = GClass30.String_0;
		if (DE81FFAF != null && DE81FFAF.Length != 0)
		{
			source = DE81FFAF;
		}
		dictionary_0 = new Dictionary<uint, List<C10B6585>>();
		ulong_0 = 0uL;
		string_1 = "emmc";
		DB09DC88 = 0u;
		ulong_1 = 0uL;
		uint_0 = 512u;
		E1105331 = null;
		byte_0 = null;
		B0357487 = null;
		method_2(D10D9B3F);
		using IEnumerator<string> enumerator = source.Reverse().GetEnumerator();
		while (Class607.B630A78B.object_0[212](enumerator))
		{
			string current = enumerator.Current;
			CA8C2D24(current);
		}
	}

	public int B89691A4(char char_0)
	{
		switch (char_0)
		{
		case '?':
		case 'B':
		case 'b':
		case 'c':
			return 1;
		case 'Q':
		case 'd':
		case 'q':
			return 8;
		case 'H':
		case 'e':
		case 'h':
			return 2;
		default:
			return 0;
		case 'I':
		case 'L':
		case 'f':
		case 'i':
		case 'l':
			return 4;
		}
	}

	public bool CA8C2D24(string ********)
	{
		return (bool)new GClass128().E6A33692(new object[2] { this, ******** }, 510515);
	}

	public bool method_0(string string_2)
	{
		bool result = false;
		try
		{
			using FileStream fileStream = Class607.B630A78B.object_0[123](string_2);
			byte[] array = new byte[108];
			Class607.B630A78B.object_0[53](fileStream, array, 0, 108);
			GStruct63 gStruct = default(GStruct63);
			Class607.B630A78B.object_0[146](Class607.B630A78B.object_0[6](typeof(GStruct63).TypeHandle));
			gStruct = (GStruct63)GClass112.smethod_15(array, Class607.B630A78B.object_0[6](typeof(GStruct63).TypeHandle));
			int f008AE9F = gStruct.int_0;
			A08FA093 = Class607.B630A78B.object_0[1240](gStruct.D820C63A, "MTK_DA_v6");
			using (IEnumerator<int> enumerator = GClass112.smethod_30(0, Class607.B630A78B.object_0[229](f008AE9F)).GetEnumerator())
			{
				while (Class607.B630A78B.object_0[212](enumerator))
				{
					int current = enumerator.Current;
					Class607.B630A78B.object_0[543](fileStream, 108 + current * 220, SeekOrigin.Begin);
					byte[] c78C = new byte[220];
					Class607.B630A78B.object_0[53](fileStream, c78C, 0, 220);
					C10B6585 c10B = new C10B6585(c78C);
					c10B.A130EB0E = A08FA093;
					c10B.EE889906(string_2);
					if (!dictionary_0.ContainsKey(c10B.ushort_1))
					{
						dictionary_0.Add(c10B.ushort_1, new List<C10B6585> { c10B });
						if (GClass112.C78DEB29.dictionary_0.ContainsKey(c10B.ushort_1))
						{
							_ = GClass112.C78DEB29.dictionary_0[c10B.ushort_1];
						}
						continue;
					}
					bool flag = false;
					foreach (C10B6585 item in dictionary_0[c10B.ushort_1])
					{
						if (c10B.ushort_3 == item.ushort_3 && c10B.E2BEC993 == item.E2BEC993 && c10B.ushort_2 == item.ushort_2)
						{
							flag = true;
							break;
						}
					}
					if (!flag)
					{
						dictionary_0[c10B.ushort_1].Add(c10B);
					}
				}
			}
			result = true;
		}
		catch
		{
		}
		return result;
	}

	public bool method_1(int int_1, int C59DA99B)
	{
		uint f1016B = GClass112.C78DEB29.A8054FBA.F1016B18;
		if (dictionary_0.ContainsKey(f1016B))
		{
			List<C10B6585> list = dictionary_0[f1016B];
			foreach (C10B6585 item in list)
			{
				if (item.ushort_3 <= int_1 && item.E2BEC993 <= C59DA99B && E1105331 == null)
				{
					if (item.A130EB0E)
					{
						GClass112.C78DEB29.A8054FBA.B72DA2BE = GClass34.AB881C92.D03BC921;
					}
					E1105331 = item;
					B0357487 = item.string_0;
					return true;
				}
			}
		}
		if (E1105331 != null || f1016B == 25185)
		{
		}
		return false;
	}

	public bool D180A435(int C7318506, int int_1)
	{
		uint f1016B = GClass112.C78DEB29.A8054FBA.F1016B18;
		if (dictionary_0.ContainsKey(f1016B))
		{
			List<C10B6585> list = dictionary_0[f1016B];
			foreach (C10B6585 item in list)
			{
				if (item.ushort_3 <= C7318506 && item.E2BEC993 <= int_1 && B0357487 == null)
				{
					if (item.A130EB0E)
					{
						GClass112.C78DEB29.A8054FBA.B72DA2BE = GClass34.AB881C92.D03BC921;
					}
					E1105331 = item;
					B0357487 = item.string_0;
					return true;
				}
			}
		}
		return false;
	}

	public void method_2(byte[] byte_3)
	{
		try
		{
			if (byte_3 == null || byte_3.Length == 0)
			{
				byte_1 = null;
				return;
			}
			D10D9B3F = byte_3;
			Tuple<int?, byte[]> tuple = A11A0534(byte_3);
			byte_1 = tuple.Item2;
			int_0 = tuple.Item1.Value;
			method_3(byte_3);
		}
		catch
		{
		}
	}

	public void method_3(byte[] DB2CEC01)
	{
		if (DB2CEC01 == null || DB2CEC01.Length == 0)
		{
			byte_2 = null;
			return;
		}
		int num = GClass112.smethod_24(DB2CEC01, GClass112.FF06B0AD("4d4d4d0130"), 0);
		if (num != -1)
		{
			byte_2 = GClass112.smethod_14(DB2CEC01, num + 12, 32);
		}
	}

	public Tuple<int?, byte[]> A11A0534(byte[] C938EEA3)
	{
		int num = Array.IndexOf(C938EEA3, (byte)77, 0, C938EEA3.Length);
		if (num != -1 && C938EEA3.Length > num + 7 && C938EEA3[num + 1] == 77 && C938EEA3[num + 2] == 77 && C938EEA3[num + 3] == 1 && C938EEA3[num + 4] == 56 && C938EEA3[num + 5] == 0 && C938EEA3[num + 6] == 0 && C938EEA3[num + 7] == 0)
		{
			C938EEA3 = C938EEA3.Skip(num).ToArray();
			uint num2 = (uint)GClass111.C3B9331C("<I", GClass112.smethod_14(C938EEA3, 32, 4))[0];
			uint num3 = (uint)GClass111.C3B9331C("<I", GClass112.smethod_14(C938EEA3, 44, 4))[0];
			C938EEA3 = C938EEA3.Take((int)(num2 - num3)).ToArray();
			uint num4 = (uint)GClass111.C3B9331C("<I", GClass112.smethod_14(C938EEA3, C938EEA3.Length - 4, 4))[0];
			if (num4 == 0)
			{
				C938EEA3 = C938EEA3.Take(C938EEA3.Length - 2048).ToArray();
				num4 = (uint)GClass111.C3B9331C("<I", GClass112.smethod_14(C938EEA3, C938EEA3.Length - 4, 4))[0];
			}
			C938EEA3 = C938EEA3.Skip(C938EEA3.Length - (int)num4 - 4).Take((int)num4).ToArray();
		}
		byte[] array = Class607.B630A78B.object_0[45](Encoding.ASCII, "MTK_BLOADER_INFO_v");
		int num5 = array.Length;
		num = -1;
		for (int i = 0; i <= C938EEA3.Length - num5; i++)
		{
			bool flag = true;
			for (int j = 0; j < num5; j++)
			{
				if (C938EEA3[i + j] != array[j])
				{
					flag = false;
					break;
				}
			}
			if (flag)
			{
				num = i;
				break;
			}
		}
		switch (num)
		{
		case -1:
			return new Tuple<int?, byte[]>(null, null);
		case 0:
			if (GClass112.C78DEB29.A8054FBA.B72DA2BE == GClass34.AB881C92.const_1)
			{
				int value = Class607.B630A78B.object_0[980](Class607.B630A78B.object_0[647](Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[47](), C938EEA3, num + num5, 2), new char[1]));
				return new Tuple<int?, byte[]>(value, C938EEA3);
			}
			break;
		}
		byte[] array2 = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[47](), "MTK_BIN");
		int num6 = -1;
		for (int k = 0; k <= C938EEA3.Length - array2.Length; k++)
		{
			bool flag2 = true;
			for (int l = 0; l < array2.Length; l++)
			{
				if (C938EEA3[k + l] != array2[l])
				{
					flag2 = false;
					break;
				}
			}
			if (flag2)
			{
				num6 = k;
				break;
			}
		}
		if (num6 != -1)
		{
			byte[] array3 = new byte[C938EEA3.Length - (num6 + 12)];
			Class607.B630A78B.object_0[242](C938EEA3, num6 + 12, array3, 0, array3.Length);
			int value2 = Class607.B630A78B.object_0[980](Class607.B630A78B.object_0[647](Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[47](), C938EEA3, num + num5, 2), new char[1]));
			return new Tuple<int?, byte[]>(value2, array3);
		}
		return new Tuple<int?, byte[]>(null, null);
	}

	public Tuple<int, byte[]> C3061990(byte[] byte_3)
	{
		byte[] array = byte_3.ToArray();
		int num = GClass112.smethod_24(array, GClass112.FF06B0AD("4d4d4d0138000000"), 0);
		int num2 = 0;
		if (num != -1)
		{
			array = GClass112.smethod_14(array, num, array.Length - num);
			int num3 = Class607.B630A78B.object_0[84](GClass111.C3B9331C("<I", GClass112.smethod_14(array, 32, 4))[0]);
			num2 = Class607.B630A78B.object_0[84](GClass111.C3B9331C("<I", GClass112.smethod_14(array, 44, 4))[0]);
			array = GClass112.smethod_14(array, 0, num3 - num2);
			int num4 = Class607.B630A78B.object_0[84](GClass111.C3B9331C("<I", GClass112.smethod_14(array, array.Length - 4, 4))[0]);
			if (num4 == 0)
			{
				array = GClass112.smethod_14(array, array.Length - 2048, 2048);
				num4 = Class607.B630A78B.object_0[84](GClass111.C3B9331C("<I", GClass112.smethod_14(array, array.Length - 4, 4))[0]);
			}
			array = GClass112.smethod_14(array, array.Length - (num4 + 4), array.Length - (array.Length - (num4 + 4)) - 4);
		}
		byte[] array2 = Class607.B630A78B.object_0[45](Encoding.Default, "MTK_BLOADER_INFO_v");
		int num5 = array2.Length;
		num = GClass112.smethod_24(array, array2, 0);
		switch (num)
		{
		case -1:
			return null;
		case 0:
			if (GClass112.C78DEB29.A8054FBA.B72DA2BE == GClass34.AB881C92.const_1)
			{
				string text = GClass112.smethod_6(Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[1058](GClass112.smethod_14(array, num + num5, 2)), "-", ""), "00");
				int item = Class247.E311E1B9(Class607.B630A78B.object_0[1205](text) ? "00" : text, 16);
				return Tuple.Create(item, array);
			}
			break;
		}
		int num6 = GClass112.smethod_24(array, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "MTK_BIN"), 0);
		if (num6 != -1)
		{
			GClass112.smethod_14(array, num6 + 12, array.Length - (num6 + 12));
			string text2 = GClass112.smethod_6(Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[1058](GClass112.smethod_14(array, num + num5, 2)), "-", ""), "00");
			int item2 = Class247.E311E1B9(Class607.B630A78B.object_0[1205](text2) ? "00" : text2, 16);
			return Tuple.Create(item2, array);
		}
		return null;
	}
}
