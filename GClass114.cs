using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Threading;
using LibUsbDotNet;
using LibUsbDotNet.Main;

public static class GClass114
{
	[CompilerGenerated]
	private sealed class Class66
	{
		public List<Tuple<string, string>> list_0;

		public Predicate<GStruct96> predicate_0;

		public Class66()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool method_0(GStruct96 gstruct96_0)
		{
			Class67 @class = new Class67
			{
				gstruct96_0 = gstruct96_0
			};
			return list_0.Find(@class.method_0) != null;
		}
	}

	[CompilerGenerated]
	private sealed class Class67
	{
		public GStruct96 gstruct96_0;

		public Class67()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool method_0(Tuple<string, string> tuple_0)
		{
			return Class607.B630A78B.object_0[787](gstruct96_0.string_2, Class607.B630A78B.object_0[1019](Class607.B630A78B.object_0[1233](tuple_0.Item1))) && Class607.B630A78B.object_0[787](gstruct96_0.string_3, Class607.B630A78B.object_0[1019](Class607.B630A78B.object_0[1233](tuple_0.Item2)));
		}
	}

	[CompilerGenerated]
	private sealed class Class68
	{
		public List<Tuple<string, string>> list_0;

		public Class68()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool DE163C8F(GStruct96 E1894F07)
		{
			C92E1A3E c92E1A3E = new C92E1A3E
			{
				A010CD31 = E1894F07
			};
			return list_0.Find(c92E1A3E.method_0) != null;
		}
	}

	[CompilerGenerated]
	private sealed class C92E1A3E
	{
		public GStruct96 A010CD31;

		public C92E1A3E()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool method_0(Tuple<string, string> tuple_0)
		{
			return Class607.B630A78B.object_0[787](A010CD31.string_2, Class607.B630A78B.object_0[1019](Class607.B630A78B.object_0[1233](tuple_0.Item1))) && Class607.B630A78B.object_0[787](A010CD31.string_3, Class607.B630A78B.object_0[1019](Class607.B630A78B.object_0[1233](tuple_0.Item2)));
		}
	}

	public static ManualResetEvent DE195FB5 = Class607.B630A78B.object_0[578](bool_0: false);

	public static ManualResetEvent manualResetEvent_0 = Class607.B630A78B.object_0[578](bool_0: false);

	public static GStruct96 F83BC8A2(List<Tuple<string, string>> ED1052B2, int int_0)
	{
		Class66 CS_0024_003C_003E8__locals2 = new Class66();
		CS_0024_003C_003E8__locals2.list_0 = ED1052B2;
		Stopwatch stopwatch = Class607.B630A78B.object_0[681]();
		try
		{
			Class607.B630A78B.object_0[887](stopwatch);
			while (Class607.B630A78B.object_0[1097](stopwatch) < int_0)
			{
				GStruct96 result = Class69.list_0.Find(delegate(GStruct96 gstruct96_0)
				{
					Class67 @class = new Class67();
					@class.gstruct96_0 = gstruct96_0;
					return CS_0024_003C_003E8__locals2.list_0.Find(@class.method_0) != null;
				});
				if (!Class607.B630A78B.object_0[1205](result.string_0))
				{
					return result;
				}
			}
		}
		catch
		{
		}
		finally
		{
			Class607.B630A78B.object_0[221](stopwatch);
		}
		return default(GStruct96);
	}

	public static GStruct96 CEB48FBB(List<Tuple<string, string>> list_0)
	{
		Class68 CS_0024_003C_003E8__locals2 = new Class68();
		CS_0024_003C_003E8__locals2.list_0 = list_0;
		try
		{
			return Class69.list_0.Find(delegate(GStruct96 E1894F07)
			{
				C92E1A3E c92E1A3E = new C92E1A3E();
				c92E1A3E.A010CD31 = E1894F07;
				return CS_0024_003C_003E8__locals2.list_0.Find(c92E1A3E.method_0) != null;
			});
		}
		catch
		{
		}
		return default(GStruct96);
	}

	public static UsbDevice E09D98AE(GStruct96 gstruct96_0, int int_0)
	{
		Stopwatch stopwatch = Class607.B630A78B.object_0[681]();
		try
		{
			Class607.B630A78B.object_0[887](stopwatch);
			while (Class607.B630A78B.object_0[1097](stopwatch) < int_0)
			{
				UsbDeviceFinder dF379A = Class607.B630A78B.object_0[177](Class607.B630A78B.object_0[259](gstruct96_0.string_3, 16), Class607.B630A78B.object_0[259](gstruct96_0.string_2, 16));
				UsbDevice val = Class607.B630A78B.object_0[691](dF379A);
				if (val != null)
				{
					return val;
				}
			}
		}
		finally
		{
			Class607.B630A78B.object_0[221](stopwatch);
		}
		return null;
	}

	public static UsbDevice smethod_0(string B422A080, string string_0, int int_0)
	{
		Stopwatch stopwatch = Class607.B630A78B.object_0[681]();
		try
		{
			Class607.B630A78B.object_0[887](stopwatch);
			while (Class607.B630A78B.object_0[1097](stopwatch) < int_0)
			{
				UsbDeviceFinder dF379A = Class607.B630A78B.object_0[177](Class607.B630A78B.object_0[259](B422A080, 16), Class607.B630A78B.object_0[259](string_0, 16));
				UsbDevice val = Class607.B630A78B.object_0[691](dF379A);
				if (val != null)
				{
					return val;
				}
			}
		}
		finally
		{
			Class607.B630A78B.object_0[221](stopwatch);
		}
		return null;
	}

	public static GStruct96 smethod_1(int int_0)
	{
		Stopwatch stopwatch = Class607.B630A78B.object_0[681]();
		try
		{
			List<Tuple<string, string>> list_ = new List<Tuple<string, string>>
			{
				new Tuple<string, string>("0003", "0E8D"),
				new Tuple<string, string>("6000", "0E8D"),
				new Tuple<string, string>("2000", "0E8D"),
				new Tuple<string, string>("2001", "0E8D"),
				new Tuple<string, string>("20FF", "0E8D"),
				new Tuple<string, string>("6000", "1004"),
				new Tuple<string, string>("0006", "22D9"),
				new Tuple<string, string>("7523", "1a86"),
				new Tuple<string, string>("D1E9", "0FCE"),
				new Tuple<string, string>("F200", "0FCE"),
				new Tuple<string, string>("0006", "22D9"),
				new Tuple<string, string>("D1E2", "0FCE")
			};
			Class607.B630A78B.object_0[887](stopwatch);
			do
			{
				GStruct96 result = CEB48FBB(list_);
				if (!Class607.B630A78B.object_0[1205](result.string_0))
				{
					return result;
				}
			}
			while (Class607.B630A78B.object_0[1097](stopwatch) < int_0);
			return default(GStruct96);
		}
		finally
		{
			Class607.B630A78B.object_0[221](stopwatch);
			stopwatch = null;
		}
	}

	public static GStruct96 smethod_2()
	{
		List<Tuple<string, string>> list_ = new List<Tuple<string, string>>
		{
			new Tuple<string, string>("0003", "0E8D"),
			new Tuple<string, string>("6000", "0E8D"),
			new Tuple<string, string>("2000", "0E8D"),
			new Tuple<string, string>("2001", "0E8D"),
			new Tuple<string, string>("20FF", "0E8D"),
			new Tuple<string, string>("6000", "1004"),
			new Tuple<string, string>("0006", "22D9"),
			new Tuple<string, string>("7523", "1a86"),
			new Tuple<string, string>("0006", "22D9"),
			new Tuple<string, string>("F200", "0FCE"),
			new Tuple<string, string>("D1E9", "0FCE"),
			new Tuple<string, string>("D1E2", "0FCE"),
			new Tuple<string, string>("D1EC", "0FCE"),
			new Tuple<string, string>("D1DD", "0FCE")
		};
		GStruct96 result = CEB48FBB(list_);
		if (!Class607.B630A78B.object_0[1205](result.string_0))
		{
			return result;
		}
		return default(GStruct96);
	}

	public static GStruct96 AD0AE925(int FF1D5F0E)
	{
		List<Tuple<string, string>> list_ = new List<Tuple<string, string>>
		{
			new Tuple<string, string>("3609", "12D1")
		};
		Stopwatch object_ = Class607.B630A78B.object_0[681]();
		Class607.B630A78B.object_0[887](object_);
		do
		{
			GStruct96 result = CEB48FBB(list_);
			if (!Class607.B630A78B.object_0[1205](result.string_0))
			{
				return result;
			}
		}
		while (Class607.B630A78B.object_0[1097](object_) < FF1D5F0E);
		return default(GStruct96);
	}

	public static GStruct96 D59A0F08()
	{
		List<Tuple<string, string>> list_ = new List<Tuple<string, string>>
		{
			new Tuple<string, string>("3609", "12D1")
		};
		GStruct96 result = CEB48FBB(list_);
		if (!Class607.B630A78B.object_0[1205](result.string_0))
		{
			return result;
		}
		return default(GStruct96);
	}

	public static GStruct96 A9AAD123(int int_0)
	{
		List<Tuple<string, string>> list_ = new List<Tuple<string, string>>
		{
			new Tuple<string, string>("9008", "05c6"),
			new Tuple<string, string>("900e", "05c6"),
			new Tuple<string, string>("9025", "05c6"),
			new Tuple<string, string>("9062", "1199"),
			new Tuple<string, string>("9070", "1199"),
			new Tuple<string, string>("9090", "1199"),
			new Tuple<string, string>("68e0", "0846"),
			new Tuple<string, string>("0076", "19d2")
		};
		Stopwatch stopwatch = Class607.B630A78B.object_0[681]();
		try
		{
			Class607.B630A78B.object_0[887](stopwatch);
			while (Class607.B630A78B.object_0[1097](stopwatch) < int_0)
			{
				if (!GClass112.E31AABAB)
				{
					GStruct96 result = CEB48FBB(list_);
					if (!Class607.B630A78B.object_0[1205](result.string_0))
					{
						return result;
					}
					continue;
				}
				throw Class607.B630A78B.object_0[778]("stopped by user");
			}
		}
		catch (Exception)
		{
		}
		finally
		{
			GClass112.E31AABAB = false;
			Class607.B630A78B.object_0[994](stopwatch);
			Class607.B630A78B.object_0[221](stopwatch);
		}
		return default(GStruct96);
	}
}
