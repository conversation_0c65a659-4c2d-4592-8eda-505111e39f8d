using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Net;
using System.Net.Http;
using System.Net.Security;
using System.Runtime.CompilerServices;
using System.Security.Cryptography.X509Certificates;
using System.Windows.Forms;

public class F3A6731B : Form
{
	[CompilerGenerated]
	private sealed class F1AE0FB8 : IAsyncStateMachine
	{
		public int D1831C96;

		public AsyncVoidMethodBuilder asyncVoidMethodBuilder_0;

		public F3A6731B f3A6731B_0;

		private string string_0;

		private string F980419A;

		private Exception C8A476AB;

		private string C2B84EA1;

		private string string_1;

		private string string_2;

		private string string_3;

		private long long_0;

		private GStruct91 CD825894;

		private string string_4;

		private string CF0E8CB7;

		private StringContent stringContent_0;

		private HttpResponseMessage D58E902C;

		private long long_1;

		private HttpResponseMessage E5922F20;

		private string FB23E42C;

		private string E81D1485;

		private string[] F8AE6600;

		private string string_5;

		private long BC31D6AB;

		private string string_6;

		private string string_7;

		private string string_8;

		private TaskAwaiter<string> taskAwaiter_0;

		private TaskAwaiter<long> taskAwaiter_1;

		private TaskAwaiter<HttpResponseMessage> taskAwaiter_2;

		public F1AE0FB8()
		{
			Class607.B630A78B.object_0[571](this);
		}

		unsafe void IAsyncStateMachine.MoveNext()
		{
			int num = D1831C96;
			try
			{
				if (num != 0)
				{
					if ((uint)(num - 1) <= 3u)
					{
						goto IL_01a3;
					}
					Class607.B630A78B.object_0[24](SecurityProtocolType.Tls12);
					Class607.B630A78B.object_0[203](9999);
				}
				try
				{
					TaskAwaiter<string> awaiter;
					if (num != 0)
					{
						awaiter = GClass112.FCAC8C37(f3A6731B_0.httpClient_0).GetAwaiter();
						if (!awaiter.IsCompleted)
						{
							num = 0;
							D1831C96 = 0;
							taskAwaiter_0 = awaiter;
							F1AE0FB8 stateMachine = this;
							asyncVoidMethodBuilder_0.AwaitUnsafeOnCompleted(ref awaiter, ref stateMachine);
							return;
						}
					}
					else
					{
						awaiter = taskAwaiter_0;
						taskAwaiter_0 = default(TaskAwaiter<string>);
						num = -1;
						D1831C96 = -1;
					}
					F980419A = awaiter.GetResult();
					string_0 = F980419A;
					F980419A = null;
					if (Class607.B630A78B.object_0[121](string_0, D09F0D3B.smethod_5(1161364u)))
					{
						GClass110.C2AB1F9F("you are using old version, Please download new version", "version", MessageBoxButtons.OK, MessageBoxIcon.Hand, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
						Class607.B630A78B.object_0[958](0);
						Class607.B630A78B.object_0[982](Class607.B630A78B.object_0[955]());
					}
					string_0 = null;
				}
				catch (Exception c8A476AB)
				{
					C8A476AB = c8A476AB;
					GClass110.C2AB1F9F(Class607.B630A78B.object_0[1160](C8A476AB), "version", MessageBoxButtons.OK, MessageBoxIcon.Hand, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
					Class607.B630A78B.object_0[958](0);
					Class607.B630A78B.object_0[982](Class607.B630A78B.object_0[955]());
				}
				A8957AA1.E1B9383D();
				GClass112.FA1A7E1F();
				goto IL_01a3;
				IL_01a3:
				try
				{
					TaskAwaiter<long> awaiter5;
					TaskAwaiter<HttpResponseMessage> awaiter4;
					TaskAwaiter<string> awaiter3;
					TaskAwaiter<string> awaiter2;
					switch (num)
					{
					default:
						if (C8087599.E41A7B83.RememberMe && !Class607.B630A78B.object_0[1205](C8087599.E41A7B83.email) && !Class607.B630A78B.object_0[1205](C8087599.E41A7B83.password))
						{
							C2B84EA1 = C8087599.E41A7B83.email;
							string_1 = C8087599.E41A7B83.password;
							string_2 = GClass112.smethod_1();
							string_3 = Class607.B630A78B.object_0[995]().ToString();
							awaiter5 = GClass112.smethod_0(f3A6731B_0.httpClient_0).GetAwaiter();
							if (!awaiter5.IsCompleted)
							{
								num = 1;
								D1831C96 = 1;
								taskAwaiter_1 = awaiter5;
								F1AE0FB8 stateMachine = this;
								asyncVoidMethodBuilder_0.AwaitUnsafeOnCompleted(ref awaiter5, ref stateMachine);
								return;
							}
							goto IL_02fe;
						}
						GClass112.smethod_28(3000);
						Class607.B630A78B.object_0[86](f3A6731B_0, Class607.B630A78B.object_0[1210](f3A6731B_0, (nint)__ldftn(F3A6731B.E6024633)));
						goto end_IL_01a3;
					case 1:
						awaiter5 = taskAwaiter_1;
						taskAwaiter_1 = default(TaskAwaiter<long>);
						num = -1;
						D1831C96 = -1;
						goto IL_02fe;
					case 2:
						awaiter4 = taskAwaiter_2;
						taskAwaiter_2 = default(TaskAwaiter<HttpResponseMessage>);
						num = -1;
						D1831C96 = -1;
						goto IL_045f;
					case 3:
						awaiter3 = taskAwaiter_0;
						taskAwaiter_0 = default(TaskAwaiter<string>);
						num = -1;
						D1831C96 = -1;
						goto IL_057b;
					case 4:
						{
							awaiter2 = taskAwaiter_0;
							taskAwaiter_0 = default(TaskAwaiter<string>);
							num = -1;
							D1831C96 = -1;
							goto IL_06d5;
						}
						IL_045f:
						E5922F20 = awaiter4.GetResult();
						D58E902C = E5922F20;
						E5922F20 = null;
						if (Class607.B630A78B.object_0[866](D58E902C) == HttpStatusCode.OK)
						{
							awaiter3 = Class607.B630A78B.object_0[452](Class607.B630A78B.object_0[1232](D58E902C)).GetAwaiter();
							if (!awaiter3.IsCompleted)
							{
								num = 3;
								D1831C96 = 3;
								taskAwaiter_0 = awaiter3;
								F1AE0FB8 stateMachine = this;
								asyncVoidMethodBuilder_0.AwaitUnsafeOnCompleted(ref awaiter3, ref stateMachine);
								return;
							}
							goto IL_057b;
						}
						awaiter2 = Class607.B630A78B.object_0[452](Class607.B630A78B.object_0[1232](D58E902C)).GetAwaiter();
						if (!awaiter2.IsCompleted)
						{
							num = 4;
							D1831C96 = 4;
							taskAwaiter_0 = awaiter2;
							F1AE0FB8 stateMachine = this;
							asyncVoidMethodBuilder_0.AwaitUnsafeOnCompleted(ref awaiter2, ref stateMachine);
							return;
						}
						goto IL_06d5;
						IL_06a1:
						FB23E42C = null;
						E81D1485 = null;
						F8AE6600 = null;
						break;
						IL_06d5:
						string_8 = awaiter2.GetResult();
						string_6 = string_8;
						string_8 = null;
						string_7 = GClass112.B904D2B6.EC2264AF(string_6);
						string_6 = null;
						string_7 = null;
						break;
						IL_057b:
						string_5 = awaiter3.GetResult();
						FB23E42C = string_5;
						string_5 = null;
						E81D1485 = GClass112.B904D2B6.EC2264AF(FB23E42C);
						F8AE6600 = Class538.smethod_0(E81D1485, new char[1] { '|' });
						if (F8AE6600.Length != 3 || !GClass112.B904D2B6.method_1(F8AE6600[0], F8AE6600[1]) || !Class607.B630A78B.object_0[787](F8AE6600[0], D09F0D3B.smethod_5(1228830u)))
						{
							goto IL_06a1;
						}
						BC31D6AB = Class607.B630A78B.object_0[588](F8AE6600[2]);
						if (Class607.B630A78B.object_0[893](BC31D6AB - long_0) > 30L)
						{
							goto IL_06a1;
						}
						Class607.B630A78B.object_0[86](f3A6731B_0, Class607.B630A78B.object_0[1210](f3A6731B_0, (nint)__ldftn(F3A6731B.method_6)));
						goto end_IL_01a3;
						IL_02fe:
						long_1 = awaiter5.GetResult();
						long_0 = long_1;
						CD825894 = new GStruct91
						{
							Usuario = C2B84EA1,
							Password = string_1,
							HWID = string_2,
							Nonce = string_3,
							Timestamp = long_0
						};
						string_4 = GClass112.B904D2B6.method_0(Class607.B630A78B.object_0[387](CD825894));
						CF0E8CB7 = Class607.B630A78B.object_0[387](string_4);
						stringContent_0 = Class607.B630A78B.object_0[1172](CF0E8CB7, Class607.B630A78B.object_0[1124](), "application/json");
						awaiter4 = Class607.B630A78B.object_0[616](f3A6731B_0.httpClient_0, D09F0D3B.smethod_5(1224530u), (HttpContent)(object)stringContent_0).GetAwaiter();
						if (!awaiter4.IsCompleted)
						{
							num = 2;
							D1831C96 = 2;
							taskAwaiter_2 = awaiter4;
							F1AE0FB8 stateMachine = this;
							asyncVoidMethodBuilder_0.AwaitUnsafeOnCompleted(ref awaiter4, ref stateMachine);
							return;
						}
						goto IL_045f;
					}
					Class607.B630A78B.object_0[86](f3A6731B_0, Class607.B630A78B.object_0[1210](f3A6731B_0, (nint)__ldftn(F3A6731B.method_7)));
					C2B84EA1 = null;
					string_1 = null;
					string_2 = null;
					string_3 = null;
					CD825894 = default(GStruct91);
					string_4 = null;
					CF0E8CB7 = null;
					stringContent_0 = null;
					D58E902C = null;
					end_IL_01a3:;
				}
				catch
				{
					Class607.B630A78B.object_0[86](f3A6731B_0, Class607.B630A78B.object_0[1210](f3A6731B_0, (nint)__ldftn(F3A6731B.method_8)));
				}
			}
			catch (Exception c8A476AB)
			{
				D1831C96 = -2;
				Class607.B630A78B.object_0[370](ref asyncVoidMethodBuilder_0, c8A476AB);
				return;
			}
			D1831C96 = -2;
			Class607.B630A78B.object_0[961](ref asyncVoidMethodBuilder_0);
		}

		[DebuggerHidden]
		private void DC867594(IAsyncStateMachine stateMachine)
		{
		}

		void IAsyncStateMachine.SetStateMachine(IAsyncStateMachine stateMachine)
		{
			//ILSpy generated this explicit interface implementation from .override directive in DC867594
			this.DC867594(stateMachine);
		}
	}

	private readonly HttpClient httpClient_0;

	private IContainer icontainer_0 = null;

	private Label label_0;

	private Label label_1;

	private PictureBox pictureBox_0;

	private Label label_2;

	private Button button_0;

	private Label FB1BE580;

	private Label label_3;

	public F3A6731B()
	{
		Class607.B630A78B.object_0[1016](this);
		method_5();
		new GClass101().method_1(this);
		HttpClientHandler obj = Class607.B630A78B.object_0[790]();
		Class345.smethod_0(obj, method_0);
		HttpClientHandler httpMessageHandler_ = obj;
		HttpClient object_ = Class607.B630A78B.object_0[290]((HttpMessageHandler)(object)httpMessageHandler_);
		Class448.smethod_0(object_, Class607.B630A78B.object_0[938](30.0));
		httpClient_0 = object_;
	}

	private bool method_0(object object_0, X509Certificate x509Certificate_0, X509Chain x509Chain_0, SslPolicyErrors sslPolicyErrors_0)
	{
		return (bool)new GClass128().DFB12B0F(new object[5] { this, object_0, x509Certificate_0, x509Chain_0, sslPolicyErrors_0 }, 1136410);
	}

	private void method_1(object sender, EventArgs e)
	{
	}

	[DebuggerStepThrough]
	[AsyncStateMachine(typeof(F1AE0FB8))]
	public void method_2()
	{
		new GClass128().E8AA1C3C(new object[1] { this }, 254954);
	}

	private void method_3(object sender, EventArgs e)
	{
		new GClass128().E8AA1C3C(new object[3] { this, sender, e }, 210330);
	}

	private void method_4(object sender, EventArgs e)
	{
		new GClass128().DFB12B0F(new object[3] { this, sender, e }, 1280343);
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && icontainer_0 != null)
		{
			icontainer_0.Dispose();
		}
		Class607.B630A78B.object_0[1072](this, disposing);
	}

	private unsafe void method_5()
	{
		ComponentResourceManager object_ = Class607.B630A78B.object_0[566](Class607.B630A78B.object_0[6](typeof(F3A6731B).TypeHandle));
		label_0 = Class607.B630A78B.object_0[113]();
		label_1 = Class607.B630A78B.object_0[113]();
		label_2 = Class607.B630A78B.object_0[113]();
		button_0 = Class607.B630A78B.object_0[1112]();
		pictureBox_0 = Class607.B630A78B.object_0[1114]();
		FB1BE580 = Class607.B630A78B.object_0[113]();
		label_3 = Class607.B630A78B.object_0[113]();
		Class607.B630A78B.object_0[361](pictureBox_0);
		Class607.B630A78B.object_0[550](this);
		Class607.B630A78B.object_0[11](label_0, E704E9A1: true);
		Class607.B630A78B.object_0[1267](label_0, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 10f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[326](label_0, Class607.B630A78B.object_0[235](33, 33, 33));
		Class607.B630A78B.object_0[388](label_0, Class607.B630A78B.object_0[760](131, 38));
		Class607.B630A78B.object_0[689](label_0, "label1");
		Class607.B630A78B.object_0[1276](label_0, Class607.B630A78B.object_0[174](221, 17));
		Class607.B630A78B.object_0[334](label_0, 6);
		Class607.B630A78B.object_0[1175](label_0, "Solutions Permantent and Bypass");
		Class607.B630A78B.object_0[11](label_1, E704E9A1: true);
		Class607.B630A78B.object_0[1267](label_1, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[326](label_1, Class607.B630A78B.object_0[235](33, 33, 33));
		Class607.B630A78B.object_0[388](label_1, Class607.B630A78B.object_0[760](12, 241));
		Class607.B630A78B.object_0[689](label_1, "label2");
		Class607.B630A78B.object_0[1276](label_1, Class607.B630A78B.object_0[174](364, 15));
		Class607.B630A78B.object_0[334](label_1, 7);
		Class607.B630A78B.object_0[1175](label_1, "Designed for permanent multi-brand MDM/FRP Bypass & Removal");
		Class607.B630A78B.object_0[355](label_1, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[11](label_2, E704E9A1: true);
		Class607.B630A78B.object_0[823](label_2, Class607.B630A78B.object_0[300]());
		Class607.B630A78B.object_0[1267](label_2, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[326](label_2, Class607.B630A78B.object_0[585]());
		Class607.B630A78B.object_0[388](label_2, Class607.B630A78B.object_0[760](119, 332));
		Class607.B630A78B.object_0[689](label_2, "AllRightReserved");
		Class607.B630A78B.object_0[1276](label_2, Class607.B630A78B.object_0[174](246, 13));
		Class607.B630A78B.object_0[334](label_2, 9);
		Class607.B630A78B.object_0[1175](label_2, "All right reserved Moto KingProTeam 2024");
		Class607.B630A78B.object_0[1242](button_0, AnchorStyles.Top | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_0, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](button_0), Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[205](Class607.B630A78B.object_0[227](button_0), 0);
		Class607.B630A78B.object_0[807](Class607.B630A78B.object_0[227](button_0), Class607.B630A78B.object_0[300]());
		Class607.B630A78B.object_0[1214](Class607.B630A78B.object_0[227](button_0), Class607.B630A78B.object_0[300]());
		Class607.B630A78B.object_0[540](button_0, FlatStyle.Flat);
		Class607.B630A78B.object_0[953](button_0, A282DB38.close_window);
		Class607.B630A78B.object_0[388](button_0, Class607.B630A78B.object_0[760](475, 5));
		Class607.B630A78B.object_0[689](button_0, "BtnClose");
		Class607.B630A78B.object_0[1276](button_0, Class607.B630A78B.object_0[174](27, 25));
		Class607.B630A78B.object_0[334](button_0, 10);
		Class607.B630A78B.object_0[270](button_0, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_0, Class607.B630A78B.object_0[935](this, (nint)__ldftn(F3A6731B.method_4)));
		Class607.B630A78B.object_0[148](pictureBox_0, A282DB38.loading);
		Class607.B630A78B.object_0[388](pictureBox_0, Class607.B630A78B.object_0[760](122, 74));
		Class607.B630A78B.object_0[689](pictureBox_0, "pictureBox1");
		Class607.B630A78B.object_0[1276](pictureBox_0, Class607.B630A78B.object_0[174](239, 164));
		Class607.B630A78B.object_0[606](pictureBox_0, PictureBoxSizeMode.Zoom);
		Class607.B630A78B.object_0[831](pictureBox_0, 8);
		Class607.B630A78B.object_0[573](pictureBox_0, FF38159D: false);
		Class607.B630A78B.object_0[11](FB1BE580, E704E9A1: true);
		Class607.B630A78B.object_0[1267](FB1BE580, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[326](FB1BE580, Class607.B630A78B.object_0[235](33, 33, 33));
		Class607.B630A78B.object_0[388](FB1BE580, Class607.B630A78B.object_0[760](12, 265));
		Class607.B630A78B.object_0[689](FB1BE580, "label3");
		Class607.B630A78B.object_0[1276](FB1BE580, Class607.B630A78B.object_0[174](205, 15));
		Class607.B630A78B.object_0[334](FB1BE580, 11);
		Class607.B630A78B.object_0[1175](FB1BE580, "assisting businesses in phone reuse");
		Class607.B630A78B.object_0[355](FB1BE580, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[11](label_3, E704E9A1: true);
		Class607.B630A78B.object_0[1267](label_3, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[326](label_3, Class607.B630A78B.object_0[235](33, 33, 33));
		Class607.B630A78B.object_0[388](label_3, Class607.B630A78B.object_0[760](12, 289));
		Class607.B630A78B.object_0[689](label_3, "label4");
		Class607.B630A78B.object_0[1276](label_3, Class607.B630A78B.object_0[174](413, 15));
		Class607.B630A78B.object_0[334](label_3, 12);
		Class607.B630A78B.object_0[1175](label_3, "We do not support illicit activities; if performed, it is under your responsibility");
		Class607.B630A78B.object_0[355](label_3, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[1043](this, AutoScaleMode.None);
		Class607.B630A78B.object_0[823](this, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[485](this, Class607.B630A78B.object_0[174](507, 354));
		Class607.B630A78B.object_0[782](this).Add(label_3);
		Class607.B630A78B.object_0[782](this).Add(FB1BE580);
		Class607.B630A78B.object_0[782](this).Add(button_0);
		Class607.B630A78B.object_0[782](this).Add(label_2);
		Class607.B630A78B.object_0[782](this).Add(label_0);
		Class607.B630A78B.object_0[782](this).Add(label_1);
		Class607.B630A78B.object_0[782](this).Add(pictureBox_0);
		Class607.B630A78B.object_0[591](this, FormBorderStyle.None);
		Class607.B630A78B.object_0[376](this, (Icon)Class607.B630A78B.object_0[656](object_, "$this.Icon"));
		Class607.B630A78B.object_0[690](this, "SplashScreen");
		Class607.B630A78B.object_0[1221](this, FormStartPosition.CenterScreen);
		Class607.B630A78B.object_0[1175](this, "SplashScreen");
		Class607.B630A78B.object_0[161](this, Class607.B630A78B.object_0[935](this, (nint)__ldftn(F3A6731B.method_1)));
		Class607.B630A78B.object_0[15](this, Class607.B630A78B.object_0[935](this, (nint)__ldftn(F3A6731B.method_3)));
		Class607.B630A78B.object_0[1040](pictureBox_0);
		Class607.B630A78B.object_0[1150](this, bool_0: false);
		Class607.B630A78B.object_0[64](this);
	}

	[CompilerGenerated]
	private void method_6()
	{
		Class607.B630A78B.object_0[605](new GForm0());
		Class607.B630A78B.object_0[813](this);
	}

	[CompilerGenerated]
	private void method_7()
	{
		Class607.B630A78B.object_0[605](new E0B17C8A());
		Class607.B630A78B.object_0[813](this);
	}

	[CompilerGenerated]
	private void E6024633()
	{
		Class607.B630A78B.object_0[605](new E0B17C8A());
		Class607.B630A78B.object_0[813](this);
	}

	[CompilerGenerated]
	private void method_8()
	{
		Class607.B630A78B.object_0[813](this);
		Class607.B630A78B.object_0[605](new E0B17C8A());
	}
}
