using System;

internal class EBBF449F : GInterface0
{
	public const ushort ushort_0 = 61267;

	public const uint uint_0 = 0u;

	public ushort C112653E;

	public uint uint_1;

	public uint uint_2;

	public uint D2B2518C;

	public uint B4BA2E9E;

	public D89D56BE d89D56BE_0;

	public uint F503EB33;

	public uint uint_3;

	public byte E3984A1A;

	public uint A094DBB1;

	public ushort A3AB621D;

	public ushort ushort_1;

	public ushort ushort_2;

	public byte byte_0;

	public uint B283BE98;

	public ushort ushort_3;

	public uint uint_4;

	public uint D98A95AD;

	public uint uint_5;

	public uint uint_6;

	public uint uint_7;

	public uint uint_8;

	public uint uint_9;

	public uint EA36B214;

	public uint[] uint_10;

	public Enum0 A381CC37;

	public uint BE97D92D;

	public ushort ushort_4;

	public uint EA825A24;

	public uint[] uint_11;

	public uint uint_12;

	public uint uint_13;

	public Guid guid_0;

	public uint uint_14;

	public string string_0;

	public uint A23CB288;

	public uint uint_15;

	public uint uint_16;

	public byte byte_1;

	public uint uint_17;

	public ushort C8340584;

	public ushort ushort_5;

	public ushort ushort_6;

	public ushort ushort_7;

	public uint uint_18;

	public ushort ushort_8;

	public uint uint_19;

	public ulong ulong_0;

	public ushort ushort_9;

	public byte C79F3F1D;

	public ushort ushort_10;

	public uint uint_20;

	public Enum1 enum1_0;

	public uint uint_21;

	public uint uint_22;

	public uint uint_23;

	public ushort ushort_11;

	public Guid guid_1;

	public string B02BA800;

	public ushort ushort_12;

	public uint E8B2FD26;

	public bool Boolean_0
	{
		get
		{
			if ((A381CC37 & Enum0.SixtyFourBit) == Enum0.SixtyFourBit)
			{
				return ushort_2 >= 64;
			}
			return false;
		}
	}

	public uint DB3035A7 => (uint)(1024 << (int)uint_15);

	public int DD8D5B35 => 1024;

	public int B0B6FA3F(byte[] BF101B2F, int DAB6249E)
	{
		C8340584 = GClass3.smethod_11(BF101B2F, DAB6249E + 56);
		if (C8340584 != 61267)
		{
			return DD8D5B35;
		}
		BE97D92D = GClass3.E038002D(BF101B2F, DAB6249E);
		uint_1 = GClass3.E038002D(BF101B2F, DAB6249E + 4);
		uint_21 = GClass3.E038002D(BF101B2F, DAB6249E + 8);
		uint_8 = GClass3.E038002D(BF101B2F, DAB6249E + 12);
		EA36B214 = GClass3.E038002D(BF101B2F, DAB6249E + 16);
		uint_4 = GClass3.E038002D(BF101B2F, DAB6249E + 20);
		uint_15 = GClass3.E038002D(BF101B2F, DAB6249E + 24);
		uint_16 = GClass3.E038002D(BF101B2F, DAB6249E + 28);
		D2B2518C = GClass3.E038002D(BF101B2F, DAB6249E + 32);
		uint_7 = GClass3.E038002D(BF101B2F, DAB6249E + 36);
		EA825A24 = GClass3.E038002D(BF101B2F, DAB6249E + 40);
		uint_19 = GClass3.E038002D(BF101B2F, DAB6249E + 44);
		E8B2FD26 = GClass3.E038002D(BF101B2F, DAB6249E + 48);
		ushort_8 = GClass3.smethod_11(BF101B2F, DAB6249E + 52);
		ushort_5 = GClass3.smethod_11(BF101B2F, DAB6249E + 54);
		ushort_11 = GClass3.smethod_11(BF101B2F, DAB6249E + 58);
		ushort_3 = GClass3.smethod_11(BF101B2F, DAB6249E + 60);
		ushort_7 = GClass3.smethod_11(BF101B2F, DAB6249E + 62);
		uint_14 = GClass3.E038002D(BF101B2F, DAB6249E + 64);
		B4BA2E9E = GClass3.E038002D(BF101B2F, DAB6249E + 68);
		uint_3 = GClass3.E038002D(BF101B2F, DAB6249E + 72);
		uint_23 = GClass3.E038002D(BF101B2F, DAB6249E + 76);
		ushort_1 = GClass3.smethod_11(BF101B2F, DAB6249E + 80);
		A3AB621D = GClass3.smethod_11(BF101B2F, DAB6249E + 82);
		D98A95AD = GClass3.E038002D(BF101B2F, DAB6249E + 84);
		ushort_4 = GClass3.smethod_11(BF101B2F, DAB6249E + 88);
		C112653E = GClass3.smethod_11(BF101B2F, DAB6249E + 90);
		d89D56BE_0 = (D89D56BE)GClass3.E038002D(BF101B2F, DAB6249E + 92);
		A381CC37 = (Enum0)GClass3.E038002D(BF101B2F, DAB6249E + 96);
		enum1_0 = (Enum1)GClass3.E038002D(BF101B2F, DAB6249E + 100);
		guid_1 = GClass3.smethod_17(BF101B2F, DAB6249E + 104);
		B02BA800 = GClass3.CDA41A19(BF101B2F, DAB6249E + 120, 16);
		string_0 = GClass3.CDA41A19(BF101B2F, DAB6249E + 136, 64);
		F503EB33 = GClass3.E038002D(BF101B2F, DAB6249E + 200);
		C79F3F1D = BF101B2F[DAB6249E + 204];
		byte_0 = BF101B2F[DAB6249E + 205];
		B283BE98 = GClass3.smethod_11(BF101B2F, DAB6249E + 206);
		guid_0 = GClass3.smethod_17(BF101B2F, DAB6249E + 208);
		uint_13 = GClass3.E038002D(BF101B2F, DAB6249E + 224);
		uint_12 = GClass3.E038002D(BF101B2F, DAB6249E + 228);
		A23CB288 = GClass3.E038002D(BF101B2F, DAB6249E + 232);
		uint_10 = new uint[4];
		uint_10[0] = GClass3.E038002D(BF101B2F, DAB6249E + 236);
		uint_10[1] = GClass3.E038002D(BF101B2F, DAB6249E + 240);
		uint_10[2] = GClass3.E038002D(BF101B2F, DAB6249E + 244);
		uint_10[3] = GClass3.E038002D(BF101B2F, DAB6249E + 248);
		E3984A1A = BF101B2F[DAB6249E + 252];
		ushort_2 = GClass3.smethod_11(BF101B2F, DAB6249E + 254);
		A094DBB1 = GClass3.E038002D(BF101B2F, DAB6249E + 256);
		uint_5 = GClass3.E038002D(BF101B2F, DAB6249E + 260);
		uint_18 = GClass3.E038002D(BF101B2F, DAB6249E + 264);
		uint_11 = new uint[17];
		for (int i = 0; i < 17; i++)
		{
			uint_11[i] = GClass3.E038002D(BF101B2F, DAB6249E + 268 + 4 * i);
		}
		uint_2 = GClass3.E038002D(BF101B2F, DAB6249E + 336);
		uint_22 = GClass3.E038002D(BF101B2F, DAB6249E + 340);
		uint_9 = GClass3.E038002D(BF101B2F, DAB6249E + 344);
		ushort_6 = GClass3.smethod_11(BF101B2F, DAB6249E + 348);
		ushort_12 = GClass3.smethod_11(BF101B2F, DAB6249E + 350);
		uint_6 = GClass3.E038002D(BF101B2F, DAB6249E + 352);
		ushort_10 = GClass3.smethod_11(BF101B2F, DAB6249E + 356);
		ushort_9 = GClass3.smethod_11(BF101B2F, DAB6249E + 358);
		ulong_0 = GClass3.smethod_12(BF101B2F, DAB6249E + 360);
		uint_20 = GClass3.E038002D(BF101B2F, DAB6249E + 368);
		byte_1 = BF101B2F[DAB6249E + 372];
		uint_17 = GClass3.E038002D(BF101B2F, DAB6249E + 584);
		return 1024;
	}

	public void E317CFB8(byte[] FC8FB9BC, int FFAB36B0)
	{
		throw Class607.B630A78B.object_0[260]();
	}

	public EBBF449F()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
