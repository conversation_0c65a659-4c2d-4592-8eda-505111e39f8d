using System;
using System.Windows.Forms;
using MotoKingPro;

namespace MotoKingPro
{
    /// <summary>
    /// Main entry point for MotoKingPro application
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Enable visual styles for modern UI
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            // Set application-wide exception handling
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += Application_ThreadException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            
            try
            {
                // Initialize application settings
                InitializeApplication();
                
                // Run the main form
                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                ShowErrorMessage("Application Startup Error", ex);
            }
        }

        private static void InitializeApplication()
        {
            // Set application title
            Application.ProductName = "MotoKingPro";
            Application.ProductVersion = "1.1.0.0";
            
            // Initialize application settings
            Core.ApplicationSettings.ApplicationPath = Application.StartupPath;
            
            // Create necessary directories
            CreateApplicationDirectories();
        }

        private static void CreateApplicationDirectories()
        {
            try
            {
                string appPath = Core.ApplicationSettings.ApplicationPath;
                
                // Create bin directory structure
                string binPath = System.IO.Path.Combine(appPath, "bin");
                if (!System.IO.Directory.Exists(binPath))
                {
                    System.IO.Directory.CreateDirectory(binPath);
                }
                
                // Create loader directory
                string loaderPath = System.IO.Path.Combine(binPath, "loader");
                if (!System.IO.Directory.Exists(loaderPath))
                {
                    System.IO.Directory.CreateDirectory(loaderPath);
                }
                
                // Create auto-loader directory
                string autoLoaderPath = System.IO.Path.Combine(loaderPath, "auto");
                if (!System.IO.Directory.Exists(autoLoaderPath))
                {
                    System.IO.Directory.CreateDirectory(autoLoaderPath);
                }
                
                // Create logs directory
                string logsPath = System.IO.Path.Combine(appPath, "logs");
                if (!System.IO.Directory.Exists(logsPath))
                {
                    System.IO.Directory.CreateDirectory(logsPath);
                }
                
                // Create temp directory
                string tempPath = System.IO.Path.Combine(appPath, "temp");
                if (!System.IO.Directory.Exists(tempPath))
                {
                    System.IO.Directory.CreateDirectory(tempPath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to create application directories: {ex.Message}");
            }
        }

        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            ShowErrorMessage("Application Thread Exception", e.Exception);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                ShowErrorMessage("Unhandled Domain Exception", ex);
            }
        }

        private static void ShowErrorMessage(string title, Exception ex)
        {
            string message = $"An error occurred in {title}:\n\n" +
                           $"Error: {ex.Message}\n\n" +
                           $"Stack Trace:\n{ex.StackTrace}";
            
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error);
            
            // Log the error
            LogError(title, ex);
        }

        private static void LogError(string title, Exception ex)
        {
            try
            {
                string logPath = System.IO.Path.Combine(Core.ApplicationSettings.ApplicationPath, "logs");
                string logFile = System.IO.Path.Combine(logPath, $"error_{DateTime.Now:yyyyMMdd}.log");
                
                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {title}\n" +
                                $"Message: {ex.Message}\n" +
                                $"Stack Trace: {ex.StackTrace}\n" +
                                $"{"".PadRight(80, '-')}\n";
                
                System.IO.File.AppendAllText(logFile, logEntry);
            }
            catch
            {
                // If logging fails, ignore to prevent recursive errors
            }
        }
    }
}
