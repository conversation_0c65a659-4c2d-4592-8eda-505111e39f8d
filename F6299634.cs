using System.Drawing;

internal sealed class F6299634
{
	internal static void smethod_0(object object_0, Brush brush_0, int E7A74492, int int_0, int AE083A0B, int int_1)
	{
		short num = 0;
		do
		{
			Class607.B630A78B.object_0[0x2C3 ^ (num ^ 0x93)](object_0, brush_0, E7A74492, int_0, AE083A0B, int_1);
		}
		while ((uint)(2 + ((((uint)num < 2378112301u) ? 1 : 0) - (int)num)) / uint.MaxValue >= 1);
	}
}
