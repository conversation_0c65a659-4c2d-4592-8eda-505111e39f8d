using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Threading.Tasks;

public sealed class GClass108 : IDisposable
{
	[CompilerGenerated]
	private sealed class EB14C0A5
	{
		public byte[] byte_0;

		public EB14C0A5()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool method_0(int AD16073B)
		{
			return AD16073B == byte_0.Length;
		}
	}

	[CompilerGenerated]
	private sealed class AB0463BB : IAsyncStateMachine
	{
		public int FA39E299;

		public AsyncTaskMethodBuilder DDAD3AA3;

		public Stream D9ADDDA8;

		public Stream C8A4C8A2;

		public int int_0;

		public GClass108 gclass108_0;

		private TaskAwaiter A21CFD2A;

		public AB0463BB()
		{
			Class607.B630A78B.object_0[571](this);
		}

		private void D592A798()
		{
			new GClass128().method_68(new object[1] { this }, 460811);
		}

		void IAsyncStateMachine.MoveNext()
		{
			//ILSpy generated this explicit interface implementation from .override directive in D592A798
			this.D592A798();
		}

		[DebuggerHidden]
		void IAsyncStateMachine.SetStateMachine(IAsyncStateMachine E6BD418D)
		{
		}
	}

	[CompilerGenerated]
	private sealed class B324FA88 : IAsyncStateMachine
	{
		public int int_0;

		public AsyncTaskMethodBuilder asyncTaskMethodBuilder_0;

		public Stream stream_0;

		public Stream stream_1;

		public int int_1;

		public GClass108 E094F316;

		private TaskAwaiter DF96002D;

		public B324FA88()
		{
			Class607.B630A78B.object_0[571](this);
		}

		void IAsyncStateMachine.MoveNext()
		{
			new GClass128().C5017C25(new object[1] { this }, 1281054);
		}

		[DebuggerHidden]
		void IAsyncStateMachine.SetStateMachine(IAsyncStateMachine D834E509)
		{
		}
	}

	[CompilerGenerated]
	private sealed class B13B4BB9 : IAsyncStateMachine
	{
		public int int_0;

		public AsyncTaskMethodBuilder EEBAD0B9;

		public Stream FA3C8E21;

		public Stream stream_0;

		public int int_1;

		public GClass108 gclass108_0;

		private byte[] A329353A;

		private byte[] E4B79B16;

		private int C4988C2E;

		private int int_2;

		private int int_3;

		private TaskAwaiter<int> taskAwaiter_0;

		private TaskAwaiter taskAwaiter_1;

		public B13B4BB9()
		{
			Class607.B630A78B.object_0[571](this);
		}

		private void AA8F2E8B()
		{
			int num = int_0;
			try
			{
				TaskAwaiter<int> awaiter3;
				TaskAwaiter awaiter2;
				TaskAwaiter<int> awaiter;
				switch (num)
				{
				default:
					A329353A = new byte[int_1];
					E4B79B16 = new byte[int_1];
					awaiter3 = Class607.B630A78B.object_0[1095](stream_0, A329353A, 0, int_1).GetAwaiter();
					if (!awaiter3.IsCompleted)
					{
						num = 0;
						int_0 = 0;
						taskAwaiter_0 = awaiter3;
						B13B4BB9 stateMachine = this;
						EEBAD0B9.AwaitUnsafeOnCompleted(ref awaiter3, ref stateMachine);
						return;
					}
					goto IL_00af;
				case 0:
					awaiter3 = taskAwaiter_0;
					taskAwaiter_0 = default(TaskAwaiter<int>);
					num = -1;
					int_0 = -1;
					goto IL_00af;
				case 1:
					awaiter2 = taskAwaiter_1;
					taskAwaiter_1 = default(TaskAwaiter);
					num = -1;
					int_0 = -1;
					goto IL_017d;
				case 2:
					{
						awaiter = taskAwaiter_0;
						taskAwaiter_0 = default(TaskAwaiter<int>);
						num = -1;
						int_0 = -1;
						goto IL_01c2;
					}
					IL_017d:
					Class607.B630A78B.object_0[527](ref awaiter2);
					awaiter = Class607.B630A78B.object_0[1095](stream_0, A329353A, 0, int_1).GetAwaiter();
					if (!awaiter.IsCompleted)
					{
						num = 2;
						int_0 = 2;
						taskAwaiter_0 = awaiter;
						B13B4BB9 stateMachine = this;
						EEBAD0B9.AwaitUnsafeOnCompleted(ref awaiter, ref stateMachine);
						return;
					}
					goto IL_01c2;
					IL_00af:
					int_2 = awaiter3.GetResult();
					C4988C2E = int_2;
					goto IL_010d;
					IL_010d:
					if (C4988C2E > 0)
					{
						gclass108_0.method_12(E4B79B16, A329353A, C4988C2E);
						awaiter2 = Class607.B630A78B.object_0[1165](Class607.B630A78B.object_0[108](FA3C8E21, E4B79B16, 0, C4988C2E));
						if (!Class607.B630A78B.object_0[258](ref awaiter2))
						{
							num = 1;
							int_0 = 1;
							taskAwaiter_1 = awaiter2;
							B13B4BB9 stateMachine = this;
							EEBAD0B9.AwaitUnsafeOnCompleted(ref awaiter2, ref stateMachine);
							return;
						}
						goto IL_017d;
					}
					break;
					IL_01c2:
					int_3 = awaiter.GetResult();
					C4988C2E = int_3;
					goto IL_010d;
				}
			}
			catch (Exception exception_)
			{
				int_0 = -2;
				A329353A = null;
				E4B79B16 = null;
				Class607.B630A78B.object_0[468](ref EEBAD0B9, exception_);
				return;
			}
			int_0 = -2;
			A329353A = null;
			E4B79B16 = null;
			Class607.B630A78B.object_0[72](ref EEBAD0B9);
		}

		void IAsyncStateMachine.MoveNext()
		{
			//ILSpy generated this explicit interface implementation from .override directive in AA8F2E8B
			this.AA8F2E8B();
		}

		[DebuggerHidden]
		private void CEA32901(IAsyncStateMachine C48F0BAD)
		{
		}

		void IAsyncStateMachine.SetStateMachine(IAsyncStateMachine C48F0BAD)
		{
			//ILSpy generated this explicit interface implementation from .override directive in CEA32901
			this.CEA32901(C48F0BAD);
		}
	}

	public static readonly int[] int_0;

	public const int int_1 = 16;

	private const int int_2 = 16;

	private const int int_3 = 16;

	private readonly byte[] ******** = new byte[16];

	private readonly ICryptoTransform AAB29438;

	private bool bool_0;

	private readonly bool F323451D;

	public GClass108(byte[] FFB1DB05, byte[] byte_0, bool bool_1 = false)
	{
		EB14C0A5 CS_0024_003C_003E8__locals5 = new EB14C0A5();
		CS_0024_003C_003E8__locals5.byte_0 = FFB1DB05;
		Class607.B630A78B.object_0[571](this);
		if (CS_0024_003C_003E8__locals5.byte_0 == null)
		{
			throw Class607.B630A78B.object_0[875]("Key is null");
		}
		if (!Array.Exists(int_0, (int AD16073B) => AD16073B == CS_0024_003C_003E8__locals5.byte_0.Length))
		{
			throw AC01680E.smethod_0(Class725.smethod_0("Key length must be {0}, {1} or {2} bytes. Actual: {3}", new object[4]
			{
				int_0[0],
				int_0[1],
				int_0[2],
				CS_0024_003C_003E8__locals5.byte_0.Length
			}));
		}
		if (byte_0 == null)
		{
			throw Class607.B630A78B.object_0[875]("Initial counter is null");
		}
		if (16 != byte_0.Length)
		{
			throw Class607.B630A78B.object_0[1005](Class607.B630A78B.object_0[1217]("Initial counter must be {0} bytes", 16));
		}
		bool_0 = false;
		AesManaged aesManaged = Class607.B630A78B.object_0[968]();
		Class631.smethod_0(aesManaged, CipherMode.ECB);
		Class680.FD83388D(aesManaged, PaddingMode.None);
		SymmetricAlgorithm object_ = aesManaged;
		Class607.B630A78B.object_0[1144](byte_0, 0, ********, 0, 16);
		F323451D = bool_1;
		byte[] byte_1 = new byte[16];
		AAB29438 = Class607.B630A78B.object_0[173](object_, CS_0024_003C_003E8__locals5.byte_0, byte_1);
	}

	public void method_0(byte[] byte_0, byte[] byte_1, int int_4)
	{
		new GClass128().method_68(new object[4] { this, byte_0, byte_1, int_4 }, 514028);
	}

	public void method_1(Stream stream_0, Stream F532B605, int B327B633 = 1024)
	{
		new GClass128().AD84C1A2(new object[4] { this, stream_0, F532B605, B327B633 }, 110089);
	}

	[DebuggerStepThrough]
	[AsyncStateMachine(typeof(B324FA88))]
	public Task AAA0080D(Stream stream_0, Stream E7246707, int DF98008E = 1024)
	{
		return (Task)new GClass128().DFB12B0F(new object[4] { this, stream_0, E7246707, DF98008E }, 22561805);
	}

	public void method_2(byte[] EC8FD72A, byte[] byte_0)
	{
		new GClass128().method_68(new object[3] { this, EC8FD72A, byte_0 }, 76216);
	}

	public byte[] method_3(byte[] byte_0, int F18BFF1D)
	{
		return (byte[])new GClass128().method_68(new object[3] { this, byte_0, F18BFF1D }, 22969399);
	}

	public byte[] method_4(byte[] byte_0)
	{
		return (byte[])new GClass128().method_68(new object[2] { this, byte_0 }, 22534938);
	}

	public byte[] method_5(string string_0)
	{
		return (byte[])new GClass128().C5017C25(new object[2] { this, string_0 }, 22574563);
	}

	public void E83D2434(byte[] FA05C5B4, byte[] byte_0, int B40E6400)
	{
		method_12(FA05C5B4, byte_0, B40E6400);
	}

	public void method_6(Stream C2046C93, Stream stream_0, int int_4 = 1024)
	{
		BD3703A2(C2046C93, stream_0, int_4);
	}

	[DebuggerStepThrough]
	[AsyncStateMachine(typeof(AB0463BB))]
	public Task F6B9DD21(Stream stream_0, Stream E5B6D997, int DD817637 = 1024)
	{
		return (Task)new GClass128().B4154402(new object[4] { this, stream_0, E5B6D997, DD817637 }, 22533667);
	}

	public void method_7(byte[] byte_0, byte[] byte_1)
	{
		method_12(byte_0, byte_1, byte_1.Length);
	}

	public byte[] method_8(byte[] byte_0, int int_4)
	{
		return (byte[])new GClass128().E8AA1C3C(new object[3] { this, byte_0, int_4 }, 11125284);
	}

	public byte[] method_9(byte[] byte_0)
	{
		return (byte[])new GClass128().DFB12B0F(new object[2] { this, byte_0 }, 11168854);
	}

	public string method_10(byte[] BF8DDAB0)
	{
		return (string)new GClass128().B4154402(new object[2] { this, BF8DDAB0 }, 525183);
	}

	private void BD3703A2(Stream stream_0, Stream stream_1, int A729B69F = 1024)
	{
		byte[] array = new byte[A729B69F];
		byte[] array2 = new byte[A729B69F];
		int int_;
		while ((int_ = Class607.B630A78B.object_0[53](stream_1, array, 0, A729B69F)) > 0)
		{
			method_12(array2, array, int_);
			Class607.B630A78B.object_0[132](stream_0, array2, 0, int_);
		}
	}

	[DebuggerStepThrough]
	[AsyncStateMachine(typeof(B13B4BB9))]
	private Task method_11(Stream BA034A38, Stream DA05350F, int int_4 = 1024)
	{
		B13B4BB9 stateMachine = new B13B4BB9();
		stateMachine.EEBAD0B9 = Class607.B630A78B.object_0[971]();
		stateMachine.gclass108_0 = this;
		stateMachine.FA3C8E21 = BA034A38;
		stateMachine.stream_0 = DA05350F;
		stateMachine.int_1 = int_4;
		stateMachine.int_0 = -1;
		stateMachine.EEBAD0B9.Start(ref stateMachine);
		return Class607.B630A78B.object_0[449](ref stateMachine.EEBAD0B9);
	}

	private void method_12(byte[] FA8DE29F, byte[] A0A10613, int int_4)
	{
		if (A0A10613 == null)
		{
			throw Class607.B630A78B.object_0[956]("input", "Input cannot be null");
		}
		if (FA8DE29F == null)
		{
			throw Class607.B630A78B.object_0[956]("output", "Output cannot be null");
		}
		if (int_4 >= 0 && int_4 <= A0A10613.Length)
		{
			if (FA8DE29F.Length < int_4)
			{
				throw Class607.B630A78B.object_0[269]("output", Class607.B630A78B.object_0[1217]("Output byte array should be able to take at least {0}", int_4));
			}
			if (bool_0)
			{
				throw Class607.B630A78B.object_0[506]("state", "AES_CTR has already been disposed");
			}
			int num = 0;
			byte[] array = new byte[16];
			while (true)
			{
				if (int_4 <= 0)
				{
					return;
				}
				Class607.B630A78B.object_0[153](AAB29438, ********, 0, 16, array, 0);
				if (F323451D)
				{
					for (int i = 0; i < 16; i++)
					{
						if (++********[i] != 0)
						{
							break;
						}
					}
				}
				else
				{
					int num2 = 15;
					while (num2 >= 0 && ++********[num2] == 0)
					{
						num2--;
					}
				}
				if (int_4 <= 16)
				{
					break;
				}
				for (int j = 0; j < 16; j++)
				{
					FA8DE29F[j + num] = (byte)(A0A10613[j + num] ^ array[j]);
				}
				int_4 -= 16;
				num += 16;
			}
			for (int k = 0; k < int_4; k++)
			{
				FA8DE29F[k + num] = (byte)(A0A10613[k + num] ^ array[k]);
			}
			return;
		}
		throw Class607.B630A78B.object_0[269]("numBytes", "The number of bytes to read must be between [0..input.Length]");
	}

	void object.Finalize()
	{
		try
		{
			C5A9BBB5(bool_1: false);
		}
		finally
		{
			Class607.B630A78B.object_0[897](this);
		}
	}

	public void Dispose()
	{
		C5A9BBB5(bool_1: true);
		Class607.B630A78B.object_0[515](this);
	}

	private void C5A9BBB5(bool bool_1)
	{
		if (!bool_0)
		{
			if (bool_1 && AAB29438 != null)
			{
				AAB29438.Dispose();
			}
			Class607.B630A78B.object_0[979](********, 0, 16);
		}
		bool_0 = true;
	}

	static GClass108()
	{
		int[] array_ = new int[3];
		DEBEDD9C.smethod_0(array_, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int_0 = array_;
	}
}
