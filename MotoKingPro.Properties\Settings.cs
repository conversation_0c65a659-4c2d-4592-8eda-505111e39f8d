using System.CodeDom.Compiler;
using System.Configuration;
using System.Runtime.CompilerServices;

namespace MotoKingPro.Properties;

[GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "11.0.0.0")]
[CompilerGenerated]
internal sealed class Settings : ApplicationSettingsBase
{
	private static Settings A4280B82 = (Settings)Class607.B630A78B.object_0[1048](new Settings());

	public static Settings Default => A4280B82;

	public Settings()
	{
		Class607.B630A78B.object_0[313](this);
	}
}
