using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;

public class GClass25
{
	public class GClass26
	{
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private Dictionary<string, uint> E21AE02D = new Dictionary<string, uint>();

		public E5964FB0 e5964FB0_0;

		public Dictionary<string, uint> A310F688
		{
			[CompilerGenerated]
			get
			{
				return E21AE02D;
			}
			[CompilerGenerated]
			private set
			{
				E21AE02D = value;
			}
		}

		internal uint this[string B63F7936, int B6094688 = 0]
		{
			get
			{
				if (DA177189.TryGetValue(B63F7936, out var value))
				{
					return e5964FB0_0.gclass51_0.EE83B4B7(value + GClass112.C78DEB29.A8054FBA.AB96C4A7 + Class607.B630A78B.object_0[836](B6094688))[0];
				}
				if (A310F688.TryGetValue(B63F7936, out var value2))
				{
					return value2;
				}
				return uint.MaxValue;
			}
			set
			{
				if (DA177189.TryGetValue(string_0, out var value2))
				{
					e5964FB0_0.gclass51_0.BE2E2DAC(value2 + GClass112.C78DEB29.A8054FBA.AB96C4A7 + Class607.B630A78B.object_0[836](int_0), value);
				}
				else
				{
					A310F688[string_0] = value;
				}
			}
		}

		public GClass26(E5964FB0 e5964FB0_1)
		{
			Class607.B630A78B.object_0[571](this);
			e5964FB0_0 = e5964FB0_1;
		}
	}

	[Serializable]
	[CompilerGenerated]
	private sealed class ADAFAB92
	{
		public static readonly ADAFAB92 _003C_003E9 = new ADAFAB92();

		public static Func<char, byte> _003C_003E9__111_0;

		public ADAFAB92()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal byte method_0(char B4AE8D87)
		{
			return (byte)B4AE8D87;
		}
	}

	private const byte byte_0 = 0;

	private const byte byte_1 = 1;

	private const byte B33F252A = 2;

	private const byte C715BB25 = 3;

	private const byte byte_2 = 4;

	private const byte byte_3 = 5;

	private const byte A81B8419 = 8;

	private const byte byte_4 = 9;

	private const byte byte_5 = 10;

	private const byte A6AD08B7 = 11;

	private const byte byte_6 = 12;

	private const byte byte_7 = 13;

	private const byte byte_8 = 14;

	private const byte byte_9 = 15;

	private const byte D707371E = 30;

	private const byte byte_10 = 31;

	private const byte byte_11 = 32;

	private const byte B6A64895 = 33;

	private const byte byte_12 = 34;

	private const byte byte_13 = 35;

	private const byte byte_14 = 36;

	private const byte byte_15 = 37;

	private const byte byte_16 = 38;

	private const byte E7925A90 = 39;

	private const byte byte_17 = 40;

	private const byte E3979726 = 54;

	private const byte EE9108A2 = 116;

	private const byte byte_18 = 117;

	private const byte byte_19 = 118;

	private const byte byte_20 = 119;

	private const byte F11E918C = 120;

	private const byte byte_21 = 121;

	private const byte byte_22 = 122;

	private const byte byte_23 = 123;

	private const byte AC1BE8B8 = 124;

	private const byte byte_24 = 125;

	private const byte byte_25 = 126;

	private const byte byte_26 = 40;

	private const byte A538F61D = 41;

	private const byte byte_27 = 42;

	private const byte CC03F6AB = 43;

	private const byte B88EA785 = 44;

	private const byte EB15752B = 45;

	private const byte byte_28 = 46;

	private const byte C4153D81 = 48;

	private const byte byte_29 = 49;

	private const byte byte_30 = 50;

	private const byte byte_31 = 51;

	private const byte byte_32 = 52;

	private const byte byte_33 = 53;

	private const byte D0A24CA8 = 55;

	private const byte byte_34 = 80;

	private const byte DE8AA9A8 = 81;

	private const byte A09BB0AA = 82;

	private const byte byte_35 = 83;

	private const byte byte_36 = 84;

	private const byte byte_37 = 85;

	private const byte A8A40F2E = 88;

	private const byte byte_38 = 89;

	private const byte BCB15826 = 90;

	private const byte DDBACC37 = 91;

	private const byte byte_39 = 92;

	private const byte byte_40 = 110;

	private const byte byte_41 = 111;

	private const byte byte_42 = 16;

	private const byte byte_43 = 17;

	private const byte byte_44 = 64;

	private const byte byte_45 = 65;

	private const byte byte_46 = 66;

	private const byte D0B62014 = 67;

	private const byte byte_47 = 94;

	private const byte byte_48 = 107;

	private const byte byte_49 = 113;

	private const byte byte_50 = 129;

	private const byte byte_51 = 140;

	private const byte byte_52 = 135;

	private const byte byte_53 = 136;

	private const int EB38D0A6 = 268435456;

	private const int A20C150D = 268435612;

	private static IReadOnlyDictionary<string, ushort> DA177189 = new Dictionary<string, ushort>
	{
		{ "GCPU_REG_CTL", 0 },
		{ "GCPU_REG_MSC", 4 },
		{ "GCPU_UNK1", 32 },
		{ "GCPU_UNK2", 36 },
		{ "GCPU_REG_PC_CTL", 1024 },
		{ "GCPU_REG_MEM_ADDR", 1028 },
		{ "GCPU_REG_MEM_DATA", 1032 },
		{ "GCPU_REG_READ_REG", 1040 },
		{ "GCPU_REG_MONCTL", 1044 },
		{ "GCPU_REG_DRAM_MON", 1048 },
		{ "GCPU_REG_CYC", 1052 },
		{ "GCPU_REG_DRAM_INST_BASE", 1056 },
		{ "GCPU_REG_TRAP_START", 1088 },
		{ "GCPU_REG_TRAP_END", 1144 },
		{ "GCPU_REG_INT_SET", 2048 },
		{ "GCPU_REG_INT_CLR", 2052 },
		{ "GCPU_REG_INT_EN", 2056 },
		{ "GCPU_UNK3", 2060 },
		{ "GCPU_REG_MEM_CMD", 3072 },
		{ "GCPU_REG_MEM_P0", 3076 },
		{ "GCPU_REG_MEM_P1", 3080 },
		{ "GCPU_REG_MEM_P2", 3084 },
		{ "GCPU_REG_MEM_P3", 3088 },
		{ "GCPU_REG_MEM_P4", 3092 },
		{ "GCPU_REG_MEM_P5", 3096 },
		{ "GCPU_REG_MEM_P6", 3100 },
		{ "GCPU_REG_MEM_P7", 3104 },
		{ "GCPU_REG_MEM_P8", 3108 },
		{ "GCPU_REG_MEM_P9", 3112 },
		{ "GCPU_REG_MEM_P10", 3116 },
		{ "GCPU_REG_MEM_P11", 3120 },
		{ "GCPU_REG_MEM_P12", 3124 },
		{ "GCPU_REG_MEM_P13", 3128 },
		{ "GCPU_REG_MEM_P14", 3132 },
		{ "GCPU_REG_MEM_Slot", 3136 }
	};

	private readonly GClass26 gclass26_0;

	public E5964FB0 ********;

	private static byte[] smethod_0(IReadOnlyList<byte> E216EF9C, IReadOnlyList<byte> ireadOnlyList_0, int int_0 = -1)
	{
		if (int_0 == -1)
		{
			int_0 = E216EF9C.Count;
		}
		byte[] array = new byte[int_0];
		for (int i = 0; i < array.Length; i++)
		{
			array[i] = (byte)(E216EF9C[i] ^ ireadOnlyList_0[i]);
		}
		return array;
	}

	public GClass25(E5964FB0 D6A24D9D)
	{
		Class607.B630A78B.object_0[571](this);
		******** = D6A24D9D;
		gclass26_0 = new GClass26(D6A24D9D);
	}

	private void method_0()
	{
		uint num = gclass26_0["GCPU_REG_CTL", 0] & 0xFFFFFFF0u;
		gclass26_0["GCPU_REG_CTL", 0] = num;
		gclass26_0["GCPU_REG_CTL", 0] = num | 0xF;
		num = gclass26_0["GCPU_REG_CTL", 0] & 0xFFFFFFE0u;
		uint value = gclass26_0["GCPU_REG_MSC", 0] | 0x10000;
		gclass26_0["GCPU_REG_CTL", 0] = num;
		gclass26_0["GCPU_REG_MSC", 0] = value;
		gclass26_0["GCPU_REG_CTL", 0] = num | 0x1F;
	}

	internal void method_1()
	{
		new GClass128().method_323(new object[1] { this }, 117648);
	}

	private void C02CA582()
	{
		new GClass128().EF8D5E3B(new object[1] { this }, 22500241);
	}

	private byte[] A10EFB0C(uint CF3DB9A3, int B388ACBB)
	{
		gclass26_0["GCPU_REG_MEM_ADDR", 0] = CF3DB9A3;
		List<uint> list = new List<uint>();
		for (int i = 0; i < B388ACBB / 4; i++)
		{
			list.Add(gclass26_0["GCPU_REG_MEM_ADDR", i * 4]);
		}
		return list.BE13F184();
	}

	private void D822C330(uint uint_0, params byte[] byte_54)
	{
		D73BFE25(uint_0, byte_54.smethod_7());
	}

	private void D73BFE25(uint uint_0, params uint[] A788D48B)
	{
		gclass26_0["GCPU_REG_MEM_ADDR", 0] = 0 | uint_0;
		for (int i = 0; i < A788D48B.Length; i++)
		{
			gclass26_0["GCPU_REG_MEM_DATA", i * 4] = A788D48B[i];
		}
	}

	internal void method_2()
	{
		if (********.int_1 == 33127)
		{
			********.method_34(268435612u, ********.B2290D81(268435612u)[0] | 0x8000000);
			gclass26_0["GCPU_REG_CTL", 0] |= 15u;
			gclass26_0["GCPU_REG_MSC", 0] = (gclass26_0["GCPU_REG_MSC", 0] & 0x7FF0BF7F) | 0x34080;
			gclass26_0["GCPU_REG_CTL", 0] |= 31u;
			gclass26_0["GCPU_REG_MSC", 0] |= 8192u;
			gclass26_0["GCPU_AXI", 0] = 34907u;
			gclass26_0["GCPU_UNK2", 0] &= 4294836221u;
			gclass26_0["GCPU_REG_MEM_ADDR", 0] = 2147491840u;
			gclass26_0["GCPU_REG_INT_CLR", 0] = 1u;
			gclass26_0["GCPU_REG_INT_EN", 0] = 0u;
		}
		else if (new int[2] { 33138, 33063 }.Contains(********.int_1))
		{
			CFBF2310();
			gclass26_0["GCPU_REG_MSC", 0] = gclass26_0["GCPU_REG_MSC", 0] & 0xFFFFDFFFu;
		}
		else if (********.int_1 == 821)
		{
			gclass26_0["GCPU_REG_CTL", 0] = gclass26_0["GCPU_REG_MSC", 0] & 0xFFFFDFFFu;
			gclass26_0["GCPU_REG_CTL", 0] |= 7u;
			gclass26_0["GCPU_REG_MSC", 0] = 2164201472u;
			gclass26_0["GCPU_AXI", 0] = 34943u;
			gclass26_0["GCPU_UNK2", 0] = 0u;
		}
		else if (new int[2] { 33123, 33142 }.Contains(********.int_1))
		{
			********.method_34(268435612u, ********.B2290D81(268435612u)[0] | 0x8000000);
			gclass26_0["GCPU_REG_CTL", 0] &= 4294967280u;
			gclass26_0["GCPU_REG_CTL", 0] |= 15u;
			gclass26_0["GCPU_REG_MSC", 0] |= 65536u;
			gclass26_0["GCPU_REG_CTL", 0] &= 4294967264u;
			gclass26_0["GCPU_REG_MSC", 0] |= 65536u;
			gclass26_0["GCPU_REG_CTL", 0] |= 31u;
			gclass26_0["GCPU_REG_MSC", 0] |= 8192u;
			gclass26_0["GCPU_AXI", 0] = 34907u;
			gclass26_0["GCPU_UNK2", 0] &= 4294836221u;
			gclass26_0["GCPU_REG_MEM_ADDR", 0] = 2147491840u;
			uint[] array = new uint[147];
			DEBEDD9C.smethod_0(array, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			uint[] array2 = array;
			uint[] array3 = array2;
			foreach (uint value in array3)
			{
				gclass26_0["GCPU_REG_MEM_DATA", 0] = value;
			}
			gclass26_0["GCPU_REG_INT_CLR", 0] = 1u;
			gclass26_0["GCPU_REG_INT_EN", 0] = 0u;
		}
		else
		{
			gclass26_0["GCPU_REG_CTL", 0] &= 4294967280u;
			gclass26_0["GCPU_REG_CTL", 0] |= 15u;
			gclass26_0["GCPU_REG_MSC", 0] |= 65536u;
			gclass26_0["GCPU_REG_CTL", 0] &= 4294967264u;
			gclass26_0["GCPU_REG_MSC", 0] |= 65536u;
			gclass26_0["GCPU_REG_CTL", 0] |= 31u;
			gclass26_0["GCPU_REG_MSC", 0] |= 8192u;
		}
	}

	private void CFBF2310()
	{
		if (********.int_1 == 33138 || ********.int_1 == 33063 || ********.int_1 == 0)
		{
			gclass26_0["GCPU_REG_CTL", 0] = gclass26_0["GCPU_REG_CTL", 0] & 0xFFFFFFF0u;
			gclass26_0["GCPU_REG_CTL", 0] |= 15u;
		}
	}

	private void EF358402(uint uint_0)
	{
		gclass26_0["GCPU_REG_PC_CTL", 0] = uint_0;
	}

	private uint A42DEE81(uint uint_0)
	{
		gclass26_0["GCPU_REG_MONCTL", 0] = uint_0;
		return gclass26_0["GCPU_REG_READ_REG", 0];
	}

	private uint[] B43CFF29()
	{
		uint[] array = new uint[32];
		for (uint num = 0u; num < array.Length; num++)
		{
			array[num] = A42DEE81(num);
		}
		return array;
	}

	private void D3A09998(uint uint_0, params uint[] uint_1)
	{
		int num = 0;
		foreach (uint uint_2 in uint_1)
		{
			********.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.AB96C4A7 + DA177189["GCPU_REG_MEM_CMD"] + Class607.B630A78B.object_0[21](num + uint_0 * 4), uint_2);
			num += 4;
		}
	}

	private byte[] method_3(uint uint_0, int int_0)
	{
		List<uint> list = new List<uint>();
		for (int i = 0; i < int_0; i += 4)
		{
			list.Add(********.gclass51_0.EE83B4B7(GClass112.C78DEB29.A8054FBA.AB96C4A7 + DA177189["GCPU_REG_MEM_CMD"] + Class607.B630A78B.object_0[21](i + uint_0 * 4))[0]);
		}
		return list.BE13F184();
	}

	private int method_4(uint B716C6AB, uint uint_0 = 0u, uint[] F519FA04 = null)
	{
		if (F519FA04 != null)
		{
			for (int i = 1; i < 48; i++)
			{
				********.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.AB96C4A7 + DA177189["GCPU_REG_MEM_CMD"] + Class607.B630A78B.object_0[836](i * 4), F519FA04[i]);
			}
		}
		if (********.int_1 == 33127)
		{
			gclass26_0["GCPU_REG_INT_CLR", 0] = 1u;
			gclass26_0["GCPU_REG_INT_EN", 0] = 0u;
		}
		else
		{
			gclass26_0["GCPU_REG_INT_CLR", 0] = 3u;
			gclass26_0["GCPU_REG_INT_EN", 0] = 3u;
		}
		gclass26_0["GCPU_REG_MEM_CMD", 0] = B716C6AB;
		gclass26_0["GCPU_REG_PC_CTL", 0] = uint_0;
		while (gclass26_0["GCPU_REG_INT_SET", 0] != 0)
		{
			Class607.B630A78B.object_0[299](1);
		}
		if ((gclass26_0["GCPU_REG_INT_SET", 0] & 2) != 0)
		{
			if ((gclass26_0["GCPU_REG_INT_SET", 0] & 1) != 0)
			{
				while (gclass26_0["GCPU_REG_INT_SET", 0] != 0)
				{
					Class607.B630A78B.object_0[299](1);
				}
			}
			gclass26_0["GCPU_REG_INT_CLR", 0] = 3u;
			return -1;
		}
		while ((gclass26_0["GCPU_REG_DRAM_MON", 0] & 1) != 0)
		{
			Class607.B630A78B.object_0[299](1);
		}
		gclass26_0["GCPU_REG_INT_CLR", 0] = 3u;
		return 0;
	}

	private int CE292533(bool E92DE5B2 = false, string BA0BF731 = "cbc", bool bool_0 = true)
	{
		byte b716C6AB = 126;
		if (E92DE5B2)
		{
			if (!Class607.B630A78B.object_0[787](BA0BF731, "ecb"))
			{
				if (Class607.B630A78B.object_0[787](BA0BF731, "cbc"))
				{
					b716C6AB = 125;
				}
			}
			else
			{
				b716C6AB = (byte)(bool_0 ? 119u : 121u);
			}
		}
		else if (!Class607.B630A78B.object_0[787](BA0BF731, "ecb"))
		{
			if (Class607.B630A78B.object_0[787](BA0BF731, "cbc"))
			{
				b716C6AB = (byte)(bool_0 ? 126u : 124u);
			}
		}
		else
		{
			b716C6AB = (byte)(bool_0 ? 118u : 120u);
		}
		return method_4(b716C6AB);
	}

	internal byte[] method_5(uint uint_0, bool D71D0F04 = false, uint uint_1 = 18u, uint uint_2 = 26u)
	{
		C7BAB4B6(D71D0F04, uint_0, 0u, 16u, uint_1, uint_2);
		return ********.gclass51_0.EE83B4B7(GClass112.C78DEB29.A8054FBA.AB96C4A7 + DA177189["GCPU_REG_MEM_CMD"] + 104, 4).BE13F184();
	}

	internal void B4A20FA8(uint uint_0, byte[] C3B72F85, byte[] E2B1BE25 = null, bool bool_0 = false)
	{
		uint num = 18u;
		int num2 = 22;
		uint num3 = 26u;
		if (E2B1BE25 == null)
		{
			E2B1BE25 = "4dd12bdf0ec7d26c482490b3482a1b1f".D72BFA2C();
		}
		if (C3B72F85.Length != 16)
		{
			throw Class607.B630A78B.object_0[561]("data must ve 16 bytes", "data");
		}
		List<uint> list = new List<uint>();
		for (int i = 0; i < 4; i++)
		{
			uint num4 = Class607.B630A78B.object_0[1251](C3B72F85, i * 4);
			uint num5 = Class607.B630A78B.object_0[1251](E2B1BE25, i * 4);
			list.Add(num4 ^ num5);
		}
		********.gclass51_0.FF33F413(GClass112.C78DEB29.A8054FBA.AB96C4A7 + DA177189["GCPU_REG_MEM_CMD"] + Class607.B630A78B.object_0[1200](num * 4), new uint[4]);
		********.gclass51_0.FF33F413(GClass112.C78DEB29.A8054FBA.AB96C4A7 + DA177189["GCPU_REG_MEM_CMD"] + Class607.B630A78B.object_0[836](num2 * 4), new uint[4]);
		********.gclass51_0.FF33F413(GClass112.C78DEB29.A8054FBA.AB96C4A7 + DA177189["GCPU_REG_MEM_CMD"] + Class607.B630A78B.object_0[1200](num3 * 4), new uint[8]);
		********.gclass51_0.FF33F413(GClass112.C78DEB29.A8054FBA.AB96C4A7 + DA177189["GCPU_REG_MEM_CMD"] + Class607.B630A78B.object_0[1200](num3 * 4), list.ToArray());
		uint f1135AA = 0u;
		if (********.int_1 == 33138)
		{
			f1135AA = 55368u;
		}
		C7BAB4B6(bool_0, f1135AA, uint_0, 16u, num, num3);
	}

	private byte[] method_6(uint uint_0, int int_0)
	{
		return ********.gclass51_0.EE83B4B7(Class607.B630A78B.object_0[1200](uint_0), int_0).BE13F184();
	}

	internal byte[] method_7(byte[] BE3EEDA8, uint[] uint_0, uint[] uint_1, uint[] uint_2, uint[] uint_3)
	{
		return (byte[])new GClass128().C5017C25(new object[6] { this, BE3EEDA8, uint_0, uint_1, uint_2, uint_3 }, 82656);
	}

	internal byte[] method_8(uint[] uint_0, bool bool_0 = false, uint E40CB632 = 18u, uint EE3D00B8 = 26u, uint B925B20F = 48u)
	{
		method_9(48u);
		D3A09998(E40CB632, uint_0);
		if (bool_0)
		{
			F1283886(B925B20F, E40CB632, EE3D00B8);
			return method_3(EE3D00B8, 16);
		}
		CAB38D27(B925B20F, E40CB632, EE3D00B8);
		return method_3(EE3D00B8, 16);
	}

	private void CAB38D27(uint uint_0, uint A117E8A8, uint uint_1)
	{
		new GClass128().E6A33692(new object[4] { this, uint_0, A117E8A8, uint_1 }, 443470);
	}

	private void F1283886(uint uint_0, uint AD810590, uint uint_1)
	{
		new GClass128().E8AA1C3C(new object[4] { this, uint_0, AD810590, uint_1 }, 22097522);
	}

	private byte[] method_9(uint BB8E77A6)
	{
		gclass26_0["GCPU_REG_MEM_P0", 0] = 88u;
		gclass26_0["GCPU_REG_MEM_P1", 0] = BB8E77A6;
		gclass26_0["GCPU_REG_MEM_P2", 0] = 4u;
		if (method_4(112u) != 0)
		{
			throw Class607.B630A78B.object_0[778]("failed to call the function! LoadHWKey");
		}
		return ********.gclass51_0.EE83B4B7(GClass112.C78DEB29.A8054FBA.AB96C4A7 + DA177189["GCPU_REG_MEM_CMD"] + 104, 4).BE13F184();
	}

	private void C7BAB4B6(bool F2043410, uint F1135AA9, uint ED259489, uint uint_0 = 16u, uint uint_1 = 18u, uint uint_2 = 26u)
	{
		uint num = uint_0 / 16;
		if (uint_0 % 16 != 0)
		{
			num++;
		}
		gclass26_0["GCPU_REG_MEM_P0", 0] = F1135AA9;
		gclass26_0["GCPU_REG_MEM_P1", 0] = ED259489;
		gclass26_0["GCPU_REG_MEM_P2", 0] = num;
		gclass26_0["GCPU_REG_MEM_P4", 0] = uint_1;
		gclass26_0["GCPU_REG_MEM_P5", 0] = uint_2;
		gclass26_0["GCPU_REG_MEM_P6", 0] = uint_2;
		if (CE292533(F2043410) != 0)
		{
			throw Class607.B630A78B.object_0[778]("Failed to call the function!");
		}
	}

	private void D4BE94A7()
	{
		new GClass128().DFB12B0F(new object[1] { this }, 22071729);
	}

	private void method_10(bool bool_0, uint uint_0, uint uint_1, uint uint_2 = 32u)
	{
		gclass26_0["GCPU_REG_CTL", 0] &= 4294967288u;
		gclass26_0["GCPU_REG_CTL", 0] |= 7u;
		gclass26_0["GCPU_REG_MSC", 0] = 2164201472u;
		gclass26_0["GCPU_UNK1", 0] = 34943u;
		gclass26_0["GCPU_UNK2", 0] = 0u;
		gclass26_0["GCPU_UNK3", 0] = uint.MaxValue;
		gclass26_0["GCPU_UNK3", 0] = uint.MaxValue;
		gclass26_0["GCPU_UNK3", 0] = uint.MaxValue;
		gclass26_0["GCPU_UNK3", 0] = 2u;
		gclass26_0["GCPU_REG_MSC", 0] |= 512u;
		if (bool_0)
		{
			gclass26_0["GCPU_REG_MEM_CMD", 0] = 123u;
		}
		else
		{
			gclass26_0["GCPU_REG_MEM_CMD", 0] = 122u;
		}
		gclass26_0["GCPU_REG_MEM_P0", 0] = uint_0;
		gclass26_0["GCPU_REG_MEM_P1", 0] = uint_1;
		gclass26_0["GCPU_REG_MEM_P2", 0] = uint_2 / 16;
		gclass26_0["GCPU_REG_MEM_P3", 0] = 0u;
		gclass26_0["GCPU_REG_MEM_P4", 0] = 0u;
		********.gclass51_0.FF33F413(GClass112.C78DEB29.A8054FBA.AB96C4A7 + DA177189["GCPU_REG_MEM_P5"], new uint[9]);
		gclass26_0["GCPU_REG_PC_CTL", 0] = 0u;
		uint num = uint.MaxValue;
		do
		{
			num = gclass26_0["GCPU_REG_INT_CLR", 0];
		}
		while (num == 0);
		gclass26_0["GCPU_REG_INT_CLR", 0] = num;
		********.gclass51_0.FF33F413(GClass112.C78DEB29.A8054FBA.AB96C4A7 + DA177189["GCPU_REG_MEM_CMD"], new uint[224]);
		gclass26_0["GCPU_REG_INT_EN", 0] = 0u;
		gclass26_0["GCPU_REG_MSC", 0] = 2164135936u;
	}

	internal byte[] B78F723D()
	{
		method_2();
		uint uint_ = 1343857024u;
		uint num = 1343857152u;
		byte[] array = "www.mediatek.com0123456789ABCDEF".Select((char B4AE8D87) => (byte)B4AE8D87).ToArray();
		********.gclass51_0.FF33F413(Class607.B630A78B.object_0[1200](uint_), array.smethod_7());
		method_10(bool_0: true, uint_, num);
		return ********.gclass51_0.EE83B4B7(Class607.B630A78B.object_0[1200](num), 8).BE13F184();
	}

	internal byte[] method_11(bool bool_0 = true, uint DF2D5684 = 19u, uint uint_0 = 19u, uint uint_1 = 48u)
	{
		method_1();
		method_2();
		method_9(uint_1);
		D3A09998(DF2D5684, "4B65796D61737465724D617374657200".D72BFA2C().smethod_7());
		if (bool_0)
		{
			F1283886(uint_1, DF2D5684, uint_0);
			return method_3(uint_0, 16);
		}
		CAB38D27(uint_1, DF2D5684, uint_0);
		return method_3(uint_0, 16);
	}

	private void B6A744B9()
	{
		if (GClass112.C78DEB29.A8054FBA.B3927213 == null)
		{
			return;
		}
		foreach (Tuple<uint, uint> item3 in GClass112.C78DEB29.A8054FBA.B3927213)
		{
			uint item = item3.Item1;
			uint item2 = item3.Item2;
			byte[] c3B72F = new int[4]
			{
				(int)item2,
				0,
				0,
				128
			}.DA9641B1();
			B4A20FA8(item, c3B72F);
		}
	}
}
