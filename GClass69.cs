using System;
using System.Collections.Generic;

public class GClass69 : GClass68
{
	public class F80FA41D : GClass68
	{
		private byte[] byte_0;

		private byte[] byte_1;

		private ulong D19C26AD;

		private ulong ulong_0;

		private ulong ulong_1;

		private string string_0;

		private GClass69 D0B93335;

		private B9AFC595 B60FAC10;

		public byte[] Byte_0 => byte_0;

		public byte[] Byte_1 => byte_1;

		public ulong F22F472A => D19C26AD;

		public ulong FC912134 => ulong_0;

		public ulong UInt64_0 => ulong_1;

		public string E20D92BB => string_0;

		public GClass69 GClass69_0 => D0B93335;

		public B9AFC595 B9AFC595_0 => B60FAC10;

		public static F80FA41D A730AC1D(string CC9A3E0B)
		{
			return new F80FA41D(new ********(CC9A3E0B));
		}

		public F80FA41D(******** e3348281_0, B9AFC595 b9AFC595_0 = null, GClass69 gclass69_0 = null)
			: base(e3348281_0)
		{
			B60FAC10 = b9AFC595_0;
			D0B93335 = gclass69_0;
			FE21B6A0();
		}

		private void FE21B6A0()
		{
			byte_0 = EB99FF1C.method_14(16L);
			byte_1 = EB99FF1C.method_14(16L);
			D19C26AD = EB99FF1C.FA987E85();
			ulong_0 = EB99FF1C.FA987E85();
			ulong_1 = EB99FF1C.FA987E85();
			string_0 = Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[44]("UTF-16LE"), EB99FF1C.method_14(72L));
		}
	}

	public class B9AFC595 : GClass68
	{
		private bool bool_0;

		private List<F80FA41D> list_0;

		private byte[] byte_0;

		private uint uint_0;

		private uint C01DADBC;

		private uint DF0CA397;

		private uint uint_1;

		private ulong ulong_0;

		private ulong ulong_1;

		private ulong ulong_2;

		private ulong ulong_3;

		private byte[] DA34359D;

		private ulong ulong_4;

		private uint A43E4FAE;

		private uint A085A522;

		private uint A3B7CC9D;

		private GClass69 AD07AE31;

		private GClass69 gclass69_0;

		private List<byte[]> DF2E9FBD;

		public List<F80FA41D> List_0
		{
			get
			{
				if (bool_0)
				{
					return list_0;
				}
				******** e3348281_ = BA308583.********_0;
				long e903D = e3348281_.E903D636;
				e3348281_.method_0(Class607.B630A78B.object_0[109](UInt64_2 * Class607.B630A78B.object_0[715](BA308583.Int32_0)));
				DF2E9FBD = new List<byte[]>((int)UInt32_3);
				list_0 = new List<F80FA41D>((int)UInt32_3);
				for (int i = 0; i < UInt32_3; i++)
				{
					DF2E9FBD.Add(e3348281_.method_14(UInt32_4));
					******** e3348281_2 = new ********(DF2E9FBD[DF2E9FBD.Count - 1]);
					list_0.Add(new F80FA41D(e3348281_2, this, AD07AE31));
				}
				e3348281_.method_0(e903D);
				bool_0 = true;
				return list_0;
			}
		}

		public byte[] D32B8A23 => byte_0;

		public uint UInt32_0 => uint_0;

		public uint UInt32_1 => C01DADBC;

		public uint UInt32_2 => DF0CA397;

		public uint E8859616 => uint_1;

		public ulong F304C326 => ulong_0;

		public ulong UInt64_0 => ulong_1;

		public ulong CEA98F93 => ulong_2;

		public ulong UInt64_1 => ulong_3;

		public byte[] Byte_0 => DA34359D;

		public ulong UInt64_2 => ulong_4;

		public uint UInt32_3 => A43E4FAE;

		public uint UInt32_4 => A085A522;

		public uint BDA7F430 => A3B7CC9D;

		public GClass69 BA308583 => AD07AE31;

		public GClass69 GClass69_0 => gclass69_0;

		public List<byte[]> List_1 => DF2E9FBD;

		public static B9AFC595 smethod_0(string BE046313)
		{
			return new B9AFC595(new ********(BE046313));
		}

		public B9AFC595(******** e3348281_0, GClass69 gclass69_1 = null, GClass69 AD07AE31 = null)
			: base(e3348281_0)
		{
			gclass69_0 = gclass69_1;
			this.AD07AE31 = AD07AE31;
			bool_0 = false;
			method_0();
		}

		private void method_0()
		{
			byte_0 = EB99FF1C.method_14(8L);
			byte[] d32B8A = D32B8A23;
			byte[] array_ = new byte[8];
			DEBEDD9C.smethod_0(array_, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			if (********.smethod_2(d32B8A, array_) != 0)
			{
				byte[] array = new byte[8];
				DEBEDD9C.smethod_0(array, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				throw new GException3(array, D32B8A23, base.********_0, "/types/partition_header/seq/0");
			}
			uint_0 = EB99FF1C.F78DC399();
			C01DADBC = EB99FF1C.F78DC399();
			DF0CA397 = EB99FF1C.F78DC399();
			uint_1 = EB99FF1C.F78DC399();
			ulong_0 = EB99FF1C.FA987E85();
			ulong_1 = EB99FF1C.FA987E85();
			ulong_2 = EB99FF1C.FA987E85();
			ulong_3 = EB99FF1C.FA987E85();
			DA34359D = EB99FF1C.method_14(16L);
			ulong_4 = EB99FF1C.FA987E85();
			A43E4FAE = EB99FF1C.F78DC399();
			A085A522 = EB99FF1C.F78DC399();
			A3B7CC9D = EB99FF1C.F78DC399();
		}
	}

	private bool bool_0;

	private int int_0;

	private bool C594EA23;

	private B9AFC595 b9AFC595_0;

	private bool bool_1;

	private B9AFC595 F5B2C117;

	private GClass69 CE849088;

	private GClass68 ********;

	public int Int32_0
	{
		get
		{
			if (bool_0)
			{
				return int_0;
			}
			int_0 = 512;
			bool_0 = true;
			return int_0;
		}
	}

	public B9AFC595 F90A0239
	{
		get
		{
			if (C594EA23)
			{
				return b9AFC595_0;
			}
			******** e3348281_ = GClass69_0.********_0;
			long e903D = e3348281_.E903D636;
			e3348281_.method_0(GClass69_0.Int32_0);
			b9AFC595_0 = new B9AFC595(e3348281_, this, CE849088);
			e3348281_.method_0(e903D);
			C594EA23 = true;
			return b9AFC595_0;
		}
	}

	public B9AFC595 C7A3C4A5
	{
		get
		{
			if (bool_1)
			{
				return F5B2C117;
			}
			******** e3348281_ = GClass69_0.********_0;
			long e903D = e3348281_.E903D636;
			e3348281_.method_0(base.********_0.FE0E00A2 - GClass69_0.Int32_0);
			F5B2C117 = new B9AFC595(e3348281_, this, CE849088);
			e3348281_.method_0(e903D);
			bool_1 = true;
			return F5B2C117;
		}
	}

	public GClass69 GClass69_0 => CE849088;

	public GClass68 GClass68_0 => ********;

	public static GClass69 smethod_0(string string_0)
	{
		return new GClass69(new ********(string_0));
	}

	public static GClass69 smethod_1(byte[] byte_0)
	{
		return new GClass69(new ********(byte_0));
	}

	public GClass69(******** AEA38426, GClass68 ******** = null, GClass69 gclass69_0 = null)
		: base(AEA38426)
	{
		this.******** = ********;
		CE849088 = gclass69_0 ?? this;
		bool_0 = false;
		C594EA23 = false;
		bool_1 = false;
		B72F509C();
	}

	private void B72F509C()
	{
	}
}
