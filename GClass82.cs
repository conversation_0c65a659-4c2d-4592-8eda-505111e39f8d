public class GClass82
{
	public struct GStruct69
	{
		public int int_0;

		public string string_0;

		public int int_1;

		public int int_2;

		public int E28C7203;

		public int E81B0926;

		public int int_3;

		public int A9191187;

		public int BD09F905;
	}

	public string string_0;

	public static string smethod_0(string BA38C217)
	{
		GStruct69 gStruct = new GStruct69
		{
			int_0 = 4,
			string_0 = BA38C217,
			int_1 = 0,
			E28C7203 = 1,
			int_2 = 0,
			E81B0926 = 0,
			int_3 = 8192,
			A9191187 = 1
		};
		return Class725.smethod_0("<?xml version = \"1.0\"?><data><configure MemoryName=\"{0}\" ZLPAwareHost=\"{1}\" SkipStorageInit=\"{2}\" SkipWrite=\"{3}\" MaxPayloadSizeToTargetInBytes=\"{4}\"/></data>", new object[5]
		{
			gStruct.string_0,
			gStruct.E28C7203,
			gStruct.int_2,
			Class607.B630A78B.object_0[1070](ref gStruct.int_1),
			gStruct.int_3
		});
	}

	public static string smethod_1(int int_0)
	{
		return Class607.B630A78B.object_0[1217]("<?xml version=\"1.0\" ?><data><configure AckRawData=\"{0}\"/></data>", int_0);
	}

	public static string smethod_2(uint uint_0, int D914B3A4)
	{
		return Class607.B630A78B.object_0[1105](" <?xml version=\"1.0\" ?><data><peek address64=\"{0}\" SizeInBytes=\"{1}\"/></data>", uint_0, D914B3A4);
	}

	public static string smethod_3(string string_1)
	{
		return Class607.B630A78B.object_0[1140]("<?xml version=\"1.0\" ?><data><getstorageinfo physical_partition_number=\"", string_1, "\"/></data>");
	}

	public static string E4BC3FB4(int int_0, string string_1, int int_1, int int_2)
	{
		return Class607.B630A78B.object_0[1230](" <?xml version=\"1.0\" ?><data><program SECTOR_SIZE_IN_BYTES=\"4096\" filename=\"{0}\" num_partition_sectors=\"{1}\" physical_partition_number=\"0\" start_sector=\"{2}\"/></data>", string_1, int_0, Class607.B630A78B.object_0[1070](ref int_2));
	}

	public static string smethod_4()
	{
		return "<?xml version=\"1.0\" ?><data><setbootablestoragedrive value=\"0\" /></data>";
	}

	public static string smethod_5()
	{
		return "<?xml version=\"1.0\" ?><data><getSecureBootStatus/></data>";
	}

	public static string B98C0815()
	{
		return "<?xml version=\"1.0\" ?><data><getserialnum /></data>";
	}

	public static string smethod_6()
	{
		return "<?xml version=\"1.0\" ?><data><readIMEI len=\"32\"/></data>";
	}

	public static string ABB9FB30()
	{
		return "<?xml version=\"1.0\" ?><data><nop /></data>";
	}

	public static string smethod_7()
	{
		return "<?xml version=\"1.0\" ?><data><power value=\"reset\"/></data>";
	}

	public static string smethod_8(int B4B20D38, int int_0, int int_1, string E896B2A0)
	{
		return Class525.smethod_0(new string[9]
		{
			"<?xml version = \"1.0\"?><data><read SECTOR_SIZE_IN_BYTES=\"",
			Class607.B630A78B.object_0[1070](ref B4B20D38),
			"\" num_partition_sectors=\"",
			Class607.B630A78B.object_0[1070](ref int_0),
			"\" physical_partition_number=\"",
			Class607.B630A78B.object_0[1070](ref int_1),
			"\" start_sector=\"",
			E896B2A0.ToString(),
			"\"/></data>"
		});
	}

	public static string smethod_9(string string_1, string string_2, string string_3, string F595A492)
	{
		return Class525.smethod_0(new string[9]
		{
			"<?xml version = \"1.0\"?><data><program SECTOR_SIZE_IN_BYTES=\"",
			string_1.ToString(),
			"\" file_sector_offset=\"0\" num_partition_sectors=\"",
			string_2.ToString(),
			"\"  physical_partition_number=\"",
			string_3.ToString(),
			"\" start_sector=\"",
			F595A492.ToString(),
			"\"/></data>"
		});
	}

	public static string A2BC229B(string string_1, string F73A7483, string B698CA1E, string string_2, string A5AFAA0A, string string_3, string CCA42222, string FD1101BE)
	{
		return Class525.smethod_0(new string[17]
		{
			"<?xml version = \"1.0\"?><data><patch SECTOR_SIZE_IN_BYTES=\"",
			string_1.ToString(),
			"\" byte_offset=\"",
			F73A7483.ToString(),
			"\" filename=\"",
			B698CA1E.ToString(),
			"\" physical_partition_number=\"",
			string_2.ToString(),
			"\" size_in_bytes=\"",
			A5AFAA0A.ToString(),
			"\" start_sector=\"",
			string_3.ToString(),
			"\" value=\"",
			CCA42222.ToString(),
			"\" what=\"",
			FD1101BE.ToString(),
			"\"/></data>"
		});
	}

	public static string smethod_10(string string_1, string DF3D4724, string string_2, string CDA5A49A)
	{
		return Class525.smethod_0(new string[9]
		{
			"<?xml version = \"1.0\"?><data><erase SECTOR_SIZE_IN_BYTES=\"",
			string_1.ToString(),
			"\" num_partition_sectors=\"",
			DF3D4724.ToString(),
			"\" physical_partition_number=\"",
			string_2.ToString(),
			"\" start_sector=\"",
			CDA5A49A.ToString(),
			"\"/></data>"
		});
	}

	public GClass82()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
