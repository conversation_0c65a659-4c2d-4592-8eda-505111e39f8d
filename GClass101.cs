using System;
using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Windows.Forms;

public class GClass101
{
	[EditorBrowsable(EditorBrowsableState.Never)]
	public struct GStruct92
	{
		public int int_0;

		public int int_1;

		public int A7BB623B;

		public int int_2;
	}

	[DllImport("dwmapi.dll")]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public static extern int DwmExtendFrameIntoClientArea(IntPtr intptr_0, ref GStruct92 gstruct92_0);

	[DllImport("dwmapi.dll")]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public static extern int DwmSetWindowAttribute(IntPtr C292C909, int int_0, ref int int_1, int A2000B1D);

	[DllImport("dwmapi.dll")]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public static extern int DwmIsCompositionEnabled(ref int int_0);

	[EditorBrowsable(EditorBrowsableState.Never)]
	public static bool F33E6A32()
	{
		if (Class607.B630A78B.object_0[310](Class607.B630A78B.object_0[1225](Class607.B630A78B.object_0[176]())) < 6)
		{
			return false;
		}
		DwmIsCompositionEnabled_1(out var bool_);
		return bool_;
	}

	[DllImport("dwmapi.dll", EntryPoint = "DwmIsCompositionEnabled")]
	private static extern int DwmIsCompositionEnabled_1(out bool bool_0);

	[DllImport("Gdi32.dll")]
	private static extern IntPtr CreateRoundRectRgn(int int_0, int int_1, int C2BBB7A3, int int_2, int ********, int AA88FB95);

	private bool method_0()
	{
		if (Class607.B630A78B.object_0[310](Class607.B630A78B.object_0[1225](Class607.B630A78B.object_0[176]())) >= 6)
		{
			int int_ = 0;
			DwmIsCompositionEnabled(ref int_);
			return int_ == 1;
		}
		return false;
	}

	public void method_1(Form BE24C40C)
	{
		int int_ = 2;
		DwmSetWindowAttribute(Class607.B630A78B.object_0[810](BE24C40C), 2, ref int_, 4);
		GStruct92 gstruct92_ = new GStruct92
		{
			int_2 = 1,
			int_0 = 0,
			int_1 = 0,
			A7BB623B = 0
		};
		DwmExtendFrameIntoClientArea(Class607.B630A78B.object_0[810](BE24C40C), ref gstruct92_);
	}

	public GClass101()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
