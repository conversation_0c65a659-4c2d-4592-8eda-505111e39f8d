using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

public class GClass9
{
	[CompilerGenerated]
	private sealed class C689C086
	{
		public string string_0;

		public C689C086()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool A4127A0E(Tuple<string, uint> D88A4330)
		{
			return Class607.B630A78B.object_0[787](D88A4330.Item1, string_0);
		}
	}

	public int int_0;

	public uint uint_0;

	public uint uint_1;

	public GClass10 gclass10_0;

	public GClass11 gclass11_0;

	public GDelegate1 BBB6952A;

	public GDelegate2 gdelegate2_0;

	public List<Tuple<string, uint>> List_0 => new List<Tuple<string, uint>>
	{
		new Tuple<string, uint>("CQDMA_INT_FLAG", 0u),
		new Tuple<string, uint>("CQDMA_INT_EN", 4u),
		new Tuple<string, uint>("CQDMA_EN", 8u),
		new Tuple<string, uint>("CQDMA_RESET", 12u),
		new Tuple<string, uint>("CQDMA_FLUSH", 20u),
		new Tuple<string, uint>("CQDMA_SRC", 28u),
		new Tuple<string, uint>("CQDMA_DST", 32u),
		new Tuple<string, uint>("CQDMA_LEN1", 36u),
		new Tuple<string, uint>("CQDMA_LEN2", 40u),
		new Tuple<string, uint>("CQDMA_SRC2", 96u),
		new Tuple<string, uint>("CQDMA_DST2", 100u)
	};

	public GClass9(GClass11 gclass11_1)
	{
		Class607.B630A78B.object_0[571](this);
		gclass11_0 = gclass11_1;
		int_0 = gclass11_1.int_0;
		uint_0 = gclass11_1.A881F311;
		uint_1 = gclass11_1.uint_5;
		BBB6952A = gclass11_1.gdelegate1_0;
		gdelegate2_0 = gclass11_1.F2B1FB97;
		gclass10_0 = new GClass10(gclass11_1);
	}

	public bool CFBA7B0B(string F8B727B6, List<uint> D9B1C535)
	{
		C689C086 CS_0024_003C_003E8__locals2 = new C689C086();
		CS_0024_003C_003E8__locals2.string_0 = F8B727B6;
		uint num = List_0.Find((Tuple<string, uint> D88A4330) => Class607.B630A78B.object_0[787](D88A4330.Item1, CS_0024_003C_003E8__locals2.string_0)).Item2 + GClass112.C78DEB29.A8054FBA.EB11D8A7;
		return gdelegate2_0(num, D9B1C535);
	}

	public void method_0()
	{
		foreach (Tuple<uint, uint> item in GClass112.C78DEB29.A8054FBA.B3927213)
		{
			method_1(item.Item1, new List<uint> { item.Item2 });
		}
	}

	public void method_1(uint B502870E, List<uint> list_0)
	{
		new List<byte>();
		uint uint_ = GClass112.C78DEB29.A8054FBA.uint_5;
		if (uint_ == 0)
		{
			return;
		}
		using IEnumerator<int> enumerator = Class607.B630A78B.object_0[159](0, list_0.Count).GetEnumerator();
		while (Class607.B630A78B.object_0[212](enumerator))
		{
			int current = enumerator.Current;
			gdelegate2_0(uint_, new List<uint> { list_0[current] });
			CFBA7B0B("CQDMA_SRC", new List<uint> { uint_ });
			CFBA7B0B("CQDMA_DST", new List<uint> { (uint)(B502870E + current * 4) });
			CFBA7B0B("CQDMA_LEN1", new List<uint> { 4u });
			CFBA7B0B("CQDMA_EN", new List<uint> { 1u });
			gdelegate2_0(uint_, new List<uint> { 3405691582u });
		}
	}
}
