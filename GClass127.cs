using System.Diagnostics;
using System.IO;

[DebuggerDisplay("{2+B630A78B.A2A5AFA5()}")]
public class GClass127
{
	private uint uint_0;

	private uint uint_1 = 1u;

	private uint uint_2;

	private Stream stream_0;

	public uint uint_3;

	private uint uint_4;

	private byte[] byte_0;

	public void method_0()
	{
		method_2();
		stream_0 = null;
	}

	public void CF252299(byte byte_1)
	{
		byte_0[uint_2++] = byte_1;
		if (uint_2 >= uint_0)
		{
			method_2();
		}
	}

	public void method_1(uint FA8E821D, uint uint_5)
	{
		uint num = uint_2 - FA8E821D - 1;
		if (num >= uint_0)
		{
			num += uint_0;
		}
		while (uint_5 != 0)
		{
			if (num >= uint_0)
			{
				num = 0u;
			}
			byte_0[uint_2++] = byte_0[num++];
			if (uint_2 >= uint_0)
			{
				method_2();
			}
			uint_5--;
		}
	}

	public byte A01421B2(uint B7BCE52D)
	{
		uint num = uint_2 - B7BCE52D - 1;
		if (num >= uint_0)
		{
			num += uint_0;
		}
		return byte_0[num];
	}

	public void method_2()
	{
		uint num = uint_2 - uint_4;
		if (num != 0)
		{
			stream_0.Write(byte_0, (int)uint_4, (int)num);
			if (uint_2 >= uint_0)
			{
				uint_2 = 0u;
			}
			uint_4 = uint_2;
		}
	}

	public void method_3(Stream stream_1, bool bool_0)
	{
		method_0();
		stream_0 = stream_1;
		if (!bool_0)
		{
			uint_4 = 0u;
			uint_2 = 0u;
			uint_3 = 0u;
		}
	}

	public void CC13C73E(uint FFA38192)
	{
		if (uint_0 != FFA38192)
		{
			byte_0 = new byte[FFA38192];
		}
		uint_0 = FFA38192;
		uint_2 = 0u;
		uint_4 = 0u;
	}
}
