using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.Linq;

namespace MotoKingPro.Core
{
    /// <summary>
    /// Helper class for deobfuscating and cleaning up code
    /// </summary>
    public static class DeobfuscationHelper
    {
        #region String Deobfuscation
        
        /// <summary>
        /// Common obfuscated string patterns and their clean equivalents
        /// </summary>
        private static readonly Dictionary<string, string> ObfuscatedStrings = new Dictionary<string, string>
        {
            // Common obfuscated method names
            { "method_0", "Initialize" },
            { "method_1", "Connect" },
            { "method_2", "Disconnect" },
            { "method_3", "Download" },
            { "method_4", "Upload" },
            { "method_5", "Format" },
            { "method_6", "Reboot" },
            { "method_7", "ReadInfo" },
            { "method_8", "WriteData" },
            { "method_9", "ReadData" },
            
            // Common obfuscated field names
            { "field_0", "deviceHandle" },
            { "field_1", "connectionState" },
            { "field_2", "deviceInfo" },
            { "field_3", "isConnected" },
            { "field_4", "lastError" },
            { "field_5", "timeout" },
            
            // Common obfuscated class names
            { "Class0", "DeviceManager" },
            { "Class1", "ConnectionHandler" },
            { "Class2", "DataProcessor" },
            { "Class3", "ErrorHandler" },
            { "Class4", "ConfigManager" },
            
            // Common obfuscated variable names
            { "var_0", "result" },
            { "var_1", "buffer" },
            { "var_2", "length" },
            { "var_3", "offset" },
            { "var_4", "data" },
            { "var_5", "status" },
            
            // MediaTek specific obfuscated strings
            { "string_0", "MediaTek Device" },
            { "string_1", "Download Mode" },
            { "string_2", "BROM Mode" },
            { "string_3", "Preloader" },
            { "string_4", "Flash Tool" },
            { "string_5", "Authentication" },
            { "string_6", "Scatter File" },
            { "string_7", "ROM Image" },
            
            // Error messages
            { "error_0", "Device not found" },
            { "error_1", "Connection failed" },
            { "error_2", "Authentication failed" },
            { "error_3", "Download failed" },
            { "error_4", "Invalid scatter file" },
            { "error_5", "Device not in download mode" },
        };

        /// <summary>
        /// Deobfuscate a string by replacing known obfuscated patterns
        /// </summary>
        public static string DeobfuscateString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            string result = input;
            
            foreach (var kvp in ObfuscatedStrings)
            {
                result = result.Replace(kvp.Key, kvp.Value);
            }
            
            return result;
        }

        /// <summary>
        /// Clean up method names by removing obfuscation patterns
        /// </summary>
        public static string CleanMethodName(string methodName)
        {
            if (string.IsNullOrEmpty(methodName))
                return methodName;

            // Remove common obfuscation patterns
            var patterns = new[]
            {
                @"^method_\d+$",      // method_0, method_1, etc.
                @"^[a-z]$",           // Single letter methods
                @"^[A-Z]{1,2}\d*$",   // A, B, AA, BB, A1, etc.
                @"^\w{32}$",          // 32-character hash-like names
                @"^[a-f0-9]{8,}$",    // Hex strings
            };

            foreach (var pattern in patterns)
            {
                if (Regex.IsMatch(methodName, pattern))
                {
                    return DeobfuscateString(methodName);
                }
            }

            return methodName;
        }

        /// <summary>
        /// Clean up variable names by removing obfuscation patterns
        /// </summary>
        public static string CleanVariableName(string variableName)
        {
            if (string.IsNullOrEmpty(variableName))
                return variableName;

            // Common obfuscated variable patterns
            var patterns = new Dictionary<string, string>
            {
                { @"^[a-z]$", "temp" },                    // Single letters
                { @"^[a-z]\d+$", "variable" },             // a1, b2, etc.
                { @"^var_\d+$", "variable" },              // var_0, var_1
                { @"^field_\d+$", "field" },               // field_0, field_1
                { @"^[a-f0-9]{8,}$", "data" },             // Hex strings
                { @"^\w{32}$", "buffer" },                 // Hash-like names
            };

            foreach (var kvp in patterns)
            {
                if (Regex.IsMatch(variableName, kvp.Key))
                {
                    return kvp.Value;
                }
            }

            return DeobfuscateString(variableName);
        }
        #endregion

        #region Code Cleaning
        
        /// <summary>
        /// Clean up code by removing unnecessary elements and improving readability
        /// </summary>
        public static string CleanCode(string code)
        {
            if (string.IsNullOrEmpty(code))
                return code;

            var result = code;
            
            // Remove excessive whitespace
            result = Regex.Replace(result, @"\s+", " ");
            
            // Remove empty lines
            result = Regex.Replace(result, @"^\s*$\n", "", RegexOptions.Multiline);
            
            // Clean up braces formatting
            result = Regex.Replace(result, @"\s*{\s*", " {\n    ");
            result = Regex.Replace(result, @"\s*}\s*", "\n}");
            
            // Clean up semicolons
            result = Regex.Replace(result, @"\s*;\s*", ";\n");
            
            return result;
        }

        /// <summary>
        /// Extract meaningful comments from obfuscated code
        /// </summary>
        public static List<string> ExtractComments(string code)
        {
            var comments = new List<string>();
            
            // Single line comments
            var singleLineMatches = Regex.Matches(code, @"//\s*(.+)");
            foreach (Match match in singleLineMatches)
            {
                var comment = match.Groups[1].Value.Trim();
                if (!string.IsNullOrEmpty(comment) && !IsObfuscatedComment(comment))
                {
                    comments.Add(comment);
                }
            }
            
            // Multi-line comments
            var multiLineMatches = Regex.Matches(code, @"/\*\s*(.+?)\s*\*/", RegexOptions.Singleline);
            foreach (Match match in multiLineMatches)
            {
                var comment = match.Groups[1].Value.Trim();
                if (!string.IsNullOrEmpty(comment) && !IsObfuscatedComment(comment))
                {
                    comments.Add(comment);
                }
            }
            
            return comments.Distinct().ToList();
        }

        private static bool IsObfuscatedComment(string comment)
        {
            // Check if comment looks obfuscated
            var obfuscatedPatterns = new[]
            {
                @"^[a-f0-9]{8,}$",        // Hex strings
                @"^\w{32,}$",             // Very long single words
                @"^[A-Z]{10,}$",          // All caps long strings
                @"^\d+$",                 // Just numbers
            };
            
            return obfuscatedPatterns.Any(pattern => Regex.IsMatch(comment, pattern));
        }
        #endregion

        #region Naming Conventions
        
        /// <summary>
        /// Generate meaningful names based on context
        /// </summary>
        public static string GenerateMeaningfulName(string context, string type)
        {
            var contextLower = context?.ToLower() ?? "";
            var typeLower = type?.ToLower() ?? "";
            
            // Method names
            if (typeLower.Contains("method"))
            {
                if (contextLower.Contains("connect")) return "ConnectDevice";
                if (contextLower.Contains("disconnect")) return "DisconnectDevice";
                if (contextLower.Contains("download")) return "DownloadFirmware";
                if (contextLower.Contains("upload")) return "UploadData";
                if (contextLower.Contains("format")) return "FormatDevice";
                if (contextLower.Contains("reboot")) return "RebootDevice";
                if (contextLower.Contains("read")) return "ReadData";
                if (contextLower.Contains("write")) return "WriteData";
                if (contextLower.Contains("init")) return "Initialize";
                if (contextLower.Contains("auth")) return "Authenticate";
            }
            
            // Field names
            if (typeLower.Contains("field"))
            {
                if (contextLower.Contains("handle")) return "deviceHandle";
                if (contextLower.Contains("state")) return "connectionState";
                if (contextLower.Contains("info")) return "deviceInfo";
                if (contextLower.Contains("error")) return "lastError";
                if (contextLower.Contains("timeout")) return "timeoutValue";
                if (contextLower.Contains("buffer")) return "dataBuffer";
                if (contextLower.Contains("size")) return "bufferSize";
                if (contextLower.Contains("offset")) return "dataOffset";
            }
            
            // Class names
            if (typeLower.Contains("class"))
            {
                if (contextLower.Contains("device")) return "DeviceManager";
                if (contextLower.Contains("connect")) return "ConnectionHandler";
                if (contextLower.Contains("data")) return "DataProcessor";
                if (contextLower.Contains("error")) return "ErrorHandler";
                if (contextLower.Contains("config")) return "ConfigurationManager";
                if (contextLower.Contains("auth")) return "AuthenticationManager";
                if (contextLower.Contains("flash")) return "FlashToolManager";
            }
            
            return "Unknown";
        }

        /// <summary>
        /// Convert obfuscated names to PascalCase
        /// </summary>
        public static string ToPascalCase(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            // Split on underscores and capitalize each part
            var parts = input.Split('_', StringSplitOptions.RemoveEmptyEntries);
            var result = new StringBuilder();
            
            foreach (var part in parts)
            {
                if (part.Length > 0)
                {
                    result.Append(char.ToUpper(part[0]));
                    if (part.Length > 1)
                    {
                        result.Append(part.Substring(1).ToLower());
                    }
                }
            }
            
            return result.ToString();
        }

        /// <summary>
        /// Convert obfuscated names to camelCase
        /// </summary>
        public static string ToCamelCase(string input)
        {
            var pascalCase = ToPascalCase(input);
            if (string.IsNullOrEmpty(pascalCase))
                return pascalCase;
                
            return char.ToLower(pascalCase[0]) + pascalCase.Substring(1);
        }
        #endregion

        #region Pattern Recognition
        
        /// <summary>
        /// Identify common code patterns and suggest improvements
        /// </summary>
        public static List<string> IdentifyPatterns(string code)
        {
            var patterns = new List<string>();
            
            // Check for common obfuscation patterns
            if (Regex.IsMatch(code, @"method_\d+"))
            {
                patterns.Add("Contains obfuscated method names");
            }
            
            if (Regex.IsMatch(code, @"field_\d+"))
            {
                patterns.Add("Contains obfuscated field names");
            }
            
            if (Regex.IsMatch(code, @"Class\d+"))
            {
                patterns.Add("Contains obfuscated class names");
            }
            
            if (Regex.IsMatch(code, @"string_\d+"))
            {
                patterns.Add("Contains obfuscated string references");
            }
            
            // Check for MediaTek specific patterns
            if (code.Contains("FlashTool") || code.Contains("BROM") || code.Contains("MediaTek"))
            {
                patterns.Add("Contains MediaTek-specific code");
            }
            
            if (code.Contains("DllImport"))
            {
                patterns.Add("Contains P/Invoke declarations");
            }
            
            if (code.Contains("Marshal"))
            {
                patterns.Add("Contains marshaling code");
            }
            
            return patterns;
        }
        #endregion
    }
}
