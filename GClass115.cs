using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

public class GClass115
{
	private struct Struct1
	{
		internal int C52BA808;

		internal int D68DA590;

		internal int int_0;

		internal Guid guid_0;

		internal short short_0;
	}

	[StructLayout(LayoutKind.Sequential)]
	internal class A12C2637
	{
		public int EA1C24B3;

		public int int_0;

		public int D5BD61A8;

		public A12C2637()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	[StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
	public struct B99CF008
	{
		public int int_0;

		public int DA993B28;

		public int int_1;

		public Guid guid_0;

		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 256)]
		public char[] char_0;
	}

	[CompilerGenerated]
	private sealed class Class70
	{
		public GEnum39 B10AA535;

		public Class70()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool DB975884(GClass120 CE999BB4)
		{
			return CE999BB4.E903E03A != B10AA535;
		}
	}

	public static List<GClass120> list_0 = new List<GClass120>();

	public const int int_0 = 32768;

	public const int int_1 = 32772;

	public const int BF9C76B0 = 537;

	private const int int_2 = 5;

	private static readonly Guid ******** = Class607.B630A78B.object_0[188]("A5DCBF10-6530-11D2-901F-00C04FB951ED");

	private static IntPtr intptr_0;

	public static void B7AC7521(GEnum39 genum39_0)
	{
		Class70 CS_0024_003C_003E8__locals2 = new Class70();
		CS_0024_003C_003E8__locals2.B10AA535 = genum39_0;
		List<GClass120> object_ = list_0;
		bool bool_ = false;
		try
		{
			Class607.B630A78B.object_0[528](object_, ref bool_);
			list_0 = list_0.Where((GClass120 CE999BB4) => CE999BB4.E903E03A != CS_0024_003C_003E8__locals2.B10AA535).ToList();
		}
		finally
		{
			if (bool_)
			{
				Class607.B630A78B.object_0[832](object_);
			}
		}
	}

	public static void smethod_0(GClass120 B138D284)
	{
		bool flag = false;
		List<GClass120> object_ = list_0;
		bool bool_ = false;
		try
		{
			Class607.B630A78B.object_0[528](object_, ref bool_);
			switch (B138D284.E903E03A)
			{
			case GEnum39.SerialPort:
				foreach (GClass120 item in list_0.ToList())
				{
					if (item.E903E03A == GEnum39.SerialPort && Class607.B630A78B.object_0[787](item.GStruct96_0.string_0, B138D284.GStruct96_0.string_0))
					{
						flag = true;
					}
				}
				break;
			case GEnum39.Fastboot:
				foreach (GClass120 item2 in list_0.ToList())
				{
					if (item2.E903E03A == GEnum39.Fastboot && Class607.B630A78B.object_0[787](item2.BF390303, B138D284.BF390303))
					{
						flag = true;
					}
				}
				break;
			case GEnum39.ADB:
			case GEnum39.Recovery:
				foreach (GClass120 item3 in list_0.ToList())
				{
					if (item3.E903E03A == GEnum39.ADB || item3.E903E03A == GEnum39.Recovery)
					{
						GStruct98 gStruct98_ = item3.GStruct98_0;
						object obj = Class607.B630A78B.object_0[787];
						string string_ = Class607.B630A78B.object_0[185](gStruct98_.Device);
						gStruct98_ = B138D284.GStruct98_0;
						if (obj(string_, Class607.B630A78B.object_0[185](gStruct98_.Device)))
						{
							flag = true;
						}
					}
				}
				break;
			}
			if (!flag)
			{
				list_0.Add(B138D284);
			}
		}
		finally
		{
			if (bool_)
			{
				Class607.B630A78B.object_0[832](object_);
			}
		}
	}

	public static void smethod_1(IntPtr intptr_1)
	{
		Struct1 structure = new Struct1
		{
			D68DA590 = 5,
			int_0 = 0,
			guid_0 = ********,
			short_0 = 0
		};
		structure.C52BA808 = Marshal.SizeOf(structure);
		IntPtr intPtr = Class607.B630A78B.object_0[263](structure.C52BA808);
		Marshal.StructureToPtr(structure, intPtr, fDeleteOld: true);
		intptr_0 = RegisterDeviceNotification(intptr_1, intPtr, 0);
	}

	public static void smethod_2()
	{
		UnregisterDeviceNotification(intptr_0);
	}

	[DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
	private static extern IntPtr RegisterDeviceNotification(IntPtr intptr_1, IntPtr intptr_2, int int_3);

	[DllImport("user32.dll")]
	private static extern bool UnregisterDeviceNotification(IntPtr intptr_1);

	public GClass115()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
