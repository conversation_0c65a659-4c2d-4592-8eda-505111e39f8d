internal class ED1C1F34 : Class0
{
	private int int_1;

	public uint uint_3;

	public uint A028A01E;

	public uint uint_4;

	public ushort ushort_2;

	public ushort ushort_3;

	public ushort ushort_4;

	public override int DD8D5B35 => int_1;

	public ED1C1F34(int int_2)
	{
		int_1 = int_2;
	}

	public override int B0B6FA3F(byte[] BF101B2F, int DAB6249E)
	{
		base.B0B6FA3F(BF101B2F, DAB6249E);
		uint_3 = GClass3.E038002D(BF101B2F, DAB6249E + 32);
		A028A01E = GClass3.E038002D(BF101B2F, DAB6249E + 36);
		uint_4 = GClass3.E038002D(BF101B2F, DAB6249E + 40);
		ushort_2 = GClass3.smethod_11(BF101B2F, DAB6249E + 44);
		ushort_3 = GClass3.smethod_11(BF101B2F, DAB6249E + 46);
		ushort_4 = GClass3.smethod_11(BF101B2F, DAB6249E + 48);
		return int_1;
	}
}
