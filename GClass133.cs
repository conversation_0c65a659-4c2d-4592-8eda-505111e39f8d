using System;

public static class GClass133
{
	private static uint[] uint_0;

	public unsafe static uint smethod_0(IntPtr FD8160B4, uint uint_1)
	{
		uint num = 0u;
		short num2 = 0;
		ushort num5 = default(ushort);
		while (true)
		{
			byte* ptr = (byte*)FD8160B4.ToPointer();
			ushort num3 = (ushort)((-314922345 * num2 >> (int)num2 >> (int)num2) / -1);
			int num4 = -1979440206 + ~((num3 + num3 >> (int)num3) + -1979440207);
			while (true)
			{
				if ((uint)(0x3D0155 & num2) < ((138128281 > (int)((uint)num2 / 4012290600u) % -1467306580) ? 1u : 0u) / (uint)(~(((num2 >>> (int)num3) % (int)(~((uint)num3 % 1602372997u))) | (byte)num2)))
				{
					continue;
				}
				num2 = (short)(31030 + num3 * 316669952);
				while (true)
				{
					switch ((uint)num2 % 3u)
					{
					case 1u:
						num2 = (short)((~(-num2) << -(num2 | -2060815720) >> 0) - 124116);
						if (num4 >= uint_1)
						{
							goto IL_0040;
						}
						goto end_IL_005e;
					case 2u:
						num2 = ((num3 < 8 * num5 % ~(num5 >>> (0x3313B581 & num2))) ? ((short)1) : ((short)0));
						return ~num;
					}
					goto end_IL_0092;
					IL_0040:
					num5 = num3;
					num2 = (short)(0x4C0A ^ (num2 / (-961171937 >> num2 / 1409922833)));
					continue;
					end_IL_005e:
					break;
				}
				num = uint_0[(byte)(ptr[num4] ^ num)] ^ (num >> 8);
				num4++;
				num3 = 0;
				num2 = 0;
				continue;
				end_IL_0092:
				break;
			}
		}
	}

	static GClass133()
	{
		uint_0 = new uint[256];
		sbyte b = 0;
		int num = 0;
		int num2 = default(int);
		uint num3 = default(uint);
		while (true)
		{
			IL_02f4:
			if (num >= uint_0.Length)
			{
				if ((((b << 14) ^ (-1484599000 % (-424029907 >> (int)b))) & ((b & -165013706) >> (798557324 << (int)b) - -1816514249)) == 0)
				{
					b = (sbyte)(0x2F ^ (b * -944752627));
					goto IL_018a;
				}
				goto IL_023d;
			}
			goto IL_0284;
			IL_01b7:
			b = (sbyte)((int)((uint)(((b - -1407737293) & b) ^ ((int)((uint)b / 2486285569u) % -1817989700)) % (873734074 + (((uint)b % (uint)b) & 0x8208E98Bu))) - -37);
			num2 += -1894810363 * b - -1388506696;
			b += -37;
			goto IL_023d;
			IL_00eb:
			b = (sbyte)((1704883616 >> (int)b) - 213110415);
			num3 >>= 1;
			goto IL_0101;
			IL_0284:
			b = 32;
			b = 33;
			goto IL_0265;
			IL_0265:
			b = (sbyte)(-1 + ~(~b) % (short)(-5292235 + b));
			num3 = (uint)num;
			b = (sbyte)((uint)b / 3574148368u);
			num2 = b;
			goto IL_023d;
			IL_023d:
			if (num2 < 9 + ~(b >>> 5) % (-2011467332 >> (-198768974 >>> (b << (int)b))))
			{
				b = 37;
				b = 18;
				goto IL_001e;
			}
			b = (sbyte)((~b >>> 2) % ((-399249402 >> (int)b) - -2146342994) + -879302475);
			b = (sbyte)(0x19BE6DC9 ^ ((uint)b % (uint)(~(236182815 % (int)(~((uint)b % (uint)b)))) % (uint)((((uint)b < (uint)b) ? 1 : 0) + 772611368)));
			goto IL_018a;
			IL_0101:
			if (((808922170 == b << 12 >>> 27 << (int)((uint)b / 725117225u) + (b | 0x330F7598)) ? 1 : 0) <= (int)(sbyte)(b >> 4 >>> 28))
			{
				b = (sbyte)(((int)((uint)b % (uint)(b % 1898026662)) >> (b << 596078900 * (b * -1155648368))) + 52);
				goto IL_018a;
			}
			break;
			IL_018a:
			while (true)
			{
				switch ((uint)b % 8u)
				{
				case 2u:
					break;
				case 5u:
					goto IL_008f;
				case 3u:
					goto IL_00eb;
				case 4u:
					goto IL_01b7;
				case 1u:
					goto IL_0265;
				case 6u:
					b = (sbyte)(-106 + (b | b));
					num += ~(b + 691995317) - -691995243;
					b ^= -76;
					goto IL_02f4;
				default:
					goto IL_02f4;
				case 7u:
					b = (sbyte)(((-586467672 | (b % b)) & -1164864092) % ~b + 16);
					return;
				}
				break;
				IL_008f:
				b = (sbyte)((((int)(1888713398u / (uint)b) >> (int)b) - (b & b)) % (int)(~((2125158538u < (uint)b) ? 1u : 0u)) - 76);
				uint_0[num] = num3;
				if (b * -48 != 0)
				{
					b = (sbyte)(0x768AE81E ^ (b + 1309594025 << (int)((uint)(b >>> (int)b) / (uint)(-2101479669 ^ b) + 2131549451)));
					continue;
				}
				goto IL_0284;
			}
			goto IL_001e;
			IL_001e:
			b = (sbyte)((0 - (((uint)(b + b + -13137) < 3918317881u) ? 1 : 0)) ^ 0x25);
			if ((num3 & (uint)(b - -2128863742 + -2128863778)) != (uint)((sbyte)(~(95758222 >> (int)b)) + 62))
			{
				b = (sbyte)(0 ^ b);
				if (~(374401102 / b) << (int)(76699191u / (uint)b) == 0)
				{
					continue;
				}
				b = (sbyte)((uint)b % (uint)(b >> ~b + 631495176) + 34);
				goto IL_018a;
			}
			num3 = (num3 >> -43807 + -(b << (int)b) * -b) ^ (uint)(-306674943 + (int)((uint)(0x202291A9 | (b & b)) % (uint)b % (uint)(416045 << (int)b)));
			goto IL_0101;
		}
	}
}
