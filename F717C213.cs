using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;

public class F717C213
{
	internal class Class6
	{
		public E5964FB0 C32A9794;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private Dictionary<string, uint> dictionary_0 = new Dictionary<string, uint>();

		public Dictionary<string, uint> AA3AA781
		{
			[CompilerGenerated]
			get
			{
				return dictionary_0;
			}
			[CompilerGenerated]
			private set
			{
				dictionary_0 = value;
			}
		}

		internal uint this[string string_0, int C4175601 = 0]
		{
			get
			{
				if (FA9D3197.TryGetValue(string_0, out var value))
				{
					return C32A9794.gclass51_0.EE83B4B7(value + GClass112.C78DEB29.A8054FBA.EB11D8A7 + Class607.B630A78B.object_0[836](C4175601))[0];
				}
				if (AA3AA781.TryGetValue(string_0, out var value2))
				{
					return value2;
				}
				return uint.MaxValue;
			}
			set
			{
				if (FA9D3197.TryGetValue(CAB32E20, out var value2))
				{
					C32A9794.gclass51_0.BE2E2DAC(value2 + GClass112.C78DEB29.A8054FBA.EB11D8A7 + Class607.B630A78B.object_0[836](int_0), value);
				}
				else
				{
					AA3AA781[CAB32E20] = value;
				}
			}
		}

		public Class6(E5964FB0 e5964FB0_0)
		{
			Class607.B630A78B.object_0[571](this);
			C32A9794 = e5964FB0_0;
		}
	}

	private static IReadOnlyDictionary<string, ushort> FA9D3197 = new Dictionary<string, ushort>
	{
		{ "CQDMA_INT_FLAG", 0 },
		{ "CQDMA_INT_EN", 4 },
		{ "CQDMA_EN", 8 },
		{ "CQDMA_RESET", 12 },
		{ "CQDMA_FLUSH", 20 },
		{ "CQDMA_SRC", 28 },
		{ "CQDMA_DST", 32 },
		{ "CQDMA_LEN1", 36 },
		{ "CQDMA_LEN2", 40 },
		{ "CQDMA_SRC2", 96 },
		{ "CQDMA_DST2", 100 }
	};

	private readonly Class6 class6_0;

	public E5964FB0 E5386DBD;

	public F717C213(E5964FB0 ACAA5C29)
	{
		Class607.B630A78B.object_0[571](this);
		E5386DBD = ACAA5C29;
		class6_0 = new Class6(ACAA5C29);
	}

	private byte[] method_0(uint uint_0, int int_0)
	{
		List<uint> list = new List<uint>();
		uint uint_1 = GClass112.C78DEB29.A8054FBA.uint_5;
		if ((ulong)GClass112.C78DEB29.A8054FBA.EB11D8A7 > 0uL)
		{
			for (uint num = 0u; num < int_0; num++)
			{
				class6_0["CQDMA_SRC", 0] = uint_0 + num * 4;
				class6_0["CQDMA_DST", 0] = uint_1;
				class6_0["CQDMA_LEN1", 0] = 4u;
				class6_0["CQDMA_EN", 0] = 1u;
				while ((class6_0["CQDMA_EN", 0] & 1) != 0)
				{
				}
				list.Add(E5386DBD.gclass51_0.EE83B4B7(uint_1)[0]);
			}
		}
		return list.BE13F184();
	}

	private void method_1(uint uint_0, params uint[] uint_1)
	{
		uint uint_2 = GClass112.C78DEB29.A8054FBA.uint_5;
		if ((long)GClass112.C78DEB29.A8054FBA.EB11D8A7 == 0L)
		{
			return;
		}
		for (uint num = 0u; num < uint_1.Length; num++)
		{
			E5386DBD.gclass51_0.BE2E2DAC(uint_2, uint_1[num]);
			class6_0["CQDMA_SRC", 0] = uint_2;
			class6_0["CQDMA_DST", 0] = uint_0 + num * 4;
			class6_0["CQDMA_LEN1", 0] = 4u;
			class6_0["CQDMA_EN", 0] = 1u;
			while ((class6_0["CQDMA_EN", 0] & 1) != 0)
			{
			}
			E5386DBD.gclass51_0.BE2E2DAC(uint_2, 3405691582u);
		}
	}

	private byte[] method_2(uint BB107B92, int int_0, bool bool_0 = false)
	{
		int num = int_0 / 4;
		if (int_0 % 4 != 0)
		{
			num++;
		}
		if (!bool_0)
		{
			return E5386DBD.gclass51_0.EE83B4B7(Class607.B630A78B.object_0[1200](BB107B92), num).BE13F184().Take(int_0)
				.ToArray();
		}
		return method_0(BB107B92, num).Take(int_0).ToArray();
	}

	private void D58D1812(uint uint_0, byte[] byte_0, bool AA9F348C = false)
	{
		uint[] array = byte_0.smethod_7();
		if (AA9F348C)
		{
			method_1(uint_0, array);
		}
		else
		{
			E5386DBD.gclass51_0.FF33F413(Class607.B630A78B.object_0[1200](uint_0), array);
		}
	}

	private void DEB6B088()
	{
		foreach (Tuple<uint, uint> item3 in GClass112.C78DEB29.A8054FBA.B3927213)
		{
			uint item = item3.Item1;
			uint item2 = item3.Item2;
			method_1(item, item2);
		}
	}
}
