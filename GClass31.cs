using System;
using System.Diagnostics;

[Serializable]
[DebuggerDisplay("{2+B630A78B.A2A5AFA5()}")]
public class GClass31
{
	public string UserName;

	public byte[] UserData;

	public DateTime Expires;

	public int RunningTime;

	public string EMail;

	public DateTime MaxBuild;

	public A538CD23 State;

	public GClass31()
	{
		State = A538CD23.Invalid;
		Expires = DateTime.MaxValue;
		MaxBuild = DateTime.MaxValue;
		RunningTime = 0;
		UserData = new byte[0];
		UserName = string.Empty;
		EMail = string.Empty;
	}
}
