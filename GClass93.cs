using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Xml;

public class GClass93
{
	public SerialPort D89F95AD = Class607.B630A78B.object_0[703]();

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private GClass112.E39CAE0B EE81671B;

	private Stream stream_0;

	public bool bool_0 = true;

	public byte[] byte_0;

	public bool AB93D313;

	public int C72B1EA4;

	public int int_0;

	public int C61BE014;

	public string string_0 = "";

	public string string_1 = "error: only nop and sig tag can be recevied before authentication";

	public bool C21AE69C;

	public bool bool_1;

	public bool FEBCFE0E;

	public string string_2 = "";

	public bool C41C8216;

	public bool bool_2;

	public string string_3 = "";

	public bool DA089C25 => Class607.B630A78B.object_0[1184](D89F95AD);

	public event GClass112.E39CAE0B Event_0
	{
		[CompilerGenerated]
		add
		{
			GClass112.E39CAE0B e39CAE0B = EE81671B;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[752](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref EE81671B, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
		[CompilerGenerated]
		remove
		{
			GClass112.E39CAE0B e39CAE0B = EE81671B;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[629](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref EE81671B, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
	}

	public bool D18845AE(string string_4)
	{
		if (Class607.B630A78B.object_0[1184](D89F95AD))
		{
			Class607.B630A78B.object_0[674](D89F95AD);
		}
		try
		{
			Class607.B630A78B.object_0[624](D89F95AD, 3000);
			Class607.B630A78B.object_0[30](D89F95AD, 3000);
			Class607.B630A78B.object_0[597](D89F95AD, 8);
			Class607.B630A78B.object_0[1103](D89F95AD, "\r");
			Class607.B630A78B.object_0[581](D89F95AD, 128000);
			Class607.B630A78B.object_0[1244](D89F95AD, Parity.None);
			Class607.B630A78B.object_0[264](D89F95AD, StopBits.One);
			Class607.B630A78B.object_0[640](D89F95AD, DC3E1432: true);
			Class607.B630A78B.object_0[701](D89F95AD, C28D088C: true);
			Class607.B630A78B.object_0[594](D89F95AD, string_4);
		}
		catch
		{
			Class607.B630A78B.object_0[674](D89F95AD);
			Class607.B630A78B.object_0[624](D89F95AD, 3000);
			Class607.B630A78B.object_0[30](D89F95AD, 3000);
			Class607.B630A78B.object_0[597](D89F95AD, 8);
			Class607.B630A78B.object_0[1103](D89F95AD, "\r");
			Class607.B630A78B.object_0[581](D89F95AD, 128000);
			Class607.B630A78B.object_0[1244](D89F95AD, Parity.None);
			Class607.B630A78B.object_0[264](D89F95AD, StopBits.One);
			Class607.B630A78B.object_0[640](D89F95AD, DC3E1432: true);
			Class607.B630A78B.object_0[701](D89F95AD, C28D088C: true);
			Class607.B630A78B.object_0[594](D89F95AD, string_4);
		}
		return true;
	}

	public bool F69732B2()
	{
		try
		{
			Class607.B630A78B.object_0[70](D89F95AD);
			stream_0 = Class607.B630A78B.object_0[688](D89F95AD);
		}
		catch
		{
			method_11();
			Class607.B630A78B.object_0[70](D89F95AD);
			stream_0 = Class607.B630A78B.object_0[688](D89F95AD);
		}
		return Class607.B630A78B.object_0[1184](D89F95AD);
	}

	public string method_0(string string_4)
	{
		D4B07210(string_4);
		byte[] array = method_1("500");
		return Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[1124](), array, 0, array.Length);
	}

	public void D4B07210(string E72205AD)
	{
		byte[] f19C = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1124](), E72205AD);
		method_3(f19C);
	}

	public byte[] method_1(string F636CD37 = "0")
	{
		try
		{
			if (GClass112.E31AABAB)
			{
				throw Class607.B630A78B.object_0[778]("Stopped by user!!");
			}
			return method_14();
		}
		catch (Exception ex)
		{
			throw ex;
		}
		finally
		{
			GClass112.E31AABAB = false;
		}
	}

	public string method_2()
	{
		try
		{
			byte[] array = method_1();
			string object_ = Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[1124](), array, 0, array.Length);
			if (Class607.B630A78B.object_0[1240](object_, "\"ACK\""))
			{
				return "ack";
			}
			if (Class607.B630A78B.object_0[1240](object_, "Only nop and sig tag"))
			{
				return "auth";
			}
		}
		catch (Exception ex)
		{
			throw ex;
		}
		return Class607.B630A78B.object_0[170](bool_0: false);
	}

	public void method_3(byte[] F19C8523)
	{
		try
		{
			if (GClass112.E31AABAB)
			{
				throw Class607.B630A78B.object_0[778]("Stopped by user!!");
			}
			method_6(F19C8523, 0, F19C8523.Length);
		}
		catch (Exception ex)
		{
			throw ex;
		}
		finally
		{
			GClass112.E31AABAB = false;
		}
	}

	public bool method_4()
	{
		int num = 20;
		while (num-- > 0 && !Class607.B630A78B.object_0[1184](D89F95AD))
		{
			try
			{
				if (F69732B2())
				{
					break;
				}
			}
			catch
			{
				GClass112.smethod_28(50);
			}
		}
		return Class607.B630A78B.object_0[1184](D89F95AD);
	}

	public byte[] method_5()
	{
		byte[] array = method_14();
		if (array == null)
		{
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[720]("can not read from port ", Class607.B630A78B.object_0[1196](D89F95AD)));
		}
		return array;
	}

	public void method_6(byte[] byte_1, int DA9713B8, int int_1)
	{
		try
		{
			if (GClass112.E31AABAB)
			{
				throw Class607.B630A78B.object_0[778]("Stopped by user!!");
			}
			if (DA089C25)
			{
				int num = 0;
				Exception ex = Class607.B630A78B.object_0[130]();
				bool flag = false;
				while (num++ <= 6 && ex != null && Class607.B630A78B.object_0[795](Class607.B630A78B.object_0[58](ex), Class607.B630A78B.object_0[6](typeof(TimeoutException).TypeHandle)))
				{
					try
					{
						Class607.B630A78B.object_0[30](D89F95AD, 2000);
						Class607.B630A78B.object_0[1073](D89F95AD, byte_1, DA9713B8, int_1);
						flag = true;
						if (AB93D313)
						{
							method_15(byte_1);
						}
						ex = null;
					}
					catch (TimeoutException ex2)
					{
						ex = ex2;
						GClass112.smethod_28(500);
					}
					catch
					{
					}
				}
				if (!flag)
				{
					throw Class607.B630A78B.object_0[778]("write time out,maybe device was disconnected.");
				}
			}
			C72B1EA4++;
		}
		catch (Exception ex3)
		{
			throw ex3;
		}
		finally
		{
			GClass112.E31AABAB = false;
		}
	}

	public bool F49D5A29(string ********)
	{
		List<byte> list = new List<byte>();
		for (int i = 0; i < Class607.B630A78B.object_0[342](********); i += 2)
		{
			string d5854B = Class607.B630A78B.object_0[31](********, i, 2);
			list.Add(Class607.B630A78B.object_0[1285](d5854B, NumberStyles.AllowHexSpecifier));
		}
		byte[] array = list.ToArray();
		string e72205AD = Class607.B630A78B.object_0[1217](B20B4631.C69D829E, array.Length);
		D4B07210(e72205AD);
		if (!EE12ED9B())
		{
			Class607.B630A78B.object_0[294]("Signature failed!");
			return true;
		}
		StringBuilder object_ = Class607.B630A78B.object_0[1086]();
		StringBuilder object_2 = Class607.B630A78B.object_0[1086]();
		byte[] array2 = array;
		for (int j = 0; j < array2.Length; j++)
		{
			byte ADBCA = array2[j];
			Class607.B630A78B.object_0[426](object_, Class607.B630A78B.object_0[720](Class607.B630A78B.object_0[446](ref ADBCA), " "));
			Class607.B630A78B.object_0[426](object_2, Class607.B630A78B.object_0[1140]("0x", Class607.B630A78B.object_0[1273](ref ADBCA, "X2"), " "));
		}
		method_3(array);
		byte[] array3 = method_1("500");
		if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[1124](), array3, 0, array3.Length), "ERROR:"))
		{
			return false;
		}
		return true;
	}

	public bool method_7(string string_4, bool bool_3)
	{
		byte[] array = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), string_4);
		method_6(array, 0, array.Length);
		return bool_3 && method_9(bool_3);
	}

	public List<XmlDocument> method_8(bool bool_3)
	{
		List<XmlDocument> list = new List<XmlDocument>();
		byte[] byte_ = method_14();
		string[] array = method_17(byte_, bool_3);
		if (array.Length >= 2)
		{
			string[] source = Class607.B630A78B.object_0[87](array[1], "\\<\\?xml");
			foreach (string item in source.ToList())
			{
				if (!Class607.B630A78B.object_0[1205](item))
				{
					if (Class607.B630A78B.object_0[1145](Class607.B630A78B.object_0[1050](item), string_1) >= 0)
					{
						C21AE69C = true;
					}
					XmlDocument xmlDocument = Class607.B630A78B.object_0[1104]();
					Class607.B630A78B.object_0[963](xmlDocument, Class607.B630A78B.object_0[720]("<?xml ", item));
					list.Add(xmlDocument);
				}
			}
			return list;
		}
		return list;
	}

	public bool method_9(bool bool_3)
	{
		bool flag = false;
		if (!bool_3)
		{
			return method_14() != null;
		}
		int num = 2;
		if (bool_3)
		{
			num = 16;
		}
		while (num-- > 0 && !flag)
		{
			List<XmlDocument> list = method_8(bool_3);
			_ = list.Count;
			foreach (XmlDocument item in list)
			{
				XmlNode object_ = Class607.B630A78B.object_0[791](item, "data");
				XmlNodeList object_2 = Class607.B630A78B.object_0[539](object_);
				XmlElement xmlElement = null;
				{
					IEnumerator object_3 = Class607.B630A78B.object_0[391](object_2);
					try
					{
						while (Class607.B630A78B.object_0[212](object_3))
						{
							XmlNode xmlNode = (XmlNode)Class607.B630A78B.object_0[107](object_3);
							xmlElement = (XmlElement)xmlNode;
							if (Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[1050](Class607.B630A78B.object_0[531](xmlElement)), "sig"))
							{
								string_0 = Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[1169](xmlElement), "blob", "sig");
							}
							{
								IEnumerator object_4 = Class607.B630A78B.object_0[954](Class607.B630A78B.object_0[160](xmlElement));
								try
								{
									while (Class607.B630A78B.object_0[212](object_4))
									{
										XmlAttribute object_5 = (XmlAttribute)Class607.B630A78B.object_0[107](object_4);
										if (Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[1050](Class607.B630A78B.object_0[531](object_5)), "maxpayloadsizetotargetinbytes"))
										{
											C61BE014 = Class607.B630A78B.object_0[754](Class607.B630A78B.object_0[769](object_5)) / int_0;
										}
										if (Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[1050](Class607.B630A78B.object_0[769](object_5)), "ack"))
										{
											flag = true;
										}
										if (Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[769](object_5), "WARN: NAK: MaxPayloadSizeToTargetInBytes sent by host 1048576 larger than supported 16384"))
										{
											flag = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x40 0x10 "))
										{
											bool_1 = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x31 0x10 "))
										{
											bool_1 = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x02 0x10 "))
										{
											bool_1 = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x00 0x10 "))
										{
											FEBCFE0E = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x51 0x10 "))
										{
											FEBCFE0E = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x32 0x10 "))
										{
											FEBCFE0E = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "UFS Inquiry Command Output"))
										{
											EE81671B?.Invoke("Storage Info : ");
											EE81671B?.Invoke(Class607.B630A78B.object_0[769](object_5), EF1F389C.Success, BB24BF3C: false, bool_0: true);
											string_2 = Class607.B630A78B.object_0[769](object_5);
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "INFO: quick_reset") && !C41C8216)
										{
											EE81671B?.Invoke("quick reset : ");
											EE81671B?.Invoke(Class607.B630A78B.object_0[769](object_5), EF1F389C.Success, BB24BF3C: false, bool_0: true);
											C41C8216 = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "Chip serial num") && !bool_2)
										{
											EE81671B?.Invoke("Chip serial number : ");
											bool_2 = true;
											int num2 = Class607.B630A78B.object_0[1129](Class607.B630A78B.object_0[769](object_5), '(') + 1;
											int num3 = Class607.B630A78B.object_0[1129](Class607.B630A78B.object_0[769](object_5), ')');
											string text = Class607.B630A78B.object_0[31](Class607.B630A78B.object_0[769](object_5), num2, num3 - num2);
											if (Class607.B630A78B.object_0[342](text) < 10)
											{
												int int_ = 10 - Class607.B630A78B.object_0[342](text);
												string object_6 = "00000000";
												text = Class607.B630A78B.object_0[1140](Class607.B630A78B.object_0[31](text, 0, 2), Class607.B630A78B.object_0[31](object_6, 0, int_), Class607.B630A78B.object_0[31](text, 2, Class607.B630A78B.object_0[342](text) - 2));
											}
											string_3 = text;
											EE81671B?.Invoke(string_3, EF1F389C.Success, BB24BF3C: false, bool_0: true);
										}
									}
								}
								finally
								{
									IDisposable disposable2 = object_4 as IDisposable;
									if (disposable2 != null)
									{
										disposable2.Dispose();
									}
								}
							}
						}
					}
					finally
					{
						IDisposable disposable = object_3 as IDisposable;
						if (disposable != null)
						{
							disposable.Dispose();
						}
					}
				}
			}
			if (bool_3)
			{
				GClass112.smethod_28(50);
			}
		}
		return flag;
	}

	public byte[] method_10()
	{
		byte[] array = method_14();
		if (array != null && array.Length != 0 && bool_0)
		{
			method_15(array);
		}
		return array;
	}

	public void method_11()
	{
		try
		{
			try
			{
				Class607.B630A78B.object_0[587](stream_0);
				Class607.B630A78B.object_0[674](D89F95AD);
			}
			catch
			{
			}
			Class607.B630A78B.object_0[674](D89F95AD);
		}
		catch
		{
		}
	}

	public void method_12()
	{
		if (Class607.B630A78B.object_0[1184](D89F95AD))
		{
			Class607.B630A78B.object_0[96](D89F95AD);
			Class607.B630A78B.object_0[1053](D89F95AD);
		}
	}

	private byte[] method_13()
	{
		try
		{
			if (GClass112.E31AABAB)
			{
				throw Class607.B630A78B.object_0[778]("Stopped by user!");
			}
			byte[] result = null;
			if (Class607.B630A78B.object_0[1184](D89F95AD))
			{
				int num = Class607.B630A78B.object_0[13](D89F95AD);
				if (num > 0)
				{
					result = new byte[num];
					Class607.B630A78B.object_0[301](D89F95AD, result, 0, num);
				}
			}
			return result;
		}
		finally
		{
			GClass112.E31AABAB = false;
		}
	}

	public byte[] method_14()
	{
		int num = 100;
		byte_0 = null;
		byte_0 = method_13();
		while (num-- >= 0 && byte_0 == null)
		{
			GClass112.smethod_28(10);
			byte_0 = method_13();
		}
		return byte_0;
	}

	private string[] method_15(byte[] byte_1)
	{
		return method_17(byte_1, E689EF8A: false);
	}

	private int C9945B96(string F2808632, string string_4)
	{
		if (Class607.B630A78B.object_0[1240](F2808632, string_4))
		{
			string fFA17C = Class607.B630A78B.object_0[99](F2808632, string_4, "");
			return (Class607.B630A78B.object_0[342](F2808632) - Class607.B630A78B.object_0[342](fFA17C)) / Class607.B630A78B.object_0[342](string_4);
		}
		return 0;
	}

	public bool EE12ED9B()
	{
		try
		{
			while (true)
			{
				byte[] array = method_1();
				string object_ = Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[1124](), array, 0, array.Length);
				if (Class607.B630A78B.object_0[1240](object_, "\"ACK\""))
				{
					break;
				}
				if (Class607.B630A78B.object_0[1240](object_, "\"NAK\""))
				{
				}
			}
			return true;
		}
		catch (Exception ex)
		{
			throw ex;
		}
	}

	public bool method_16(out string CB0E618D, int int_1)
	{
		CB0E618D = null;
		byte[] byte_ = method_14();
		string[] array = method_17(byte_, E689EF8A: true);
		string string_ = "<response value=\"ACK\"";
		int num = 10;
		while ((array.Length != 2 || Class607.B630A78B.object_0[1145](array[1], string_) < 0) && num-- >= 0)
		{
			GClass112.smethod_28(10);
			byte_ = method_14();
			array = method_17(byte_, E689EF8A: true);
		}
		if (array.Length == 2 && Class607.B630A78B.object_0[1145](array[1], string_) >= 0)
		{
			int i = C9945B96(array[1], string_);
			num = 10;
			for (; i < int_1 * 2; i += C9945B96(array[1], string_))
			{
				if (num-- <= 0)
				{
					break;
				}
				GClass112.smethod_28(10);
				byte_ = method_14();
				array = method_17(byte_, E689EF8A: true);
			}
			if (int_1 * 2 > i)
			{
				throw Class607.B630A78B.object_0[778]("ACK count don't match!");
			}
			method_12();
			C72B1EA4 = 0;
			return true;
		}
		CB0E618D = array[1];
		return false;
	}

	private string[] method_17(byte[] byte_1, bool E689EF8A)
	{
		if (byte_1 == null)
		{
			return new string[2] { "", "" };
		}
		StringBuilder stringBuilder = Class607.B630A78B.object_0[1086]();
		StringBuilder stringBuilder2 = Class607.B630A78B.object_0[1086]();
		Class607.B630A78B.object_0[1086]();
		Class607.B630A78B.object_0[1086]();
		for (int i = 0; i < byte_1.Length; i++)
		{
			object obj = Class607.B630A78B.object_0[426];
			char char_ = Class607.B630A78B.object_0[972](byte_1[i]);
			obj(stringBuilder2, Class607.B630A78B.object_0[1219](ref char_));
		}
		return new string[2]
		{
			stringBuilder.ToString(),
			stringBuilder2.ToString()
		};
	}

	public GClass93()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
