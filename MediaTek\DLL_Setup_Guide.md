# MediaTek DLL Setup Guide

## 📋 DLLs Requeridas

### **1. FlashToolLib.dll**
- **Fuente**: SP Flash Tool oficial de MediaTek
- **Versión**: v5.2032 o superior
- **Arquitectura**: x64 (64-bit)
- **Ubicación**: `bin/FlashToolLib.dll`

### **2. setupapi.dll**
- **Fuente**: Windows System32 (incluida en Windows)
- **Versión**: Windows 10/11
- **Arquitectura**: x64 (64-bit)
- **Ubicación**: Sistema (no requiere copia)

### **3. winusb.dll**
- **Fuente**: Windows System32 (incluida en Windows)
- **Versión**: Windows 10/11
- **Arquitectura**: x64 (64-bit)
- **Ubicación**: Sistema (no requiere copia)

## 🔧 Instalación

### **Paso 1: Descargar SP Flash Tool**
```bash
# Descargar SP Flash Tool oficial
# URL: https://spflashtool.com/
# Extraer FlashToolLib.dll de la instalación
```

### **Paso 2: Verificar APIs de Windows**
```bash
# Las APIs de Windows están incluidas automáticamente:
# - setupapi.dll (en System32)
# - winusb.dll (en System32)
# No requieren descarga adicional
```

### **Paso 3: Copiar DLLs**
```
MotoKingPro/
├── bin/
│   └── FlashToolLib.dll
└── ...
```

## 🚗 Drivers USB

### **Instalar WinUSB Driver**
1. **Descargar Zadig**: https://zadig.akeo.ie/
2. **Conectar dispositivo MediaTek** en download mode
3. **Abrir Zadig** como administrador
4. **Seleccionar dispositivo** MediaTek
5. **Instalar driver** WinUSB o libusb-win32

### **Verificar Instalación**
```csharp
// Código de verificación
var devices = usbManager.GetConnectedMediaTekDevices();
if (devices.Any())
{
    Console.WriteLine("Drivers instalados correctamente");
}
```

## ⚙️ Configuración del Proyecto

### **app.config**
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="bin" />
    </assemblyBinding>
  </runtime>
</configuration>
```

### **Propiedades del Proyecto**
```xml
<PropertyGroup>
  <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  <Platform>x64</Platform>
</PropertyGroup>
```

## 🔍 Verificación

### **Test de DLLs**
```csharp
// Verificar FlashToolLib.dll
try
{
    var handle = IntPtr.Zero;
    int result = FlashToolLibrary.FlashTool_Connect_BROM(0, ref arg, ref handle, IntPtr.Zero);
    Console.WriteLine("FlashToolLib.dll: OK");
}
catch (DllNotFoundException)
{
    Console.WriteLine("FlashToolLib.dll: MISSING");
}

// Verificar APIs nativas de Windows
try
{
    var usbManager = new UsbManager();
    var devices = usbManager.GetConnectedMediaTekDevices();
    Console.WriteLine("Windows USB APIs: OK");
}
catch (Exception)
{
    Console.WriteLine("Windows USB APIs: ERROR");
}
```

## 🚨 Troubleshooting

### **Errores Comunes**

#### **DllNotFoundException**
```
Error: Unable to load DLL 'FlashToolLib.dll'
Solución: Verificar que la DLL esté en bin/ y sea x64
```

#### **BadImageFormatException**
```
Error: An attempt was made to load a program with an incorrect format
Solución: Usar DLLs de 64-bit para proyecto x64
```

#### **AccessViolationException**
```
Error: Attempted to read or write protected memory
Solución: Verificar estructuras P/Invoke y marshaling
```

### **Verificación de Arquitectura**
```bash
# Verificar arquitectura de DLL
dumpbin /headers FlashToolLib.dll | findstr machine
# Debe mostrar: 8664 machine (x64)
```

## 📁 Estructura Completa

```
MotoKingPro/
├── bin/
│   ├── FlashToolLib.dll          # SP Flash Tool Library
│   └── loader/
│       └── auto/                # Auto-loader files
├── MediaTek/
│   ├── FlashToolLibrary.cs      # P/Invoke declarations
│   ├── UsbManager.cs            # USB device management
│   ├── ScatterFileManager.cs    # Scatter file parsing
│   └── AuthenticationBypass.cs  # Bypass exploits
└── Platforms/
    └── MediaTekPage.cs          # UI and logic
```

## ✅ Checklist de Instalación

- [ ] **FlashToolLib.dll** copiada a bin/
- [ ] **Drivers USB** instalados con Zadig
- [ ] **Proyecto configurado** para x64
- [ ] **AllowUnsafeBlocks** habilitado
- [ ] **NuGet packages** instalados
- [ ] **Test de verificación** ejecutado

## 🎯 Resultado Final

Una vez completada la instalación:

```csharp
// Código funcional
var usbManager = new UsbManager();
var devices = usbManager.GetConnectedMediaTekDevices();
var mediaTekManager = new MediaTekManager();
bool connected = await mediaTekManager.ConnectDevice(devices.First());
// ✅ Conexión exitosa!
```

**¡MediaTek Platform completamente funcional!** 🚀
