using System.Collections.Generic;

public static class GClass4
{
	public static Dictionary<uint, string> A3A6B33B = new Dictionary<uint, string>
	{
		{ 0u, "OK" },
		{ 1000u, "STOP" },
		{ 1001u, "UNDEFINED_ERROR" },
		{ 1002u, "INVALID_ARGUMENTS" },
		{ 1003u, "INVALID_BBCHIP_TYPE" },
		{ 1004u, "INVALID_EXT_CLOCK" },
		{ 1005u, "INVALID_BMTSIZE" },
		{ 1006u, "GET_DLL_VER_FAIL" },
		{ 1007u, "INVALID_BUF" },
		{ 1008u, "BUF_IS_NULL" },
		{ 1009u, "BUF_LEN_IS_ZERO" },
		{ 1010u, "BUF_SIZE_TOO_SMALL" },
		{ 1011u, "NOT_ENOUGH_STORAGE_SPACE" },
		{ 1012u, "NOT_ENOUGH_MEMORY" },
		{ 1013u, "COM_PORT_OPEN_FAIL" },
		{ 1014u, "COM_PORT_SET_TIMEOUT_FAIL" },
		{ 1015u, "COM_PORT_SET_STATE_FAIL" },
		{ 1016u, "COM_PORT_PURGE_FAIL" },
		{ 1017u, "FILEPATH_NOT_SPECIFIED_YET" },
		{ 1018u, "UNKNOWN_TARGET_BBCHIP" },
		{ 1019u, "SKIP_BBCHIP_HW_VER_CHECK" },
		{ 1020u, "UNSUPPORTED_VER_OF_BOOT_ROM" },
		{ 1021u, "UNSUPPORTED_VER_OF_BOOTLOADER" },
		{ 1022u, "UNSUPPORTED_VER_OF_DA" },
		{ 1023u, "UNSUPPORTED_VER_OF_SEC_INFO" },
		{ 1024u, "UNSUPPORTED_VER_OF_ROM_INFO" },
		{ 1025u, "SEC_INFO_NOT_FOUND" },
		{ 1026u, "ROM_INFO_NOT_FOUND" },
		{ 1027u, "CUST_PARA_NOT_SUPPORTED" },
		{ 1028u, "CUST_PARA_WRITE_LEN_INCONSISTENT" },
		{ 1029u, "SEC_RO_NOT_SUPPORTED" },
		{ 1030u, "SEC_RO_WRITE_LEN_INCONSISTENT" },
		{ 1031u, "ADDR_N_LEN_NOT_32BITS_ALIGNMENT" },
		{ 1032u, "UART_CHKSUM_ERROR" },
		{ 1033u, "EMMC_FLASH_BOOT" },
		{ 1034u, "NOR_FLASH_BOOT" },
		{ 1035u, "NAND_FLASH_BOOT" },
		{ 1036u, "UNSUPPORTED_VER_OF_EMI_INFO" },
		{ 1037u, "PART_NO_VALID_TABLE" },
		{ 1038u, "PART_NO_SPACE_FOUND" },
		{ 1039u, "UNSUPPORTED_VER_OF_SEC_CFG" },
		{ 1040u, "UNSUPPORTED_OPERATION" },
		{ 1041u, "CHKSUM_ERROR" },
		{ 1042u, "TIMEOUT" },
		{ 2000u, "SET_META_REG_FAIL" },
		{ 2001u, "SET_FLASHTOOL_REG_FAIL" },
		{ 2002u, "SET_REMAP_REG_FAIL" },
		{ 2003u, "SET_EMI_FAIL" },
		{ 2004u, "DOWNLOAD_DA_FAIL" },
		{ 2005u, "CMD_STARTCMD_FAIL" },
		{ 2006u, "CMD_STARTCMD_TIMEOUT" },
		{ 2007u, "CMD_JUMP_FAIL" },
		{ 2008u, "CMD_WRITE16_MEM_FAIL" },
		{ 2009u, "CMD_READ16_MEM_FAIL" },
		{ 2010u, "CMD_WRITE16_REG_FAIL" },
		{ 2011u, "CMD_READ16_REG_FAIL" },
		{ 2012u, "CMD_CHKSUM16_MEM_FAIL" },
		{ 2013u, "CMD_WRITE32_MEM_FAIL" },
		{ 2014u, "CMD_READ32_MEM_FAIL" },
		{ 2015u, "CMD_WRITE32_REG_FAIL" },
		{ 2016u, "CMD_READ32_REG_FAIL" },
		{ 2017u, "CMD_CHKSUM32_MEM_FAIL" },
		{ 2018u, "JUMP_TO_META_MODE_FAIL" },
		{ 2019u, "WR16_RD16_MEM_RESULT_DIFF" },
		{ 2020u, "CHKSUM16_MEM_RESULT_DIFF" },
		{ 2021u, "BBCHIP_HW_VER_INCORRECT" },
		{ 2022u, "FAIL_TO_GET_BBCHIP_HW_VER" },
		{ 2023u, "AUTOBAUD_FAIL" },
		{ 2024u, "SPEEDUP_BAUDRATE_FAIL" },
		{ 2025u, "LOCK_POWERKEY_FAIL" },
		{ 2026u, "WM_APP_MSG_OUT_OF_RANGE" },
		{ 2027u, "NOT_SUPPORT_MT6205B" },
		{ 2028u, "EXCEED_MAX_DATA_BLOCKS" },
		{ 2029u, "EXTERNAL_SRAM_DETECTION_FAIL" },
		{ 2030u, "EXTERNAL_DRAM_DETECTION_FAIL" },
		{ 2031u, "GET_FW_VER_FAIL" },
		{ 2032u, "CONNECT_TO_BOOTLOADER_FAIL" },
		{ 2033u, "CMD_SEND_DA_FAIL" },
		{ 2034u, "CMD_SEND_DA_CHKSUM_DIFF" },
		{ 2035u, "CMD_JUMP_DA_FAIL" },
		{ 2036u, "CMD_JUMP_BL_FAIL" },
		{ 2037u, "EFUSE_REG_NO_MATCH_WITH_TARGET" },
		{ 2038u, "EFUSE_WRITE_TIMEOUT" },
		{ 2039u, "EFUSE_DATA_PROCESS_ERROR" },
		{ 2040u, "EFUSE_BLOW_ERROR" },
		{ 2041u, "EFUSE_ALREADY_BROKEN" },
		{ 2042u, "EFUSE_BLOW_PARTIAL" },
		{ 2043u, "SEC_VER_FAIL" },
		{ 2044u, "PL_SEC_VER_FAIL" },
		{ 2045u, "SET_WATCHDOG_FAIL" },
		{ 2046u, "EFUSE_VALUE_IS_NOT_ZERO" },
		{ 2047u, "EFUSE_WRITE_TIMEOUT_WITHOUT_EFUSE_VERIFY" },
		{ 2048u, "EFUSE_UNKNOW_EXCEPTION_WITHOUT_EFUSE_VERIFY" },
		{ 3000u, "INT_RAM_ERROR" },
		{ 3001u, "EXT_RAM_ERROR" },
		{ 3002u, "SETUP_DRAM_FAIL" },
		{ 3003u, "SETUP_PLL_ERR" },
		{ 3004u, "SETUP_EMI_PLL_ERR" },
		{ 3005u, "DRAM_ABNORMAL_TYPE_SETTING" },
		{ 3006u, "DRAMC_RANK0_CALIBRATION_FAILED" },
		{ 3007u, "DRAMC_RANK1_CALIBRATION_FAILED" },
		{ 3008u, "DRAM_NOT_SUPPORT" },
		{ 3009u, "RAM_FLOARTING" },
		{ 3010u, "RAM_UNACCESSABLE" },
		{ 3011u, "RAM_ERROR" },
		{ 3012u, "DEVICE_NOT_FOUND" },
		{ 3013u, "NOR_UNSUPPORTED_DEV_ID" },
		{ 3014u, "NAND_UNSUPPORTED_DEV_ID" },
		{ 3015u, "NOR_FLASH_NOT_FOUND" },
		{ 3016u, "NAND_FLASH_NOT_FOUND" },
		{ 3017u, "SOC_CHECK_FAIL" },
		{ 3018u, "NOR_PROGRAM_FAILED" },
		{ 3019u, "NOR_ERASE_FAILED" },
		{ 3020u, "NAND_PAGE_PROGRAM_FAILED" },
		{ 3021u, "NAND_SPARE_PROGRAM_FAILED" },
		{ 3022u, "NAND_HW_COPYBACK_FAILED" },
		{ 3023u, "NAND_ERASE_FAILED" },
		{ 3024u, "TIMEOUT" },
		{ 3025u, "IN_PROGRESS" },
		{ 3026u, "SUPERAND_ONLY_SUPPORT_PAGE_READ" },
		{ 3027u, "SUPERAND_PAGE_PRGRAM_NOT_SUPPORT" },
		{ 3028u, "SUPERAND_SPARE_PRGRAM_NOT_SUPPORT" },
		{ 3029u, "SUPERAND_COPYBACK_NOT_SUPPORT" },
		{ 3030u, "NOR_CMD_SEQUENCE_ERR" },
		{ 3031u, "NOR_BLOCK_IS_LOCKED" },
		{ 3032u, "NAND_BLOCK_IS_LOCKED" },
		{ 3033u, "NAND_BLOCK_DATA_UNSTABLE" },
		{ 3034u, "NOR_BLOCK_DATA_UNSTABLE" },
		{ 3035u, "NOR_VPP_RANGE_ERR" },
		{ 3036u, "INVALID_BEGIN_ADDR" },
		{ 3037u, "NOR_INVALID_ERASE_BEGIN_ADDR" },
		{ 3038u, "NOR_INVALID_READ_BEGIN_ADDR" },
		{ 3039u, "NOR_INVALID_PROGRAM_BEGIN_ADDR" },
		{ 3040u, "INVALID_RANGE" },
		{ 3041u, "NOR_PROGRAM_AT_ODD_ADDR" },
		{ 3042u, "NOR_PROGRAM_WITH_ODD_LENGTH" },
		{ 3043u, "NOR_BUFPGM_NO_SUPPORT" },
		{ 3044u, "NAND_UNKNOWN_ERR" },
		{ 3045u, "NAND_BAD_BLOCK" },
		{ 3046u, "NAND_ECC_1BIT_CORRECT" },
		{ 3047u, "NAND_ECC_2BITS_ERR" },
		{ 3048u, "NAND_SPARE_CHKSUM_ERR" },
		{ 3049u, "NAND_HW_COPYBACK_DATA_INCONSISTENT" },
		{ 3050u, "NAND_INVALID_PAGE_INDEX" },
		{ 3051u, "NFI_NOT_SUPPORT" },
		{ 3052u, "NFI_CS1_NOT_SUPPORT" },
		{ 3053u, "NFI_16BITS_IO_NOT_SUPPORT" },
		{ 3054u, "NFB_BOOTLOADER_NOT_EXIST" },
		{ 3055u, "NAND_NO_GOOD_BLOCK" },
		{ 3056u, "NAND_UBIIMG_NOT_SPARSEIMAGE" },
		{ 3057u, "BOOTLOADER_IS_TOO_LARGE" },
		{ 3058u, "SIBLEY_REWRITE_OBJ_MODE_REGION" },
		{ 3059u, "SIBLEY_WRITE_B_HALF_IN_CTRL_MODE_REGION" },
		{ 3060u, "SIBLEY_ILLEGAL_CMD" },
		{ 3061u, "SIBLEY_PROGRAM_AT_THE_SAME_REGIONS" },
		{ 3062u, "UART_GET_DATA_TIMEOUT" },
		{ 3063u, "UART_GET_CHKSUM_LSB_TIMEOUT" },
		{ 3064u, "UART_GET_CHKSUM_MSB_TIMEOUT" },
		{ 3065u, "UART_DATA_CKSUM_ERROR" },
		{ 3066u, "UART_RX_BUF_FULL" },
		{ 3067u, "UART_RX_BUF_NOT_ENOUGH" },
		{ 3068u, "FLASH_RECOVERY_BUF_NOT_ENOUGH" },
		{ 3069u, "HANDSET_SEC_INFO_NOT_FOUND" },
		{ 3070u, "HANDSET_SEC_INFO_MAC_VERIFY_FAIL" },
		{ 3071u, "HANDSET_ROM_INFO_NOT_FOUND" },
		{ 3072u, "HANDSET_FAT_INFO_NOT_FOUND" },
		{ 3073u, "OPERATION_UNSUPPORT_FOR_NFB" },
		{ 3074u, "BYPASS_POST_PROCESS" },
		{ 3075u, "NOR_OTP_NOT_SUPPORT" },
		{ 3076u, "NOR_OTP_EXIST" },
		{ 3077u, "NOR_OTP_LOCKED" },
		{ 3078u, "NOR_OTP_GETSIZE_FAIL" },
		{ 3079u, "NOR_OTP_READ_FAIL" },
		{ 3080u, "NOR_OTP_PROGRAM_FAIL" },
		{ 3081u, "NOR_OTP_LOCK_FAIL" },
		{ 3082u, "NOR_OTP_LOCK_CHECK_STATUS_FAIL" },
		{ 3083u, "BLANK_FLASH" },
		{ 3084u, "CODE_AREA_IS_BLANK" },
		{ 3085u, "SEC_RO_AREA_IS_BLANK" },
		{ 3086u, "NOR_OTP_UNLOCKED" },
		{ 3087u, "UNSUPPORTED_BBCHIP" },
		{ 3088u, "FAT_NOT_EXIST" },
		{ 3089u, "EXT_SRAM_NOT_FOUND" },
		{ 3090u, "EXT_DRAM_NOT_FOUND" },
		{ 3091u, "MT_PIN_LOW" },
		{ 3092u, "MT_PIN_HIGH" },
		{ 3093u, "MT_PIN_SHORT" },
		{ 3094u, "MT_BUS_ERROR" },
		{ 3095u, "MT_ADDR_NOT_2BYTE_ALIGNMENT" },
		{ 3096u, "MT_ADDR_NOT_4BYTE_ALIGNMENT" },
		{ 3097u, "MT_SIZE_NOT_2BYTE_ALIGNMENT" },
		{ 3098u, "MT_SIZE_NOT_4BYTE_ALIGNMENT" },
		{ 3099u, "MT_DEDICATED_PATTERN_ERROR" },
		{ 3100u, "MT_INC_PATTERN_ERROR" },
		{ 3101u, "MT_DEC_PATTERN_ERROR" },
		{ 3102u, "NFB_BLOCK_0_IS_BAD" },
		{ 3103u, "CUST_PARA_AREA_IS_BLANK" },
		{ 3104u, "ENTER_RELAY_MODE_FAIL" },
		{ 3105u, "ENTER_RELAY_MODE_IS_FORBIDDEN_AFTER_META" },
		{ 3106u, "NAND_PAGE_READ_FAILED" },
		{ 3107u, "NAND_IMAGE_BLOCK_NO_EXIST" },
		{ 3108u, "NAND_IMAGE_LIST_NOT_EXIST" },
		{ 3109u, "MBA_RESOURCE_NO_EXIST_IN_TARGET" },
		{ 3110u, "MBA_PROJECT_VERSION_NO_MATCH_WITH_TARGET" },
		{ 3111u, "MBA_UPDATING_RESOURCE_NO_EXIST_IN_TARGET" },
		{ 3112u, "MBA_UPDATING_RESOURCE_SIZE_EXCEED_IN_TARGET" },
		{ 3113u, "NAND_BIN_SIZE_EXCEED_MAX_SIZE" },
		{ 3114u, "NAND_EXCEED_CONTAINER_LIMIT" },
		{ 3115u, "NAND_REACH_END_OF_FLASH" },
		{ 3116u, "NAND_OTP_NOT_SUPPORT" },
		{ 3117u, "NAND_OTP_EXIST" },
		{ 3118u, "NAND_OTP_LOCKED" },
		{ 3119u, "NAND_OTP_LOCK_FAIL" },
		{ 3120u, "NAND_OTP_UNLOCKED" },
		{ 3121u, "OTP_NOT_SUPPORT" },
		{ 3122u, "OTP_EXIST" },
		{ 3123u, "OTP_LOCKED" },
		{ 3124u, "OTP_GETSIZE_FAIL" },
		{ 3125u, "OTP_READ_FAIL" },
		{ 3126u, "OTP_PROGRAM_FAIL" },
		{ 3127u, "OTP_LOCK_FAIL" },
		{ 3128u, "OTP_LOCK_CHECK_STATUS_FAIL" },
		{ 3129u, "OTP_UNLOCKED" },
		{ 3130u, "SEC_RO_ILLEGAL_MAGIC_TAIL" },
		{ 3131u, "HANDSET_FOTA_INFO_NOT_FOUND" },
		{ 3132u, "HANDSET_UA_INFO_NOT_FOUND" },
		{ 3133u, "SB_FSM_INVALID_INFO" },
		{ 3134u, "NFB_TARGET_DUAL_BL_PAIRED_VERSION_NOT_MATCHED_WITH_MAUI" },
		{ 3135u, "NFB_TARGET_DUAL_BL_FEATURE_COMBINATION_NOT_MATCHED_WITH_MAUI" },
		{ 3136u, "NFB_TARGET_IS_SINGLE_BL_BUT_PC_NOT" },
		{ 3137u, "NFB_TARGET_IS_DUAL_BL_BUT_PC_NOT" },
		{ 3138u, "NOR_TARGET_BL_PAIRED_VERSION_NOT_MATCHED_WITH_MAUI" },
		{ 3139u, "NOR_TARGET_BL_FEATURE_COMBINATION_NOT_MATCHED_WITH_MAUI" },
		{ 3140u, "NOR_TARGET_IS_NOT_NEW_BL_BUT_PC_IS" },
		{ 3141u, "NOR_TARGET_IS_NEW_BL_BUT_PC_NOT" },
		{ 3142u, "DOWNLOAD_BOOTLOADER_FLASH_DEV_IS_NONE" },
		{ 3143u, "DOWNLOAD_BOOTLOADER_FLASH_DEV_IS_NOT_SUPPORTED" },
		{ 3144u, "DOWNLOAD_BOOTLOADER_BEGIN_ADDR_OVERLAPS_WITH_PREVIOUS_BOUNDARY" },
		{ 3145u, "UPDATE_BOOTLOADER_EXIST_MAGIC_NOT_MATCHED" },
		{ 3146u, "UPDATE_BOOTLOADER_FILE_TYPE_NOT_MATCHED" },
		{ 3147u, "UPDATE_BOOTLOADER_FILE_SIZE_EXCEEDS_BOUNDARY_ADDR" },
		{ 3148u, "UPDATE_BOOTLOADER_BEGIN_ADDR_NOT_MATCHED" },
		{ 3149u, "EMMC_FLASH_NOT_FOUND" },
		{ 3150u, "EMMC_FW_VER_CHECK_FAIL" },
		{ 3151u, "SDMMC_FLASH_NOT_FOUND" },
		{ 3152u, "SDMMC_CONFIG_FAILED" },
		{ 3153u, "SDMMC_READ_FAILED" },
		{ 3154u, "SDMMC_WRITE_FAILED" },
		{ 3155u, "SDMMC_ERR_CRC" },
		{ 3156u, "SDMMC_ERR_TIMEOUT" },
		{ 3157u, "SDMMC_UNSUPPORTED" },
		{ 3158u, "DSPBL_CHECK_PLATFORM_FAILED" },
		{ 3159u, "UFS_FLASH_NOT_FOUND" },
		{ 3160u, "UFS_CONFIG_FAILED" },
		{ 3161u, "UFS_READ_FAILED" },
		{ 3162u, "UFS_WRITE_FAILED" },
		{ 3163u, "UFS_ERR_TIMEOUT" },
		{ 3164u, "UFS_UNSUPPORTED" },
		{ 3165u, "UFS_OTP_NOT_SUPPORT" },
		{ 3166u, "UFS_OTP_EXIST" },
		{ 3167u, "UFS_OTP_LOCKED" },
		{ 3168u, "UFS_OTP_LOCK_FAIL" },
		{ 3169u, "UFS_OTP_UNLOCKED" },
		{ 3170u, "HANDSET_SEC_CFG_NOT_FOUND" },
		{ 3171u, "EMMC_OTP_NOT_SUPPORT" },
		{ 3172u, "EMMC_OTP_EXIST" },
		{ 3173u, "EMMC_OTP_LOCKED" },
		{ 3174u, "EMMC_OTP_LOCK_FAIL" },
		{ 3175u, "EMMC_OTP_UNLOCKED" },
		{ 3176u, "READ_IMEI_PID_SWV_NOT_SUPPORT" },
		{ 3177u, "NFI_EMPTY_PAGE" },
		{ 3178u, "INVALID_STORAGE_TYPE" },
		{ 3179u, "SEND_CMD_FAIL" },
		{ 3180u, "READ_CMD_ACK_FAIL" },
		{ 3181u, "READ_FLASH_STATUS_INFO_FAIL" },
		{ 3182u, "PL_VALIDATION_FAIL" },
		{ 3183u, "STORAGE_NOT_MATCH" },
		{ 3184u, "CHIP_TYPE_NOT_MATCH" },
		{ 3185u, "EXCEED_MAX_PARTITION_COUNT" },
		{ 6000u, "CALLBACK_SLA_CHALLENGE_FAIL" },
		{ 6001u, "SLA_WRONG_AUTH_FILE" },
		{ 6002u, "SLA_INVALID_AUTH_FILE" },
		{ 6003u, "SLA_CHALLENGE_FAIL" },
		{ 6004u, "SLA_FAIL" },
		{ 6005u, "DAA_FAIL" },
		{ 6006u, "SBC_FAIL" },
		{ 6007u, "SF_SECURE_VER_CHECK_FAIL" },
		{ 6008u, "SF_HANDSET_SECURE_CUSTOM_NAME_NOT_MATCH" },
		{ 6009u, "SF_FTCFG_LOCKDOWN" },
		{ 6010u, "SF_CODE_DOWNLOAD_FORBIDDEN" },
		{ 6011u, "SF_CODE_READBACK_FORBIDDEN" },
		{ 6012u, "SF_CODE_FORMAT_FORBIDDEN" },
		{ 6013u, "SF_SEC_RO_DOWNLOAD_FORBIDDEN" },
		{ 6014u, "SF_SEC_RO_READBACK_FORBIDDEN" },
		{ 6015u, "SF_SEC_RO_FORMAT_FORBIDDEN" },
		{ 6016u, "SF_FAT_DOWNLOAD_FORBIDDEN" },
		{ 6017u, "SF_FAT_READBACK_FORBIDDEN" },
		{ 6018u, "SF_FAT_FORMAT_FORBIDDEN" },
		{ 6019u, "SF_RESTRICTED_AREA_ACCESS_FORBIDDEN" },
		{ 6020u, "SECURE_CUSTOM_NAME_NOT_MATCH_BETWEEN_AUTH_AND_DL_HANDLE" },
		{ 6021u, "DOWNLOAD_FILE_IS_CORRUPTED" },
		{ 6022u, "NOT_SUPPORT" },
		{ 6023u, "BOOTLOADER_IMAGE_SIGNATURE_FAIL" },
		{ 6024u, "BOOTLOADER_ELDER_SW_VERSION_CANNOT_BE_DOWNLOADED" },
		{ 6025u, "BOOTLOADER_IMAGE_NO_SIGNATURE" },
		{ 6026u, "BOOTLOADER_CORRUPTED_SCATTER_FILE" },
		{ 6027u, "SECURE_USB_DL_NO_MAUI_IN_SCATTER_FILE" },
		{ 6028u, "SEND_CERT_FAIL" },
		{ 6029u, "SEND_AUTH_FAIL" },
		{ 6030u, "GET_SEC_CONFIG_FAIL" },
		{ 6031u, "GET_ME_ID_FAIL" },
		{ 6032u, "GET_HW_SW_VER_FAIL" },
		{ 6033u, "GET_HW_CODE_FAIL" },
		{ 6034u, "ROM_INFO_NOT_FOUND" },
		{ 6035u, "ROM_INFO_ID_MISMATCH" },
		{ 6036u, "SEC_CTRL_ID_MISMATCH" },
		{ 6037u, "SEC_KEY_ID_MISMATCH" },
		{ 6038u, "SECURE_USB_DL_FAIL" },
		{ 6039u, "SECURE_USB_DL_CHECK_TARGET_STATUS_FAIL" },
		{ 6040u, "SECURE_USB_DL_SEND_CHIP_STATUS_FAIL" },
		{ 6041u, "SECURE_USB_DL_DISABLED" },
		{ 6042u, "SECURE_USB_DL_ENABLED" },
		{ 6043u, "SECURE_USB_DL_IMAGE_PUBLIC_N_KEY_READ_FAIL" },
		{ 6044u, "SECURE_USB_DL_IMAGE_PUBLIC_E_KEY_READ_FAIL" },
		{ 6045u, "SECURE_USB_DL_IMAGE_SIGN_HEADER_NOT_FOUND" },
		{ 6046u, "SECURE_USB_DL_IMGAE_SIGNATURE_VERIFY_FAIL" },
		{ 6047u, "SECURE_USB_DL_IMAGE_HASH_FAIL" },
		{ 6048u, "SECURE_USB_DL_IMAGE_NOT_FOUND" },
		{ 6049u, "SECURE_USB_DL_INVALID_IMAGE_ARGUMENT" },
		{ 6050u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_INIT_FAIL" },
		{ 6051u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_IMAGE_NAME_FAIL" },
		{ 6052u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_IMAGE_NAME_LEN_FAIL" },
		{ 6053u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_TYPE_FAIL" },
		{ 6054u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_HEADER_FAIL" },
		{ 6055u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_IMAGE_OFFSET_FAIL" },
		{ 6056u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRITE_SIGNATURE_HASH_FAIL" },
		{ 6057u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_GET_CHECK_RESULT_FAIL" },
		{ 6058u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_DOWNLOAD_IMAGE_INVALID" },
		{ 6059u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_UNKNOWN_CHECK_RESULT" },
		{ 6060u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_WRONG_OPERATION" },
		{ 6061u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_INVALID_HEADER_LENGTH" },
		{ 6062u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_INVALID_IMAGE_OFFSET" },
		{ 6063u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_INVALID_SIGNATURE_LENGTH" },
		{ 6128u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_SIGNATURE_LENGTH_TOO_LARGE" },
		{ 6129u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_IMAGE_NAME_LENGTH_TOO_LONG" },
		{ 6130u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_EXT_HEADER_TOO_LARGE" },
		{ 6131u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_EXT_HEADER_OFFSET_INVALID" },
		{ 6132u, "SECURE_USB_DL_IMAGE_INFO_CHECK_CMD_EXT_HEADER_SELF_COPY_FAIL" },
		{ 6133u, "SEC_CFG_NOT_EXIST" },
		{ 6134u, "SEC_CFG_WRITE_CMD_INIT_FAIL" },
		{ 6135u, "SEC_CFG_WRONG_MAGIC_NUMBER" },
		{ 6136u, "SEC_CFG_IS_FULL_CANNOT_ADD_NEW_IMAGE" },
		{ 6137u, "SEC_CFG_IMAGE_NOT_FOUND_SO_CANNOT_UPDATE" },
		{ 6138u, "SEC_CFG_IMAGE_CUST_NAME_MISMATCH" },
		{ 6139u, "SEC_CFG_IMAGE_CANNOT_ROLL_BACK_SW_LOAD" },
		{ 6140u, "SEC_CFG_IMAGE_EXIST_CANNOT_CREATE_MEW_FILE" },
		{ 6141u, "SEC_CFG_WRITE_FAIL_CFG_NOT_EXIST" },
		{ 6142u, "SEC_CFG_WRITE_FAIL_MAGIC_INCORRECT" },
		{ 6143u, "SEC_CFG_WRITE_FAIL_CANNOT_WRITE_TO_FIRST_BLOCK" },
		{ 6144u, "SEC_CFG_WRITE_FAIL_YAFFS2_POST_PROCESS_FAIL" },
		{ 6145u, "SEC_CFG_WRITE_FAIL_NAND_DEVICE" },
		{ 6146u, "SEC_CFG_WRITE_FAIL_CANNOT_READ_BACK" },
		{ 6147u, "SEC_CFG_WRITE_FAIL_READ_BACK_MAGIC_INCORRECT" },
		{ 6148u, "SEC_CFG_WRITE_FAIL_READ_BACK_ID_INCORRECT" },
		{ 6149u, "SEC_CFG_WRITE_FAIL_READ_BACK_STATUS_INCORRECT" },
		{ 6150u, "SEC_CFG_WRITE_FAIL_READ_BACK_END_PATTERN_INCORRECT" },
		{ 6151u, "SEC_CFG_WRITE_FAIL_SEC_CFG_CANNOT_OVERWRITE_NEXT_PARTITION" },
		{ 6152u, "SEC_CFG_WRITE_FAIL_NAND_NOT_DETECTED" },
		{ 6153u, "SEC_CFG_WRITE_FAIL_EMMC_NOT_DETECTED" },
		{ 6154u, "SEC_CFG_WRITE_FAIL_EMMC_DEVICE" },
		{ 6155u, "SEC_CFG_WRITE_FAIL_NAND_PAGE_SIZE_NOT_SUPPORT" },
		{ 6156u, "SEC_CFG_WRITE_FAIL_NAND_FIND_GOOD_BLK_FAIL" },
		{ 6157u, "SEC_CFG_WRITE_FAIL_NAND_ERASE_FAIL" },
		{ 6158u, "SEC_CFG_WRITE_FAIL_UNKNOWN_DEVIE_TYPE" },
		{ 6159u, "SEC_CFG_READ_FAIL_NAND_NOT_DETECTED" },
		{ 6160u, "SEC_CFG_READ_FAIL_EMMC_NOT_DETECTED" },
		{ 6161u, "SEC_CFG_READ_FAIL_EMMC_DEVICE" },
		{ 6162u, "SEC_CFG_READ_FAIL_NAND_PAGE_SIZE_NOT_SUPPORT" },
		{ 6163u, "SEC_CFG_READ_FAIL_NAND_FIND_GOOD_BLK_FAIL" },
		{ 6164u, "SEC_CFG_READ_FAIL_UNKNOWN_DEVIE_TYPE" },
		{ 6165u, "SEC_CFG_READ_FAIL_NAND_LOGICAL_READ_FAIL" },
		{ 6166u, "SEC_CFG_EXT_REGION_SPACE_OVERFLOW" },
		{ 6167u, "SECURE_USB_DL_ROM_INFO_UPDATE_REQUEST_FAIL" },
		{ 6168u, "SECURE_USB_DL_DA_RETURN_INVALID_TYPE" },
		{ 6169u, "SECURE_USB_DL_MOVE_IMAGE_HEADER_TO_END_FAIL" },
		{ 6170u, "SECURE_USB_DL_NO_NEED_TO_MOVE_IMAGE_HEADER" },
		{ 6171u, "SECURE_USB_DL_NO_NEED_TO_REMOVE_IMAGE_HEADER_AND_SIG" },
		{ 6172u, "CIPHER_DATA_UNALIGNED" },
		{ 6173u, "CIPHER_MODE_INVALID" },
		{ 6174u, "CIPHER_KEY_INVALID" },
		{ 6175u, "CIPHER_INIT_FAIL" },
		{ 6176u, "CIPHER_ROM_NOT_LOADED" },
		{ 6177u, "CIPHER_DEC_FAIL" },
		{ 6178u, "CIPHER_ENC_TEST_FAIL" },
		{ 6179u, "AES_VER_INVALID" },
		{ 6180u, "INVALID_IMGDEC_CFG" },
		{ 6181u, "IMGDEC_INVALID_FORCE_DEC_PARAM" },
		{ 6182u, "IMGDEC_INVALID_AES_KEY_SIZE" },
		{ 6183u, "IMGDEC_FAIL_IMAGE_NOT_ENCRYPTED" },
		{ 6184u, "IMGDEC_INIT_FAIL_FTH_IS_NULL" },
		{ 6185u, "IMGDEC_INIT_FAIL_DECH_IS_NULL" },
		{ 6186u, "INIDEC_FAIL_INI_NOT_ENCRYPTED" },
		{ 6187u, "INIDEC_INVALID_AES_KEY_SIZE" },
		{ 6188u, "INVALID_PROJECT" },
		{ 6189u, "SECRO_ANTICLONE_LENGTH_INVALID" },
		{ 6190u, "SECRO_HASH_INCORRECT" },
		{ 6191u, "SECRO_ENCRYPT_FAIL" },
		{ 6192u, "AC_REGION_NOT_FOUND_IN_SECROIMG" },
		{ 7000u, "EPP_FAIL" },
		{ 7001u, "EPP_EXT_DRAM_NOT_FOUND" },
		{ 7002u, "EPP_EXT_DRAM_INIT_FAIL" },
		{ 7003u, "EPP_NO_EMI_CONFIG_PARAM_FAIL" },
		{ 10001u, "PL_MODE_UNSUPPORTED" },
		{ 10002u, "PL_MODE_FORBIDDEN" },
		{ 10003u, "PL_MODE_INVALID_ARGUMETS" },
		{ 10004u, "PL_READ_FAIL" },
		{ 10005u, "PL_WRITE_FAIL" },
		{ 10006u, "PL_READ_TIMEOUT" },
		{ 10007u, "PL_WRITE_TIMEOUT" },
		{ 10008u, "PL_DISC_CMD_NEEDED" },
		{ 4096u, "CRYPTO_INIT_FAIL" },
		{ 4097u, "CRYPTO_DEINIT_FAIL" },
		{ 4098u, "CRYPTO_MODE_INVALID" },
		{ 4099u, "CRYPTO_KEY_INVALID" },
		{ 4100u, "CRYPTO_DATA_UNALIGNED" },
		{ 4101u, "CRYPTO_SEEK_LEN_ERROR" },
		{ 7424u, "ADDRESS_TOO_HIGH_80000000" },
		{ 7425u, "ADDRESS_TOO_HIGH_40000000" },
		{ 7426u, "INVALID_ADDRESS" },
		{ 7436u, "NO_AUTH_NEEDED" },
		{ 7437u, "SLA_ERROR" },
		{ 7438u, "DA_OVERLAP" },
		{ 7439u, "DA_INVALID_JUMP_ADDR" },
		{ 7440u, "DA_EXCEED_MAX_DA_BLOCKS" },
		{ 7441u, "DA_ADDR_NOT_16BITS_ALIGNED" },
		{ 7442u, "DA_INVALID_ADDR_AND_LEN" },
		{ 7443u, "DA_LEN_IS_ZERO" },
		{ 7444u, "DA_SIG_LEN_EXCEED_DA_LEN" },
		{ 7445u, "DA_SIG_LEN_IS_ZERO_AND_DAA_ACTIVE" },
		{ 7446u, "DA_NOT_EXIST" },
		{ 7447u, "DA_BLOCKS_INCONSISTENT_WITH_TOOL_AUTH" },
		{ 7457u, "DA_VERSION_NOT_SUPPORTED" },
		{ 8192u, "AUTH_IMAGE_VERIFY_FAIL" },
		{ 8193u, "DA_IMAGE_SIG_VERIFY_FAIL" },
		{ 8194u, "DA_IMAGE_NO_MEM_FAIL" },
		{ 8195u, "DA_INIT_KEY_FAIL" },
		{ 8196u, "IMG_INIT_KEY_FAIL" },
		{ 8197u, "HASH_IMAGE_FAIL" },
		{ 8198u, "DA_RELOCATE_SIZE_NOT_ENOUGH" },
		{ 12288u, "LIB_SEC_CFG_NOT_EXIST" },
		{ 12289u, "LIB_VER_INVALID" },
		{ 12290u, "LIB_SEC_CFG_ERASE_FAIL" },
		{ 12291u, "LIB_SEC_CFG_CANNOT_WRITE" },
		{ 12292u, "LIB_SEC_CFG_END_PATTERN_NOT_EXIST" },
		{ 12293u, "LIB_SEC_CFG_STATUS_INVALID" },
		{ 12294u, "LIB_SEC_CFG_READ_SIZE_NOT_ENOUGH" },
		{ 12295u, "LIB_SEC_CFG_RSA_KEY_INIT_FAIL" },
		{ 16384u, "IMG_VERIFY_THIS_IMG_INFO_NOT_EXIST" },
		{ 16385u, "IMG_VERIFY_HASH_COMPARE_FAIL" },
		{ 16386u, "IMG_VERIFY_NO_SPACE_ADD_IMG_INFO" },
		{ 16387u, "SEC_DL_TOKEN_NOT_FOUND_IN_IMG" },
		{ 16388u, "SEC_DL_FLOW_ERROR" },
		{ 16389u, "IMG_VERIFY_INVALID_IMG_INFO_ATTR" },
		{ 16390u, "IMG_SECROIMG_NOT_FOUND" },
		{ 16391u, "IMG_READ_FAIL" },
		{ 16392u, "IMG_VERIFY_SIGNATURE_FAIL" },
		{ 16393u, "IMG_SIGN_FORMAT_NOT_MATCH" },
		{ 16394u, "IMG_EXTENSION_HDR_NOT_FOUND" },
		{ 16395u, "IMG_EXTENSION_MAGIC_WRONG" },
		{ 16396u, "IMG_EXTENSION_TYPE_NOT_SUPPORT" },
		{ 16397u, "IMG_EXTENSION_HASH_CAL_FAIL" },
		{ 20480u, "IMG_LOCK_TABLE_NOT_EXIST" },
		{ 20481u, "IMG_LOCK_ALL_LOCK" },
		{ 20482u, "IMG_LOCK_NO_SPACE_ADD_LOCK_INFO" },
		{ 20483u, "IMG_LOCK_THIS_IMG_INFO_NOT_EXIST" },
		{ 20484u, "IMG_LOCK_MAGIC_ERROR" },
		{ 24576u, "SBC_KEY_NOT_FOUND" },
		{ 24577u, "BR_SEC_CFG_NOT_FOUND" },
		{ 24578u, "ERR_PUBK_NOT_INITIALIZED" },
		{ 24579u, "SHA256_OP_FAIL" },
		{ 24580u, "ERR_PSS_CHK_FAIL" },
		{ 24581u, "ERR_SIG_CHK_FAIL" },
		{ 28672u, "REGION_INVALID_INCLUDE" },
		{ 28673u, "REGION_INVALID_OVERLAP" },
		{ 28674u, "REGION_INVALID_OVERFLOW" },
		{ 28675u, "DA_INVALID_LOCATION" },
		{ 28676u, "DA_INVALID_LENGTH" },
		{ 28693u, "DAA_Security_Error_Signature" },
		{ 28695u, "DAA_Security_Error" },
		{ 28708u, "DAA_SIG_VERIFY_FAILED" },
		{ 32768u, "INVALID_SIG_SZ" },
		{ 32769u, "INVALID_PADDING_SZ" },
		{ 36864u, "SW_ID_MISMATCH" },
		{ 36865u, "INVALID_IMG_TYPE" },
		{ 36866u, "BUF_NOT_ENOUGH_FOR_CERT" },
		{ 36867u, "IMG_NOT_FOUND" },
		{ 36868u, "IMG_HDR_HASH_VFY_FAIL" },
		{ 36869u, "IMG_HASH_VFY_FAIL" },
		{ 36870u, "INVALID_OID_SZ" },
		{ 36871u, "INVALID_OID_BUF_SZ" },
		{ 36872u, "END_OF_CERT" },
		{ 36873u, "CERT_OBJ_ID_NOT_MATCH" },
		{ 36874u, "CERT_OBJ_SZ_NOT_MATCH" },
		{ 36875u, "CERT_TRAVERSE_UNKNOWN_MODE" },
		{ 36876u, "INVALID_OID_IDX" },
		{ 36877u, "OID_NOT_FOUND" },
		{ 36878u, "OBJ_NOT_FOUND" },
		{ 36879u, "E_KEY_LEN_NOT_MATCH" },
		{ 36880u, "E_KEY_NOT_MATCH" },
		{ 36881u, "N_KEY_LEN_NOT_MATCH" },
		{ 36882u, "N_KEY_NOT_MATCH" },
		{ 36883u, "PUBK_AUTH_FAIL" },
		{ 36884u, "INVALID_IMG_HDR" },
		{ 36885u, "NOT_EXPECTED_IMG" },
		{ 40960u, "CERT_IMG_VER_NOT_SYNC" },
		{ 40961u, "IMAGE_IMG_VER_NOT_SYNC" },
		{ 40962u, "IMG_VER_ROLLBACK" },
		{ 40963u, "OTP_FIELD_NOT_ENOUGH" },
		{ 40964u, "OTP_REG_BIT_CONFIG_WRONG" },
		{ 40965u, "OTP_WRITE_FAIL" }
	};

	public static Dictionary<uint, string> dictionary_0 = new Dictionary<uint, string>
	{
		{ 0u, "OK" },
		{ 3221291009u, "Error" },
		{ 3221291010u, "Abort" },
		{ 3221291011u, "Unsupported command" },
		{ 3221291012u, "Unsupported ctrl code" },
		{ 3221291013u, "Protocol error" },
		{ 3221291014u, "Protocol buffer overflow" },
		{ 3221291015u, "Insufficient buffer" },
		{ 3221291016u, "USB SCAN error" },
		{ 3221291017u, "Invalid hsession" },
		{ 3221291018u, "Invalid session" },
		{ 3221291019u, "Invalid stage" },
		{ 3221291020u, "Not implemented" },
		{ 3221291021u, "File not found" },
		{ 3221291022u, "Open file error" },
		{ 3221291023u, "Write file error" },
		{ 3221291024u, "Read file error" },
		{ 3221291025u, "Create File error / Unsupported Version" },
		{ 3221356545u, "Rom info not found" },
		{ 3221356546u, "Cust name not found" },
		{ 3221356547u, "Device not supported" },
		{ 3221356548u, "DL forbidden" },
		{ 3221356549u, "Img too large" },
		{ 3221356550u, "PL verify fail" },
		{ 3221356551u, "Image verify fail" },
		{ 3221356552u, "Hash operation fail" },
		{ 3221356553u, "Hash binding check fail" },
		{ 3221356554u, "Invalid buf" },
		{ 3221356555u, "Binding hash not available" },
		{ 3221356556u, "Write data not allowed" },
		{ 3221356557u, "Format not allowed" },
		{ 3221356558u, "SV5 public key auth failed" },
		{ 3221356559u, "SV5 hash verify failed" },
		{ 3221356560u, "SV5 RSA OP failed" },
		{ 3221356561u, "SV5 RSA verify failed" },
		{ 3221356562u, "SV5 GFH not found" },
		{ 3221356563u, "Cert1 invalid" },
		{ 3221356564u, "Cert2 invalid" },
		{ 3221356565u, "Imghdr invalid" },
		{ 3221356566u, "Sig size invalid" },
		{ 3221356567u, "RSA pss op fail" },
		{ 3221356568u, "Cert auth failed" },
		{ 3221356569u, "Public key auth mismatch n size" },
		{ 3221356570u, "Public key auth mismatch e size" },
		{ 3221356571u, "Public key auth mismatch n" },
		{ 3221356572u, "Public key auth mismatch e" },
		{ 3221356573u, "Public key auth mismatch hash" },
		{ 3221356574u, "Cert obj not found" },
		{ 3221356575u, "Cert oid not found" },
		{ 3221356576u, "Cert out of range" },
		{ 3221356577u, "Oid doesn't match" },
		{ 3221356578u, "Length doesn't match" },
		{ 3221356579u, "ASN1 unknown op" },
		{ 3221356580u, "OID index out of range" },
		{ 3221356581u, "OID too large" },
		{ 3221356582u, "Public key size mismatch" },
		{ 3221356583u, "SWID mismatch" },
		{ 3221356584u, "Hash size mismatch" },
		{ 3221356585u, "IMGHDR type mismatch" },
		{ 3221356586u, "IMG type mismatch" },
		{ 3221356587u, "IMGHDR hash verify failed" },
		{ 3221356588u, "IMG has verify failed" },
		{ 3221356589u, "Anti rollback violation" },
		{ 3221356590u, "SECCFG not found" },
		{ 3221356591u, "SECCFG magic incorrect" },
		{ 3221356592u, "SECCFG invalid" },
		{ 3221356593u, "Cipher mode invalid" },
		{ 3221356594u, "Cipher key invalid" },
		{ 3221356595u, "Cipher data unaligned" },
		{ 3221356596u, "GFH file info not found" },
		{ 3221356597u, "GFH anti clone not found" },
		{ 3221356598u, "GFH sec cfg not found" },
		{ 3221356599u, "Unsupported source type" },
		{ 3221356600u, "Cust name mismatch" },
		{ 3221356601u, "Invalid address" },
		{ 3221356608u, "Certificate version not synced" },
		{ 3221356609u, "Signature not synced" },
		{ 3221356610u, "Ext AllInOne Signature rejected" },
		{ 3221356611u, "Ext AllInOne Signature missing" },
		{ 3221356612u, "Comm Key is not set" },
		{ 3221356613u, "DevInfo Check failed" },
		{ 3221356614u, "Bootimg count overflow" },
		{ 3221356615u, "Signature not found" },
		{ 3221356616u, "Bootimg special handle" },
		{ 3221356617u, "Remote Seucrity policy disabled" },
		{ 3221356618u, "RSA OAEP fail" },
		{ 3221356619u, "Insufficient buffer" },
		{ 3221356620u, "DA Anti-Rollback error" },
		{ 3221356621u, "Get OTP value fail" },
		{ 3221356622u, "Invalid unit size" },
		{ 3221356623u, "Invalid group idx" },
		{ 3221356624u, "Img version overflow" },
		{ 3221356625u, "Otp table not initialized" },
		{ 3221356626u, "Invalid Partition name" },
		{ 3221356627u, "DA version Anti-Rollback error" },
		{ 3221356628u, "Invalid msg size" },
		{ 3221356629u, "Security level unsupported" },
		{ 3221356630u, "Security level mismatch" },
		{ 3221356631u, "Fault injection error" },
		{ 3221356632u, "Public Key hash group invalid - too many root keys" },
		{ 3221356633u, "Security level too large" },
		{ 3221356634u, "Security config is formatted" },
		{ 3221356635u, "Security config unknown error" },
		{ 3221356636u, "Lockstate seccfg fail" },
		{ 3221356637u, "Lockstate custom fail" },
		{ 3221356638u, "Lockstate inconsistent" },
		{ 3221422081u, "Scatter file invalid" },
		{ 3221422082u, "DA file invalid" },
		{ 3221422083u, "DA selection error" },
		{ 3221422084u, "Preloader invalid" },
		{ 3221422085u, "EMI hdr invalid" },
		{ 3221422086u, "Storage mismatch" },
		{ 3221422087u, "Invalid parameters" },
		{ 3221422088u, "Invalid GPT" },
		{ 3221422089u, "Invalid PMT" },
		{ 3221422090u, "Layout changed" },
		{ 3221422091u, "Invalid format param" },
		{ 3221422092u, "Unknown storage section type" },
		{ 3221422093u, "Unknown scatter field" },
		{ 3221422094u, "Partition tbl doesn't exist" },
		{ 3221422095u, "Scatter hw chip id mismatch" },
		{ 3221422096u, "SEC cert file not found" },
		{ 3221422097u, "SEC auth file not found" },
		{ 3221422098u, "SEC auth file needed" },
		{ 3221422099u, "EMI containter file not found" },
		{ 3221422100u, "Scatter file not found" },
		{ 3221422101u, "Xml file op error" },
		{ 3221422102u, "Unsupported page size" },
		{ 3221422103u, "EMI info length offset invalid" },
		{ 3221422104u, "EMI info length invalid" },
		{ 3221487617u, "Unsupported operation" },
		{ 3221487618u, "Thread error" },
		{ 3221487619u, "Checksum error" },
		{ 3221487620u, "Unknown sparse" },
		{ 3221487621u, "Unknown sparse chunk type" },
		{ 3221487622u, "Partition not found" },
		{ 3221487623u, "Read parttbl failed" },
		{ 3221487624u, "Exceeded max partition number" },
		{ 3221487625u, "Unknown storage type" },
		{ 3221487626u, "Dram Test failed" },
		{ 3221487627u, "Exceed available range" },
		{ 3221487628u, "Write sparse image failed" },
		{ 3221487664u, "MMC error" },
		{ 3221487680u, "Nand error" },
		{ 3221487681u, "Nand in progress" },
		{ 3221487682u, "Nand timeout" },
		{ 3221487683u, "Nand bad block" },
		{ 3221487684u, "Nand erase failed" },
		{ 3221487685u, "Nand page program failed" },
		{ 3221487696u, "EMI setting version error" },
		{ 3221487712u, "UFS error" },
		{ 3221487872u, "DA OTP not supported" },
		{ 3221487874u, "DA OTP lock failed" },
		{ 3221488128u, "EFUSE unknown error" },
		{ 3221488129u, "EFUSE write timeout without verify" },
		{ 3221488130u, "EFUSE blown" },
		{ 3221488131u, "EFUSE revert bit" },
		{ 3221488132u, "EFUSE blown partly" },
		{ 3221488133u, "EFUSE invalid argument" },
		{ 3221488134u, "EFUSE value is not zero" },
		{ 3221488135u, "EFUSE blown incorrect data" },
		{ 3221488136u, "EFUSE broken" },
		{ 3221488137u, "EFUSE blow error" },
		{ 3221488138u, "EFUSE data process error" },
		{ 3221488139u, "EFUSE unlock bpkey error" },
		{ 3221488140u, "EFUSE create list error" },
		{ 3221488141u, "EFUSE write register error" },
		{ 3221488142u, "EFUSE padding type mismatch" },
		{ 3221553153u, "Device ctrl exception" },
		{ 3221553154u, "Shutdown Cmd exception" },
		{ 3221553155u, "Download exception" },
		{ 3221553156u, "Upload exception" },
		{ 3221553157u, "Ext Ram exception" },
		{ 3221553158u, "Notify Switch Usb Speed exception" },
		{ 3221553159u, "Read data exception" },
		{ 3221553160u, "Write data exception" },
		{ 3221553161u, "Format exception" },
		{ 3221553162u, "OTP operation exception" },
		{ 3221553163u, "Switch usb exception" },
		{ 3221553164u, "Write efuse exception" },
		{ 3221553165u, "Read efuse exception" },
		{ 3221618689u, "Brom start cmd/connect not preloader failed" },
		{ 3221618690u, "Brom get bbchip hw ver failed" },
		{ 3221618691u, "Brom cmd send da failed" },
		{ 3221618692u, "Brom cmd jump da failed" },
		{ 3221618693u, "Brom cmd failed" },
		{ 3221618694u, "Brom stage callback failed" },
		{ 3221684225u, "DA Version mismatch" },
		{ 3221684226u, "DA not found" },
		{ 3221684227u, "DA section not found" },
		{ 3221684228u, "DA hash mismatch" },
		{ 3221684229u, "DA exceed max num" }
	};

	public static Dictionary<uint, string> dictionary_1 = new Dictionary<uint, string>
	{
		{ 3000u, "S_DA_INT_RAM_ERROR" },
		{ 3001u, "S_DA_EXT_RAM_ERROR" },
		{ 3002u, "S_DA_SETUP_DRAM_FAIL" },
		{ 3003u, "S_DA_SETUP_PLL_ERR" },
		{ 3004u, "S_DA_DRAM_NOT_SUPPORT" },
		{ 3005u, "S_DA_RAM_FLOARTING" },
		{ 3006u, "S_DA_RAM_UNACCESSABLE" },
		{ 3007u, "S_DA_RAM_ERROR" },
		{ 3008u, "S_DA_DEVICE_NOT_FOUND" },
		{ 3009u, "S_DA_NOR_UNSUPPORTED_DEV_ID" },
		{ 3010u, "S_DA_NAND_UNSUPPORTED_DEV_ID" },
		{ 3011u, "S_DA_NOR_FLASH_NOT_FOUND" },
		{ 3012u, "S_DA_NAND_FLASH_NOT_FOUND" },
		{ 3013u, "S_DA_SOC_CHECK_FAIL" },
		{ 3014u, "S_DA_NOR_PROGRAM_FAILED" },
		{ 3015u, "S_DA_NOR_ERASE_FAILED" },
		{ 3016u, "S_DA_NAND_PAGE_PROGRAM_FAILED" },
		{ 3017u, "S_DA_NAND_SPARE_PROGRAM_FAILED" },
		{ 3018u, "S_DA_NAND_HW_COPYBACK_FAILED" },
		{ 3019u, "S_DA_NAND_ERASE_FAILED" },
		{ 3020u, "S_DA_TIMEOUT" },
		{ 3021u, "S_DA_IN_PROGRESS" },
		{ 3022u, "S_DA_SUPERAND_ONLY_SUPPORT_PAGE_READ" },
		{ 3023u, "S_DA_SUPERAND_PAGE_READ_NOT_SUPPORT" },
		{ 3024u, "S_DA_SUPERAND_PAGE_PRGRAM_NOT_SUPPORT" },
		{ 3025u, "S_DA_SUPERAND_SPARE_PRGRAM_NOT_SUPPORT" },
		{ 3026u, "S_DA_SUPERAND_SPARE_READ_NOT_SUPPORT" },
		{ 3027u, "S_DA_SUPERAND_PAGE_SPARE_PRGRAM_NOT_SUPPORT" },
		{ 3028u, "S_DA_SUPERAND_COPYBACK_NOT_SUPPORT" },
		{ 3029u, "S_DA_NOR_CMD_SEQUENCE_ERR" },
		{ 3030u, "S_DA_NOR_BLOCK_IS_LOCKED" },
		{ 3031u, "S_DA_NAND_BLOCK_IS_LOCKED" },
		{ 3032u, "S_DA_NAND_BLOCK_DATA_UNSTABLE" },
		{ 3033u, "S_DA_NOR_BLOCK_DATA_UNSTABLE" },
		{ 3034u, "S_DA_NOR_VPP_RANGE_ERR" },
		{ 3035u, "S_DA_INVALID_BEGIN_ADDR" },
		{ 3036u, "S_DA_NOR_INVALID_ERASE_BEGIN_ADDR" },
		{ 3037u, "S_DA_NOR_INVALID_READ_BEGIN_ADDR" },
		{ 3038u, "S_DA_NOR_INVALID_PROGRAM_BEGIN_ADDR" },
		{ 3039u, "S_DA_INVALID_RANGE" },
		{ 3040u, "S_DA_NOR_PROGRAM_AT_ODD_ADDR" },
		{ 3041u, "S_DA_NOR_PROGRAM_WITH_ODD_LENGTH" },
		{ 3042u, "S_DA_NOR_BUFPGM_NO_SUPPORT" },
		{ 3043u, "S_DA_NAND_UNKNOWN_ERR" },
		{ 3044u, "S_DA_NAND_BAD_BLOCK" },
		{ 3045u, "S_DA_NAND_ECC_1BIT_CORRECT" },
		{ 3046u, "S_DA_NAND_ECC_2BITS_ERR" },
		{ 3047u, "S_DA_NAND_ECC_UNCORRECTABLE_ERROR" },
		{ 3048u, "S_DA_NAND_SPARE_CHKSUM_ERR" },
		{ 3049u, "S_DA_NAND_HW_COPYBACK_DATA_INCONSISTENT" },
		{ 3050u, "S_DA_NAND_INVALID_PAGE_INDEX" },
		{ 3051u, "S_DA_NFI_NOT_SUPPORT" },
		{ 3052u, "S_DA_NFI_CS1_NOT_SUPPORT" },
		{ 3053u, "S_DA_NFI_16BITS_IO_NOT_SUPPORT" },
		{ 3054u, "S_DA_NFB_BOOTLOADER_NOT_EXIST" },
		{ 3055u, "S_DA_NAND_NO_GOOD_BLOCK" },
		{ 3056u, "S_DA_BOOTLOADER_IS_TOO_LARGE" },
		{ 3057u, "S_DA_SIBLEY_REWRITE_OBJ_MODE_REGION" },
		{ 3058u, "S_DA_SIBLEY_WRITE_B_HALF_IN_CTRL_MODE_REGION" },
		{ 3059u, "S_DA_SIBLEY_ILLEGAL_CMD" },
		{ 3060u, "S_DA_SIBLEY_PROGRAM_AT_THE_SAME_REGIONS" },
		{ 3061u, "S_DA_UART_GET_DATA_TIMEOUT" },
		{ 3062u, "S_DA_UART_GET_CHKSUM_LSB_TIMEOUT" },
		{ 3063u, "S_DA_UART_GET_CHKSUM_MSB_TIMEOUT" },
		{ 3064u, "S_DA_UART_DATA_CKSUM_ERROR" },
		{ 3065u, "S_DA_UART_RX_BUF_FULL" },
		{ 3066u, "S_DA_FLASH_RECOVERY_BUF_NOT_ENOUGH" },
		{ 3067u, "S_DA_HANDSET_SEC_INFO_NOT_FOUND" },
		{ 3068u, "S_DA_HANDSET_SEC_INFO_MAC_VERIFY_FAIL" },
		{ 3069u, "S_DA_HANDSET_ROM_INFO_NOT_FOUND" },
		{ 3070u, "S_DA_HANDSET_FAT_INFO_NOT_FOUND" },
		{ 3071u, "S_DA_OPERATION_UNSUPPORT_FOR_NFB" },
		{ 3072u, "S_DA_BYPASS_POST_PROCESS" },
		{ 3073u, "S_DA_NOR_OTP_NOT_SUPPORT" },
		{ 3074u, "S_DA_NOR_OTP_EXIST" },
		{ 3075u, "S_DA_NOR_OTP_LOCKED" },
		{ 3076u, "S_DA_NOR_OTP_GETSIZE_FAIL" },
		{ 3077u, "S_DA_NOR_OTP_READ_FAIL" },
		{ 3078u, "S_DA_NOR_OTP_PROGRAM_FAIL" },
		{ 3079u, "S_DA_NOR_OTP_LOCK_FAIL" },
		{ 3080u, "S_DA_NOR_OTP_LOCK_CHECK_STATUS_FAIL" },
		{ 3081u, "S_DA_BLANK_FLASH" },
		{ 3082u, "S_DA_CODE_AREA_IS_BLANK" },
		{ 3083u, "S_DA_SEC_RO_AREA_IS_BLANK" },
		{ 3084u, "S_DA_NOR_OTP_UNLOCKED" },
		{ 3085u, "S_DA_UNSUPPORTED_BBCHIP" },
		{ 3086u, "S_DA_FAT_NOT_EXIST" },
		{ 3087u, "S_DA_EXT_SRAM_NOT_FOUND" },
		{ 3088u, "S_DA_EXT_DRAM_NOT_FOUND" },
		{ 3089u, "S_DA_MT_PIN_LOW" },
		{ 3090u, "S_DA_MT_PIN_HIGH" },
		{ 3091u, "S_DA_MT_PIN_SHORT" },
		{ 3092u, "S_DA_MT_BUS_ERROR" },
		{ 3093u, "S_DA_MT_ADDR_NOT_2BYTE_ALIGNMENT" },
		{ 3094u, "S_DA_MT_ADDR_NOT_4BYTE_ALIGNMENT" },
		{ 3095u, "S_DA_MT_SIZE_NOT_2BYTE_ALIGNMENT" },
		{ 3096u, "S_DA_MT_SIZE_NOT_4BYTE_ALIGNMENT" },
		{ 3097u, "S_DA_MT_DEDICATED_PATTERN_ERROR" },
		{ 3098u, "S_DA_MT_INC_PATTERN_ERROR" },
		{ 3099u, "S_DA_MT_DEC_PATTERN_ERROR" },
		{ 3100u, "S_DA_NFB_BLOCK_0_IS_BAD" },
		{ 3101u, "S_DA_CUST_PARA_AREA_IS_BLANK" },
		{ 3102u, "S_DA_ENTER_RELAY_MODE_FAIL" },
		{ 3103u, "S_DA_ENTER_RELAY_MODE_IS_FORBIDDEN_AFTER_META" },
		{ 3104u, "S_DA_NAND_PAGE_READ_FAILED" },
		{ 3105u, "S_DA_NAND_IMAGE_BLOCK_NO_EXIST" },
		{ 3106u, "S_DA_NAND_IMAGE_LIST_NOT_EXIST" },
		{ 3107u, "S_DA_MBA_RESOURCE_NO_EXIST_IN_TARGET" },
		{ 3108u, "S_DA_MBA_PROJECT_VERSION_NO_MATCH_WITH_TARGET" },
		{ 3109u, "S_DA_MBA_UPDATING_RESOURCE_NO_EXIST_IN_TARGET" },
		{ 3110u, "S_DA_MBA_UPDATING_RESOURCE_SIZE_EXCEED_IN_TARGET" },
		{ 3111u, "S_DA_NAND_BIN_SIZE_EXCEED_MAX_SIZE" },
		{ 3112u, "S_DA_NAND_EXCEED_CONTAINER_LIMIT" },
		{ 3113u, "S_DA_NAND_REACH_END_OF_FLASH" },
		{ 3114u, "S_DA_NAND_OTP_NOT_SUPPORT" },
		{ 3115u, "S_DA_NAND_OTP_EXIST" },
		{ 3116u, "S_DA_NAND_OTP_LOCKED" },
		{ 3117u, "S_DA_NAND_OTP_LOCK_FAIL" },
		{ 3118u, "S_DA_NAND_OTP_UNLOCKED" },
		{ 3119u, "S_DA_OTP_NOT_SUPPORT" },
		{ 3120u, "S_DA_OTP_EXIST" },
		{ 3121u, "S_DA_OTP_LOCKED" },
		{ 3122u, "S_DA_OTP_GETSIZE_FAIL" },
		{ 3123u, "S_DA_OTP_READ_FAIL" },
		{ 3124u, "S_DA_OTP_PROGRAM_FAIL" },
		{ 3125u, "S_DA_OTP_LOCK_FAIL" },
		{ 3126u, "S_DA_OTP_LOCK_CHECK_STATUS_FAIL" },
		{ 3127u, "S_DA_OTP_UNLOCKED" },
		{ 3128u, "S_DA_SEC_RO_ILLEGAL_MAGIC_TAIL" },
		{ 3129u, "S_DA_HANDSET_FOTA_INFO_NOT_FOUND" },
		{ 3130u, "S_DA_HANDSET_UA_INFO_NOT_FOUND" },
		{ 3131u, "S_DA_SB_FSM_INVALID_INFO" },
		{ 3132u, "S_DA_NFB_TARGET_DUAL_BL_PAIRED_VERSION_NOT_MATCHED_WITH_MAUI" },
		{ 3133u, "S_DA_NFB_TARGET_DUAL_BL_FEATURE_COMBINATION_NOT_MATCHED_WITH_MAUI" },
		{ 3134u, "S_DA_NFB_TARGET_IS_SINGLE_BL_BUT_PC_NOT" },
		{ 3135u, "S_DA_NFB_TARGET_IS_DUAL_BL_BUT_PC_NOT" },
		{ 3136u, "S_DA_NOR_TARGET_BL_PAIRED_VERSION_NOT_MATCHED_WITH_MAUI" },
		{ 3137u, "S_DA_NOR_TARGET_BL_FEATURE_COMBINATION_NOT_MATCHED_WITH_MAUI" },
		{ 3138u, "S_DA_NOR_TARGET_IS_NOT_NEW_BL_BUT_PC_IS" },
		{ 3139u, "S_DA_NOR_TARGET_IS_NEW_BL_BUT_PC_NOT" },
		{ 3140u, "S_DA_GEN_DA_VERSION_INFO_TEMP_ILB_FAIL" },
		{ 3141u, "S_DA_FLASH_NOT_FOUND" },
		{ 3142u, "S_DA_BOOT_CERT_NOT_EXIST" },
		{ 3143u, "S_DA_NAND_CODE_IMAGE_OVERLAP_FAT_REGION" },
		{ 3144u, "S_DA_DOWNLOAD_BOOTLOADER_FLASH_DEV_IS_NONE" },
		{ 3145u, "S_DA_DOWNLOAD_BOOTLOADER_FLASH_DEV_IS_NOT_SUPPORTED" },
		{ 3146u, "S_DA_DOWNLOAD_BOOTLOADER_BEGIN_ADDR_OVERLAPS_WITH_PREVIOUS_BOUNDARY" },
		{ 3147u, "S_DA_UPDATE_BOOTLOADER_EXIST_MAGIC_NOT_MATCHED" },
		{ 3148u, "S_DA_UPDATE_BOOTLOADER_FILE_TYPE_NOT_MATCHED" },
		{ 3149u, "S_DA_UPDATE_BOOTLOADER_FILE_SIZE_EXCEEDS_BOUNDARY_ADDR" },
		{ 3150u, "S_DA_UPDATE_BOOTLOADER_BEGIN_ADDR_NOT_MATCHED" },
		{ 3151u, "S_DA_CBR_SET_BUF_AND_API_FAIL" },
		{ 3152u, "S_DA_CBR_NOT_FOUND" },
		{ 3153u, "S_DA_CBR_FLASH_LAYOUT_NOT_FOUND" },
		{ 3154u, "S_DA_CBR_FLASH_SPACE_INFO_NOT_FOUND" },
		{ 3155u, "S_DA_CBR_FLASH_CONFIG_NOT_FOUND" },
		{ 3156u, "S_DA_CBR_SET_ENVRIONMENT_FAILED" },
		{ 3157u, "S_DA_CBR_CREAT_FAILED" },
		{ 3158u, "S_DA_CBR_COMPARE_FAILED" },
		{ 3159u, "S_DA_CBR_WRONG_VERSION" },
		{ 3160u, "S_DA_CBR_ALREADY_EXIST" },
		{ 3161u, "S_DA_CBR_RECORD_BUF_TOO_SMALL" },
		{ 3162u, "S_DA_CBR_RECORD_NOT_EXIST" },
		{ 3163u, "S_DA_CBR_RECORD_ALREADY_EXIST" },
		{ 3164u, "S_DA_CBR_FULL" },
		{ 3165u, "S_DA_CBR_RECORD_WRITE_LEN_INCONSISTENT" },
		{ 3166u, "S_DA_CBR_VERSION_NOT_MATCHED" },
		{ 3167u, "S_DA_CBR_NOT_SUPPORT_PCT_FLASH" },
		{ 3168u, "S_DA_CBR_UNKNOWN_ERROR" },
		{ 3169u, "S_DA_SEC_RO_ACC_PARSE_ERROR" },
		{ 3170u, "S_DA_HEADER_BLOCK_NOT_EXIST" },
		{ 3171u, "S_DA_S_PRE_PARSE_CUSTOMER_NAME_FAIL" },
		{ 3172u, "S_DA_S_RETRIEVE_SEC_RO_FAIL_IN_SECURE_INIT" },
		{ 3173u, "S_DA_S_FLASH_INFO_NOT_EXIST" },
		{ 3174u, "S_DA_S_MAUI_INFO_NOT_EXIST" },
		{ 3175u, "S_DA_S_BOOTLOADER_SHARE_DATA_NOT_EXIST" },
		{ 3176u, "S_DA_GFH_FILE_INFO_RETREIVAL_FAIL" },
		{ 3177u, "S_DA_NAND_REMARK_FAIL" },
		{ 3178u, "S_DA_TARGET_IS_NOT_NEW_BL_BUT_PC_IS" },
		{ 3179u, "S_DA_EMMC_FLASH_NOT_FOUND" },
		{ 3180u, "S_DA_EMMC_ENABLE_BOOT_FAILED" },
		{ 3181u, "S_DA_HB_FOUND_IN_OTHER_FLASH_DEV" },
		{ 3182u, "S_DA_USB_2_0_NOT_SUPPORT" },
		{ 3183u, "S_DA_CBR_INIT_FAILED" },
		{ 3184u, "S_DA_CBR_MAUI_INFO_SIZE_TOO_BIG" },
		{ 3185u, "S_DA_CBR_WRITE_MAUI_INFO_FAILED" },
		{ 3186u, "S_DA_CBR_READ_MAUI_INFO_FAILED" },
		{ 3187u, "S_DA_UNSUPPORTED_OPERATION" },
		{ 3188u, "S_DA_MBA_RESOURCE_BIN_NUMBER_NOT_MATCH_WITH_TARGET" },
		{ 3189u, "S_DA_MBA_HEADER_NOT_EXIST" },
		{ 3190u, "S_DA_MBA_RESOURCE_VERSION_NO_MATCH_WITH_TARGET" },
		{ 3191u, "S_DA_BOOTLOADER_SELF_UPDATE_FAIL" },
		{ 3192u, "S_DA_SEARCH_BL_SELF_UPDATE_INFO_FAIL" },
		{ 3193u, "S_DA_SPACE_NOT_ENOUGH_FOR_EXT_BL_MARKER" },
		{ 3194u, "S_DA_FIND_EXT_BL_MARKER_FAIL" },
		{ 3195u, "S_DA_TOO_MANY_BAD_BLOCKS_FOR_EXT_BL_MARKER" },
		{ 3196u, "S_DA_TOO_MANY_BAD_BLOCKS_FOR_EXT_BL_BACKUP" },
		{ 3197u, "S_DA_EXT_BL_VER_MISMATCHED" },
		{ 3198u, "S_DA_EXT_BL_VER_NOT_FOUND" },
		{ 3199u, "S_DA_BL_SELF_UPDATE_FEATURE_CHECK_FAILED" },
		{ 3200u, "S_DA_BL_ROM_INFO_NOT_FOUND" },
		{ 3201u, "S_DA_EXT_BL_MAX_SIZE_MISMATCHED" },
		{ 3202u, "S_DA_INVALID_PARAMETER_FROM_PC" },
		{ 3203u, "S_DA_BL_SELF_UPDATE_NOT_SUPPORTED" },
		{ 3204u, "S_DA_EXT_BL_HDR_NOT_FOUND" },
		{ 3205u, "S_DA_S_FLASH_LAYOUT_NOT_EXIST" },
		{ 3206u, "S_DA_S_FLASH_ID_NOT_EXIST" },
		{ 3207u, "S_DA_MAUI_GFH_FLASH_ID_NOT_MATCH_WITH_TARGET" },
		{ 3208u, "S_DA_FLASH_ERASE_SIZE_NOT_SUPPORT" },
		{ 3209u, "S_DA_SRD_NOT_FOUND" },
		{ 3210u, "S_DA_SRD_UPDATE_FAILED" },
		{ 3211u, "S_DA_NAND_DATA_ADDR_NOT_PAGE_ALIGNMENT" },
		{ 3212u, "S_DA_BL_GFH_BROM_SEC_CFG_NOT_FOUND" },
		{ 3213u, "S_DA_BL_CUSTOMER_NAME_BUFFER_INSUFFICIENT" },
		{ 3214u, "S_DA_COM_BUSY" },
		{ 3215u, "S_DA_INITIAL_BMT_FAILED_CAUSED_FROM_POOL_SIZE_ERROR" },
		{ 3216u, "S_DA_LOAD_ORIGINAL_BMT_FAILED" },
		{ 3217u, "S_DA_INVALID_NAND_PAGE_BUFFER" },
		{ 3218u, "S_DA_DL_BOOT_REGION_IS_OVERLAP_CONTROL_BLOCK_REGION" },
		{ 3219u, "S_DA_PRE_DL_HB_INIT_FAIL" },
		{ 3220u, "S_DA_POST_DL_HB_WRITE_FAIL" },
		{ 3221u, "S_DA_LOAD_IMG_PARA_FAIL" },
		{ 3222u, "S_DA_WRITE_IMG_PARA_FAIL" },
		{ 3223u, "S_DA_UPDATE_HB_FAIL" },
		{ 3224u, "S_DA_BIN_SIZE_EXCEED_MAX_ERR" },
		{ 3225u, "S_DA_PARTIAL_BIN_TYPE_ERR" },
		{ 3226u, "S_DA_IMAGE_PARA_QUERY_ERR" },
		{ 3227u, "S_DA_IMAGE_PARA_UPDATE_ERR" },
		{ 3228u, "S_DA_FLASH_LAYOUT_BIN_NOT_FOUND" },
		{ 3229u, "S_DA_FLASH_LAYOUT_GET_ELEMENT_FAIL" },
		{ 3230u, "S_DA_FLASH_LAYOUT_ADD_ELEMENT_FAIL" },
		{ 3231u, "S_DA_CBR_FOUND_BUT_MAUI_NOT_EXIST" },
		{ 3232u, "S_DA_UPDATE_BOOTLOADER_NOT_CONTAIN_CRITICAL_DATA" },
		{ 3233u, "S_DA_DUMP_FLASH_LAYOUT_FAIL" },
		{ 3234u, "S_DA_BMT_NO_INIT" },
		{ 3235u, "S_DA_NOR_PROGRAM_REGION_IS_OVERLAP" }
	};

	public static string D40D06B0(uint uint_0)
	{
		if (A3A6B33B.ContainsKey(uint_0))
		{
			return Class607.B630A78B.object_0[398](A3A6B33B[uint_0], " (0x", Class607.B630A78B.object_0[993](ref uint_0, "X"), ")");
		}
		if (dictionary_0.ContainsKey(uint_0))
		{
			return Class607.B630A78B.object_0[398](dictionary_0[uint_0], " (0x", Class607.B630A78B.object_0[993](ref uint_0, "X"), ")");
		}
		if (dictionary_1.ContainsKey(uint_0))
		{
			return Class607.B630A78B.object_0[398](dictionary_1[uint_0], " (0x", Class607.B630A78B.object_0[993](ref uint_0, "X"), ")");
		}
		return Class607.B630A78B.object_0[720]("Unknown: 0x", Class607.B630A78B.object_0[993](ref uint_0, "X"));
	}
}
