using System.Text;

public class GException2 : GException1
{
	protected E3348281 C68599A9;

	public GException2(string string_0, E3348281 AA3C238B, string string_1)
	{
		object obj = Class607.B630A78B.object_0[398];
		long DB1A9A9E = AA3C238B.E903D636;
		base._002Ector(obj("at pos ", Class607.B630A78B.object_0[125](ref DB1A9A9E), ": validation failed: ", string_0), string_1);
		C68599A9 = AA3C238B;
	}

	protected static string smethod_0(byte[] byte_0)
	{
		StringBuilder stringBuilder = Class607.B630A78B.object_0[261]("[");
		for (int i = 0; i < byte_0.Length; i++)
		{
			if (i > 0)
			{
				Class607.B630A78B.object_0[761](stringBuilder, ' ');
			}
			Class607.B630A78B.object_0[426](stringBuilder, Class607.B630A78B.object_0[1217]("{0:X2}", byte_0[i]));
		}
		Class607.B630A78B.object_0[761](stringBuilder, ']');
		return stringBuilder.ToString();
	}
}
