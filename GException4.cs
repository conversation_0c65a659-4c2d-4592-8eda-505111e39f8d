public class GException4 : GException2
{
	protected object object_0;

	protected object D405B589;

	public GException4(byte[] byte_0, byte[] D405B589, E3348281 e3348281_0, string string_0)
		: base(Class607.B630A78B.object_0[398]("not in range, min ", GException2.smethod_0(byte_0), ", but got ", GException2.smethod_0(D405B589)), e3348281_0, string_0)
	{
		object_0 = byte_0;
		this.D405B589 = D405B589;
	}

	public GException4(object object_1, object object_2, E3348281 e3348281_0, string string_0)
		: base(Class334.smethod_0("not in range, min ", object_1?.ToString(), ", but got ", object_2?.ToString()), e3348281_0, string_0)
	{
		object_0 = object_1;
		D405B589 = object_2;
	}
}
