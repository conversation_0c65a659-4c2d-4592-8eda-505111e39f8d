using System;
using System.Collections;
using System.Drawing;
using System.Drawing.Text;
using System.Linq;
using System.Runtime.InteropServices;
using System.Windows.Forms;

public class GClass106
{
	public enum C4A86298 : byte
	{
		const_0,
		const_1
	}

	private static GClass106 gclass106_0;

	private C4A86298 F61B7FAC;

	private GClass99 C62B1C2A;

	private static readonly Color B3284999 = Class607.B630A78B.object_0[1194](222, 0, 0, 0);

	private static readonly Brush AF89348A = Class607.B630A78B.object_0[1044](B3284999);

	public static Color CC2497B0 = Class607.B630A78B.object_0[1194](138, 0, 0, 0);

	public static Brush brush_0 = Class607.B630A78B.object_0[1044](CC2497B0);

	private static readonly Color color_0 = Class607.B630A78B.object_0[1194](66, 0, 0, 0);

	private static readonly Brush brush_1 = Class607.B630A78B.object_0[1044](color_0);

	private static readonly Color color_1 = Class607.B630A78B.object_0[1194](31, 0, 0, 0);

	private static readonly Brush E721B818 = Class607.B630A78B.object_0[1044](color_1);

	private static readonly Color color_2 = Class607.B630A78B.object_0[1194](255, 255, 255, 255);

	private static readonly Brush brush_2 = Class607.B630A78B.object_0[1044](color_2);

	public static Color D6B1D502 = Class607.B630A78B.object_0[1194](179, 255, 255, 255);

	public static Brush BE2E9721 = Class607.B630A78B.object_0[1044](D6B1D502);

	private static readonly Color color_3 = Class607.B630A78B.object_0[1194](77, 255, 255, 255);

	private static readonly Brush C6B18636 = Class607.B630A78B.object_0[1044](color_3);

	private static readonly Color AA8AF030 = Class607.B630A78B.object_0[1194](31, 255, 255, 255);

	private static readonly Brush brush_3 = Class607.B630A78B.object_0[1044](AA8AF030);

	private static readonly Color color_4 = Class607.B630A78B.object_0[1194](138, 0, 0, 0);

	private static readonly Brush brush_4 = Class607.B630A78B.object_0[1044](color_4);

	private static readonly Color color_5 = Class607.B630A78B.object_0[1194](66, 0, 0, 0);

	private static readonly Brush brush_5 = Class607.B630A78B.object_0[1044](color_5);

	private static readonly Color color_6 = Class607.B630A78B.object_0[1194](179, 255, 255, 255);

	private static readonly Brush brush_6 = Class607.B630A78B.object_0[1044](color_6);

	private static readonly Color color_7 = Class607.B630A78B.object_0[1194](77, 255, 255, 255);

	private static readonly Brush brush_7 = Class607.B630A78B.object_0[1044](color_7);

	private static readonly Color D58F56B5 = Class607.B630A78B.object_0[1194](255, 255, 255, 255);

	private static readonly Brush brush_8 = Class607.B630A78B.object_0[1044](D58F56B5);

	private static readonly Color color_8 = color_2;

	private static readonly Brush EB1D5F90 = Class607.B630A78B.object_0[1044](color_8);

	private static readonly Color color_9 = B3284999;

	private static readonly Brush brush_9 = Class607.B630A78B.object_0[1044](color_9);

	private static readonly Color EC8F3B9D = Class607.B630A78B.object_0[117](20.smethod_1(), 10066329.smethod_0());

	private static readonly Brush brush_10 = Class607.B630A78B.object_0[1044](EC8F3B9D);

	private static readonly Color color_10 = Class607.B630A78B.object_0[117](40.smethod_1(), 10066329.smethod_0());

	private static readonly Brush brush_11 = Class607.B630A78B.object_0[1044](color_10);

	private static readonly Color color_11 = Class607.B630A78B.object_0[117](26.smethod_1(), 0.smethod_0());

	private static readonly Brush brush_12 = Class607.B630A78B.object_0[1044](color_11);

	private static readonly Color color_12 = Class607.B630A78B.object_0[117](15.smethod_1(), 13421772.smethod_0());

	private static readonly Brush brush_13 = Class607.B630A78B.object_0[1044](color_12);

	private static readonly Color color_13 = Class607.B630A78B.object_0[117](25.smethod_1(), 13421772.smethod_0());

	private static readonly Brush brush_14 = Class607.B630A78B.object_0[1044](color_13);

	private static readonly Color C1345097 = Class607.B630A78B.object_0[117](30.smethod_1(), 16777215.smethod_0());

	private static readonly Brush brush_15 = Class607.B630A78B.object_0[1044](C1345097);

	private static readonly Color C99EF7A8 = Class607.B630A78B.object_0[1194](255, 238, 238, 238);

	private static readonly Brush A43EAF0D = Class607.B630A78B.object_0[1044](C99EF7A8);

	private static readonly Color color_14 = Class607.B630A78B.object_0[1194](38, 204, 204, 204);

	private static readonly Brush brush_16 = Class607.B630A78B.object_0[1044](color_14);

	private static readonly Color color_15 = Class607.B630A78B.object_0[1194](255, 255, 255, 255);

	private static Brush brush_17 = Class607.B630A78B.object_0[1044](color_15);

	private static readonly Color ******** = Class607.B630A78B.object_0[1194](255, 51, 51, 51);

	private static Brush brush_18 = Class607.B630A78B.object_0[1044](********);

	public readonly Color color_16 = Class607.B630A78B.object_0[1194](255, 255, 255, 255);

	public readonly Brush BA2C682B = Class607.B630A78B.object_0[1044](Class607.B630A78B.object_0[1194](255, 255, 255, 255));

	public readonly Color color_17 = Class607.B630A78B.object_0[1194](153, 255, 255, 255);

	public readonly Brush D4291EAF = Class607.B630A78B.object_0[1044](Class607.B630A78B.object_0[1194](153, 255, 255, 255));

	public Font D708BEA2;

	public Font font_0;

	public Font font_1;

	public Font font_2;

	public Font B39C48BD;

	public Font font_3;

	public int int_0 = 14;

	private readonly PrivateFontCollection EDB46408 = Class607.B630A78B.object_0[475]();

	public C4A86298 C4A86298_0
	{
		get
		{
			return F61B7FAC;
		}
		set
		{
			F61B7FAC = value;
			method_5();
		}
	}

	public GClass99 CEBAB4AB
	{
		get
		{
			return C62B1C2A;
		}
		set
		{
			C62B1C2A = value;
			method_5();
		}
	}

	public static GClass106 GClass106_0 => gclass106_0 ?? (gclass106_0 = new GClass106());

	public Color method_0()
	{
		return (C4A86298_0 == C4A86298.const_0) ? B3284999 : color_2;
	}

	public Brush EB0DA2B0()
	{
		return brush_8;
	}

	public Brush BE2B6333(bool bool_0)
	{
		return bool_0 ? EB1D5F90 : brush_9;
	}

	public Color method_1()
	{
		return (C4A86298_0 == C4A86298.const_0) ? color_5 : color_7;
	}

	public Brush method_2()
	{
		return (C4A86298_0 == C4A86298.const_0) ? brush_1 : C6B18636;
	}

	public Color method_3()
	{
		return (C4A86298_0 == C4A86298.const_0) ? color_15 : ********;
	}

	public Color method_4()
	{
		return (C4A86298_0 == C4A86298.const_0) ? color_4 : color_6;
	}

	public Brush C38AE18D()
	{
		return (C4A86298_0 == C4A86298.const_0) ? AF89348A : brush_2;
	}

	[DllImport("gdi32.dll")]
	private static extern IntPtr AddFontMemResourceEx(IntPtr D0117F3F, uint uint_0, IntPtr E733542F, [In] ref uint F4818D0D);

	private GClass106()
	{
		Class607.B630A78B.object_0[571](this);
		font_3 = Class607.B630A78B.object_0[393](A6047DA8(A282DB38.Roboto_Medium), 9f);
		B39C48BD = Class607.B630A78B.object_0[393](A6047DA8(A282DB38.Roboto_Medium), 10f);
		font_2 = Class607.B630A78B.object_0[393](A6047DA8(A282DB38.Roboto_Medium), 11f);
		D708BEA2 = Class607.B630A78B.object_0[393](A6047DA8(A282DB38.Roboto_Medium), 12f);
		font_0 = Class607.B630A78B.object_0[393](A6047DA8(A282DB38.Roboto_Regular), 11f);
		font_1 = Class607.B630A78B.object_0[393](A6047DA8(A282DB38.Roboto_Regular), 8f);
		C4A86298_0 = C4A86298.const_0;
		CEBAB4AB = new GClass99(GEnum34.F924FEBC, GEnum34.const_195, GEnum34.********, GEnum35.const_6, GEnum33.const_0);
	}

	private FontFamily A6047DA8(byte[] DFBE4A3F)
	{
		int num = DFBE4A3F.Length;
		IntPtr intPtr = Class607.B630A78B.object_0[770](num);
		Class607.B630A78B.object_0[150](DFBE4A3F, 0, intPtr, num);
		uint F4818D0D = 0u;
		AddFontMemResourceEx(intPtr, (uint)DFBE4A3F.Length, Class607.B630A78B.object_0[120](), ref F4818D0D);
		Class607.B630A78B.object_0[678](EDB46408, intPtr, num);
		return Class607.B630A78B.object_0[254](EDB46408).Last();
	}

	private void method_5()
	{
		method_3();
	}

	private void method_6(Control control_0, Color DD17CD2C)
	{
		if (control_0 == null)
		{
			return;
		}
		if (control_0 is GControl1 object_)
		{
			{
				IEnumerator object_2 = Class607.B630A78B.object_0[1227](object_).GetEnumerator();
				try
				{
					while (Class607.B630A78B.object_0[212](object_2))
					{
						TabPage object_3 = (TabPage)Class607.B630A78B.object_0[107](object_2);
						Class607.B630A78B.object_0[823](object_3, DD17CD2C);
					}
				}
				finally
				{
					IDisposable disposable = object_2 as IDisposable;
					if (disposable != null)
					{
						disposable.Dispose();
					}
				}
			}
		}
		IEnumerator object_4 = Class607.B630A78B.object_0[409](Class607.B630A78B.object_0[781](control_0));
		try
		{
			while (Class607.B630A78B.object_0[212](object_4))
			{
				Control control_1 = (Control)Class607.B630A78B.object_0[107](object_4);
				method_6(control_1, DD17CD2C);
			}
		}
		finally
		{
			IDisposable disposable2 = object_4 as IDisposable;
			if (disposable2 != null)
			{
				disposable2.Dispose();
			}
		}
		Class607.B630A78B.object_0[584](control_0);
	}
}
