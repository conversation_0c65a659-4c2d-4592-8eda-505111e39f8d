public class GClass109
{
	public string BC9C7B9E;

	public GClass109()
	{
		Class607.B630A78B.object_0[571](this);
		BC9C7B9E = D09F0D3B.smethod_5(1220599u);
	}

	public string method_0(string string_0)
	{
		return (string)new GClass128().method_68(new object[2] { this, string_0 }, 304548);
	}

	public string EC2264AF(string string_0)
	{
		return (string)new GClass128().DFB12B0F(new object[2] { this, string_0 }, 272524);
	}

	public bool method_1(string string_0, string DD9A4526)
	{
		return (bool)new GClass128().AD84C1A2(new object[3] { this, string_0, DD9A4526 }, 37436);
	}
}
