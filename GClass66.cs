using System;
using System.Runtime.InteropServices;
using Microsoft.Win32.SafeHandles;

public class GClass66
{
	private struct Struct0
	{
		public uint uint_0;

		public uint D681709C;

		public uint E210C18A;

		public uint uint_1;

		public uint uint_2;

		public uint uint_3;

		public uint uint_4;

		public uint EA389FB5;

		public uint uint_5;

		public uint uint_6;
	}

	public struct GStruct62
	{
		public uint uint_0;

		public uint uint_1;

		public uint FF10CB14;

		public uint uint_2;

		public uint uint_3;

		public uint D487329F;

		public uint uint_4;

		public uint uint_5;

		public uint uint_6;

		public uint F5092E09;

		public uint uint_7;

		public uint uint_8;

		public uint uint_9;

		public uint uint_10;

		public uint uint_11;

		public uint uint_12;

		public ushort ushort_0;

		public ushort ushort_1;

		public ushort E6A9FDBD;

		public byte byte_0;

		public byte byte_1;

		public byte byte_2;

		public char char_0;

		public char char_1;

		public char char_2;

		public char char_3;

		public char char_4;

		public ushort ********;
	}

	private struct D8A61107
	{
		public long FFAC94AA;

		public long long_0;

		public long long_1;

		public long F09DB7B3;

		public long long_2;
	}

	private const uint uint_0 = 2147483648u;

	private const uint C7BC26AC = 1073741824u;

	private const uint uint_1 = 536870912u;

	private const uint A0227690 = 268435456u;

	private const int int_0 = 110;

	private const int F608FD0A = 300;

	private const int int_1 = 600;

	private const int int_2 = 1200;

	private const int CA874280 = 2400;

	private const int int_3 = 4800;

	private const int int_4 = 9600;

	private const int F18A2DB7 = 14400;

	private const int int_5 = 19200;

	private const int FE06D2A6 = 38400;

	private const int int_6 = 56000;

	private const int int_7 = 57600;

	private const int int_8 = 115200;

	private const int int_9 = 128000;

	private const int int_10 = 256000;

	private const int FE15FEB8 = 0;

	private const int E22EF617 = 1;

	private const int int_11 = 2;

	private const int int_12 = 3;

	private const int int_13 = 32768;

	private const int D88F601D = 0;

	private const int int_14 = 1;

	private const int int_15 = 1;

	private const int int_16 = 1;

	private const int ******** = 3;

	private Struct0 E608BD9F;

	private string string_0;

	private SafeFileHandle safeFileHandle_0;

	public const int CA0F4503 = 1073741824;

	public const int int_17 = 128;

	public string String_0
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public bool C1B6CD1E => safeFileHandle_0 != null;

	internal int Int32_0
	{
		get
		{
			uint uint_ = 0u;
			if (ClearCommError(safeFileHandle_0, out uint_, out E608BD9F) <= 0)
			{
				return 0;
			}
			return (int)E608BD9F.uint_5;
		}
	}

	public void A11451B5(int EB29BE86)
	{
		D8A61107 d8A61107_ = new D8A61107
		{
			FFAC94AA = EB29BE86,
			long_0 = EB29BE86,
			F09DB7B3 = EB29BE86,
			long_1 = EB29BE86,
			long_2 = EB29BE86
		};
		if (SetCommTimeouts(safeFileHandle_0, ref d8A61107_) == 0)
		{
			E61BA82A();
			throw Class607.B630A78B.object_0[778]("[PORT ERR] FAILED TO SET TIMEOUT SETTING");
		}
	}

	public bool method_0()
	{
		uint uint_ = 0u;
		ClearCommError(safeFileHandle_0, out uint_, out var _);
		SetupComm(safeFileHandle_0, 8192, 8192);
		GStruct62 gstruct62_ = default(GStruct62);
		gstruct62_.uint_0 = (uint)Marshal.SizeOf(gstruct62_);
		if (GetCommState(safeFileHandle_0, out gstruct62_) <= 0)
		{
			E61BA82A();
			throw Class607.B630A78B.object_0[778]("[PORT ERR] FAILED TO GET CONFIGURATION SETTINGS");
		}
		gstruct62_.uint_1 = 115200u;
		gstruct62_.uint_11 = 1u;
		gstruct62_.FF10CB14 = 1u;
		gstruct62_.byte_0 = 8;
		gstruct62_.byte_2 = 0;
		gstruct62_.uint_2 = 0u;
		gstruct62_.byte_1 = 0;
		gstruct62_.uint_4 = 1u;
		gstruct62_.uint_10 = 1u;
		gstruct62_.uint_3 = 0u;
		gstruct62_.D487329F = 0u;
		gstruct62_.uint_5 = 0u;
		gstruct62_.F5092E09 = 0u;
		gstruct62_.uint_7 = 0u;
		if (SetCommState(safeFileHandle_0, ref gstruct62_) == 0)
		{
			E61BA82A();
			throw Class607.B630A78B.object_0[778]("[PORT ERR] FAILED TO SET CONFIGURATION SETTINGS");
		}
		ClearCommBreak(safeFileHandle_0);
		EscapeCommFunction(safeFileHandle_0, 3);
		EscapeCommFunction(safeFileHandle_0, 5);
		PurgeComm(safeFileHandle_0, 15u);
		D8A61107 d8A61107_ = new D8A61107
		{
			FFAC94AA = 0L,
			long_0 = 0L,
			F09DB7B3 = 0L,
			long_1 = 5000L,
			long_2 = 5000L
		};
		if (SetCommTimeouts(safeFileHandle_0, ref d8A61107_) == 0)
		{
			E61BA82A();
			throw Class607.B630A78B.object_0[778]("[PORT ERR] FAILED TO SET TIMEOUT SETTING");
		}
		return true;
	}

	public bool method_1()
	{
		if (Class607.B630A78B.object_0[1205](string_0))
		{
			throw Class607.B630A78B.object_0[778]("[PORT ERR] EMPTY PORT NUMBER");
		}
		safeFileHandle_0 = CreateFile(Class607.B630A78B.object_0[720]("\\\\.\\", string_0), 3221225472u, 0u, Class607.B630A78B.object_0[120](), 3u, 0u, Class607.B630A78B.object_0[120]());
		if (!AC9E0D93())
		{
			int num = Class607.B630A78B.object_0[727]();
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[1217]("[PORT ERR] FAILED TO CONNECT PORT Error code: {0}", num));
		}
		return method_0();
	}

	public void E61BA82A()
	{
		if (safeFileHandle_0 != null && !Class607.B630A78B.object_0[3](safeFileHandle_0) && !Class607.B630A78B.object_0[1087](safeFileHandle_0))
		{
			try
			{
				method_6();
				method_7();
				method_8();
				ClearCommError(safeFileHandle_0, out var _, out var _);
				Class607.B630A78B.object_0[621](safeFileHandle_0);
			}
			catch
			{
			}
		}
	}

	public bool AC9E0D93()
	{
		if (safeFileHandle_0 == null || Class607.B630A78B.object_0[1087](safeFileHandle_0) || Class607.B630A78B.object_0[3](safeFileHandle_0))
		{
			return false;
		}
		return true;
	}

	private unsafe int ********(byte* pByte_0, int int_18)
	{
		int int_19 = 0;
		if (!WriteFile(safeFileHandle_0, pByte_0, int_18, out int_19, Class607.B630A78B.object_0[120]()))
		{
			int num = Class607.B630A78B.object_0[727]();
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[1217]("[PORT ERR] {0}", num));
		}
		return Class607.B630A78B.object_0[229](int_19);
	}

	public string method_2()
	{
		int int32_ = Int32_0;
		if (int32_ > 0)
		{
			byte[] array = new byte[int32_];
			int num = method_5(array, int32_);
			if (num > 0)
			{
				return Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[1096](), array);
			}
		}
		return "";
	}

	private unsafe int method_3(byte* pByte_0, int int_18)
	{
		int int_19 = 0;
		if (!ReadFile(safeFileHandle_0, pByte_0, int_18, out int_19, Class607.B630A78B.object_0[120]()))
		{
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[1217]("[PORT ERR] {0}", Class607.B630A78B.object_0[727]()));
		}
		return int_19;
	}

	public bool B5B5FAB2(string string_1)
	{
		byte[] array = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), string_1);
		return method_4(array, array.Length);
	}

	public unsafe bool method_4(byte[] F12A408E, int int_18)
	{
		if (!AC9E0D93())
		{
			method_1();
		}
		int num = 0;
		fixed (byte* ptr = F12A408E)
		{
			while (int_18 != 0)
			{
				int num2 = ********(ptr + num, int_18);
				num += num2;
				int_18 -= num2;
			}
		}
		return true;
	}

	public unsafe int method_5(byte[] byte_0, int C6A1D99E)
	{
		if (!AC9E0D93())
		{
			method_1();
		}
		int num = 0;
		fixed (byte* ptr = byte_0)
		{
			while (C6A1D99E != 0)
			{
				int num2 = method_3(ptr + num, C6A1D99E);
				num += num2;
				C6A1D99E -= num2;
				if (num2 == 0 || C6A1D99E == 0)
				{
					break;
				}
			}
		}
		return num;
	}

	public void method_6()
	{
		if (AC9E0D93())
		{
			FlushFileBuffers(safeFileHandle_0);
		}
	}

	public void method_7()
	{
		if (AC9E0D93())
		{
			PurgeComm(safeFileHandle_0, 8u);
		}
	}

	public void method_8()
	{
		if (AC9E0D93())
		{
			PurgeComm(safeFileHandle_0, 4u);
		}
	}

	[DllImport("Kernel32.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
	private static extern int CloseHandle(SafeFileHandle E006EF03);

	[DllImport("Kernel32.dll", SetLastError = true)]
	internal static extern int GetFileType(UIntPtr uintptr_0);

	[DllImport("Kernel32.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
	private static extern UIntPtr CreateFileW(string A70C7894, uint uint_2, uint uint_3, UIntPtr uintptr_0, uint ********, uint uint_4, UIntPtr uintptr_1);

	[DllImport("kernel32.dll", CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern SafeFileHandle CreateFile(string AF23893E, uint uint_2, uint uint_3, IntPtr A334369E, uint E82FD027, uint uint_4, IntPtr intptr_0);

	[DllImport("Kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
	internal static extern bool PurgeComm(SafeFileHandle safeFileHandle_1, uint ABB1D490);

	[DllImport("Kernel32.dll", SetLastError = true)]
	internal unsafe static extern bool WriteFile(SafeFileHandle safeFileHandle_1, byte* pByte_0, int B01A8404, out int int_18, IntPtr intptr_0);

	[DllImport("Kernel32.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
	private static extern int ClearCommError(SafeFileHandle BF3E7385, out uint uint_2, out Struct0 struct0_0);

	[DllImport("Kernel32.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
	private static extern int FlushFileBuffers(SafeFileHandle safeFileHandle_1);

	[DllImport("Kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
	internal static extern bool ClearCommBreak(SafeFileHandle safeFileHandle_1);

	[DllImport("Kernel32.dll", SetLastError = true)]
	internal static extern bool EscapeCommFunction(SafeFileHandle F62B56B9, int int_18);

	[DllImport("Kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
	internal static extern bool SetupComm(SafeFileHandle safeFileHandle_1, int int_18, int A18E4B3A);

	[DllImport("Kernel32.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
	private static extern int GetCommState(SafeFileHandle E7855D31, out GStruct62 gstruct62_0);

	[DllImport("Kernel32.dll", SetLastError = true)]
	internal unsafe static extern bool ReadFile(SafeFileHandle F20E5C82, byte* CD1090AA, int E3A32713, out int int_18, IntPtr A1BCA828);

	[DllImport("Kernel32.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
	private static extern int SetCommState(SafeFileHandle safeFileHandle_1, ref GStruct62 gstruct62_0);

	[DllImport("Kernel32.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode)]
	private static extern int SetCommTimeouts(SafeFileHandle safeFileHandle_1, ref D8A61107 d8A61107_0);

	public GClass66()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
