using System;
using System.Runtime.ExceptionServices;
using System.Runtime.InteropServices;
using System.Security;

public class GClass79
{
	public enum GEnum26
	{
		CC891C22,
		const_1
	}

	public struct GStruct66
	{
		public uint uint_0;

		public GEnum26 genum26_0;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
		public string string_0;

		public byte[] ED932A3E;

		public uint B617FE12;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
		public string EE1FEAB2;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
		public string A711DA35;
	}

	public static IntPtr intptr_0;

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	[SecurityCritical]
	[HandleProcessCorruptedStateExceptions]
	private static extern int SCERT_Create(ref IntPtr FE232993);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	private static extern int SCERT_Unload(IntPtr C399231F);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	private static extern int SCERT_Destroy(ref IntPtr AC92CC29);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int SCERT_Load(IntPtr DFA6F313, byte[] E1BF73A3);

	public static bool smethod_0(string string_0)
	{
		SCERT_Unload(intptr_0);
		if (SCERT_Load(intptr_0, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), string_0)) == 0)
		{
			return true;
		}
		return false;
	}

	public static void smethod_1()
	{
		int num = -1;
		num = SCERT_Create(ref intptr_0);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
	}

	public static void smethod_2()
	{
		if (!Class607.B630A78B.object_0[1182](intptr_0, Class607.B630A78B.object_0[120]()))
		{
			int num = -1;
			num = SCERT_Unload(intptr_0);
			if (num != 0)
			{
				string f097F68F = GClass29.smethod_0(num);
				f097F68F = GClass29.smethod_1(f097F68F, num);
			}
			num = SCERT_Destroy(ref intptr_0);
			if (num != 0)
			{
				string f097F68F2 = GClass29.smethod_0(num);
				f097F68F2 = GClass29.smethod_1(f097F68F2, num);
			}
		}
	}

	public GClass79()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
