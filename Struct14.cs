using System;

internal struct Struct14
{
	private readonly int int_0;

	private readonly uint[] DD3C9F8A;

	private static readonly Struct14 struct14_0 = new Struct14(-1, new uint[1] { 2147483648u });

	private static readonly Struct14 struct14_1 = new Struct14(1);

	private static readonly Struct14 struct14_2 = new Struct14(0);

	private static readonly Struct14 struct14_3 = new Struct14(-1);

	private static void smethod_0(ref Struct32 struct32_0, ref Struct32 FB2D09BC, ref Struct32 struct32_1)
	{
		B315D385(ref struct32_0, ref struct32_1);
		struct32_0.method_7(struct32_1, struct32_1);
		struct32_0.A905BE24(FB2D09BC);
	}

	internal static void B315D385(ref Struct32 struct32_0, ref Struct32 struct32_1)
	{
		Struct32 @struct = struct32_0;
		struct32_0 = struct32_1;
		struct32_1 = @struct;
	}

	internal static Struct14 smethod_1(Struct14 AD2C7384, Struct14 F1071684, Struct14 A7A914BC)
	{
		if (F1071684.method_0() < 0)
		{
			throw new ArgumentOutOfRangeException();
		}
		bool flag = F1071684.method_2();
		Struct32 struct32_ = new Struct32(struct14_1.int_0, struct14_1.DD3C9F8A);
		Struct32 struct32_2 = new Struct32(AD2C7384.int_0, AD2C7384.DD3C9F8A);
		Struct32 E7B3C7BB = new Struct32(A7A914BC.int_0, A7A914BC.DD3C9F8A);
		Struct32 struct32_3 = new Struct32(struct32_2.method_5());
		struct32_.A905BE24(E7B3C7BB);
		uint num;
		if (F1071684.DD3C9F8A == null)
		{
			num = (uint)F1071684.int_0;
		}
		else
		{
			int num2 = smethod_4(F1071684.DD3C9F8A);
			for (int i = 0; i < num2 - 1; i++)
			{
				num = F1071684.DD3C9F8A[i];
				int num3 = 0;
				while (num3 < 32)
				{
					if ((num & 1) == 1)
					{
						F32789A3(ref struct32_, ref struct32_2, ref E7B3C7BB, ref struct32_3);
					}
					smethod_0(ref struct32_2, ref E7B3C7BB, ref struct32_3);
					num >>= 1;
					i++;
				}
			}
			num = F1071684.DD3C9F8A[num2 - 1];
		}
		while (num != 0)
		{
			if ((num & 1) == 1)
			{
				F32789A3(ref struct32_, ref struct32_2, ref E7B3C7BB, ref struct32_3);
			}
			if (num == 1)
			{
				break;
			}
			smethod_0(ref struct32_2, ref E7B3C7BB, ref struct32_3);
			num >>= 1;
		}
		return struct32_.method_4((AD2C7384.int_0 > 0) ? 1 : (flag ? 1 : (-1)));
	}

	public static int smethod_2(Struct14 struct14_4, Struct14 E63B390F)
	{
		return struct14_4.method_1(E63B390F);
	}

	internal static void smethod_3(uint[] uint_0)
	{
		int i = 0;
		uint num = 0u;
		for (; i < uint_0.Length; i++)
		{
			num = (uint_0[i] = ~uint_0[i] + 1);
			if (num != 0)
			{
				i++;
				break;
			}
		}
		if (num != 0)
		{
			for (; i < uint_0.Length; i++)
			{
				uint_0[i] = ~uint_0[i];
			}
		}
		else
		{
			Array.Resize(ref uint_0, uint_0.Length + 1);
			uint_0[uint_0.Length - 1] = 1u;
		}
	}

	private int method_0()
	{
		return (int_0 >> 31) - (-int_0 >> 31);
	}

	internal Struct14(byte[] E21CE608)
	{
		if (E21CE608 == null)
		{
			throw new ArgumentNullException();
		}
		int num = E21CE608.Length;
		bool flag = num > 0 && (E21CE608[num - 1] & 0x80) == 128;
		while (num > 0 && E21CE608[num - 1] == 0)
		{
			num--;
		}
		if (num == 0)
		{
			int_0 = 0;
			DD3C9F8A = null;
			return;
		}
		if (num <= 4)
		{
			if (flag)
			{
				int_0 = -1;
			}
			else
			{
				int_0 = 0;
			}
			for (int num2 = num - 1; num2 >= 0; num2--)
			{
				int_0 <<= 8;
				int_0 |= E21CE608[num2];
			}
			DD3C9F8A = null;
			if (int_0 < 0 && !flag)
			{
				DD3C9F8A = new uint[1];
				DD3C9F8A[0] = (uint)int_0;
				int_0 = 1;
			}
			if (int_0 == int.MinValue)
			{
				this = struct14_0;
			}
			return;
		}
		int num3 = num % 4;
		int num4 = num / 4 + ((num3 != 0) ? 1 : 0);
		bool flag2 = true;
		uint[] array = new uint[num4];
		int num5 = 3;
		int i;
		for (i = 0; i < num4 - ((num3 != 0) ? 1 : 0); i++)
		{
			for (int j = 0; j < 4; j++)
			{
				if (E21CE608[num5] != 0)
				{
					flag2 = false;
				}
				array[i] = (array[i] << 8) | E21CE608[num5];
				num5--;
			}
			num5 += 8;
		}
		if (num3 != 0)
		{
			if (flag)
			{
				array[num4 - 1] = uint.MaxValue;
			}
			for (num5 = num - 1; num5 >= num - num3; num5--)
			{
				if (E21CE608[num5] != 0)
				{
					flag2 = false;
				}
				array[i] = (array[i] << 8) | E21CE608[num5];
			}
		}
		if (flag2)
		{
			this = struct14_2;
		}
		else if (flag)
		{
			smethod_3(array);
			int num6 = array.Length;
			while (num6 > 0 && array[num6 - 1] == 0)
			{
				num6--;
			}
			if (num6 == 1 && (int)array[0] > 0)
			{
				if (array[0] == 1)
				{
					this = struct14_3;
					return;
				}
				if (array[0] == 2147483648u)
				{
					this = struct14_0;
					return;
				}
				int_0 = -1 * (int)array[0];
				DD3C9F8A = null;
			}
			else if (num6 != array.Length)
			{
				int_0 = -1;
				DD3C9F8A = new uint[num6];
				Array.Copy(array, DD3C9F8A, num6);
			}
			else
			{
				int_0 = -1;
				DD3C9F8A = array;
			}
		}
		else
		{
			int_0 = 1;
			DD3C9F8A = array;
		}
	}

	private int method_1(Struct14 struct14_4)
	{
		if ((int_0 ^ struct14_4.int_0) < 0)
		{
			if (int_0 >= 0)
			{
				return 1;
			}
			return -1;
		}
		if (DD3C9F8A == null)
		{
			if (struct14_4.DD3C9F8A == null)
			{
				if (int_0 >= struct14_4.int_0)
				{
					if (int_0 <= struct14_4.int_0)
					{
						return 0;
					}
					return 1;
				}
				return -1;
			}
			return -struct14_4.int_0;
		}
		int num;
		int num2;
		if (struct14_4.DD3C9F8A != null && (num = smethod_4(DD3C9F8A)) <= (num2 = smethod_4(struct14_4.DD3C9F8A)))
		{
			if (num < num2)
			{
				return -int_0;
			}
			int num3 = CF104324(DD3C9F8A, struct14_4.DD3C9F8A, num);
			if (num3 == 0)
			{
				return 0;
			}
			if (DD3C9F8A[num3 - 1] >= struct14_4.DD3C9F8A[num3 - 1])
			{
				return int_0;
			}
			return -int_0;
		}
		return int_0;
	}

	private static void F32789A3(ref Struct32 struct32_0, ref Struct32 struct32_1, ref Struct32 E7B3C7BB, ref Struct32 struct32_2)
	{
		B315D385(ref struct32_0, ref struct32_2);
		struct32_0.method_7(struct32_2, struct32_1);
		struct32_0.A905BE24(E7B3C7BB);
	}

	internal byte[] DF93CB0A()
	{
		if (DD3C9F8A == null && int_0 == 0)
		{
			return new byte[1];
		}
		uint[] array;
		byte b;
		if (DD3C9F8A == null)
		{
			array = new uint[1] { (uint)int_0 };
			b = (byte)((int_0 < 0) ? 255u : 0u);
		}
		else if (int_0 == -1)
		{
			array = (uint[])DD3C9F8A.Clone();
			smethod_3(array);
			b = byte.MaxValue;
		}
		else
		{
			array = DD3C9F8A;
			b = 0;
		}
		int num = ((array[array.Length - 1] >> 31 != b >> 7) ? 1 : 0);
		int num2 = num + 4 * array.Length;
		byte[] array2 = new byte[num2];
		uint[] array3 = array;
		for (int i = 0; i < array3.Length; i++)
		{
			uint num3 = array3[i];
			for (int j = 0; j < 4; j++)
			{
				array2[--num2] = (byte)num3;
				num3 >>= 8;
			}
		}
		if (num != 0)
		{
			array2[0] = b;
		}
		return array2;
	}

	private static int CF104324(uint[] AC2F9807, uint[] C625940C, int DA1A6D35)
	{
		int num = DA1A6D35;
		do
		{
			if (--num < 0)
			{
				return 0;
			}
		}
		while (AC2F9807[num] == C625940C[num]);
		return num + 1;
	}

	private static int smethod_4(uint[] uint_0)
	{
		int num = uint_0.Length;
		if (uint_0[num - 1] != 0)
		{
			return num;
		}
		return num - 1;
	}

	internal Struct14(int int_1, uint[] uint_0)
	{
		int_0 = int_1;
		DD3C9F8A = uint_0;
	}

	private Struct14(int int_1)
	{
		if (int_1 == int.MinValue)
		{
			this = struct14_0;
			return;
		}
		int_0 = int_1;
		DD3C9F8A = null;
	}

	private bool method_2()
	{
		if (DD3C9F8A != null)
		{
			return (DD3C9F8A[0] & 1) == 0;
		}
		return (int_0 & 1) == 0;
	}
}
