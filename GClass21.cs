using System.Collections.Generic;

public class GClass21
{
	public GClass20 D6B7BEB4;

	public uint uint_0;

	public uint uint_1;

	public uint A1A10A0C;

	public uint uint_2;

	public uint BBB51023;

	public uint D5BBA61C;

	public uint uint_3;

	public byte[] byte_0;

	public GClass21(GClass20 B9B1502D)
	{
		Class607.B630A78B.object_0[571](this);
		D6B7BEB4 = B9B1502D;
		uint_0 = 1296911693u;
		uint_3 = 1162167621u;
	}

	public bool B388D22E(byte[] E4B62425)
	{
		object[] array = GClass111.C3B9331C("<IIIIIII", GClass112.smethod_14(E4B62425, 0, 28));
		uint_0 = Class607.B630A78B.object_0[40](array[0]);
		uint_1 = Class607.B630A78B.object_0[40](array[1]);
		A1A10A0C = Class607.B630A78B.object_0[40](array[2]);
		uint_2 = Class607.B630A78B.object_0[40](array[3]);
		BBB51023 = Class607.B630A78B.object_0[40](array[4]);
		D5BBA61C = Class607.B630A78B.object_0[40](array[5]);
		uint_3 = Class607.B630A78B.object_0[40](array[6]);
		byte_0 = GClass112.smethod_14(E4B62425, 28, 32);
		if ((uint_0 != 1296911693) | (uint_3 != 1162167621))
		{
			return false;
		}
		return true;
	}

	public byte[] method_0(GClass21 A015BD1A, string string_0, string string_1 = "unlock", bool bool_0 = false)
	{
		if (A015BD1A == null)
		{
			if (Class607.B630A78B.object_0[787](string_1, "unlock"))
			{
				uint_2 = 3u;
				BBB51023 = 1u;
				uint_1 = 4u;
				A1A10A0C = 60u;
				D5BBA61C = 0u;
			}
			else if (Class607.B630A78B.object_0[787](string_1, "lock"))
			{
				uint_2 = 1u;
				BBB51023 = 1u;
				uint_1 = 4u;
				A1A10A0C = 60u;
				D5BBA61C = 0u;
			}
		}
		else
		{
			uint_2 = A015BD1A.uint_2;
			BBB51023 = A015BD1A.BBB51023;
			A1A10A0C = A015BD1A.A1A10A0C;
			D5BBA61C = A015BD1A.D5BBA61C;
			uint_1 = A015BD1A.uint_1;
		}
		byte[] collection = GClass111.smethod_2("<IIIIIII", new object[7] { uint_0, uint_1, A1A10A0C, uint_2, BBB51023, D5BBA61C, 1162167621 });
		byte[] array = GClass112.DB37D01D(collection);
		byte[] collection2 = (byte_0 = (Class607.B630A78B.object_0[787](string_0, "sw") ? D6B7BEB4.c51E6F3B_0.method_8(array) : (bool_0 ? D6B7BEB4.c51E6F3B_0.E0B6E3AB(array) : D6B7BEB4.c51E6F3B_0.E88B4902(array))));
		List<byte> list = new List<byte>();
		list.AddRange(collection);
		list.AddRange(collection2);
		list.AddRange(new byte[512 - list.Count]);
		return list.ToArray();
	}
}
