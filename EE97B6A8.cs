using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net.Security;
using System.Runtime.CompilerServices;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Windows.Forms;
using System.Xml;
using ICSharpCode.SharpZipLib.Zip;

public class EE97B6A8 : UserControl
{
	[Serializable]
	[CompilerGenerated]
	private sealed class B131B718
	{
		public static readonly B131B718 _003C_003E9 = new B131B718();

		public static Predicate<string> _003C_003E9__41_0;

		public static Predicate<GClass88> _003C_003E9__53_0;

		public B131B718()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool F30FC600(string string_0)
		{
			return Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[386](string_0), "modemst1") || Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[386](string_0), "modemst2") || Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[386](string_0), "fsg");
		}

		internal bool method_0(GClass88 gclass88_0)
		{
			return gclass88_0.Enable;
		}
	}

	[CompilerGenerated]
	private sealed class Class28
	{
		public EE97B6A8 C8A5EE9B;

		public bool bool_0;

		public Class28()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void method_0(object sender, EventArgs e)
		{
			C8A5EE9B.method_1(bool_0);
		}
	}

	[CompilerGenerated]
	private sealed class Class29
	{
		public EE97B6A8 C40826A1;

		public ulong B2066AAD;

		public ulong ulong_0;

		public Class29()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void A32E09A8(object sender, EventArgs e)
		{
			C40826A1.method_2(B2066AAD, ulong_0);
		}
	}

	[CompilerGenerated]
	private sealed class E42060B4
	{
		public EE97B6A8 BD9E7A2A;

		public string F1BED124;

		public bool bool_0;

		public EF1F389C AB8B079F;

		public bool AD17F5BA;

		public E42060B4()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void method_0(object sender, EventArgs e)
		{
			BD9E7A2A.DB08D385(F1BED124, bool_0, AB8B079F, AD17F5BA);
		}
	}

	[CompilerGenerated]
	private sealed class Class30
	{
		public EE97B6A8 FA851EAD;

		public List<B2AC1304> list_0;

		public Class30()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	[CompilerGenerated]
	private sealed class Class31
	{
		public bool bool_0;

		public Class30 class30_0;

		public Class31()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void method_0()
		{
			class30_0.FA851EAD.F92D1A9A(class30_0.list_0, bool_0);
		}
	}

	[CompilerGenerated]
	private sealed class Class32
	{
		public string A9AEDFAC;

		public EE97B6A8 ee97B6A8_0;

		public Class32()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void method_0()
		{
			ee97B6A8_0.method_21(A9AEDFAC);
		}
	}

	[CompilerGenerated]
	private sealed class Class33
	{
		public GClass88 gclass88_0;

		public Class33()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool method_0(string E6A232A8)
		{
			return Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[1050](Class607.B630A78B.object_0[386](E6A232A8)), Class607.B630A78B.object_0[1050](gclass88_0.name));
		}
	}

	[CompilerGenerated]
	private sealed class C81E1135
	{
		public string string_0;

		public EE97B6A8 ee97B6A8_0;

		public C81E1135()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void CA11F888()
		{
			ee97B6A8_0.F1323A91(string_0);
		}
	}

	[CompilerGenerated]
	private sealed class Class34
	{
		public List<GClass88> B1318F8E;

		public EE97B6A8 DD1DB780;

		public Class34()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void method_0()
		{
			DD1DB780.list_1.Clear();
			DD1DB780.list_1.AddRange(B1318F8E);
			Class607.B630A78B.object_0[559](Class607.B630A78B.object_0[317](DD1DB780.dataGridView_1));
			foreach (GClass88 item in DD1DB780.list_1)
			{
				int f = Class825.F6039005(Class607.B630A78B.object_0[317](DD1DB780.dataGridView_1), new object[5] { true, item.name, item.lun, item.Sector, item.Sectors });
				Class607.B630A78B.object_0[535](Class607.B630A78B.object_0[522](Class607.B630A78B.object_0[317](DD1DB780.dataGridView_1), f), item);
			}
		}
	}

	[CompilerGenerated]
	private sealed class Class35
	{
		public List<GClass88> list_0;

		public EE97B6A8 B4317F91;

		public Class35()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void method_0()
		{
			B4317F91.method_27(list_0);
		}
	}

	[CompilerGenerated]
	private sealed class Class36
	{
		public List<GClass88> list_0;

		public EE97B6A8 E99B9896;

		public Class36()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	[CompilerGenerated]
	private sealed class Class37
	{
		public string D28C678F;

		public Class36 F72B3C13;

		public Class37()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void method_0()
		{
			F72B3C13.E99B9896.method_29(F72B3C13.list_0, D28C678F);
		}
	}

	private Form F6AAC6B2;

	private List<B2AC1304> list_0 = new List<B2AC1304>();

	public List<GClass88> list_1;

	public CCB47F95 ccb47F95_0;

	private GStruct96 gstruct96_0;

	private FileStream fileStream_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private GClass112.D3A6F5AB d3A6F5AB_0;

	public List<A808BE0B> list_2 = new List<A808BE0B>();

	private GClass113 A088073A;

	private IContainer icontainer_0 = null;

	private GroupBox E9AE2482;

	private E402748E e402748E_0;

	private Label label_0;

	private Button E5BB31B8;

	private GControl2 B2111D3A;

	private GControl1 EC14989A;

	private TabPage tabPage_0;

	private TabPage A8208E90;

	private TabPage tabPage_1;

	private TabPage EB0ADA0C;

	private GroupBox EA1CF18C;

	private GroupBox B394B3AE;

	private GClass102 gclass102_0;

	internal DataGridView dataGridView_0;

	private GClass0 ********;

	private DataGridViewTextBoxColumn ********;

	private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_0;

	private DataGridViewTextBoxColumn F594AF83;

	internal Button button_0;

	internal Button button_1;

	private Button button_2;

	private GroupBox groupBox_0;

	private Button button_3;

	private Button button_4;

	private Button button_5;

	private GroupBox groupBox_1;

	private Button DB8E4E1B;

	private Button B32BC996;

	private Button F41873B3;

	private GroupBox groupBox_2;

	internal DataGridView dataGridView_1;

	private Button DC0388A3;

	private Button button_6;

	private Button button_7;

	private GroupBox groupBox_3;

	private Label label_1;

	private Button C98C9D0E;

	private Button button_8;

	private Button button_9;

	private Panel B6101030;

	private Panel panel_0;

	private Panel ********;

	private GClass103 C8BC6199;

	public RichTextBox CB868C81;

	private GClass0 A7BA0627;

	private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_1;

	private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_2;

	private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_3;

	private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_4;

	private PictureBox pictureBox_0;

	public event GClass112.D3A6F5AB D3027184
	{
		[CompilerGenerated]
		add
		{
			GClass112.D3A6F5AB d3A6F5AB = d3A6F5AB_0;
			GClass112.D3A6F5AB d3A6F5AB2;
			do
			{
				d3A6F5AB2 = d3A6F5AB;
				GClass112.D3A6F5AB value2 = (GClass112.D3A6F5AB)Class607.B630A78B.object_0[752](d3A6F5AB2, value);
				d3A6F5AB = Interlocked.CompareExchange(ref d3A6F5AB_0, value2, d3A6F5AB2);
			}
			while ((object)d3A6F5AB != d3A6F5AB2);
		}
		[CompilerGenerated]
		remove
		{
			GClass112.D3A6F5AB d3A6F5AB = d3A6F5AB_0;
			GClass112.D3A6F5AB d3A6F5AB2;
			do
			{
				d3A6F5AB2 = d3A6F5AB;
				GClass112.D3A6F5AB value2 = (GClass112.D3A6F5AB)Class607.B630A78B.object_0[629](d3A6F5AB2, value);
				d3A6F5AB = Interlocked.CompareExchange(ref d3A6F5AB_0, value2, d3A6F5AB2);
			}
			while ((object)d3A6F5AB != d3A6F5AB2);
		}
	}

	public EE97B6A8()
	{
		Class607.B630A78B.object_0[194](this);
		method_32();
		A088073A = new GClass113();
		method_3(D09F0D3B.smethod_5(1161902u), EF1F389C.Word, bool_0: false, bool_1: true);
		method_3("Version: ");
		method_3(D09F0D3B.smethod_5(1161364u), EF1F389C.Success, bool_0: false);
		method_3("Selected Tab: ");
		method_3("Qualcomm", EF1F389C.Success, bool_0: false, bool_1: true);
		list_1 = new List<GClass88>();
	}

	private bool method_0(object object_0, X509Certificate F109DF92, X509Chain x509Chain_0, SslPolicyErrors sslPolicyErrors_0)
	{
		return (bool)new GClass128().DFB12B0F(new object[5] { this, object_0, F109DF92, x509Chain_0, sslPolicyErrors_0 }, 11154566);
	}

	public void FA00212E(GForm0 gform0_0)
	{
		F6AAC6B2 = gform0_0;
	}

	public unsafe void F2079996(bool bool_0)
	{
		Class28 @class = new Class28();
		@class.C8A5EE9B = this;
		@class.bool_0 = bool_0;
		if (Class607.B630A78B.object_0[1012](this))
		{
			Class607.B630A78B.object_0[86](this, Class607.B630A78B.object_0[935](@class, (nint)__ldftn(Class28.method_0)));
		}
		else
		{
			method_1(@class.bool_0);
		}
	}

	public void method_1(bool bool_0)
	{
		IEnumerator object_ = Class607.B630A78B.object_0[409](Class607.B630A78B.object_0[781](B394B3AE));
		try
		{
			while (Class607.B630A78B.object_0[212](object_))
			{
				object obj = Class607.B630A78B.object_0[107](object_);
				Class607.B630A78B.object_0[722]((Control)obj, !bool_0);
			}
		}
		finally
		{
			IDisposable disposable = object_ as IDisposable;
			if (disposable != null)
			{
				disposable.Dispose();
			}
		}
		IEnumerator object_2 = Class607.B630A78B.object_0[409](Class607.B630A78B.object_0[781](groupBox_0));
		try
		{
			while (Class607.B630A78B.object_0[212](object_2))
			{
				object obj2 = Class607.B630A78B.object_0[107](object_2);
				Class607.B630A78B.object_0[722]((Control)obj2, !bool_0);
			}
		}
		finally
		{
			IDisposable disposable2 = object_2 as IDisposable;
			if (disposable2 != null)
			{
				disposable2.Dispose();
			}
		}
		IEnumerator object_3 = Class607.B630A78B.object_0[409](Class607.B630A78B.object_0[781](groupBox_1));
		try
		{
			while (Class607.B630A78B.object_0[212](object_3))
			{
				object obj3 = Class607.B630A78B.object_0[107](object_3);
				Class607.B630A78B.object_0[722]((Control)obj3, !bool_0);
			}
		}
		finally
		{
			IDisposable disposable3 = object_3 as IDisposable;
			if (disposable3 != null)
			{
				disposable3.Dispose();
			}
		}
		IEnumerator object_4 = Class607.B630A78B.object_0[409](Class607.B630A78B.object_0[781](groupBox_2));
		try
		{
			while (Class607.B630A78B.object_0[212](object_4))
			{
				object obj4 = Class607.B630A78B.object_0[107](object_4);
				Class607.B630A78B.object_0[722]((Control)obj4, !bool_0);
			}
		}
		finally
		{
			IDisposable disposable4 = object_4 as IDisposable;
			if (disposable4 != null)
			{
				disposable4.Dispose();
			}
		}
		IEnumerator object_5 = Class607.B630A78B.object_0[409](Class607.B630A78B.object_0[781](groupBox_3));
		try
		{
			while (Class607.B630A78B.object_0[212](object_5))
			{
				object obj5 = Class607.B630A78B.object_0[107](object_5);
				Class607.B630A78B.object_0[722]((Control)obj5, !bool_0);
			}
		}
		finally
		{
			IDisposable disposable5 = object_5 as IDisposable;
			if (disposable5 != null)
			{
				disposable5.Dispose();
			}
		}
		Class607.B630A78B.object_0[722](E5BB31B8, bool_0);
	}

	private unsafe void A201311C(ulong ulong_0, ulong E3B3378D)
	{
		Class29 @class = new Class29();
		@class.C40826A1 = this;
		@class.B2066AAD = E3B3378D;
		@class.ulong_0 = ulong_0;
		if (Class607.B630A78B.object_0[1012](this))
		{
			Class607.B630A78B.object_0[1241](this, Class607.B630A78B.object_0[935](@class, (nint)__ldftn(Class29.A32E09A8)));
		}
		else
		{
			method_2(@class.B2066AAD, @class.B2066AAD);
		}
	}

	private void method_2(ulong C2036616, ulong ulong_0)
	{
		if (ulong_0 > C2036616)
		{
			ulong_0 = C2036616;
		}
		double num = Class607.B630A78B.object_0[1259]((double)ulong_0 * 100.0 / (double)C2036616);
		Class607.B630A78B.object_0[811](e402748E_0, 100);
		if ((int)num < 0)
		{
			Class607.B630A78B.object_0[238](e402748E_0, 0);
		}
		else
		{
			Class607.B630A78B.object_0[238](e402748E_0, (int)num);
		}
		Class607.B630A78B.object_0[1175](label_0, Class607.B630A78B.object_0[1105]("{0:###,###,###} | {1:###,###,###}", C2036616, ulong_0));
	}

	public unsafe void method_3(string D216D186, EF1F389C ef1F389C_0 = EF1F389C.Word, bool bool_0 = true, bool bool_1 = false)
	{
		E42060B4 e42060B = new E42060B4();
		e42060B.BD9E7A2A = this;
		e42060B.F1BED124 = D216D186;
		e42060B.bool_0 = bool_0;
		e42060B.AB8B079F = ef1F389C_0;
		e42060B.AD17F5BA = bool_1;
		if (Class607.B630A78B.object_0[1012](this))
		{
			Class607.B630A78B.object_0[1241](this, Class607.B630A78B.object_0[935](e42060B, (nint)__ldftn(E42060B4.method_0)));
		}
		else
		{
			DB08D385(e42060B.F1BED124, e42060B.bool_0, e42060B.AB8B079F, e42060B.AD17F5BA);
		}
	}

	private void DB08D385(string B221610E, bool E08F0839 = true, EF1F389C ef1F389C_0 = EF1F389C.Word, bool DCBC9839 = false)
	{
		list_2.Add(new A808BE0B
		{
			bold = DCBC9839,
			color = ef1F389C_0,
			newline = E08F0839,
			text = B221610E
		});
		Class607.B630A78B.object_0[335](CB868C81, Class607.B630A78B.object_0[206](CB868C81));
		Class607.B630A78B.object_0[1076](CB868C81, 0);
		if (E08F0839 && ef1F389C_0 != EF1F389C.Success)
		{
			B221610E = Class607.B630A78B.object_0[720]("\r", B221610E);
		}
		switch (ef1F389C_0)
		{
		case EF1F389C.Word:
			Class607.B630A78B.object_0[551](CB868C81, Class607.B630A78B.object_0[223]());
			break;
		case EF1F389C.Yellow:
			Class607.B630A78B.object_0[551](CB868C81, Class607.B630A78B.object_0[909]());
			Class607.B630A78B.object_0[218](CB868C81, Class607.B630A78B.object_0[542](Class607.B630A78B.object_0[129](CB868C81), FontStyle.Bold));
			break;
		default:
			Class452.smethod_0(CB868C81, (ef1F389C_0 == EF1F389C.Success) ? Class607.B630A78B.object_0[923]() : Class607.B630A78B.object_0[909]());
			Class607.B630A78B.object_0[218](CB868C81, Class607.B630A78B.object_0[542](Class607.B630A78B.object_0[129](CB868C81), FontStyle.Bold));
			break;
		}
		if (DCBC9839)
		{
			Class607.B630A78B.object_0[218](CB868C81, Class607.B630A78B.object_0[542](Class607.B630A78B.object_0[129](CB868C81), FontStyle.Bold));
		}
		Class607.B630A78B.object_0[327](CB868C81, B221610E);
		Class607.B630A78B.object_0[551](CB868C81, Class607.B630A78B.object_0[546](CB868C81));
		Class607.B630A78B.object_0[183](CB868C81);
	}

	private void method_4(object sender, EventArgs e)
	{
		new GClass128().E6A33692(new object[3] { this, sender, e }, 22972756);
	}

	private void E125C4BE(object sender, EventArgs e)
	{
		new GClass128().B4154402(new object[3] { this, sender, e }, 11173162);
	}

	private void method_5(object sender, EventArgs e)
	{
		new GClass128().method_68(new object[3] { this, sender, e }, 315480);
	}

	private void method_6(object sender, EventArgs e)
	{
		new GClass128().DFB12B0F(new object[3] { this, sender, e }, 236100);
	}

	private void method_7(object sender, EventArgs e)
	{
		new GClass128().B4154402(new object[3] { this, sender, e }, 11158195);
	}

	private void method_8(string string_0)
	{
		if (Class607.B630A78B.object_0[121](GClass81.string_1, string_0))
		{
			method_3("Storage type config changed to : ");
			method_3(string_0, EF1F389C.Success, bool_0: false, bool_1: true);
			GClass81.string_1 = string_0;
			method_3("this is firmware storage type, you can change from \"custom config\"", EF1F389C.Yellow, bool_0: true, bool_1: true);
		}
	}

	public void method_9(string string_0)
	{
		GStruct72 dC = default(GStruct72);
		XmlDocument object_ = Class607.B630A78B.object_0[1104]();
		XmlReaderSettings xmlReaderSettings = Class607.B630A78B.object_0[1021]();
		Class287.E2079DB9(xmlReaderSettings, bool_0: true);
		XmlReader xmlReader = DE9A152B.A01018A1(string_0, xmlReaderSettings);
		Class607.B630A78B.object_0[943](object_, xmlReader);
		XmlNodeList object_2 = Class607.B630A78B.object_0[539](Class607.B630A78B.object_0[791](object_, "data"));
		try
		{
			dC.D7852495 = false;
			dC.string_1 = "";
			dC.string_6 = "0";
			dC.B29F9A3F = "0";
			dC.string_3 = "0";
			dC.string_5 = "0";
			dC.EEAFF5A5 = "512";
			dC.string_0 = "";
			IEnumerator object_3 = Class607.B630A78B.object_0[391](object_2);
			try
			{
				while (Class607.B630A78B.object_0[212](object_3))
				{
					XmlNode xmlNode = (XmlNode)Class607.B630A78B.object_0[107](object_3);
					if (!Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[1050](Class607.B630A78B.object_0[531](xmlNode)), "program"))
					{
						continue;
					}
					{
						IEnumerator object_4 = Class607.B630A78B.object_0[954](Class607.B630A78B.object_0[160](xmlNode));
						try
						{
							while (Class607.B630A78B.object_0[212](object_4))
							{
								XmlAttribute object_5 = (XmlAttribute)Class607.B630A78B.object_0[107](object_4);
								string text = Class607.B630A78B.object_0[1050](Class607.B630A78B.object_0[531](object_5));
								string text2 = text;
								if (text2 == null)
								{
									continue;
								}
								switch (Class607.B630A78B.object_0[343](text2))
								{
								case 12:
									if (Class607.B630A78B.object_0[787](text2, "start_sector"))
									{
										dC.B29F9A3F = Class607.B630A78B.object_0[769](object_5);
									}
									break;
								case 5:
									if (Class607.B630A78B.object_0[787](text2, "label"))
									{
										dC.string_0 = Class607.B630A78B.object_0[769](object_5);
									}
									break;
								case 6:
									if (Class607.B630A78B.object_0[787](text2, "sparse"))
									{
										dC.D7852495 = Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[769](object_5), "true");
									}
									break;
								case 8:
									if (Class607.B630A78B.object_0[787](text2, "filename"))
									{
										dC.string_1 = Class607.B630A78B.object_0[769](object_5);
									}
									break;
								case 25:
									if (Class607.B630A78B.object_0[787](text2, "physical_partition_number"))
									{
										dC.string_5 = Class607.B630A78B.object_0[769](object_5);
									}
									break;
								case 18:
									if (Class607.B630A78B.object_0[787](text2, "file_sector_offset"))
									{
										dC.string_6 = Class607.B630A78B.object_0[769](object_5);
									}
									break;
								case 20:
									if (Class607.B630A78B.object_0[787](text2, "sector_size_in_bytes"))
									{
										dC.EEAFF5A5 = Class607.B630A78B.object_0[769](object_5);
									}
									break;
								case 21:
									if (Class607.B630A78B.object_0[787](text2, "num_partition_sectors"))
									{
										dC.string_3 = Class607.B630A78B.object_0[769](object_5);
									}
									break;
								}
							}
						}
						finally
						{
							IDisposable disposable2 = object_4 as IDisposable;
							if (disposable2 != null)
							{
								disposable2.Dispose();
							}
						}
					}
					if (Class607.B630A78B.object_0[787](dC.EEAFF5A5, "4096"))
					{
						method_8("ufs");
					}
					else
					{
						method_8("emmc");
					}
					string text3 = Class607.B630A78B.object_0[1140](Class607.B630A78B.object_0[321](string_0), "\\", dC.string_1);
					if (Class607.B630A78B.object_0[695](text3))
					{
						dC.string_2 = text3;
						list_0.Add(new B2AC1304(dC.string_1, text3, dC.string_5, dC.B29F9A3F, dC.string_3, dC.A8AC8EBF, dC.string_0, dC));
					}
				}
			}
			finally
			{
				IDisposable disposable = object_3 as IDisposable;
				if (disposable != null)
				{
					disposable.Dispose();
				}
			}
			Class607.B630A78B.object_0[559](Class607.B630A78B.object_0[317](dataGridView_0));
			foreach (B2AC1304 item in list_0)
			{
				int f = Class825.F6039005(Class607.B630A78B.object_0[317](dataGridView_0), new object[4] { true, item.file_name, item.start_sector, item.filepath });
				Class607.B630A78B.object_0[535](Class607.B630A78B.object_0[522](Class607.B630A78B.object_0[317](dataGridView_0), f), item);
			}
		}
		catch (Exception object_6)
		{
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[1160](object_6));
		}
		finally
		{
			Class607.B630A78B.object_0[1189](xmlReader);
		}
	}

	private void method_10(string string_0)
	{
		if (Class607.B630A78B.object_0[121](GClass81.string_0, string_0))
		{
			method_3("firehose loader Finded in firmware: ");
			method_3(string_0, EF1F389C.Success, bool_0: false, bool_1: true);
			GClass81.string_0 = string_0;
			method_3("this loader exist in firmware path, you can change from \"custom config\"", EF1F389C.Yellow, bool_0: true, bool_1: true);
		}
	}

	private void method_11(object sender, EventArgs e)
	{
		bool flag = false;
		if (Class607.B630A78B.object_0[1205](Class607.B630A78B.object_0[814](gclass102_0)) || !Class607.B630A78B.object_0[488](Class607.B630A78B.object_0[814](gclass102_0)))
		{
			return;
		}
		list_0.Clear();
		string[] array = GClass112.smethod_2(Class607.B630A78B.object_0[814](gclass102_0), Class17.String_0);
		string[] array2 = GClass112.smethod_2(Class607.B630A78B.object_0[814](gclass102_0), Class17.String_3);
		string[] array3 = GClass112.smethod_2(Class607.B630A78B.object_0[814](gclass102_0), Class17.String_4);
		if (array2.Length != 0)
		{
			flag = true;
			string[] array4 = array2;
			foreach (string string_ in array4)
			{
				method_9(string_);
			}
		}
		else
		{
			GClass110.C2AB1F9F(D09F0D3B.smethod_5(1202318u), D09F0D3B.smethod_5(1202374u), MessageBoxButtons.OK, MessageBoxIcon.Hand, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
		}
		if (array3.Length != 0)
		{
			GClass89.string_0 = array3;
		}
		if (array.Length != 0)
		{
			method_10(array[0]);
			GClass81.string_0 = array[0];
		}
		Class607.B630A78B.object_0[890](dataGridView_0);
		if (flag)
		{
			Class166.smethod_0(button_0, !Class607.B630A78B.object_0[1205](Class607.B630A78B.object_0[814](gclass102_0)));
		}
	}

	public List<B2AC1304> method_12()
	{
		List<B2AC1304> list = new List<B2AC1304>();
		IEnumerator object_ = Class607.B630A78B.object_0[728](Class607.B630A78B.object_0[317](dataGridView_0));
		try
		{
			while (Class607.B630A78B.object_0[212](object_))
			{
				DataGridViewRow object_2 = (DataGridViewRow)Class607.B630A78B.object_0[107](object_);
				if ((bool)Class607.B630A78B.object_0[262](Class607.B630A78B.object_0[1138](Class607.B630A78B.object_0[907](object_2), 0)))
				{
					list.Add((B2AC1304)Class607.B630A78B.object_0[650](object_2));
				}
			}
		}
		finally
		{
			IDisposable disposable = object_ as IDisposable;
			if (disposable != null)
			{
				disposable.Dispose();
			}
		}
		return list;
	}

	private void method_13(object sender, EventArgs e)
	{
		new GClass128().DFB12B0F(new object[3] { this, sender, e }, 314736);
	}

	private void method_14(ulong C687573E, ulong ulong_0)
	{
		A201311C(C687573E, ulong_0);
	}

	public byte[] B2254AB5(FileStream fileStream_1, long B331B62C, int int_0, out int int_1)
	{
		Class607.B630A78B.object_0[61](fileStream_1);
		byte[] array = new byte[int_0];
		Class607.B630A78B.object_0[543](fileStream_1, B331B62C, SeekOrigin.Begin);
		int_1 = Class607.B630A78B.object_0[53](fileStream_1, array, 0, int_0);
		return array;
	}

	public bool method_15()
	{
		return (bool)new GClass128().C5017C25(new object[1] { this }, 22413827);
	}

	public void F92D1A9A(List<B2AC1304> C519D681, bool bool_0)
	{
		new GClass128().EF8D5E3B(new object[3] { this, C519D681, bool_0 }, 11028008);
	}

	public void method_16()
	{
		string string_ = "Wipe Storage";
		try
		{
			d3A6F5AB_0?.Invoke(bool_0: true, string_);
			if (!A088073A.C48D0DB3(method_3) || !method_15())
			{
				return;
			}
			using (ccb47F95_0 = new CCB47F95())
			{
				ccb47F95_0.Event_0 += method_3;
				ccb47F95_0.B3B8A6BD += method_14;
				if (ccb47F95_0.method_4(gstruct96_0))
				{
					method_3("Reading partition tables: ");
					List<GClass88> list = ccb47F95_0.method_32();
					if (list != null && list.Count > 0)
					{
						method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						foreach (GClass88 item in list)
						{
							string name = item.name;
							string string_2 = name;
							if (Class607.B630A78B.object_0[787](string_2, "userdata") || Class607.B630A78B.object_0[787](string_2, "persistent") || Class607.B630A78B.object_0[787](string_2, "frp"))
							{
								method_3(Class607.B630A78B.object_0[1140]("Wiping ", item.name, ": "));
								CCB47F95 cCB47F = ccb47F95_0;
								string sECTOR_SIZE_IN_BYTES = item.SECTOR_SIZE_IN_BYTES;
								ulong ulong_ = item.Sectors;
								string string_3 = Class607.B630A78B.object_0[651](ref ulong_);
								int int_ = item.lun;
								string eFBD2FAE = Class607.B630A78B.object_0[1070](ref int_);
								ulong_ = item.Sector;
								if (cCB47F.D68B7E1A(sECTOR_SIZE_IN_BYTES, string_3, eFBD2FAE, Class607.B630A78B.object_0[651](ref ulong_)))
								{
									method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
								}
								else
								{
									method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
								}
							}
						}
						DialogResult dialogResult = GClass110.C2AB1F9F("do you want reboot device?", "Question about reboot", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
						if (dialogResult == DialogResult.Yes)
						{
							method_3("Rebooting device : ");
							ccb47F95_0.method_2();
							method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						}
						ccb47F95_0.C5BE39BD.method_4();
					}
					else
					{
						method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
						ccb47F95_0.gclass93_0.method_11();
					}
				}
				else
				{
					ccb47F95_0.gclass93_0.method_11();
				}
			}
		}
		catch (Exception object_)
		{
			method_3("Error : ");
			method_3(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, bool_1: true);
		}
		finally
		{
			d3A6F5AB_0?.Invoke(bool_0: false, string_);
		}
	}

	private void method_17(object sender, EventArgs e)
	{
		new GClass128().E8AA1C3C(new object[3] { this, sender, e }, 21821714);
	}

	private void EA3F0091(object sender, EventArgs e)
	{
		new GClass128().F3BD1601(new object[3] { this, sender, e }, 20810150);
	}

	private void method_18(object sender, EventArgs e)
	{
		new GClass128().B4154402(new object[3] { this, sender, e }, 1285616);
	}

	private void method_19(object sender, EventArgs e)
	{
		new GClass128().B4154402(new object[3] { this, sender, e }, 22115261);
	}

	public void method_20(string string_0)
	{
		//IL_005e: Unknown result type (might be due to invalid IL or missing references)
		//IL_0064: Expected O, but got Unknown
		string string_1 = Class607.B630A78B.object_0[720](Class607.B630A78B.object_0[321](string_0), "\\data");
		ZipFile val = Class607.B630A78B.object_0[1271](string_0);
		try
		{
			IEnumerator object_ = Class607.B630A78B.object_0[764](val);
			while (Class607.B630A78B.object_0[212](object_))
			{
				ZipEntry val2 = (ZipEntry)Class607.B630A78B.object_0[107](object_);
				if (!Class607.B630A78B.object_0[1059](val2))
				{
					continue;
				}
				using Stream a5864A = Class607.B630A78B.object_0[1106](val, val2);
				using FileStream stream_ = Class607.B630A78B.object_0[1236](Class607.B630A78B.object_0[351](string_1, Class607.B630A78B.object_0[1121](val2)));
				Class607.B630A78B.object_0[115](a5864A, stream_);
			}
		}
		finally
		{
			((IDisposable)val)?.Dispose();
		}
	}

	public void F1323A91(string string_0)
	{
		string text = Class607.B630A78B.object_0[720](Class607.B630A78B.object_0[321](string_0), "\\data");
		GClass112.E209C304(text);
		string string_1 = "Restore EFS";
		try
		{
			method_20(string_0);
			string[] array = Class607.B630A78B.object_0[536](text);
			if (array.Length != 0 && !array.ToList().Exists((string aCBD683F) => Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[386](aCBD683F), "modemst1") || Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[386](aCBD683F), "modemst2") || Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[386](aCBD683F), "fsg")))
			{
				return;
			}
			d3A6F5AB_0?.Invoke(bool_0: true, string_1);
			if (!A088073A.C48D0DB3(method_3) || !method_15())
			{
				return;
			}
			using (ccb47F95_0 = new CCB47F95())
			{
				ccb47F95_0.Event_0 += method_3;
				ccb47F95_0.B3B8A6BD += method_14;
				if (ccb47F95_0.method_4(gstruct96_0))
				{
					method_3("Reading partition tables: ");
					List<GClass88> list = ccb47F95_0.method_32();
					if (list != null && list.Count > 0)
					{
						method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						using (List<GClass88>.Enumerator enumerator = list.GetEnumerator())
						{
							while (enumerator.MoveNext())
							{
								Class33 CS_0024_003C_003E8__locals8 = new Class33();
								CS_0024_003C_003E8__locals8.gclass88_0 = enumerator.Current;
								string name = CS_0024_003C_003E8__locals8.gclass88_0.name;
								string string_2 = name;
								if (!Class607.B630A78B.object_0[787](string_2, "modemst1") && !Class607.B630A78B.object_0[787](string_2, "modemst2") && !Class607.B630A78B.object_0[787](string_2, "fsg"))
								{
									continue;
								}
								string text2 = array.ToList().Find((string E6A232A8) => Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[1050](Class607.B630A78B.object_0[386](E6A232A8)), Class607.B630A78B.object_0[1050](CS_0024_003C_003E8__locals8.gclass88_0.name)));
								if (Class607.B630A78B.object_0[695](text2))
								{
									method_3(Class607.B630A78B.object_0[1140]("Flashing ", CS_0024_003C_003E8__locals8.gclass88_0.name, ": "));
									CCB47F95 cCB47F = ccb47F95_0;
									string sECTOR_SIZE_IN_BYTES = CS_0024_003C_003E8__locals8.gclass88_0.SECTOR_SIZE_IN_BYTES;
									ulong ulong_ = CS_0024_003C_003E8__locals8.gclass88_0.Sectors;
									string string_3 = Class607.B630A78B.object_0[651](ref ulong_);
									int int_ = CS_0024_003C_003E8__locals8.gclass88_0.lun;
									string string_4 = Class607.B630A78B.object_0[1070](ref int_);
									ulong_ = CS_0024_003C_003E8__locals8.gclass88_0.Sector;
									if (cCB47F.C711648A(sECTOR_SIZE_IN_BYTES, string_3, string_4, Class607.B630A78B.object_0[651](ref ulong_), text2))
									{
										method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
									}
									else
									{
										method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
									}
								}
							}
						}
						DialogResult dialogResult = GClass110.C2AB1F9F("do you want reboot device?", "Question about reboot", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
						if (dialogResult == DialogResult.Yes)
						{
							method_3("Rebooting device : ");
							ccb47F95_0.method_2();
							method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						}
						ccb47F95_0.C5BE39BD.method_4();
					}
					else
					{
						method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
						ccb47F95_0.gclass93_0.method_11();
					}
				}
				else
				{
					ccb47F95_0.gclass93_0.method_11();
				}
			}
		}
		catch (Exception object_)
		{
			method_3("Error : ");
			method_3(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, bool_1: true);
		}
		finally
		{
			d3A6F5AB_0?.Invoke(bool_0: false, string_1);
		}
	}

	private void AE9FA705(object sender, EventArgs e)
	{
		new GClass128().method_68(new object[3] { this, sender, e }, 22596680);
	}

	public void method_21(string string_0)
	{
		string string_1 = "Backup EFS";
		try
		{
			d3A6F5AB_0?.Invoke(bool_0: true, string_1);
			if (!A088073A.C48D0DB3(method_3) || !method_15())
			{
				return;
			}
			using (ccb47F95_0 = new CCB47F95())
			{
				ccb47F95_0.Event_0 += method_3;
				ccb47F95_0.B3B8A6BD += method_14;
				if (ccb47F95_0.method_4(gstruct96_0))
				{
					method_3("Reading partition tables: ");
					List<GClass88> list = ccb47F95_0.method_32();
					if (list != null && list.Count > 0)
					{
						GClass112.E209C304(string_0);
						method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						foreach (GClass88 item in list)
						{
							string name = item.name;
							string string_2 = name;
							if (Class607.B630A78B.object_0[787](string_2, "modemst1") || Class607.B630A78B.object_0[787](string_2, "modemst2") || Class607.B630A78B.object_0[787](string_2, "fsg"))
							{
								method_3(Class607.B630A78B.object_0[1140]("Reading ", item.name, ": "));
								CCB47F95 cCB47F = ccb47F95_0;
								ulong ulong_ = item.Sector;
								string string_3 = Class607.B630A78B.object_0[651](ref ulong_);
								ulong_ = item.Sectors;
								string string_4 = Class607.B630A78B.object_0[651](ref ulong_);
								int int_ = item.lun;
								if (cCB47F.method_0(string_3, string_4, Class607.B630A78B.object_0[1070](ref int_), item.SECTOR_SIZE_IN_BYTES, Class607.B630A78B.object_0[351](string_0, Class607.B630A78B.object_0[720](item.name, ".img"))))
								{
									method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
								}
								else
								{
									method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
								}
							}
						}
						DialogResult dialogResult = GClass110.C2AB1F9F("do you want reboot device?", "Question about reboot", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
						if (dialogResult == DialogResult.Yes)
						{
							method_3("Rebooting device : ");
							ccb47F95_0.method_2();
							method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						}
						ccb47F95_0.C5BE39BD.method_4();
						string[] array = Class607.B630A78B.object_0[536](string_0);
						if (array == null || array.Length == 0)
						{
							return;
						}
						method_3("saving EFS Backup as zip: ");
						method_3("saving NV Backup as zip: ");
						ZipOutputStream val = Class607.B630A78B.object_0[363](Class607.B630A78B.object_0[1236](Class607.B630A78B.object_0[351](string_0, D09F0D3B.smethod_5(1203412u))));
						try
						{
							string text = Class607.B630A78B.object_0[720](string_0, "\\modemst1.img");
							string text2 = Class607.B630A78B.object_0[720](string_0, "\\modemst2.img");
							string text3 = Class607.B630A78B.object_0[720](string_0, "\\fsg.img");
							Class607.B630A78B.object_0[214](val, 9);
							if (Class607.B630A78B.object_0[695](text))
							{
								val.FA89759F(text);
								Class607.B630A78B.object_0[353](text);
							}
							if (Class607.B630A78B.object_0[695](text2))
							{
								val.FA89759F(text2);
								Class607.B630A78B.object_0[353](text2);
							}
							if (Class607.B630A78B.object_0[695](text3))
							{
								val.FA89759F(text3);
								Class607.B630A78B.object_0[353](text3);
							}
						}
						finally
						{
							((IDisposable)val)?.Dispose();
						}
						method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						method_3("Saved path: ");
						method_3(string_0, EF1F389C.Success, bool_0: false, bool_1: true);
					}
					else
					{
						method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
						ccb47F95_0.gclass93_0.method_11();
						ccb47F95_0.C5BE39BD.method_4();
					}
				}
				else
				{
					ccb47F95_0.gclass93_0.method_11();
					ccb47F95_0.C5BE39BD.method_4();
				}
			}
		}
		catch (Exception object_)
		{
			method_3("Error : ");
			method_3(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, bool_1: true);
		}
		finally
		{
			d3A6F5AB_0?.Invoke(bool_0: false, string_1);
		}
	}

	public void method_22()
	{
		string string_ = "My Account Erase";
		try
		{
			d3A6F5AB_0?.Invoke(bool_0: true, string_);
			if (!A088073A.C48D0DB3(method_3) || !method_15())
			{
				return;
			}
			using (ccb47F95_0 = new CCB47F95())
			{
				ccb47F95_0.Event_0 += method_3;
				ccb47F95_0.B3B8A6BD += method_14;
				if (ccb47F95_0.method_4(gstruct96_0))
				{
					method_3("Reading partition tables: ");
					List<GClass88> list = ccb47F95_0.method_32();
					if (list != null && list.Count > 0)
					{
						method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						foreach (GClass88 item in list)
						{
							string name = item.name;
							string string_2 = name;
							if (!Class607.B630A78B.object_0[787](string_2, "persistent"))
							{
								if (Class607.B630A78B.object_0[787](string_2, "frp") || Class607.B630A78B.object_0[787](string_2, "config"))
								{
									method_3(Class607.B630A78B.object_0[1140]("Wiping ", item.name, ": "));
									CCB47F95 cCB47F = ccb47F95_0;
									string sECTOR_SIZE_IN_BYTES = item.SECTOR_SIZE_IN_BYTES;
									ulong ulong_ = item.Sectors;
									string string_3 = Class607.B630A78B.object_0[651](ref ulong_);
									int int_ = item.lun;
									string eFBD2FAE = Class607.B630A78B.object_0[1070](ref int_);
									ulong_ = item.Sector;
									if (cCB47F.D68B7E1A(sECTOR_SIZE_IN_BYTES, string_3, eFBD2FAE, Class607.B630A78B.object_0[651](ref ulong_)))
									{
										method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
									}
									else
									{
										method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
									}
								}
							}
							else
							{
								method_3("Resetting Mi Cloud data: ");
								CCB47F95 cCB47F2 = ccb47F95_0;
								string sECTOR_SIZE_IN_BYTES2 = item.SECTOR_SIZE_IN_BYTES;
								ulong ulong_ = item.Sectors;
								string string_4 = Class607.B630A78B.object_0[651](ref ulong_);
								int int_ = item.lun;
								string string_5 = Class607.B630A78B.object_0[1070](ref int_);
								ulong_ = item.Sector;
								if (cCB47F2.C711648A(sECTOR_SIZE_IN_BYTES2, string_4, string_5, Class607.B630A78B.object_0[651](ref ulong_), Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\loader\\data.ext")))
								{
									method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
								}
								else
								{
									method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
								}
							}
						}
						DialogResult dialogResult = GClass110.C2AB1F9F("do you want reboot device?", "Question about reboot", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
						if (dialogResult == DialogResult.Yes)
						{
							method_3("Rebooting device : ");
							ccb47F95_0.method_2();
							method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						}
						ccb47F95_0.C5BE39BD.method_4();
					}
					else
					{
						method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
						ccb47F95_0.gclass93_0.method_11();
					}
				}
				else
				{
					ccb47F95_0.gclass93_0.method_11();
				}
			}
		}
		catch (Exception object_)
		{
			method_3("Error : ");
			method_3(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, bool_1: true);
		}
		finally
		{
			d3A6F5AB_0?.Invoke(bool_0: false, string_);
		}
	}

	public void method_23()
	{
		string string_ = "FRP Erase";
		try
		{
			d3A6F5AB_0?.Invoke(bool_0: true, string_);
			if (!A088073A.C48D0DB3(method_3) || !method_15())
			{
				return;
			}
			using CCB47F95 cCB47F = new CCB47F95();
			cCB47F.Event_0 += method_3;
			cCB47F.B3B8A6BD += method_14;
			if (cCB47F.method_4(gstruct96_0))
			{
				method_3("Reading partition tables: ");
				List<GClass88> list = cCB47F.method_32();
				if (list != null && list.Count > 0)
				{
					method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
					foreach (GClass88 item in list)
					{
						string name = item.name;
						string string_2 = name;
						if (Class607.B630A78B.object_0[787](string_2, "persistent") || Class607.B630A78B.object_0[787](string_2, "frp") || Class607.B630A78B.object_0[787](string_2, "config"))
						{
							method_3(Class607.B630A78B.object_0[1140]("Wiping ", item.name, ": "));
							string sECTOR_SIZE_IN_BYTES = item.SECTOR_SIZE_IN_BYTES;
							ulong ulong_ = item.Sectors;
							string string_3 = Class607.B630A78B.object_0[651](ref ulong_);
							int int_ = item.lun;
							string eFBD2FAE = Class607.B630A78B.object_0[1070](ref int_);
							ulong_ = item.Sector;
							if (cCB47F.D68B7E1A(sECTOR_SIZE_IN_BYTES, string_3, eFBD2FAE, Class607.B630A78B.object_0[651](ref ulong_)))
							{
								method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
							}
							else
							{
								method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
							}
						}
					}
					DialogResult dialogResult = GClass110.C2AB1F9F("do you want reboot device?", "Question about reboot", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
					if (dialogResult == DialogResult.Yes)
					{
						method_3("Rebooting device : ");
						cCB47F.method_2();
						method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
					}
					cCB47F.C5BE39BD.method_4();
				}
				else
				{
					method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
					cCB47F.gclass93_0.method_11();
				}
			}
			else
			{
				cCB47F.gclass93_0.method_11();
			}
		}
		catch (Exception object_)
		{
			method_3("Error : ");
			method_3(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, bool_1: true);
		}
		finally
		{
			d3A6F5AB_0?.Invoke(bool_0: false, string_);
		}
	}

	public void method_24()
	{
		string string_ = "Erase EFS";
		try
		{
			d3A6F5AB_0?.Invoke(bool_0: true, string_);
			if (!A088073A.C48D0DB3(method_3) || !method_15())
			{
				return;
			}
			using (ccb47F95_0 = new CCB47F95())
			{
				ccb47F95_0.Event_0 += method_3;
				ccb47F95_0.B3B8A6BD += method_14;
				if (ccb47F95_0.method_4(gstruct96_0))
				{
					method_3("Reading partition tables: ");
					List<GClass88> list = ccb47F95_0.method_32();
					if (list != null && list.Count > 0)
					{
						object obj = Class607.B630A78B.object_0[398];
						string a = GClass112.A6360694;
						DateTime B = Class607.B630A78B.object_0[402]();
						string text = obj(a, "\\bin\\backup\\", Class607.B630A78B.object_0[460](ref B, "M.dd.yyy_h.mm.ss_tt"), "\\");
						GClass112.E209C304(text);
						method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						foreach (GClass88 item in list)
						{
							string name = item.name;
							string string_2 = name;
							if (Class607.B630A78B.object_0[787](string_2, "modemst1") || Class607.B630A78B.object_0[787](string_2, "modemst2") || Class607.B630A78B.object_0[787](string_2, "fsg"))
							{
								method_3(Class607.B630A78B.object_0[1140]("Reading ", item.name, ": "));
								CCB47F95 cCB47F = ccb47F95_0;
								ulong ulong_ = item.Sector;
								string string_3 = Class607.B630A78B.object_0[651](ref ulong_);
								ulong_ = item.Sectors;
								string string_4 = Class607.B630A78B.object_0[651](ref ulong_);
								int int_ = item.lun;
								if (cCB47F.method_0(string_3, string_4, Class607.B630A78B.object_0[1070](ref int_), item.SECTOR_SIZE_IN_BYTES, Class607.B630A78B.object_0[351](text, Class607.B630A78B.object_0[720](item.name, ".img"))))
								{
									method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
								}
								else
								{
									method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
								}
								method_3(Class607.B630A78B.object_0[1140]("Wiping ", item.name, ": "));
								CCB47F95 cCB47F2 = ccb47F95_0;
								string sECTOR_SIZE_IN_BYTES = item.SECTOR_SIZE_IN_BYTES;
								ulong_ = item.Sectors;
								string string_5 = Class607.B630A78B.object_0[651](ref ulong_);
								int_ = item.lun;
								string eFBD2FAE = Class607.B630A78B.object_0[1070](ref int_);
								ulong_ = item.Sector;
								if (cCB47F2.D68B7E1A(sECTOR_SIZE_IN_BYTES, string_5, eFBD2FAE, Class607.B630A78B.object_0[651](ref ulong_)))
								{
									method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
								}
								else
								{
									method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
								}
							}
						}
						string[] array = Class607.B630A78B.object_0[536](text);
						if (array != null && array.Length != 0)
						{
							method_3("saving NV Backup as zip: ");
							ZipOutputStream val = Class607.B630A78B.object_0[363](Class607.B630A78B.object_0[1236](Class607.B630A78B.object_0[351](text, D09F0D3B.smethod_5(1203806u))));
							try
							{
								string text2 = Class607.B630A78B.object_0[720](text, "modemst1.img");
								string text3 = Class607.B630A78B.object_0[720](text, "modemst2.img");
								string text4 = Class607.B630A78B.object_0[720](text, "fsg.img");
								Class607.B630A78B.object_0[214](val, 9);
								if (Class607.B630A78B.object_0[695](text2))
								{
									val.FA89759F(text2);
									Class607.B630A78B.object_0[353](text2);
								}
								if (Class607.B630A78B.object_0[695](text3))
								{
									val.FA89759F(text3);
									Class607.B630A78B.object_0[353](text3);
								}
								if (Class607.B630A78B.object_0[695](text4))
								{
									val.FA89759F(text4);
									Class607.B630A78B.object_0[353](text4);
								}
							}
							finally
							{
								((IDisposable)val)?.Dispose();
							}
							method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						}
						DialogResult dialogResult = GClass110.C2AB1F9F("do you want reboot device?", "Question about reboot", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
						if (dialogResult == DialogResult.Yes)
						{
							method_3("Rebooting device : ");
							ccb47F95_0.method_2();
							method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
						}
						ccb47F95_0.C5BE39BD.method_4();
					}
					else
					{
						method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
						ccb47F95_0.gclass93_0.method_11();
					}
				}
				else
				{
					ccb47F95_0.gclass93_0.method_11();
				}
			}
		}
		catch (Exception object_)
		{
			method_3("Error : ");
			method_3(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, bool_1: true);
		}
		finally
		{
			d3A6F5AB_0?.Invoke(bool_0: false, string_);
		}
	}

	public unsafe void method_25()
	{
		string string_ = "Read GPT";
		try
		{
			d3A6F5AB_0?.Invoke(bool_0: true, string_);
			if (!A088073A.C48D0DB3(method_3) || !method_15())
			{
				return;
			}
			ccb47F95_0 = new CCB47F95();
			ccb47F95_0.Event_0 += method_3;
			ccb47F95_0.B3B8A6BD += method_14;
			if (ccb47F95_0.method_4(gstruct96_0))
			{
				Class34 @class = new Class34();
				@class.DD1DB780 = this;
				method_3("Reading partition tables: ");
				@class.B1318F8E = ccb47F95_0.method_32();
				if (@class.B1318F8E != null && @class.B1318F8E.Count > 0)
				{
					method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
					Class607.B630A78B.object_0[86](this, Class607.B630A78B.object_0[1210](@class, (nint)__ldftn(Class34.method_0)));
					method_3("Please do not unplug usb cable,You can use erase, read features as many times as you want.", EF1F389C.Yellow, bool_0: true, bool_1: true);
				}
				else
				{
					method_3("Failed", EF1F389C.Error, bool_0: false, bool_1: true);
					ccb47F95_0.C5BE39BD.method_4();
					ccb47F95_0.gclass93_0.method_11();
				}
			}
			else
			{
				ccb47F95_0.C5BE39BD.method_4();
				ccb47F95_0.gclass93_0.method_11();
			}
		}
		catch (Exception object_)
		{
			method_3("Error : ");
			method_3(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, bool_1: true);
		}
		finally
		{
			d3A6F5AB_0?.Invoke(bool_0: false, string_);
		}
	}

	private void D41F1334(object sender, EventArgs e)
	{
		new GClass128().method_68(new object[3] { this, sender, e }, 11159231);
	}

	private void method_26(object sender, EventArgs e)
	{
		new GClass128().E6A33692(new object[3] { this, sender, e }, 22073271);
	}

	public List<GClass88> BC8F840A()
	{
		List<GClass88> list = new List<GClass88>();
		IEnumerator object_ = Class607.B630A78B.object_0[728](Class607.B630A78B.object_0[317](dataGridView_1));
		try
		{
			while (Class607.B630A78B.object_0[212](object_))
			{
				DataGridViewRow object_2 = (DataGridViewRow)Class607.B630A78B.object_0[107](object_);
				if ((bool)Class607.B630A78B.object_0[262](Class607.B630A78B.object_0[1138](Class607.B630A78B.object_0[907](object_2), 0)))
				{
					list.Add((GClass88)Class607.B630A78B.object_0[650](object_2));
				}
			}
		}
		finally
		{
			IDisposable disposable = object_ as IDisposable;
			if (disposable != null)
			{
				disposable.Dispose();
			}
		}
		return list;
	}

	public void method_27(List<GClass88> list_3)
	{
		new GClass128().B4154402(new object[2] { this, list_3 }, 48565);
	}

	private void method_28(object sender, EventArgs e)
	{
		new GClass128().method_68(new object[3] { this, sender, e }, 237392);
	}

	public void method_29(List<GClass88> DC18E11B, string string_0)
	{
		try
		{
			d3A6F5AB_0?.Invoke(bool_0: true, "read partitions");
			List<GClass88> list = DC18E11B.FindAll((GClass88 gclass88_0) => gclass88_0.Enable);
			GClass81.string_1 = (Class607.B630A78B.object_0[787](list[0].SECTOR_SIZE_IN_BYTES, "4096") ? "ufs" : "emmc");
			ccb47F95_0.method_1(DC18E11B, string_0);
			if (Class607.B630A78B.object_0[695](GClass81.string_0))
			{
				if (Class607.B630A78B.object_0[787](GClass81.string_1, "ufs"))
				{
					string string_1 = "prog_ufs_firehose.elf";
					Class607.B630A78B.object_0[1253](Class607.B630A78B.object_0[351](string_0, string_1), Class607.B630A78B.object_0[649](GClass81.string_0));
				}
				else
				{
					string string_2 = "prog_emmc_firehose.elf";
					Class607.B630A78B.object_0[1253](Class607.B630A78B.object_0[351](string_0, string_2), Class607.B630A78B.object_0[649](GClass81.string_0));
				}
			}
			foreach (GClass88 item in list)
			{
				method_3(Class607.B630A78B.object_0[1140]("reading ", item.name, " : "));
				CCB47F95 cCB47F = ccb47F95_0;
				ulong ulong_ = item.Sector;
				string string_3 = Class607.B630A78B.object_0[651](ref ulong_);
				ulong_ = item.Sectors;
				string string_4 = Class607.B630A78B.object_0[651](ref ulong_);
				int int_ = item.lun;
				if (cCB47F.method_0(string_3, string_4, Class607.B630A78B.object_0[1070](ref int_), item.SECTOR_SIZE_IN_BYTES, Class607.B630A78B.object_0[351](string_0, Class607.B630A78B.object_0[720](item.name, ".img"))))
				{
					method_3("Ok", EF1F389C.Success, bool_0: false, bool_1: true);
				}
				else
				{
					method_3("faild", EF1F389C.Error, bool_0: false, bool_1: true);
				}
			}
		}
		catch (Exception object_)
		{
			method_3("Error : ");
			method_3(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, bool_1: true);
		}
		finally
		{
			d3A6F5AB_0?.Invoke(bool_0: false, "read partitions");
		}
	}

	private void method_30(object sender, EventArgs e)
	{
		new GClass128().AD84C1A2(new object[3] { this, sender, e }, 360076);
	}

	private void method_31(object sender, EventArgs e)
	{
		new GClass128().C5017C25(new object[3] { this, sender, e }, 22565989);
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && icontainer_0 != null)
		{
			icontainer_0.Dispose();
		}
		Class607.B630A78B.object_0[878](this, disposing);
	}

	private unsafe void method_32()
	{
		DataGridViewCellStyle dataGridViewCellStyle = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle2 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle3 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle4 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle5 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle6 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle7 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle8 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle9 = Class607.B630A78B.object_0[595]();
		ComponentResourceManager object_ = Class607.B630A78B.object_0[566](Class607.B630A78B.object_0[6](typeof(EE97B6A8).TypeHandle));
		DataGridViewCellStyle dataGridViewCellStyle10 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle11 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle12 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle13 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle14 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle15 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle16 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle17 = Class607.B630A78B.object_0[595]();
		E9AE2482 = Class607.B630A78B.object_0[1206]();
		e402748E_0 = new E402748E();
		label_0 = Class607.B630A78B.object_0[113]();
		E5BB31B8 = Class607.B630A78B.object_0[1112]();
		EA1CF18C = Class607.B630A78B.object_0[1206]();
		pictureBox_0 = Class607.B630A78B.object_0[1114]();
		CB868C81 = Class607.B630A78B.object_0[344]();
		groupBox_3 = Class607.B630A78B.object_0[1206]();
		label_1 = Class607.B630A78B.object_0[113]();
		C98C9D0E = Class607.B630A78B.object_0[1112]();
		button_8 = Class607.B630A78B.object_0[1112]();
		button_9 = Class607.B630A78B.object_0[1112]();
		B6101030 = Class607.B630A78B.object_0[853]();
		panel_0 = Class607.B630A78B.object_0[853]();
		EC14989A = new GControl1();
		tabPage_0 = Class607.B630A78B.object_0[139]();
		B394B3AE = Class607.B630A78B.object_0[1206]();
		C8BC6199 = new GClass103();
		gclass102_0 = new GClass102();
		dataGridView_0 = Class607.B630A78B.object_0[1186]();
		******** = new GClass0();
		******** = Class607.B630A78B.object_0[156]();
		dataGridViewTextBoxColumn_0 = Class607.B630A78B.object_0[156]();
		F594AF83 = Class607.B630A78B.object_0[156]();
		button_0 = Class607.B630A78B.object_0[1112]();
		button_1 = Class607.B630A78B.object_0[1112]();
		button_2 = Class607.B630A78B.object_0[1112]();
		A8208E90 = Class607.B630A78B.object_0[139]();
		groupBox_0 = Class607.B630A78B.object_0[1206]();
		button_3 = Class607.B630A78B.object_0[1112]();
		button_4 = Class607.B630A78B.object_0[1112]();
		button_5 = Class607.B630A78B.object_0[1112]();
		tabPage_1 = Class607.B630A78B.object_0[139]();
		groupBox_1 = Class607.B630A78B.object_0[1206]();
		DB8E4E1B = Class607.B630A78B.object_0[1112]();
		B32BC996 = Class607.B630A78B.object_0[1112]();
		F41873B3 = Class607.B630A78B.object_0[1112]();
		EB0ADA0C = Class607.B630A78B.object_0[139]();
		groupBox_2 = Class607.B630A78B.object_0[1206]();
		dataGridView_1 = Class607.B630A78B.object_0[1186]();
		A7BA0627 = new GClass0();
		dataGridViewTextBoxColumn_1 = Class607.B630A78B.object_0[156]();
		dataGridViewTextBoxColumn_2 = Class607.B630A78B.object_0[156]();
		dataGridViewTextBoxColumn_3 = Class607.B630A78B.object_0[156]();
		dataGridViewTextBoxColumn_4 = Class607.B630A78B.object_0[156]();
		DC0388A3 = Class607.B630A78B.object_0[1112]();
		button_6 = Class607.B630A78B.object_0[1112]();
		button_7 = Class607.B630A78B.object_0[1112]();
		******** = Class607.B630A78B.object_0[853]();
		B2111D3A = new GControl2();
		Class607.B630A78B.object_0[549](E9AE2482);
		Class607.B630A78B.object_0[549](EA1CF18C);
		Class607.B630A78B.object_0[361](pictureBox_0);
		Class607.B630A78B.object_0[549](groupBox_3);
		Class607.B630A78B.object_0[549](B6101030);
		Class607.B630A78B.object_0[549](panel_0);
		Class607.B630A78B.object_0[549](EC14989A);
		Class607.B630A78B.object_0[549](tabPage_0);
		Class607.B630A78B.object_0[549](B394B3AE);
		Class607.B630A78B.object_0[361](dataGridView_0);
		Class607.B630A78B.object_0[549](A8208E90);
		Class607.B630A78B.object_0[549](groupBox_0);
		Class607.B630A78B.object_0[549](tabPage_1);
		Class607.B630A78B.object_0[549](groupBox_1);
		Class607.B630A78B.object_0[549](EB0ADA0C);
		Class607.B630A78B.object_0[549](groupBox_2);
		Class607.B630A78B.object_0[361](dataGridView_1);
		Class607.B630A78B.object_0[549](********);
		Class607.B630A78B.object_0[550](this);
		Class607.B630A78B.object_0[781](E9AE2482).Add(e402748E_0);
		Class607.B630A78B.object_0[781](E9AE2482).Add(label_0);
		Class607.B630A78B.object_0[781](E9AE2482).Add(E5BB31B8);
		Class607.B630A78B.object_0[1278](E9AE2482, DockStyle.Fill);
		Class607.B630A78B.object_0[388](E9AE2482, Class607.B630A78B.object_0[760](5, 0));
		Class607.B630A78B.object_0[689](E9AE2482, "GrpBxResult");
		Class607.B630A78B.object_0[1276](E9AE2482, Class607.B630A78B.object_0[174](1138, 39));
		Class607.B630A78B.object_0[334](E9AE2482, 30);
		Class607.B630A78B.object_0[1183](E9AE2482, bool_0: false);
		Class607.B630A78B.object_0[1175](E9AE2482, "Result");
		Class607.B630A78B.object_0[1242](e402748E_0, AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
		e402748E_0.Int32_0 = 0;
		Class607.B630A78B.object_0[388](e402748E_0, Class607.B630A78B.object_0[760](6, 23));
		e402748E_0.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](e402748E_0, "ProgBar");
		Class607.B630A78B.object_0[1276](e402748E_0, Class607.B630A78B.object_0[174](920, 5));
		Class607.B630A78B.object_0[334](e402748E_0, 26);
		Class607.B630A78B.object_0[1242](label_0, AnchorStyles.Bottom | AnchorStyles.Right);
		Class607.B630A78B.object_0[11](label_0, E704E9A1: true);
		Class607.B630A78B.object_0[388](label_0, Class607.B630A78B.object_0[760](932, 18));
		Class607.B630A78B.object_0[689](label_0, "lblstate");
		Class607.B630A78B.object_0[1276](label_0, Class607.B630A78B.object_0[174](15, 13));
		Class607.B630A78B.object_0[334](label_0, 28);
		Class607.B630A78B.object_0[1175](label_0, "%");
		Class607.B630A78B.object_0[1242](E5BB31B8, AnchorStyles.Bottom | AnchorStyles.Right);
		Class607.B630A78B.object_0[823](E5BB31B8, Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[308](E5BB31B8, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[722](E5BB31B8, E91CBAA4: false);
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](E5BB31B8), Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[540](E5BB31B8, FlatStyle.Flat);
		Class607.B630A78B.object_0[326](E5BB31B8, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](E5BB31B8, A282DB38.banned);
		Class607.B630A78B.object_0[997](E5BB31B8, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](E5BB31B8, Class607.B630A78B.object_0[760](1066, 10));
		Class607.B630A78B.object_0[349](E5BB31B8, Class607.B630A78B.object_0[1237](2));
		Class607.B630A78B.object_0[689](E5BB31B8, "BtnStop");
		Class607.B630A78B.object_0[1276](E5BB31B8, Class607.B630A78B.object_0[174](65, 22));
		Class607.B630A78B.object_0[334](E5BB31B8, 27);
		Class607.B630A78B.object_0[1175](E5BB31B8, "STOP");
		Class607.B630A78B.object_0[494](E5BB31B8, ContentAlignment.MiddleRight);
		Class607.B630A78B.object_0[270](E5BB31B8, CA2A151B: false);
		Class607.B630A78B.object_0[1239](E5BB31B8, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_31)));
		Class607.B630A78B.object_0[781](EA1CF18C).Add(pictureBox_0);
		Class607.B630A78B.object_0[781](EA1CF18C).Add(CB868C81);
		Class607.B630A78B.object_0[1278](EA1CF18C, DockStyle.Fill);
		Class607.B630A78B.object_0[388](EA1CF18C, Class607.B630A78B.object_0[760](5, 115));
		Class607.B630A78B.object_0[689](EA1CF18C, "GrpBxLog");
		Class607.B630A78B.object_0[1276](EA1CF18C, Class607.B630A78B.object_0[174](629, 318));
		Class607.B630A78B.object_0[334](EA1CF18C, 31);
		Class607.B630A78B.object_0[1183](EA1CF18C, bool_0: false);
		Class607.B630A78B.object_0[1175](EA1CF18C, "Result");
		Class607.B630A78B.object_0[823](pictureBox_0, Class607.B630A78B.object_0[235](33, 33, 33));
		Class607.B630A78B.object_0[148](pictureBox_0, A282DB38.Loading______);
		Class607.B630A78B.object_0[388](pictureBox_0, Class607.B630A78B.object_0[760](498, 212));
		Class607.B630A78B.object_0[689](pictureBox_0, "pictureBox1");
		Class607.B630A78B.object_0[1276](pictureBox_0, Class607.B630A78B.object_0[174](100, 100));
		Class607.B630A78B.object_0[606](pictureBox_0, PictureBoxSizeMode.StretchImage);
		Class607.B630A78B.object_0[831](pictureBox_0, 38);
		Class607.B630A78B.object_0[573](pictureBox_0, FF38159D: false);
		Class607.B630A78B.object_0[823](CB868C81, Class607.B630A78B.object_0[235](33, 33, 33));
		Class607.B630A78B.object_0[1278](CB868C81, DockStyle.Fill);
		Class607.B630A78B.object_0[1267](CB868C81, Class607.B630A78B.object_0[1152]("Courier New", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[388](CB868C81, Class607.B630A78B.object_0[760](3, 16));
		Class607.B630A78B.object_0[689](CB868C81, "RichLog");
		Class607.B630A78B.object_0[664](CB868C81, B9A668BD: true);
		Class607.B630A78B.object_0[1276](CB868C81, Class607.B630A78B.object_0[174](623, 299));
		Class607.B630A78B.object_0[334](CB868C81, 5);
		Class607.B630A78B.object_0[1175](CB868C81, "");
		Class607.B630A78B.object_0[781](groupBox_3).Add(label_1);
		Class607.B630A78B.object_0[781](groupBox_3).Add(C98C9D0E);
		Class607.B630A78B.object_0[781](groupBox_3).Add(button_8);
		Class607.B630A78B.object_0[781](groupBox_3).Add(button_9);
		Class607.B630A78B.object_0[1278](groupBox_3, DockStyle.Top);
		Class607.B630A78B.object_0[388](groupBox_3, Class607.B630A78B.object_0[760](5, 5));
		Class607.B630A78B.object_0[689](groupBox_3, "groupBox1");
		Class607.B630A78B.object_0[1276](groupBox_3, Class607.B630A78B.object_0[174](629, 110));
		Class607.B630A78B.object_0[334](groupBox_3, 34);
		Class607.B630A78B.object_0[1183](groupBox_3, bool_0: false);
		Class607.B630A78B.object_0[1175](groupBox_3, "firehose setting");
		Class607.B630A78B.object_0[11](label_1, E704E9A1: true);
		Class607.B630A78B.object_0[388](label_1, Class607.B630A78B.object_0[760](9, 89));
		Class607.B630A78B.object_0[689](label_1, "lblfirehose");
		Class607.B630A78B.object_0[1276](label_1, Class607.B630A78B.object_0[174](101, 13));
		Class607.B630A78B.object_0[334](label_1, 2);
		Class607.B630A78B.object_0[1175](label_1, "auto firehose detect");
		Class607.B630A78B.object_0[308](C98C9D0E, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[540](C98C9D0E, FlatStyle.Flat);
		Class607.B630A78B.object_0[953](C98C9D0E, A282DB38.hand);
		Class607.B630A78B.object_0[388](C98C9D0E, Class607.B630A78B.object_0[760](317, 19));
		Class607.B630A78B.object_0[689](C98C9D0E, "BtnSelectLoader");
		Class607.B630A78B.object_0[1276](C98C9D0E, Class607.B630A78B.object_0[174](150, 64));
		Class607.B630A78B.object_0[334](C98C9D0E, 1);
		Class607.B630A78B.object_0[1175](C98C9D0E, "Select loader");
		Class607.B630A78B.object_0[347](C98C9D0E, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](C98C9D0E, CA2A151B: true);
		Class607.B630A78B.object_0[1239](C98C9D0E, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.E125C4BE)));
		Class607.B630A78B.object_0[308](button_8, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[540](button_8, FlatStyle.Flat);
		Class607.B630A78B.object_0[953](button_8, A282DB38.smartphone);
		Class607.B630A78B.object_0[388](button_8, Class607.B630A78B.object_0[760](161, 19));
		Class607.B630A78B.object_0[689](button_8, "BtnFromDevice");
		Class607.B630A78B.object_0[1276](button_8, Class607.B630A78B.object_0[174](150, 64));
		Class607.B630A78B.object_0[334](button_8, 1);
		Class607.B630A78B.object_0[1175](button_8, "Select Device");
		Class607.B630A78B.object_0[347](button_8, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_8, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_8, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_5)));
		Class607.B630A78B.object_0[823](button_9, Class607.B630A78B.object_0[211]());
		Class607.B630A78B.object_0[308](button_9, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[540](button_9, FlatStyle.Flat);
		Class607.B630A78B.object_0[326](button_9, Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[953](button_9, A282DB38.search);
		Class607.B630A78B.object_0[388](button_9, Class607.B630A78B.object_0[760](6, 19));
		Class607.B630A78B.object_0[689](button_9, "BtnAutofirehose");
		Class607.B630A78B.object_0[1276](button_9, Class607.B630A78B.object_0[174](150, 64));
		Class607.B630A78B.object_0[334](button_9, 1);
		Class607.B630A78B.object_0[1175](button_9, "Auto Detect Device");
		Class607.B630A78B.object_0[347](button_9, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_9, CA2A151B: false);
		Class607.B630A78B.object_0[1239](button_9, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_4)));
		Class607.B630A78B.object_0[823](B6101030, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](B6101030).Add(E9AE2482);
		Class607.B630A78B.object_0[1278](B6101030, DockStyle.Bottom);
		Class607.B630A78B.object_0[388](B6101030, Class607.B630A78B.object_0[760](0, 465));
		Class607.B630A78B.object_0[689](B6101030, "panel1");
		Class607.B630A78B.object_0[356](B6101030, Class607.B630A78B.object_0[570](5, 0, 5, 5));
		Class607.B630A78B.object_0[1276](B6101030, Class607.B630A78B.object_0[174](1148, 44));
		Class607.B630A78B.object_0[334](B6101030, 35);
		Class607.B630A78B.object_0[823](panel_0, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](panel_0).Add(EC14989A);
		Class607.B630A78B.object_0[1278](panel_0, DockStyle.Right);
		Class607.B630A78B.object_0[388](panel_0, Class607.B630A78B.object_0[760](639, 27));
		Class607.B630A78B.object_0[689](panel_0, "panel2");
		Class607.B630A78B.object_0[356](panel_0, Class607.B630A78B.object_0[1237](5));
		Class607.B630A78B.object_0[1276](panel_0, Class607.B630A78B.object_0[174](509, 438));
		Class607.B630A78B.object_0[334](panel_0, 36);
		Class607.B630A78B.object_0[781](EC14989A).Add(tabPage_0);
		Class607.B630A78B.object_0[781](EC14989A).Add(A8208E90);
		Class607.B630A78B.object_0[781](EC14989A).Add(tabPage_1);
		Class607.B630A78B.object_0[781](EC14989A).Add(EB0ADA0C);
		EC14989A.Int32_0 = 0;
		Class607.B630A78B.object_0[1278](EC14989A, DockStyle.Fill);
		Class607.B630A78B.object_0[388](EC14989A, Class607.B630A78B.object_0[760](5, 5));
		EC14989A.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](EC14989A, "TPFeatures");
		Class607.B630A78B.object_0[734](EC14989A, 0);
		Class607.B630A78B.object_0[1276](EC14989A, Class607.B630A78B.object_0[174](499, 428));
		Class607.B630A78B.object_0[334](EC14989A, 32);
		Class607.B630A78B.object_0[781](tabPage_0).Add(B394B3AE);
		Class607.B630A78B.object_0[1250](tabPage_0, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](tabPage_0, "tpflash");
		Class607.B630A78B.object_0[356](tabPage_0, Class607.B630A78B.object_0[1237](3));
		Class607.B630A78B.object_0[1276](tabPage_0, Class607.B630A78B.object_0[174](491, 402));
		Class607.B630A78B.object_0[295](tabPage_0, 0);
		Class607.B630A78B.object_0[1175](tabPage_0, "flash");
		Class607.B630A78B.object_0[1031](tabPage_0, bool_0: true);
		Class607.B630A78B.object_0[823](B394B3AE, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](B394B3AE).Add(C8BC6199);
		Class607.B630A78B.object_0[781](B394B3AE).Add(gclass102_0);
		Class607.B630A78B.object_0[781](B394B3AE).Add(dataGridView_0);
		Class607.B630A78B.object_0[781](B394B3AE).Add(button_0);
		Class607.B630A78B.object_0[781](B394B3AE).Add(button_1);
		Class607.B630A78B.object_0[781](B394B3AE).Add(button_2);
		Class607.B630A78B.object_0[1278](B394B3AE, DockStyle.Fill);
		Class607.B630A78B.object_0[388](B394B3AE, Class607.B630A78B.object_0[760](3, 3));
		Class607.B630A78B.object_0[689](B394B3AE, "GrpBxFlash");
		Class607.B630A78B.object_0[1276](B394B3AE, Class607.B630A78B.object_0[174](485, 396));
		Class607.B630A78B.object_0[334](B394B3AE, 4);
		Class607.B630A78B.object_0[1183](B394B3AE, bool_0: false);
		Class607.B630A78B.object_0[1175](B394B3AE, "Flash");
		Class607.B630A78B.object_0[1242](C8BC6199, AnchorStyles.Bottom | AnchorStyles.Left);
		Class607.B630A78B.object_0[11](C8BC6199, E704E9A1: true);
		C8BC6199.Int32_0 = 0;
		Class607.B630A78B.object_0[1267](C8BC6199, Class607.B630A78B.object_0[1269]("Roboto Medium", 10f));
		Class607.B630A78B.object_0[388](C8BC6199, Class607.B630A78B.object_0[760](6, 357));
		Class607.B630A78B.object_0[349](C8BC6199, Class607.B630A78B.object_0[1237](0));
		C8BC6199.BD040C96 = Class607.B630A78B.object_0[760](-1, -1);
		C8BC6199.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](C8BC6199, "CheckBxAutoReboot");
		C8BC6199.Boolean_0 = true;
		Class607.B630A78B.object_0[1276](C8BC6199, Class607.B630A78B.object_0[174](106, 30));
		Class607.B630A78B.object_0[334](C8BC6199, 22);
		Class607.B630A78B.object_0[1175](C8BC6199, "Auto Reboot");
		Class607.B630A78B.object_0[270](C8BC6199, CA2A151B: true);
		Class607.B630A78B.object_0[1242](gclass102_0, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[1267](gclass102_0, Class607.B630A78B.object_0[1269]("Microsoft Sans Serif", 8f));
		Class607.B630A78B.object_0[388](gclass102_0, Class607.B630A78B.object_0[760](6, 18));
		Class607.B630A78B.object_0[689](gclass102_0, "TxtBxEDL");
		gclass102_0.String_0 = "Select firmware";
		Class607.B630A78B.object_0[664](gclass102_0, B9A668BD: true);
		Class607.B630A78B.object_0[1276](gclass102_0, Class607.B630A78B.object_0[174](409, 20));
		Class607.B630A78B.object_0[334](gclass102_0, 21);
		Class607.B630A78B.object_0[443](gclass102_0, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_11)));
		Class607.B630A78B.object_0[884](dataGridView_0, AB1299B1: false);
		Class607.B630A78B.object_0[280](dataGridView_0, bool_0: false);
		Class607.B630A78B.object_0[437](dataGridView_0, bool_0: false);
		Class607.B630A78B.object_0[1009](dataGridView_0, A03EA011: false);
		Class607.B630A78B.object_0[949](dataGridViewCellStyle, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[1257](dataGridView_0, dataGridViewCellStyle);
		Class607.B630A78B.object_0[1242](dataGridView_0, AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[852](dataGridView_0, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[83](dataGridView_0, DataGridViewHeaderBorderStyle.Single);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle2, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle2, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[772](dataGridViewCellStyle2, Class607.B630A78B.object_0[1237](3));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle2, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle2, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[352](dataGridViewCellStyle2, DataGridViewTriState.True);
		Class607.B630A78B.object_0[1228](dataGridView_0, dataGridViewCellStyle2);
		Class607.B630A78B.object_0[505](dataGridView_0, 32);
		Class607.B630A78B.object_0[459](dataGridView_0, DataGridViewColumnHeadersHeightSizeMode.DisableResizing);
		Class374.AF266ABD(Class607.B630A78B.object_0[182](dataGridView_0), new DataGridViewColumn[4] { ********, ********, dataGridViewTextBoxColumn_0, F594AF83 });
		Class607.B630A78B.object_0[308](dataGridView_0, Class607.B630A78B.object_0[541]());
		Class607.B630A78B.object_0[949](dataGridViewCellStyle3, DataGridViewContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle3, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle3, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle3, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle3, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle3, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[352](dataGridViewCellStyle3, DataGridViewTriState.True);
		Class607.B630A78B.object_0[20](dataGridView_0, dataGridViewCellStyle3);
		Class607.B630A78B.object_0[599](dataGridView_0, DataGridViewEditMode.EditOnEnter);
		Class607.B630A78B.object_0[1283](dataGridView_0, Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[388](dataGridView_0, Class607.B630A78B.object_0[760](6, 45));
		Class607.B630A78B.object_0[662](dataGridView_0, A8B78281: false);
		Class607.B630A78B.object_0[689](dataGridView_0, "DGVEDLFlash");
		Class607.B630A78B.object_0[949](dataGridViewCellStyle4, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle4, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle4, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle4, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle4, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle4, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[42](dataGridView_0, dataGridViewCellStyle4);
		Class607.B630A78B.object_0[733](dataGridView_0, bool_0: false);
		Class607.B630A78B.object_0[962](dataGridView_0, 51);
		Class607.B630A78B.object_0[949](dataGridViewCellStyle5, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle5, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle5, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle5, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle5, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle5, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[440](dataGridView_0, dataGridViewCellStyle5);
		Class607.B630A78B.object_0[949](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_0)), DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_0)), Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_0)), Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_0)), Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_0)), Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_0)), Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[1276](dataGridView_0, Class607.B630A78B.object_0[174](469, 307));
		Class607.B630A78B.object_0[334](dataGridView_0, 19);
		Class607.B630A78B.object_0[949](dataGridViewCellStyle6, DataGridViewContentAlignment.TopCenter);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle6, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle6, Class607.B630A78B.object_0[1269]("Microsoft Sans Serif", 9f));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle6, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[147](dataGridViewCellStyle6, false);
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle6, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle6, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[941](********, dataGridViewCellStyle6);
		Class607.B630A78B.object_0[766](********, bool_0: true);
		Class607.B630A78B.object_0[220](********, "*");
		Class607.B630A78B.object_0[818](********, 6);
		Class607.B630A78B.object_0[325](********, "CheckedDGV");
		Class607.B630A78B.object_0[48](********, DataGridViewTriState.True);
		Class607.B630A78B.object_0[94](********, 25);
		Class607.B630A78B.object_0[617](********, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle7, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle7, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle7, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle7, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle7, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[941](********, dataGridViewCellStyle7);
		Class607.B630A78B.object_0[882](********, 197.5309f);
		Class607.B630A78B.object_0[220](********, "Partition");
		Class607.B630A78B.object_0[818](********, 6);
		Class607.B630A78B.object_0[325](********, "dataGridViewTextBoxColumn5");
		Class607.B630A78B.object_0[12](********, bool_0: true);
		Class607.B630A78B.object_0[94](********, 83);
		Class607.B630A78B.object_0[617](dataGridViewTextBoxColumn_0, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle8, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[46](dataGridViewCellStyle8, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle8, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle8, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[352](dataGridViewCellStyle8, DataGridViewTriState.False);
		Class607.B630A78B.object_0[941](dataGridViewTextBoxColumn_0, dataGridViewCellStyle8);
		Class607.B630A78B.object_0[882](dataGridViewTextBoxColumn_0, 67.48971f);
		Class607.B630A78B.object_0[220](dataGridViewTextBoxColumn_0, "Begin");
		Class607.B630A78B.object_0[818](dataGridViewTextBoxColumn_0, 6);
		Class607.B630A78B.object_0[325](dataGridViewTextBoxColumn_0, "dataGridViewTextBoxColumn6");
		Class607.B630A78B.object_0[12](dataGridViewTextBoxColumn_0, bool_0: true);
		Class607.B630A78B.object_0[94](dataGridViewTextBoxColumn_0, 70);
		Class607.B630A78B.object_0[617](F594AF83, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[949](dataGridViewCellStyle9, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle9, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle9, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle9, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle9, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle9, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[941](F594AF83, dataGridViewCellStyle9);
		Class607.B630A78B.object_0[882](F594AF83, 67.48971f);
		Class607.B630A78B.object_0[220](F594AF83, "File");
		Class607.B630A78B.object_0[818](F594AF83, 6);
		Class607.B630A78B.object_0[325](F594AF83, "DGVMediatekFlashBeginAddress");
		Class607.B630A78B.object_0[12](F594AF83, bool_0: true);
		Class607.B630A78B.object_0[94](F594AF83, 58);
		Class607.B630A78B.object_0[1242](button_0, AnchorStyles.Top | AnchorStyles.Right);
		Class607.B630A78B.object_0[823](button_0, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[1281](button_0, ImageLayout.Zoom);
		Class607.B630A78B.object_0[308](button_0, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](button_0), Class607.B630A78B.object_0[68]());
		Class607.B630A78B.object_0[1267](button_0, Class607.B630A78B.object_0[1269]("Microsoft Sans Serif", 9f));
		Class607.B630A78B.object_0[326](button_0, Class607.B630A78B.object_0[223]());
		Class607.B630A78B.object_0[953](button_0, (Image)Class607.B630A78B.object_0[656](object_, "BtnClear.Image"));
		Class607.B630A78B.object_0[388](button_0, Class607.B630A78B.object_0[760](421, 15));
		Class607.B630A78B.object_0[689](button_0, "BtnClear");
		Class607.B630A78B.object_0[1276](button_0, Class607.B630A78B.object_0[174](28, 24));
		Class607.B630A78B.object_0[334](button_0, 11);
		Class607.B630A78B.object_0[270](button_0, CA2A151B: false);
		Class607.B630A78B.object_0[1239](button_0, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_7)));
		Class607.B630A78B.object_0[1242](button_1, AnchorStyles.Top | AnchorStyles.Right);
		Class607.B630A78B.object_0[823](button_1, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[1281](button_1, ImageLayout.Stretch);
		Class607.B630A78B.object_0[308](button_1, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](button_1), Class607.B630A78B.object_0[68]());
		Class607.B630A78B.object_0[1267](button_1, Class607.B630A78B.object_0[1269]("Microsoft Sans Serif", 9f));
		Class607.B630A78B.object_0[326](button_1, Class607.B630A78B.object_0[223]());
		Class607.B630A78B.object_0[953](button_1, (Image)Class607.B630A78B.object_0[656](object_, "BtnSelectFirmware.Image"));
		Class607.B630A78B.object_0[388](button_1, Class607.B630A78B.object_0[760](451, 15));
		Class607.B630A78B.object_0[689](button_1, "BtnSelectFirmware");
		Class607.B630A78B.object_0[1276](button_1, Class607.B630A78B.object_0[174](28, 24));
		Class607.B630A78B.object_0[334](button_1, 10);
		Class607.B630A78B.object_0[270](button_1, CA2A151B: false);
		Class607.B630A78B.object_0[1239](button_1, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_6)));
		Class607.B630A78B.object_0[1242](button_2, AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[823](button_2, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[308](button_2, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](button_2), Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[540](button_2, FlatStyle.Flat);
		Class607.B630A78B.object_0[326](button_2, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_2, A282DB38.bolt);
		Class607.B630A78B.object_0[997](button_2, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_2, Class607.B630A78B.object_0[760](131, 358));
		Class607.B630A78B.object_0[689](button_2, "FlashBtn");
		Class607.B630A78B.object_0[1276](button_2, Class607.B630A78B.object_0[174](344, 29));
		Class607.B630A78B.object_0[334](button_2, 17);
		Class607.B630A78B.object_0[1175](button_2, "Flash");
		Class607.B630A78B.object_0[494](button_2, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_2, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_2, CA2A151B: false);
		Class607.B630A78B.object_0[1239](button_2, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_13)));
		Class607.B630A78B.object_0[823](A8208E90, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](A8208E90).Add(groupBox_0);
		Class607.B630A78B.object_0[1250](A8208E90, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](A8208E90, "tpfeature");
		Class607.B630A78B.object_0[356](A8208E90, Class607.B630A78B.object_0[1237](3));
		Class607.B630A78B.object_0[1276](A8208E90, Class607.B630A78B.object_0[174](491, 402));
		Class607.B630A78B.object_0[295](A8208E90, 1);
		Class607.B630A78B.object_0[1175](A8208E90, "Service");
		Class607.B630A78B.object_0[781](groupBox_0).Add(button_3);
		Class607.B630A78B.object_0[781](groupBox_0).Add(button_4);
		Class607.B630A78B.object_0[781](groupBox_0).Add(button_5);
		Class607.B630A78B.object_0[1278](groupBox_0, DockStyle.Top);
		Class607.B630A78B.object_0[388](groupBox_0, Class607.B630A78B.object_0[760](3, 3));
		Class607.B630A78B.object_0[689](groupBox_0, "GrpBxBypassUtility");
		Class607.B630A78B.object_0[1276](groupBox_0, Class607.B630A78B.object_0[174](485, 142));
		Class607.B630A78B.object_0[334](groupBox_0, 2);
		Class607.B630A78B.object_0[1183](groupBox_0, bool_0: false);
		Class607.B630A78B.object_0[1175](groupBox_0, "Bypass Utility");
		Class607.B630A78B.object_0[1242](button_3, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_3, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_3, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_3, A282DB38.christmas);
		Class607.B630A78B.object_0[997](button_3, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_3, Class607.B630A78B.object_0[760](7, 97));
		Class607.B630A78B.object_0[689](button_3, "BtnMiAccount");
		Class607.B630A78B.object_0[1276](button_3, Class607.B630A78B.object_0[174](472, 33));
		Class607.B630A78B.object_0[334](button_3, 1);
		Class607.B630A78B.object_0[1175](button_3, "Bypass MiAccount");
		Class607.B630A78B.object_0[494](button_3, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_3, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_3, CA2A151B: true);
		Class607.B630A78B.object_0[128](button_3, B7128B0A: false);
		Class607.B630A78B.object_0[1239](button_3, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.EA3F0091)));
		Class607.B630A78B.object_0[1242](button_4, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_4, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_4, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_4, A282DB38.eraser);
		Class607.B630A78B.object_0[997](button_4, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_4, Class607.B630A78B.object_0[760](7, 19));
		Class607.B630A78B.object_0[689](button_4, "BtnEraseUserdata");
		Class607.B630A78B.object_0[1276](button_4, Class607.B630A78B.object_0[174](472, 33));
		Class607.B630A78B.object_0[334](button_4, 0);
		Class607.B630A78B.object_0[1175](button_4, "Erase userdata");
		Class607.B630A78B.object_0[494](button_4, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_4, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_4, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_4, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_17)));
		Class607.B630A78B.object_0[1242](button_5, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_5, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_5, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_5, A282DB38.Glogo2);
		Class607.B630A78B.object_0[997](button_5, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_5, Class607.B630A78B.object_0[760](7, 58));
		Class607.B630A78B.object_0[689](button_5, "BtnFRPBypass");
		Class607.B630A78B.object_0[1276](button_5, Class607.B630A78B.object_0[174](472, 33));
		Class607.B630A78B.object_0[334](button_5, 0);
		Class607.B630A78B.object_0[1175](button_5, "FRP Bypass");
		Class607.B630A78B.object_0[494](button_5, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_5, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_5, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_5, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_18)));
		Class607.B630A78B.object_0[823](tabPage_1, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](tabPage_1).Add(groupBox_1);
		Class607.B630A78B.object_0[1250](tabPage_1, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](tabPage_1, "tpnetwork");
		Class607.B630A78B.object_0[356](tabPage_1, Class607.B630A78B.object_0[1237](5));
		Class607.B630A78B.object_0[1276](tabPage_1, Class607.B630A78B.object_0[174](491, 402));
		Class607.B630A78B.object_0[295](tabPage_1, 2);
		Class607.B630A78B.object_0[1175](tabPage_1, "efs");
		Class607.B630A78B.object_0[781](groupBox_1).Add(DB8E4E1B);
		Class607.B630A78B.object_0[781](groupBox_1).Add(B32BC996);
		Class607.B630A78B.object_0[781](groupBox_1).Add(F41873B3);
		Class607.B630A78B.object_0[1278](groupBox_1, DockStyle.Top);
		Class607.B630A78B.object_0[388](groupBox_1, Class607.B630A78B.object_0[760](5, 5));
		Class607.B630A78B.object_0[689](groupBox_1, "GRPBxEFS");
		Class607.B630A78B.object_0[1276](groupBox_1, Class607.B630A78B.object_0[174](481, 147));
		Class607.B630A78B.object_0[334](groupBox_1, 1);
		Class607.B630A78B.object_0[1183](groupBox_1, bool_0: false);
		Class607.B630A78B.object_0[1175](groupBox_1, "EFS Utility");
		Class607.B630A78B.object_0[1242](DB8E4E1B, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](DB8E4E1B, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](DB8E4E1B, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](DB8E4E1B, A282DB38.eraser);
		Class607.B630A78B.object_0[997](DB8E4E1B, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](DB8E4E1B, Class607.B630A78B.object_0[760](7, 97));
		Class607.B630A78B.object_0[689](DB8E4E1B, "BtnEFSReset");
		Class607.B630A78B.object_0[1276](DB8E4E1B, Class607.B630A78B.object_0[174](468, 33));
		Class607.B630A78B.object_0[334](DB8E4E1B, 1);
		Class607.B630A78B.object_0[1175](DB8E4E1B, "EFS Erase");
		Class607.B630A78B.object_0[494](DB8E4E1B, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](DB8E4E1B, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](DB8E4E1B, CA2A151B: true);
		Class607.B630A78B.object_0[1239](DB8E4E1B, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.D41F1334)));
		Class607.B630A78B.object_0[1242](B32BC996, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](B32BC996, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](B32BC996, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](B32BC996, A282DB38.bolt);
		Class607.B630A78B.object_0[997](B32BC996, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](B32BC996, Class607.B630A78B.object_0[760](7, 58));
		Class607.B630A78B.object_0[689](B32BC996, "BtnEFSRestore");
		Class607.B630A78B.object_0[1276](B32BC996, Class607.B630A78B.object_0[174](468, 33));
		Class607.B630A78B.object_0[334](B32BC996, 0);
		Class607.B630A78B.object_0[1175](B32BC996, "EFS Flash");
		Class607.B630A78B.object_0[494](B32BC996, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](B32BC996, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](B32BC996, CA2A151B: true);
		Class607.B630A78B.object_0[1239](B32BC996, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.AE9FA705)));
		Class607.B630A78B.object_0[1242](F41873B3, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](F41873B3, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](F41873B3, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](F41873B3, A282DB38.sync);
		Class607.B630A78B.object_0[997](F41873B3, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](F41873B3, Class607.B630A78B.object_0[760](7, 19));
		Class607.B630A78B.object_0[689](F41873B3, "BtnEFSBackup");
		Class607.B630A78B.object_0[1276](F41873B3, Class607.B630A78B.object_0[174](468, 33));
		Class607.B630A78B.object_0[334](F41873B3, 0);
		Class607.B630A78B.object_0[1175](F41873B3, "EFS Backup");
		Class607.B630A78B.object_0[494](F41873B3, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](F41873B3, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](F41873B3, CA2A151B: true);
		Class607.B630A78B.object_0[1239](F41873B3, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_19)));
		Class607.B630A78B.object_0[823](EB0ADA0C, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](EB0ADA0C).Add(groupBox_2);
		Class607.B630A78B.object_0[1250](EB0ADA0C, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](EB0ADA0C, "tppartition");
		Class607.B630A78B.object_0[356](EB0ADA0C, Class607.B630A78B.object_0[1237](5));
		Class607.B630A78B.object_0[1276](EB0ADA0C, Class607.B630A78B.object_0[174](491, 402));
		Class607.B630A78B.object_0[295](EB0ADA0C, 3);
		Class607.B630A78B.object_0[1175](EB0ADA0C, "partitions");
		Class607.B630A78B.object_0[781](groupBox_2).Add(dataGridView_1);
		Class607.B630A78B.object_0[781](groupBox_2).Add(DC0388A3);
		Class607.B630A78B.object_0[781](groupBox_2).Add(button_6);
		Class607.B630A78B.object_0[781](groupBox_2).Add(button_7);
		Class607.B630A78B.object_0[1278](groupBox_2, DockStyle.Fill);
		Class607.B630A78B.object_0[388](groupBox_2, Class607.B630A78B.object_0[760](5, 5));
		Class607.B630A78B.object_0[689](groupBox_2, "GrpBxPartition");
		Class607.B630A78B.object_0[1276](groupBox_2, Class607.B630A78B.object_0[174](481, 392));
		Class607.B630A78B.object_0[334](groupBox_2, 2);
		Class607.B630A78B.object_0[1183](groupBox_2, bool_0: false);
		Class607.B630A78B.object_0[1175](groupBox_2, "Partition Managment");
		Class607.B630A78B.object_0[884](dataGridView_1, AB1299B1: false);
		Class607.B630A78B.object_0[280](dataGridView_1, bool_0: false);
		Class607.B630A78B.object_0[437](dataGridView_1, bool_0: false);
		Class607.B630A78B.object_0[1009](dataGridView_1, A03EA011: false);
		Class607.B630A78B.object_0[949](dataGridViewCellStyle10, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle10, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle10, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle10, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle10, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle10, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[1257](dataGridView_1, dataGridViewCellStyle10);
		Class607.B630A78B.object_0[1242](dataGridView_1, AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[852](dataGridView_1, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[83](dataGridView_1, DataGridViewHeaderBorderStyle.Single);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle11, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle11, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[772](dataGridViewCellStyle11, Class607.B630A78B.object_0[1237](3));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle11, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle11, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[352](dataGridViewCellStyle11, DataGridViewTriState.True);
		Class607.B630A78B.object_0[1228](dataGridView_1, dataGridViewCellStyle11);
		Class607.B630A78B.object_0[505](dataGridView_1, 32);
		Class607.B630A78B.object_0[459](dataGridView_1, DataGridViewColumnHeadersHeightSizeMode.DisableResizing);
		Class374.AF266ABD(Class607.B630A78B.object_0[182](dataGridView_1), new DataGridViewColumn[5] { A7BA0627, dataGridViewTextBoxColumn_1, dataGridViewTextBoxColumn_2, dataGridViewTextBoxColumn_3, dataGridViewTextBoxColumn_4 });
		Class607.B630A78B.object_0[308](dataGridView_1, Class607.B630A78B.object_0[541]());
		Class607.B630A78B.object_0[949](dataGridViewCellStyle12, DataGridViewContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle12, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle12, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle12, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle12, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle12, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[352](dataGridViewCellStyle12, DataGridViewTriState.True);
		Class607.B630A78B.object_0[20](dataGridView_1, dataGridViewCellStyle12);
		Class607.B630A78B.object_0[599](dataGridView_1, DataGridViewEditMode.EditOnEnter);
		Class607.B630A78B.object_0[1283](dataGridView_1, Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[388](dataGridView_1, Class607.B630A78B.object_0[760](6, 54));
		Class607.B630A78B.object_0[662](dataGridView_1, A8B78281: false);
		Class607.B630A78B.object_0[689](dataGridView_1, "DGVPartition");
		Class607.B630A78B.object_0[949](dataGridViewCellStyle13, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle13, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle13, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle13, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle13, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle13, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[42](dataGridView_1, dataGridViewCellStyle13);
		Class607.B630A78B.object_0[733](dataGridView_1, bool_0: false);
		Class607.B630A78B.object_0[962](dataGridView_1, 51);
		Class607.B630A78B.object_0[949](dataGridViewCellStyle14, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle14, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle14, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle14, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle14, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle14, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[440](dataGridView_1, dataGridViewCellStyle14);
		Class607.B630A78B.object_0[949](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_1)), DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_1)), Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_1)), Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_1)), Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_1)), Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](dataGridView_1)), Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[1276](dataGridView_1, Class607.B630A78B.object_0[174](467, 262));
		Class607.B630A78B.object_0[334](dataGridView_1, 20);
		Class607.B630A78B.object_0[220](A7BA0627, "");
		Class607.B630A78B.object_0[325](A7BA0627, "Column1");
		Class607.B630A78B.object_0[94](A7BA0627, 50);
		Class607.B630A78B.object_0[617](dataGridViewTextBoxColumn_1, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle15, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle15, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle15, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle15, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle15, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[941](dataGridViewTextBoxColumn_1, dataGridViewCellStyle15);
		Class607.B630A78B.object_0[882](dataGridViewTextBoxColumn_1, 197.5309f);
		Class607.B630A78B.object_0[220](dataGridViewTextBoxColumn_1, "Name");
		Class607.B630A78B.object_0[818](dataGridViewTextBoxColumn_1, 6);
		Class607.B630A78B.object_0[325](dataGridViewTextBoxColumn_1, "DGVMediatekFlashName");
		Class607.B630A78B.object_0[12](dataGridViewTextBoxColumn_1, bool_0: true);
		Class607.B630A78B.object_0[94](dataGridViewTextBoxColumn_1, 72);
		Class607.B630A78B.object_0[617](dataGridViewTextBoxColumn_2, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle16, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[46](dataGridViewCellStyle16, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle16, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle16, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[352](dataGridViewCellStyle16, DataGridViewTriState.False);
		Class607.B630A78B.object_0[941](dataGridViewTextBoxColumn_2, dataGridViewCellStyle16);
		Class607.B630A78B.object_0[882](dataGridViewTextBoxColumn_2, 67.48971f);
		Class607.B630A78B.object_0[220](dataGridViewTextBoxColumn_2, "Lun");
		Class607.B630A78B.object_0[818](dataGridViewTextBoxColumn_2, 6);
		Class607.B630A78B.object_0[325](dataGridViewTextBoxColumn_2, "DGBMediatekFlashEndAddress");
		Class607.B630A78B.object_0[12](dataGridViewTextBoxColumn_2, bool_0: true);
		Class607.B630A78B.object_0[94](dataGridViewTextBoxColumn_2, 59);
		Class607.B630A78B.object_0[617](dataGridViewTextBoxColumn_3, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle17, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle17, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle17, Class607.B630A78B.object_0[777]());
		Class607.B630A78B.object_0[751](dataGridViewCellStyle17, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[352](dataGridViewCellStyle17, DataGridViewTriState.False);
		Class607.B630A78B.object_0[941](dataGridViewTextBoxColumn_3, dataGridViewCellStyle17);
		Class607.B630A78B.object_0[220](dataGridViewTextBoxColumn_3, "Sectors");
		Class607.B630A78B.object_0[818](dataGridViewTextBoxColumn_3, 6);
		Class607.B630A78B.object_0[325](dataGridViewTextBoxColumn_3, "OperationDGV");
		Class607.B630A78B.object_0[12](dataGridViewTextBoxColumn_3, bool_0: true);
		Class607.B630A78B.object_0[94](dataGridViewTextBoxColumn_3, 79);
		Class607.B630A78B.object_0[220](dataGridViewTextBoxColumn_4, "Sector");
		Class607.B630A78B.object_0[325](dataGridViewTextBoxColumn_4, "LUNColumn");
		Class607.B630A78B.object_0[12](dataGridViewTextBoxColumn_4, bool_0: true);
		Class607.B630A78B.object_0[94](dataGridViewTextBoxColumn_4, 73);
		Class607.B630A78B.object_0[1242](DC0388A3, AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[823](DC0388A3, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[308](DC0388A3, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](DC0388A3), Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[540](DC0388A3, FlatStyle.Flat);
		Class607.B630A78B.object_0[326](DC0388A3, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](DC0388A3, A282DB38.sync);
		Class607.B630A78B.object_0[997](DC0388A3, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](DC0388A3, Class607.B630A78B.object_0[760](6, 358));
		Class607.B630A78B.object_0[689](DC0388A3, "BtnReadPartition");
		Class607.B630A78B.object_0[1276](DC0388A3, Class607.B630A78B.object_0[174](467, 29));
		Class607.B630A78B.object_0[334](DC0388A3, 17);
		Class607.B630A78B.object_0[1175](DC0388A3, "Read");
		Class607.B630A78B.object_0[494](DC0388A3, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](DC0388A3, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](DC0388A3, CA2A151B: false);
		Class607.B630A78B.object_0[1239](DC0388A3, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_30)));
		Class607.B630A78B.object_0[1242](button_6, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[823](button_6, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[308](button_6, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](button_6), Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[540](button_6, FlatStyle.Flat);
		Class607.B630A78B.object_0[326](button_6, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_6, A282DB38.partitions);
		Class607.B630A78B.object_0[997](button_6, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_6, Class607.B630A78B.object_0[760](6, 19));
		Class607.B630A78B.object_0[689](button_6, "BtnGetPartition");
		Class607.B630A78B.object_0[1276](button_6, Class607.B630A78B.object_0[174](467, 29));
		Class607.B630A78B.object_0[334](button_6, 18);
		Class607.B630A78B.object_0[1175](button_6, "Read Partition info");
		Class607.B630A78B.object_0[494](button_6, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_6, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_6, CA2A151B: false);
		Class607.B630A78B.object_0[1239](button_6, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_26)));
		Class607.B630A78B.object_0[1242](button_7, AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[823](button_7, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[308](button_7, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](button_7), Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[540](button_7, FlatStyle.Flat);
		Class607.B630A78B.object_0[326](button_7, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_7, A282DB38.eraser);
		Class607.B630A78B.object_0[997](button_7, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_7, Class607.B630A78B.object_0[760](6, 323));
		Class607.B630A78B.object_0[689](button_7, "BtnErasePartition");
		Class607.B630A78B.object_0[1276](button_7, Class607.B630A78B.object_0[174](467, 29));
		Class607.B630A78B.object_0[334](button_7, 19);
		Class607.B630A78B.object_0[1175](button_7, "Erase");
		Class607.B630A78B.object_0[494](button_7, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_7, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_7, CA2A151B: false);
		Class607.B630A78B.object_0[1239](button_7, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EE97B6A8.method_28)));
		Class607.B630A78B.object_0[823](********, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](********).Add(EA1CF18C);
		Class607.B630A78B.object_0[781](********).Add(groupBox_3);
		Class607.B630A78B.object_0[1278](********, DockStyle.Fill);
		Class607.B630A78B.object_0[388](********, Class607.B630A78B.object_0[760](0, 27));
		Class607.B630A78B.object_0[689](********, "panel3");
		Class607.B630A78B.object_0[356](********, Class607.B630A78B.object_0[1237](5));
		Class607.B630A78B.object_0[1276](********, Class607.B630A78B.object_0[174](639, 438));
		Class607.B630A78B.object_0[334](********, 37);
		B2111D3A.GControl1_0 = EC14989A;
		B2111D3A.Int32_0 = 0;
		Class607.B630A78B.object_0[1278](B2111D3A, DockStyle.Top);
		Class607.B630A78B.object_0[388](B2111D3A, Class607.B630A78B.object_0[760](0, 0));
		B2111D3A.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](B2111D3A, "TPNav");
		Class607.B630A78B.object_0[1276](B2111D3A, Class607.B630A78B.object_0[174](1148, 27));
		B2111D3A.E4A0A31D = GEnum37.B7AD2FB4;
		Class607.B630A78B.object_0[334](B2111D3A, 33);
		Class607.B630A78B.object_0[511](this, Class607.B630A78B.object_0[1223](6f, 13f));
		Class607.B630A78B.object_0[1043](this, AutoScaleMode.Font);
		Class607.B630A78B.object_0[782](this).Add(********);
		Class607.B630A78B.object_0[782](this).Add(panel_0);
		Class607.B630A78B.object_0[782](this).Add(B6101030);
		Class607.B630A78B.object_0[782](this).Add(B2111D3A);
		Class607.B630A78B.object_0[690](this, "Qualcomm");
		Class607.B630A78B.object_0[1277](this, Class607.B630A78B.object_0[174](1148, 509));
		Class607.B630A78B.object_0[1149](E9AE2482, EB25F899: false);
		Class607.B630A78B.object_0[63](E9AE2482);
		Class607.B630A78B.object_0[1149](EA1CF18C, EB25F899: false);
		Class607.B630A78B.object_0[1040](pictureBox_0);
		Class607.B630A78B.object_0[1149](groupBox_3, EB25F899: false);
		Class607.B630A78B.object_0[63](groupBox_3);
		Class607.B630A78B.object_0[1149](B6101030, EB25F899: false);
		Class607.B630A78B.object_0[1149](panel_0, EB25F899: false);
		Class607.B630A78B.object_0[1149](EC14989A, EB25F899: false);
		Class607.B630A78B.object_0[1149](tabPage_0, EB25F899: false);
		Class607.B630A78B.object_0[1149](B394B3AE, EB25F899: false);
		Class607.B630A78B.object_0[63](B394B3AE);
		Class607.B630A78B.object_0[1040](dataGridView_0);
		Class607.B630A78B.object_0[1149](A8208E90, EB25F899: false);
		Class607.B630A78B.object_0[1149](groupBox_0, EB25F899: false);
		Class607.B630A78B.object_0[1149](tabPage_1, EB25F899: false);
		Class607.B630A78B.object_0[1149](groupBox_1, EB25F899: false);
		Class607.B630A78B.object_0[1149](EB0ADA0C, EB25F899: false);
		Class607.B630A78B.object_0[1149](groupBox_2, EB25F899: false);
		Class607.B630A78B.object_0[1040](dataGridView_1);
		Class607.B630A78B.object_0[1149](********, EB25F899: false);
		Class607.B630A78B.object_0[1150](this, bool_0: false);
	}

	[CompilerGenerated]
	private void F5825B01()
	{
		method_16();
	}

	[CompilerGenerated]
	private void F3136B2F()
	{
		method_22();
	}

	[CompilerGenerated]
	private void method_33()
	{
		method_23();
	}

	[CompilerGenerated]
	private void method_34()
	{
		method_24();
	}

	[CompilerGenerated]
	private void D61E4F90()
	{
		method_25();
	}
}
