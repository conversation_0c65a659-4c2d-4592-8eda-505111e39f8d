internal class EC82573E : GInterface0
{
	public const ushort ushort_0 = 62218;

	public ushort ushort_1;

	public ushort BE890A12;

	public uint C41CEAA0;

	public ushort ushort_2;

	public ushort ushort_3;

	public int DD8D5B35 => 12;

	public int B0B6FA3F(byte[] BF101B2F, int A7844013)
	{
		ushort_2 = GClass3.smethod_11(BF101B2F, A7844013);
		BE890A12 = GClass3.smethod_11(BF101B2F, A7844013 + 2);
		ushort_3 = GClass3.smethod_11(BF101B2F, A7844013 + 4);
		ushort_1 = GClass3.smethod_11(BF101B2F, A7844013 + 6);
		C41CEAA0 = GClass3.E038002D(BF101B2F, A7844013 + 8);
		return 12;
	}

	public void E317CFB8(byte[] FC8FB9BC, int DCB9372E)
	{
		throw Class607.B630A78B.object_0[260]();
	}

	public EC82573E()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
