using System;
using System.Drawing;
using System.Windows.Forms;
using MotoKingPro.Core;

namespace MotoKingPro.UI
{
    /// <summary>
    /// Custom label control (A09F0208 -> CustomLabel)
    /// </summary>
    public class CustomLabel : Label
    {
        public CustomLabel()
        {
            SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            BackColor = Color.Transparent;
            ForeColor = Color.White;
            Font = new Font("Segoe UI", 9F, FontStyle.Regular);
        }
    }

    /// <summary>
    /// Custom tab selector control (GControl2 -> TabSelector)
    /// </summary>
    public class TabSelector : Control
    {
        private int selectedIndex = 0;
        private string[] tabNames = { "Motorola", "Qualcomm", "Services", "Mediatek" };

        public event EventHandler SelectedIndexChanged;

        public int SelectedIndex
        {
            get => selectedIndex;
            set
            {
                if (selectedIndex != value && value >= 0 && value < tabNames.Length)
                {
                    selectedIndex = value;
                    Invalidate();
                    SelectedIndexChanged?.Invoke(this, EventArgs.Empty);
                }
            }
        }

        public TabSelector()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            BackColor = Color.FromArgb(37, 37, 38);
            Size = new Size(400, 40);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            
            Graphics g = e.Graphics;
            int tabWidth = Width / tabNames.Length;
            
            for (int i = 0; i < tabNames.Length; i++)
            {
                Rectangle tabRect = new Rectangle(i * tabWidth, 0, tabWidth, Height);
                Color backColor = (i == selectedIndex) ? Color.FromArgb(0, 122, 204) : Color.FromArgb(45, 45, 48);
                Color textColor = Color.White;
                
                using (Brush brush = new SolidBrush(backColor))
                {
                    g.FillRectangle(brush, tabRect);
                }
                
                TextRenderer.DrawText(g, tabNames[i], Font, tabRect, textColor, 
                    TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
            }
        }

        protected override void OnMouseClick(MouseEventArgs e)
        {
            base.OnMouseClick(e);
            int tabWidth = Width / tabNames.Length;
            int clickedIndex = e.X / tabWidth;
            SelectedIndex = clickedIndex;
        }
    }

    /// <summary>
    /// Custom tab control (GControl1 -> CustomTabControl)
    /// </summary>
    public class CustomTabControl : TabControl
    {
        public CustomTabControl()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            DrawMode = TabDrawMode.OwnerDrawFixed;
            Appearance = TabAppearance.Normal;
            BackColor = Color.FromArgb(45, 45, 48);
        }

        protected override void OnDrawItem(DrawItemEventArgs e)
        {
            Graphics g = e.Graphics;
            TabPage tabPage = TabPages[e.Index];
            Rectangle tabBounds = GetTabRect(e.Index);
            
            Color backColor = (e.State == DrawItemState.Selected) ? Color.FromArgb(0, 122, 204) : Color.FromArgb(45, 45, 48);
            Color textColor = Color.White;
            
            using (Brush brush = new SolidBrush(backColor))
            {
                g.FillRectangle(brush, tabBounds);
            }
            
            TextRenderer.DrawText(g, tabPage.Text, Font, tabBounds, textColor,
                TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
        }
    }

    /// <summary>
    /// Device status control (GControl0 -> DeviceStatusControl)
    /// </summary>
    public class DeviceStatusControl : UserControl
    {
        private DeviceInfo currentDevice;
        private DeviceConnectionState connectionState = DeviceConnectionState.Disconnected;

        public DeviceInfo CurrentDevice
        {
            get => currentDevice;
            set
            {
                currentDevice = value;
                Invalidate();
            }
        }

        public DeviceConnectionState ConnectionState
        {
            get => connectionState;
            set
            {
                connectionState = value;
                Invalidate();
            }
        }

        public DeviceStatusControl()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            BackColor = Color.FromArgb(37, 37, 38);
            Size = new Size(300, 200);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            
            Graphics g = e.Graphics;
            Font titleFont = new Font("Segoe UI", 12F, FontStyle.Bold);
            Font normalFont = new Font("Segoe UI", 9F);
            
            // Draw connection status
            string statusText = GetConnectionStatusText();
            Color statusColor = GetConnectionStatusColor();
            
            using (Brush brush = new SolidBrush(statusColor))
            {
                g.DrawString($"Status: {statusText}", titleFont, brush, 10, 10);
            }
            
            // Draw device info if connected
            if (currentDevice != null && connectionState != DeviceConnectionState.Disconnected)
            {
                int y = 40;
                using (Brush whiteBrush = new SolidBrush(Color.White))
                {
                    g.DrawString($"Model: {currentDevice.Model}", normalFont, whiteBrush, 10, y);
                    y += 20;
                    g.DrawString($"Manufacturer: {currentDevice.Manufacturer}", normalFont, whiteBrush, 10, y);
                    y += 20;
                    g.DrawString($"Android: {currentDevice.AndroidVersion}", normalFont, whiteBrush, 10, y);
                    y += 20;
                    g.DrawString($"Serial: {currentDevice.SerialNumber}", normalFont, whiteBrush, 10, y);
                    y += 20;
                    g.DrawString($"Platform: {currentDevice.Platform}", normalFont, whiteBrush, 10, y);
                }
            }
        }

        private string GetConnectionStatusText()
        {
            return connectionState switch
            {
                DeviceConnectionState.Disconnected => "Disconnected",
                DeviceConnectionState.ADB => "ADB Connected",
                DeviceConnectionState.Fastboot => "Fastboot Mode",
                DeviceConnectionState.EDL => "EDL Mode",
                DeviceConnectionState.DownloadMode => "Download Mode",
                DeviceConnectionState.Recovery => "Recovery Mode",
                _ => "Unknown"
            };
        }

        private Color GetConnectionStatusColor()
        {
            return connectionState switch
            {
                DeviceConnectionState.Disconnected => Color.Red,
                DeviceConnectionState.ADB => Color.Green,
                DeviceConnectionState.Fastboot => Color.Orange,
                DeviceConnectionState.EDL => Color.Blue,
                DeviceConnectionState.DownloadMode => Color.Purple,
                DeviceConnectionState.Recovery => Color.Yellow,
                _ => Color.Gray
            };
        }
    }

    /// <summary>
    /// Rich text box with logging capabilities
    /// </summary>
    public class LogRichTextBox : RichTextBox
    {
        public LogRichTextBox()
        {
            BackColor = Color.FromArgb(30, 30, 30);
            ForeColor = Color.White;
            Font = new Font("Consolas", 9F);
            ReadOnly = true;
            WordWrap = true;
        }

        public void LogMessage(string message, LogLevel level = LogLevel.Info, bool newLine = true, bool bold = false)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => LogMessage(message, level, newLine, bold)));
                return;
            }

            Color color = level switch
            {
                LogLevel.Success => Color.Green,
                LogLevel.Warning => Color.Orange,
                LogLevel.Error => Color.Red,
                LogLevel.Word => Color.Cyan,
                _ => Color.White
            };

            SelectionStart = TextLength;
            SelectionLength = 0;
            SelectionColor = color;
            
            if (bold)
            {
                SelectionFont = new Font(Font, FontStyle.Bold);
            }
            else
            {
                SelectionFont = Font;
            }

            AppendText(message);
            
            if (newLine)
            {
                AppendText(Environment.NewLine);
            }

            SelectionStart = TextLength;
            ScrollToCaret();
        }

        public void ClearLog()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(ClearLog));
                return;
            }
            
            Clear();
        }
    }
}
