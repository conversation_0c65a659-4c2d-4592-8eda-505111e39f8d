using System;
using System.Runtime.InteropServices;
using System.Security;
using System.Security.Permissions;

namespace MotoKingPro.MediaTek
{
    /// <summary>
    /// FlashToolLib.dll P/Invoke declarations for MediaTek operations
    /// </summary>
    public static class FlashToolLibrary
    {
        #region Constants
        public const int S_DONE = 0;
        public const int S_STOP = 1;
        public const int S_UNDEFINED_ERROR = -1;
        public const int S_FT_NEED_DOWNLOAD_ALL_FAIL = -2;
        public const int S_FT_NEED_DOWNLOAD_ALL_PASS = -3;
        public const int S_BROM_CMD_STARTCMD_FAIL = -4;
        public const int S_BROM_CMD_JUMP_FAIL = -5;
        public const int S_BROM_CMD_SEND_DA_FAIL = -6;
        public const int S_BROM_CMD_JUMP_DA_FAIL = -7;
        public const int S_DA_HANDLE_ERROR = -8;
        public const int S_TIMEOUT = -9;
        public const int S_COM_PORT_OPEN_FAIL = -10;
        public const int S_INVALID_ARGUMENTS = -11;
        public const int S_UNSUPPORTED_OPERATION = -12;
        public const int S_INVALID_DA_FILE = -13;
        public const int S_INVALID_SCATTER_FILE = -14;
        public const int S_INVALID_ROM_FILE = -15;
        public const int S_SECURITY_CHECK_FAIL = -16;
        public const int S_AUTH_HANDLE_ERROR = -17;
        public const int S_DOWNLOAD_FAIL = -18;
        public const int S_UPLOAD_FAIL = -19;
        public const int S_FORMAT_FAIL = -20;
        public const int S_READBACK_FAIL = -21;
        public const int S_WRITE_MEMORY_FAIL = -22;
        public const int S_READ_MEMORY_FAIL = -23;
        public const int S_DEVICE_NOT_FOUND = -24;
        public const int S_USB_DEVICE_NOT_FOUND = -25;
        public const int S_BROM_EXCEPTION_OCCUR = -26;
        public const int S_DA_EXCEPTION_OCCUR = -27;
        public const int S_BOOTLOADER_EXCEPTION_OCCUR = -28;
        #endregion

        #region Enums
        public enum FLASHTOOL_API_HANDLE_TYPE
        {
            FLASHTOOL_HANDLE_INVALID = 0,
            FLASHTOOL_HANDLE_BROM = 1,
            FLASHTOOL_HANDLE_DA = 2
        }

        public enum HW_StorageType_E
        {
            HW_STORAGE_NAND = 0,
            HW_STORAGE_EMMC = 1,
            HW_STORAGE_SDMMC = 2,
            HW_STORAGE_UFS = 3
        }

        public enum AUTH_HANDLE_TYPE
        {
            AUTH_NONE = 0,
            AUTH_SLA = 1,
            AUTH_DAA = 2
        }

        public enum NUTL_ADDR_TYPE_E
        {
            NUTL_ADDR_LOGICAL = 0,
            NUTL_ADDR_PHYSICAL = 1
        }

        public enum CBFS_API_HANDLE_TYPE
        {
            CBFS_HANDLE_INVALID = 0,
            CBFS_HANDLE_FLASHTOOL = 1
        }
        #endregion

        #region Structures
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct FLASHTOOL_ARG
        {
            public IntPtr m_cb_download_flash_init;
            public IntPtr m_cb_download_flash;
            public IntPtr m_cb_download_flash_init_arg;
            public IntPtr m_cb_download_flash_arg;
            public IntPtr m_cb_security_pre_process_notify;
            public IntPtr m_cb_security_post_process_notify;
            public IntPtr m_cb_security_pre_process_notify_arg;
            public IntPtr m_cb_security_post_process_notify_arg;
            public IntPtr m_cb_operation_process_notify;
            public IntPtr m_cb_operation_process_notify_arg;
            public uint m_ms_boot_timeout;
            public uint m_max_start_cmd_retry_count;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct FLASHTOOL_CONNECT_ARG
        {
            public IntPtr m_p_stopflag;
            public uint m_com_ms_timeout;
            public uint m_usb_ms_timeout;
            public AUTH_HANDLE_TYPE m_auth_handle_type;
            public IntPtr m_p_auth_handle;
            public IntPtr m_cb_com_init_stage;
            public IntPtr m_cb_com_init_stage_arg;
            public IntPtr m_cb_usb_init_stage;
            public IntPtr m_cb_usb_init_stage_arg;
            public IntPtr m_cb_in_brom_stage;
            public IntPtr m_cb_in_brom_stage_arg;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct FLASHTOOL_DOWNLOAD_ARG
        {
            public IntPtr m_dl_handle;
            public IntPtr m_dl_handle_list;
            public uint m_dl_handle_list_count;
            public IntPtr m_cb_download_flash_init;
            public IntPtr m_cb_download_flash;
            public IntPtr m_cb_download_flash_init_arg;
            public IntPtr m_cb_download_flash_arg;
            public IntPtr m_p_stopflag;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct FLASHTOOL_FORMAT_ARG
        {
            public IntPtr m_format_handle;
            public IntPtr m_cb_format_report_init;
            public IntPtr m_cb_format_report;
            public IntPtr m_cb_format_report_init_arg;
            public IntPtr m_cb_format_report_arg;
            public IntPtr m_p_stopflag;
            public uint m_format_begin_addr;
            public uint m_format_length;
            public uint m_part_id;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct DA_INFO
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_version;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_da_version;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_expected_da_version;
            public uint m_random_seed;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct BROM_INFO
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_version;
            public uint m_hw_code;
            public uint m_hw_sub_code;
            public uint m_hw_version;
            public uint m_sw_version;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct PLATFORM_INFO
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_szChipName;
            public HW_StorageType_E m_storage_type;
            public uint m_nand_acccon;
            public uint m_nand_pagesize;
            public uint m_nand_sparesize;
            public uint m_nand_pages_of_block;
            public uint m_emmc_boot1_size;
            public uint m_emmc_boot2_size;
            public uint m_emmc_rpmb_size;
            public uint m_emmc_gp_size_1;
            public uint m_emmc_gp_size_2;
            public uint m_emmc_gp_size_3;
            public uint m_emmc_gp_size_4;
            public uint m_emmc_ua_size;
            public uint m_sdmmc_ua_size;
            public uint m_ufs_lu0_size;
            public uint m_ufs_lu1_size;
            public uint m_ufs_lu2_size;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct DL_HANDLE_T
        {
            public IntPtr m_dl_handle;
            public uint m_dl_handle_index;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_filepath;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_partition_name;
            public uint m_start_addr;
            public uint m_partition_size;
            public uint m_part_id;
            public bool m_enable;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct SCATTER_Head_Info
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_szPlatform;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_szConfig;
            public uint m_nPlatformId;
            public uint m_nConfigId;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct USB_STATUS_T
        {
            public bool m_usb_status;
            public ushort m_usb_vid;
            public ushort m_usb_pid;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_usb_manufacturer_string;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_usb_product_string;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Ansi)]
        public struct FLASHTOOL_READBACK_ARG
        {
            public IntPtr m_readback_handle;
            public IntPtr m_cb_readback_flash_init;
            public IntPtr m_cb_readback_flash;
            public IntPtr m_cb_readback_flash_init_arg;
            public IntPtr m_cb_readback_flash_arg;
            public IntPtr m_p_stopflag;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
            public string m_readback_save_path;
        }
        #endregion

        #region DLL Imports - Connection
        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_Connect(
            short com_port,
            ref FLASHTOOL_ARG flashtool_arg,
            ref FLASHTOOL_CONNECT_ARG connect_arg,
            ref BROM_INFO brom_info,
            int timeout_ms,
            ref IntPtr p_flashtool_handle,
            bool is_usb = true);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_Connect_BROM(
            short com_port,
            ref FLASHTOOL_ARG flashtool_arg,
            ref IntPtr p_flashtool_handle,
            IntPtr p_stopflag,
            bool is_usb = true);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_Connect_BROM_ByName(
            byte[] com_port_name,
            ref FLASHTOOL_ARG flashtool_arg,
            ref IntPtr p_flashtool_handle,
            ref int timeout_ms,
            bool is_usb = true);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_Disconnect(ref IntPtr p_flashtool_handle);
        #endregion

        #region DLL Imports - Device Information
        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int DL_GetPlatformInfo(
            IntPtr p_flashtool_handle,
            ref PLATFORM_INFO platform_info);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_GetDAInfo(
            IntPtr p_flashtool_handle,
            ref DA_INFO da_info);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_CheckUSBStatus(
            IntPtr p_flashtool_handle,
            ref USB_STATUS_T usb_status,
            ref BROM_INFO brom_info);
        #endregion

        #region DLL Imports - Download Operations
        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int DL_Create(ref IntPtr p_dl_handle);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int DL_LoadScatter(
            IntPtr p_dl_handle,
            [MarshalAs(UnmanagedType.LPStr)] string scatter_file_path,
            ref SCATTER_Head_Info scatter_head_info);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int DL_GetCount(
            IntPtr p_dl_handle,
            out ushort p_rom_count);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int DL_GetHandle(
            IntPtr p_dl_handle,
            ushort index,
            ref DL_HANDLE_T p_dl_handle_node);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int DL_SetEnableAttr(
            IntPtr p_dl_handle,
            ushort index,
            bool enable);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_Download(
            IntPtr p_flashtool_handle,
            ref FLASHTOOL_DOWNLOAD_ARG download_arg,
            ref BROM_INFO brom_info);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int DL_Destroy(ref IntPtr p_dl_handle);
        #endregion

        #region DLL Imports - Format Operations
        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_Format(
            IntPtr p_flashtool_handle,
            ref FLASHTOOL_FORMAT_ARG format_arg,
            ref BROM_INFO brom_info);
        #endregion

        #region DLL Imports - Readback Operations
        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_Readback(
            IntPtr p_flashtool_handle,
            ref FLASHTOOL_READBACK_ARG readback_arg,
            ref BROM_INFO brom_info);
        #endregion

        #region DLL Imports - Memory Operations
        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_WriteMemory(
            IntPtr p_flashtool_handle,
            uint address,
            IntPtr p_data,
            uint length,
            IntPtr p_stopflag);

        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_ReadMemory(
            IntPtr p_flashtool_handle,
            uint address,
            IntPtr p_data,
            uint length,
            IntPtr p_stopflag);
        #endregion

        #region DLL Imports - Boot Operations
        [DllImport("lib/FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Auto, SetLastError = true)]
        [SecurityCritical]
        public static extern int FlashTool_Reboot(
            IntPtr p_flashtool_handle,
            IntPtr p_stopflag);
        #endregion

        #region Helper Methods
        public static string GetErrorMessage(int errorCode)
        {
            return errorCode switch
            {
                S_DONE => "Operation completed successfully",
                S_STOP => "Operation stopped by user",
                S_UNDEFINED_ERROR => "Undefined error occurred",
                S_FT_NEED_DOWNLOAD_ALL_FAIL => "Download all failed",
                S_FT_NEED_DOWNLOAD_ALL_PASS => "Download all passed",
                S_BROM_CMD_STARTCMD_FAIL => "BROM start command failed",
                S_BROM_CMD_JUMP_FAIL => "BROM jump command failed",
                S_BROM_CMD_SEND_DA_FAIL => "BROM send DA failed",
                S_BROM_CMD_JUMP_DA_FAIL => "BROM jump DA failed",
                S_DA_HANDLE_ERROR => "DA handle error",
                S_TIMEOUT => "Operation timeout",
                S_COM_PORT_OPEN_FAIL => "COM port open failed",
                S_INVALID_ARGUMENTS => "Invalid arguments",
                S_UNSUPPORTED_OPERATION => "Unsupported operation",
                S_INVALID_DA_FILE => "Invalid DA file",
                S_INVALID_SCATTER_FILE => "Invalid scatter file",
                S_INVALID_ROM_FILE => "Invalid ROM file",
                S_SECURITY_CHECK_FAIL => "Security check failed",
                S_AUTH_HANDLE_ERROR => "Authentication handle error",
                S_DOWNLOAD_FAIL => "Download failed",
                S_UPLOAD_FAIL => "Upload failed",
                S_FORMAT_FAIL => "Format failed",
                S_READBACK_FAIL => "Readback failed",
                S_WRITE_MEMORY_FAIL => "Write memory failed",
                S_READ_MEMORY_FAIL => "Read memory failed",
                S_DEVICE_NOT_FOUND => "Device not found",
                S_USB_DEVICE_NOT_FOUND => "USB device not found",
                S_BROM_EXCEPTION_OCCUR => "BROM exception occurred",
                S_DA_EXCEPTION_OCCUR => "DA exception occurred",
                S_BOOTLOADER_EXCEPTION_OCCUR => "Bootloader exception occurred",
                _ => $"Unknown error code: {errorCode}"
            };
        }
        #endregion
    }
}

