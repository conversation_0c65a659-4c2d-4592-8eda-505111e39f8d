using System.Collections.Generic;
using System.IO;
using System.Linq;

public class GClass63
{
	public byte[] D6053718;

	public uint D62136BC;

	public uint uint_0;

	public uint uint_1;

	public uint uint_2;

	public uint FCB50A3D;

	public uint uint_3;

	public byte E037E112;

	public byte byte_0;

	public ushort ushort_0;

	public uint uint_4;

	public List<byte[]> AB9D0C25;

	public uint uint_5;

	public uint F7B72EAE;

	public uint uint_6;

	public byte[] BEBC4A9F;

	public B42DC004 DC01848E;

	public byte[] byte_1;

	public string string_0;

	public byte[] byte_2;

	public GClass63(B42DC004 DC01848E)
	{
		Class607.B630A78B.object_0[571](this);
		this.DC01848E = DC01848E;
		D62136BC = 1296911693u;
		uint_0 = 3u;
		uint_1 = 6240u;
		uint_2 = 16777216u;
		FCB50A3D = 0u;
		uint_3 = 1162167621u;
		E037E112 = 0;
		byte_0 = 0;
		ushort_0 = 0;
		uint_4 = 0u;
		AB9D0C25 = new List<byte[]>();
		uint_5 = 0u;
		F7B72EAE = 1128481603u;
		uint_6 = 858993459u;
		BEBC4A9F = new byte[4100];
	}

	public bool BABD1F1C(byte[] byte_3)
	{
		if (!GClass112.smethod_14(byte_3, 0, 16).SequenceEqual(GClass112.FF06B0AD("414e445f5345434346475f7600000000")))
		{
			return false;
		}
		GClass65 gClass = new GClass65(byte_3);
		D6053718 = gClass.method_5(16);
		D62136BC = gClass.method_0();
		uint_0 = gClass.method_0();
		uint_1 = gClass.method_0();
		FCB50A3D = gClass.method_0();
		uint_2 = gClass.method_0();
		E037E112 = gClass.method_5()[0];
		byte_0 = gClass.method_5()[0];
		ushort_0 = gClass.method_1();
		uint_4 = gClass.method_0();
		byte_1 = gClass.method_5(Class607.B630A78B.object_0[1262](uint_1) - 44 - 4);
		uint_3 = gClass.method_0();
		if (D62136BC != 1296911693 || uint_3 != 1162167621)
		{
			return false;
		}
		byte[] array = DC01848E.GClass27_0.method_8(byte_1, DA31A1BE: false);
		byte[] first = GClass112.smethod_14(array, 0, 4);
		if (!first.SequenceEqual(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "IIII")) && !first.SequenceEqual(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "CCCC")) && !first.SequenceEqual(new byte[4]))
		{
			array = DC01848E.GClass27_0.method_10(byte_1, B1B013AD: false);
			first = GClass112.smethod_14(array, 0, 4);
			if (!first.SequenceEqual(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "IIII")) && !first.SequenceEqual(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "CCCC")) && !first.SequenceEqual(new byte[4]))
			{
				array = DC01848E.GClass27_0.F911B912(byte_1, E63DE020: false);
				first = GClass112.smethod_14(array, 0, 4);
				if (!first.SequenceEqual(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "IIII")) && !first.SequenceEqual(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "CCCC")) && !first.SequenceEqual(new byte[4]))
				{
					return false;
				}
				string_0 = "V3";
			}
			else
			{
				string_0 = "V2";
			}
		}
		else
		{
			string_0 = "SW";
		}
		byte_2 = array;
		GClass65 gClass2 = new GClass65(array);
		new List<byte>();
		for (int i = 0; i < 20; i++)
		{
			AB9D0C25.Add(gClass2.method_5(104));
		}
		uint_5 = gClass2.method_0();
		F7B72EAE = gClass2.method_0();
		if (F7B72EAE != 1128481603 || F7B72EAE != 1229539657)
		{
			return false;
		}
		uint_6 = gClass2.method_0();
		if (uint_6 != 858993459 || uint_6 != 1145324612 || uint_6 != 24579 || uint_6 != 24576 || uint_6 != 24578 || uint_6 != 24577)
		{
			return false;
		}
		BEBC4A9F = gClass2.method_5(4100);
		return true;
	}

	public byte[] method_0(string DA05AD8F = "unlock")
	{
		int num = 858993459;
		if (Class607.B630A78B.object_0[787](DA05AD8F, "unlock"))
		{
			uint_2 = 133300224u;
			num = 1145324612;
		}
		else if (Class607.B630A78B.object_0[787](DA05AD8F, "lock"))
		{
			uint_2 = 16777216u;
			num = 858993459;
		}
		if (Class607.B630A78B.object_0[787](DA05AD8F, "lock") && uint_6 != 1145324612)
		{
			throw Class607.B630A78B.object_0[778]("Can't find lock state");
		}
		if (Class607.B630A78B.object_0[787](DA05AD8F, "unlock") && uint_6 != 858993459 && uint_6 != 24579 && uint_6 != 24578 && uint_6 != 24577 && uint_6 != 24576)
		{
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[1217]("Can't find unlock state, current {0}", uint_6));
		}
		using MemoryStream object_ = Class607.B630A78B.object_0[939]();
		Class607.B630A78B.object_0[132](object_, D6053718, 0, D6053718.Length);
		Class169.C9AEEF3D(object_, GClass111.smethod_2("<I", new object[1] { D62136BC }), 0, 4);
		Class169.C9AEEF3D(object_, GClass111.smethod_2("<I", new object[1] { uint_0 }), 0, 4);
		Class169.C9AEEF3D(object_, GClass111.smethod_2("<I", new object[1] { uint_1 }), 0, 4);
		Class169.C9AEEF3D(object_, GClass111.smethod_2("<I", new object[1] { FCB50A3D }), 0, 4);
		Class169.C9AEEF3D(object_, GClass111.smethod_2("<I", new object[1] { uint_2 }), 0, 4);
		Class169.C9AEEF3D(object_, GClass111.smethod_2("<B", new object[1] { E037E112 }), 0, 1);
		Class169.C9AEEF3D(object_, GClass111.smethod_2("<B", new object[1] { byte_0 }), 0, 1);
		Class169.C9AEEF3D(object_, GClass111.smethod_2("<H", new object[1] { ushort_0 }), 0, 2);
		Class169.C9AEEF3D(object_, GClass111.smethod_2("<I", new object[1] { uint_4 }), 0, 4);
		using MemoryStream object_2 = Class607.B630A78B.object_0[939]();
		foreach (byte[] item in AB9D0C25)
		{
			Class607.B630A78B.object_0[132](object_2, item, 0, item.Length);
		}
		Class169.C9AEEF3D(object_2, GClass111.smethod_2("<I", new object[1] { uint_5 }), 0, 4);
		Class169.C9AEEF3D(object_2, GClass111.smethod_2("<I", new object[1] { F7B72EAE }), 0, 4);
		Class169.C9AEEF3D(object_2, GClass111.smethod_2("<I", new object[1] { num }), 0, 4);
		Class607.B630A78B.object_0[132](object_2, BEBC4A9F, 0, BEBC4A9F.Length);
		byte[] array = Class607.B630A78B.object_0[1126](object_2);
		if (Class607.B630A78B.object_0[787](string_0, "SW"))
		{
			array = DC01848E.GClass27_0.method_8(array);
		}
		else if (Class607.B630A78B.object_0[787](string_0, "V2"))
		{
			array = DC01848E.GClass27_0.F911B912(array, E63DE020: true);
		}
		else
		{
			if (!Class607.B630A78B.object_0[787](string_0, "V3"))
			{
				throw Class607.B630A78B.object_0[778]("Unknown error");
			}
			array = DC01848E.GClass27_0.method_10(array);
		}
		Class607.B630A78B.object_0[132](object_, array, 0, array.Length);
		Class169.C9AEEF3D(object_, GClass111.smethod_2("<I", new object[1] { uint_3 }), 0, 4);
		List<byte> list = new List<byte>();
		list.AddRange(Class607.B630A78B.object_0[1126](object_));
		while ((list.Count & 0x200) != 0)
		{
			list.Add(0);
		}
		return list.ToArray();
	}
}
