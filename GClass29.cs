using System;
using System.Runtime.InteropServices;

public class GClass29
{
	public enum GEnum1
	{
		DF86F993,
		const_1
	}

	public struct GStruct0
	{
		public uint FFB70E85;

		public GEnum1 genum1_0;

		public HandleRef handleRef_0;

		public IntPtr intptr_0;
	}

	public struct C9815AB1
	{
		public int int_0;

		public uint uint_0;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
		public string string_0;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
		public string string_1;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 512)]
		public string string_2;

		public bool bool_0;
	}

	public const ushort CF13A1B7 = 128;

	public const int int_0 = 533;

	public const int int_1 = 87654321;

	public const int int_2 = 87979987;

	[DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
	public static extern IntPtr StatusToString(int E796B617);

	public static string smethod_0(int CF83F884)
	{
		IntPtr f1A189B = StatusToString(CF83F884);
		string text = Class607.B630A78B.object_0[330](f1A189B);
		if (!Class607.B630A78B.object_0[1205](text))
		{
			text = Class607.B630A78B.object_0[99](text, "_", " ");
		}
		return text;
	}

	[DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
	public static extern int Brom_Debug_SetLogFilename(byte[] ********);

	[DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
	public static extern int Brom_DebugOn();

	[DllImport("FlashToolLib.dll", CharSet = CharSet.Unicode, SetLastError = true)]
	public static extern int GetSpecialCOMPortWithFilter(ref GStruct0 gstruct0_0, int A1ACFC12, ref C9815AB1 F1AE663B, IntPtr intptr_0, double double_0);

	public static string smethod_1(string F097F68F, int CF97000E)
	{
		return Class607.B630A78B.object_0[1105]("{0} ({1})", F097F68F, CF97000E);
	}

	public static GClass32.F8BA6731 smethod_2(uint C892802F)
	{
		if (Class607.B630A78B.object_0[901](Class607.B630A78B.object_0[6](typeof(GClass32.F8BA6731).TypeHandle), C892802F))
		{
			return (GClass32.F8BA6731)Class607.B630A78B.object_0[1081](Class607.B630A78B.object_0[6](typeof(GClass32.F8BA6731).TypeHandle), C892802F);
		}
		return GClass32.F8BA6731.AB059089;
	}

	public GClass29()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
