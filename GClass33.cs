public static class GClass33
{
	public static string String_0 => Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\Libusb\\x64\\install-filter.exe");

	public static string B3B9482F => Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\Libusb\\x86\\install-filter.exe");

	private static string C6B73DA3(string D28DC912, string string_0)
	{
		return GClass35.smethod_0(D28DC912, string_0, "", 5000);
	}

	public static bool smethod_0(string A2BA0B0D, string A59BE908)
	{
		string text = ((!Class607.B630A78B.object_0[25]()) ? C6B73DA3(B3B9482F, Class525.smethod_0(new string[5] { "install --device=\"USB\\Vid_", A2BA0B0D, "&Pid_", A59BE908, "\"" })) : C6B73DA3(String_0, Class525.smethod_0(new string[5] { "install --device=\"USB\\Vid_", A2BA0B0D, "&Pid_", A59BE908, "\"" })));
		if (Class607.B630A78B.object_0[1205](text))
		{
			return false;
		}
		return !Class607.B630A78B.object_0[1240](text, "installer failed") && !Class607.B630A78B.object_0[1240](text, "err:");
	}

	public static bool smethod_1(string C2039817, string string_0)
	{
		string text = ((!Class607.B630A78B.object_0[25]()) ? C6B73DA3(B3B9482F, Class525.smethod_0(new string[5] { "uninstall --device=\"USB\\Vid_", C2039817, "&Pid_", string_0, "\"" })) : C6B73DA3(String_0, Class525.smethod_0(new string[5] { "uninstall --device=\"USB\\Vid_", C2039817, "&Pid_", string_0, "\"" })));
		if (Class607.B630A78B.object_0[1205](text))
		{
			return false;
		}
		Class69.DC3F988F();
		return !Class607.B630A78B.object_0[1240](text, "installer failed");
	}
}
