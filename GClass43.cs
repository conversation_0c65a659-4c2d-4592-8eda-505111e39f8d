using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;

public class GClass43
{
	public class GClass44
	{
		public const byte byte_0 = 193;

		public const byte B7156B05 = 207;

		public const byte byte_1 = 192;

		public const byte ADAAD304 = 105;

		public const byte byte_2 = 150;

		public const byte F19113BF = 90;

		public const byte byte_3 = 165;

		public const byte DF349C3D = 187;

		public GClass44()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass45
	{
		public const byte C200443D = 81;

		public const byte byte_0 = 82;

		public const byte byte_1 = 96;

		public const byte byte_2 = 97;

		public const byte byte_3 = 98;

		public const byte A99BE628 = 99;

		public const byte C912CEAE = 100;

		public const byte byte_4 = 128;

		public const byte AA22E3BE = 129;

		public const byte A21B1115 = 130;

		public const byte E039B5A8 = 133;

		public const byte byte_5 = 137;

		public const byte BF9B2115 = 138;

		public const byte byte_6 = 139;

		public const byte A019D539 = 140;

		public const byte B73EAC91 = 141;

		public const byte byte_7 = 142;

		public const byte byte_8 = 143;

		public const byte byte_9 = 112;

		public const byte A834EEB1 = 113;

		public const byte byte_10 = 114;

		public const byte D383FF30 = 115;

		public const byte byte_11 = 122;

		public const byte BC94F03F = 123;

		public const byte byte_12 = 124;

		public const byte DDA551BB = 125;

		public const byte C53D0BAD = 126;

		public const byte byte_13 = 127;

		public const byte E61DCD08 = 153;

		public const byte FEA3B33C = 154;

		public const byte byte_14 = 155;

		public const byte B9220F3B = 156;

		public const byte F71E1F9F = 157;

		public const byte E499A81F = 158;

		public const byte byte_15 = 160;

		public const byte byte_16 = 161;

		public const byte byte_17 = 162;

		public const byte byte_18 = 163;

		public const byte byte_19 = 164;

		public const byte E13DF717 = 165;

		public const byte A904861C = 166;

		public const byte CD32610D = 167;

		public const byte byte_20 = 168;

		public const byte byte_21 = 169;

		public const byte byte_22 = 170;

		public const byte FF19EA28 = 171;

		public const byte byte_23 = 176;

		public const byte byte_24 = 177;

		public const byte EB32549D = 178;

		public const byte D40D1226 = 179;

		public const byte DE8D0024 = 180;

		public const byte byte_25 = 181;

		public const byte byte_26 = 182;

		public const byte byte_27 = 183;

		public const byte byte_28 = 184;

		public const byte A5377E18 = 185;

		public const byte D52F2DB6 = 186;

		public const byte D5B2728A = 187;

		public const byte DAB7142C = 188;

		public const byte byte_29 = 190;

		public const byte byte_30 = 191;

		public const byte byte_31 = 189;

		public const byte byte_32 = 208;

		public const byte CAB29A18 = 209;

		public const byte byte_33 = 210;

		public const byte byte_34 = 211;

		public const byte byte_35 = 212;

		public const byte byte_36 = 213;

		public const byte byte_37 = 214;

		public const byte byte_38 = 215;

		public const byte E028DB0B = 216;

		public const byte byte_39 = 217;

		public const byte byte_40 = 218;

		public const byte byte_41 = 219;

		public const byte byte_42 = 220;

		public const byte byte_43 = 221;

		public const byte byte_44 = 222;

		public const byte byte_45 = 223;

		public const byte CE82EFA7 = 224;

		public const byte byte_46 = 225;

		public const byte DA9C319E = 226;

		public const byte B688AF8A = 227;

		public const byte EFA72A26 = 228;

		public const byte B9121533 = 229;

		public const byte byte_47 = 230;

		public const byte byte_48 = 231;

		public const byte CAB56CB0 = 232;

		public const byte byte_49 = 233;

		public const byte DB240E0D = 234;

		public const byte BF066D95 = 235;

		public const byte byte_50 = 236;

		public const byte D5B61292 = 237;

		public const byte byte_51 = 238;

		public const byte byte_52 = 239;

		public const byte E39C4D9C = 240;

		public const byte byte_53 = 241;

		public const byte byte_54 = 242;

		public const byte byte_55 = 243;

		public const byte byte_56 = 244;

		public const byte byte_57 = 245;

		public const byte byte_58 = 246;

		public const byte C03113AD = 247;

		public const byte CB93AC21 = 248;

		public const byte byte_59 = 249;

		public const byte byte_60 = 250;

		public const byte byte_61 = 251;

		public const byte byte_62 = 252;

		public const byte byte_63 = 253;

		public const byte byte_64 = 254;

		public const byte F7288888 = byte.MaxValue;

		public GClass45()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass46
	{
		public uint D19FC33C = 0u;

		public byte[] byte_0;

		public uint EF1DD7A4 = 0u;

		public uint E20F5F3D = 0u;

		public List<ushort> list_0;

		public uint A33EF2BE = 0u;

		public uint uint_0 = 0u;

		public GClass46(byte[] F717F82C)
		{
			Class607.B630A78B.object_0[571](this);
			if (F717F82C != null && F717F82C.Length != 0)
			{
				GClass65 gClass = new GClass65(F717F82C);
				D19FC33C = gClass.method_0(bool_0: true);
				byte_0 = gClass.method_5(2);
				EF1DD7A4 = gClass.method_1(C9A971BB: true);
				E20F5F3D = gClass.method_0(bool_0: true);
				list_0 = gClass.method_2(4, bool_0: true);
				A33EF2BE = gClass.method_0(bool_0: true);
				uint_0 = gClass.method_0(bool_0: true);
			}
		}
	}

	public class GClass47
	{
		public uint CA9C221D = 0u;

		public byte[] E714ED3A;

		public uint C507AAA2 = 0u;

		public ulong ulong_0 = 0uL;

		public uint BA8AE29F = 0u;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private List<ushort> A0B1C694;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private GClass49 DF20F03D;

		public List<ushort> C8906790
		{
			[CompilerGenerated]
			get
			{
				return A0B1C694;
			}
			[CompilerGenerated]
			set
			{
				A0B1C694 = value;
			}
		}

		public GClass49 A2BCD3AF
		{
			[CompilerGenerated]
			get
			{
				return DF20F03D;
			}
			[CompilerGenerated]
			set
			{
				DF20F03D = value;
			}
		}

		public GClass47(byte[] A2A47B05)
		{
			Class607.B630A78B.object_0[571](this);
			if (A2A47B05 != null && A2A47B05.Length != 0)
			{
				GClass65 gClass = new GClass65(A2A47B05);
				CA9C221D = gClass.method_0(bool_0: true);
				E714ED3A = gClass.method_5();
				C507AAA2 = gClass.method_1(C9A971BB: true);
				ulong_0 = gClass.BD305808(bool_0: true);
				BA8AE29F = gClass.method_1(C9A971BB: true);
			}
		}
	}

	public class GClass48
	{
		public uint C7200E32 = 0u;

		public byte[] byte_0;

		public uint uint_0 = 0u;

		public uint uint_1 = 0u;

		public uint D13F483A = 0u;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private GClass49 gclass49_0;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private List<ushort> EDA6EC9A;

		public GClass49 D216FC13
		{
			[CompilerGenerated]
			get
			{
				return gclass49_0;
			}
			[CompilerGenerated]
			set
			{
				gclass49_0 = value;
			}
		}

		public List<ushort> List_0
		{
			[CompilerGenerated]
			get
			{
				return EDA6EC9A;
			}
			[CompilerGenerated]
			set
			{
				EDA6EC9A = value;
			}
		}

		public GClass48(byte[] byte_1)
		{
			Class607.B630A78B.object_0[571](this);
			if (byte_1 != null && byte_1.Length != 0)
			{
				GClass65 gClass = new GClass65(byte_1);
				C7200E32 = gClass.method_0(bool_0: true);
				byte_0 = gClass.method_5();
				uint_0 = gClass.method_1(C9A971BB: true);
				uint_1 = gClass.method_0(bool_0: true);
				D13F483A = gClass.method_1(C9A971BB: true);
				D216FC13 = null;
			}
		}
	}

	public class GClass49
	{
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private ushort ushort_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private ushort B3A34A1C;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private ushort ushort_1;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private byte[] byte_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private byte[] C494FE1A;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private byte[] byte_1;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private GClass49 gclass49_0;

		public ushort UInt16_0
		{
			[CompilerGenerated]
			get
			{
				return ushort_0;
			}
			[CompilerGenerated]
			set
			{
				ushort_0 = value;
			}
		}

		public ushort UInt16_1
		{
			[CompilerGenerated]
			get
			{
				return B3A34A1C;
			}
			[CompilerGenerated]
			set
			{
				B3A34A1C = value;
			}
		}

		public ushort F38B2ABF
		{
			[CompilerGenerated]
			get
			{
				return ushort_1;
			}
			[CompilerGenerated]
			set
			{
				ushort_1 = value;
			}
		}

		public byte[] Byte_0
		{
			[CompilerGenerated]
			get
			{
				return byte_0;
			}
			[CompilerGenerated]
			set
			{
				byte_0 = value;
			}
		}

		public byte[] C5AF4B33
		{
			[CompilerGenerated]
			get
			{
				return C494FE1A;
			}
			[CompilerGenerated]
			set
			{
				C494FE1A = value;
			}
		}

		public byte[] D1BB6589
		{
			[CompilerGenerated]
			get
			{
				return byte_1;
			}
			[CompilerGenerated]
			set
			{
				byte_1 = value;
			}
		}

		public GClass49 GClass49_0
		{
			[CompilerGenerated]
			get
			{
				return gclass49_0;
			}
			[CompilerGenerated]
			set
			{
				gclass49_0 = value;
			}
		}

		public GClass49(byte[] D493DB16)
		{
			Class607.B630A78B.object_0[571](this);
			if (D493DB16 != null && D493DB16.Length != 0)
			{
				GClass65 gClass = new GClass65(D493DB16);
				UInt16_0 = gClass.method_1(C9A971BB: true);
				UInt16_1 = gClass.method_1(C9A971BB: true);
				F38B2ABF = gClass.method_1(C9A971BB: true);
				Byte_0 = gClass.method_5();
				C5AF4B33 = gClass.method_5();
				D1BB6589 = gClass.method_5();
				GClass49_0 = null;
			}
		}
	}

	public class D0239F0F
	{
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private uint uint_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private ulong B59AFE8A;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private ulong ulong_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private ulong CBAA259A;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private List<ulong> list_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private ulong ulong_1;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private List<ulong> list_1;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private byte[] byte_0;

		public uint UInt32_0
		{
			[CompilerGenerated]
			get
			{
				return uint_0;
			}
			[CompilerGenerated]
			set
			{
				uint_0 = value;
			}
		}

		public ulong UInt64_0
		{
			[CompilerGenerated]
			get
			{
				return B59AFE8A;
			}
			[CompilerGenerated]
			set
			{
				B59AFE8A = value;
			}
		}

		public ulong UInt64_1
		{
			[CompilerGenerated]
			get
			{
				return ulong_0;
			}
			[CompilerGenerated]
			set
			{
				ulong_0 = value;
			}
		}

		public ulong UInt64_2
		{
			[CompilerGenerated]
			get
			{
				return CBAA259A;
			}
			[CompilerGenerated]
			set
			{
				CBAA259A = value;
			}
		}

		public List<ulong> A80C739F
		{
			[CompilerGenerated]
			get
			{
				return list_0;
			}
			[CompilerGenerated]
			set
			{
				list_0 = value;
			}
		}

		public ulong C19D9CA1
		{
			[CompilerGenerated]
			get
			{
				return ulong_1;
			}
			[CompilerGenerated]
			set
			{
				ulong_1 = value;
			}
		}

		public List<ulong> List_0
		{
			[CompilerGenerated]
			get
			{
				return list_1;
			}
			[CompilerGenerated]
			set
			{
				list_1 = value;
			}
		}

		public byte[] Byte_0
		{
			[CompilerGenerated]
			get
			{
				return byte_0;
			}
			[CompilerGenerated]
			set
			{
				byte_0 = value;
			}
		}

		public D0239F0F(byte[] byte_1)
		{
			Class607.B630A78B.object_0[571](this);
			if (byte_1 != null && byte_1.Length != 0)
			{
				GClass65 gClass = new GClass65(byte_1);
				UInt32_0 = gClass.method_0(bool_0: true);
				UInt64_0 = gClass.BD305808(bool_0: true);
				UInt64_1 = gClass.BD305808(bool_0: true);
				UInt64_2 = gClass.BD305808(bool_0: true);
				A80C739F = gClass.F09F2117(4, bool_0: true);
				C19D9CA1 = gClass.BD305808(bool_0: true);
				List_0 = gClass.F09F2117(2, bool_0: true);
				Byte_0 = gClass.method_5(8);
			}
		}
	}

	public class B891A187
	{
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private uint F5AC0716;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private ulong F93A2CB6;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private List<ulong> A6B5D7BF;

		public uint C72EC40B
		{
			[CompilerGenerated]
			get
			{
				return F5AC0716;
			}
			[CompilerGenerated]
			set
			{
				F5AC0716 = value;
			}
		}

		public ulong BF254E16
		{
			[CompilerGenerated]
			get
			{
				return F93A2CB6;
			}
			[CompilerGenerated]
			set
			{
				F93A2CB6 = value;
			}
		}

		public List<ulong> List_0
		{
			[CompilerGenerated]
			get
			{
				return A6B5D7BF;
			}
			[CompilerGenerated]
			set
			{
				A6B5D7BF = value;
			}
		}

		public B891A187(byte[] byte_0)
		{
			Class607.B630A78B.object_0[571](this);
			if (byte_0 != null && byte_0.Length != 0)
			{
				GClass65 gClass = new GClass65(byte_0);
				C72EC40B = gClass.method_0(bool_0: true);
				BF254E16 = gClass.BD305808(bool_0: true);
				List_0 = gClass.F09F2117(2, bool_0: true);
			}
		}
	}

	public class AA26AA0A
	{
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private uint uint_0;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private uint uint_1;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private uint uint_2;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private byte[] byte_0;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private byte[] E709720B;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private ulong ulong_0;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private List<ulong> list_0;

		public uint UInt32_0
		{
			[CompilerGenerated]
			get
			{
				return uint_0;
			}
			[CompilerGenerated]
			set
			{
				uint_0 = value;
			}
		}

		public uint UInt32_1
		{
			[CompilerGenerated]
			get
			{
				return uint_1;
			}
			[CompilerGenerated]
			set
			{
				uint_1 = value;
			}
		}

		public uint UInt32_2
		{
			[CompilerGenerated]
			get
			{
				return uint_2;
			}
			[CompilerGenerated]
			set
			{
				uint_2 = value;
			}
		}

		public byte[] Byte_0
		{
			[CompilerGenerated]
			get
			{
				return byte_0;
			}
			[CompilerGenerated]
			set
			{
				byte_0 = value;
			}
		}

		public byte[] BCAF8005
		{
			[CompilerGenerated]
			get
			{
				return E709720B;
			}
			[CompilerGenerated]
			set
			{
				E709720B = value;
			}
		}

		public ulong UInt64_0
		{
			[CompilerGenerated]
			get
			{
				return ulong_0;
			}
			[CompilerGenerated]
			set
			{
				ulong_0 = value;
			}
		}

		public List<ulong> List_0
		{
			[CompilerGenerated]
			get
			{
				return list_0;
			}
			[CompilerGenerated]
			set
			{
				list_0 = value;
			}
		}

		public AA26AA0A(byte[] byte_1)
		{
			Class607.B630A78B.object_0[571](this);
			if (byte_1 != null && byte_1.Length != 0)
			{
				GClass65 gClass = new GClass65(byte_1);
				UInt32_0 = gClass.method_0(bool_0: true);
				UInt32_1 = gClass.method_0(bool_0: true);
				UInt32_2 = gClass.method_0(bool_0: true);
				Byte_0 = gClass.method_5();
				BCAF8005 = gClass.method_5();
				UInt64_0 = gClass.BD305808(bool_0: true);
				List_0 = gClass.F09F2117(2, bool_0: true);
			}
		}
	}

	public class GClass50
	{
		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private byte[] DE016E17;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private uint uint_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private uint D727FB39;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private byte[] byte_0;

		public byte[] Byte_0
		{
			[CompilerGenerated]
			get
			{
				return DE016E17;
			}
			[CompilerGenerated]
			set
			{
				DE016E17 = value;
			}
		}

		public uint A18610A9
		{
			[CompilerGenerated]
			get
			{
				return uint_0;
			}
			[CompilerGenerated]
			set
			{
				uint_0 = value;
			}
		}

		public uint C9122D27
		{
			[CompilerGenerated]
			get
			{
				return D727FB39;
			}
			[CompilerGenerated]
			set
			{
				D727FB39 = value;
			}
		}

		public byte[] Byte_1
		{
			[CompilerGenerated]
			get
			{
				return byte_0;
			}
			[CompilerGenerated]
			set
			{
				byte_0 = value;
			}
		}

		public GClass50(byte[] byte_1)
		{
			Class607.B630A78B.object_0[571](this);
			if (byte_1 != null && byte_1.Length != 0)
			{
				GClass65 gClass = new GClass65(byte_1);
				Byte_0 = gClass.method_5();
				A18610A9 = gClass.method_0(bool_0: true);
				C9122D27 = gClass.method_0(bool_0: true);
				Byte_1 = gClass.method_5();
			}
		}
	}

	[Serializable]
	[CompilerGenerated]
	private sealed class AB975AAB
	{
		public static readonly AB975AAB _003C_003E9 = new AB975AAB();

		public static Func<byte, int> _003C_003E9__52_0;

		public static Func<byte, int> _003C_003E9__52_1;

		public AB975AAB()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal int FA1F2632(byte byte_0)
		{
			return (int)new GClass128().E6A33692(new object[2] { this, byte_0 }, 22531702);
		}

		internal int FEABF99D(byte byte_0)
		{
			return byte_0;
		}
	}

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private GDelegate25 gdelegate25_0;

	public Dictionary<int, string> dictionary_0;

	public E5964FB0 e5964FB0_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private GClass112.E39CAE0B e39CAE0B_0;

	public GClass51 gclass51_0;

	public GClass46 DA968917;

	public GClass47 gclass47_0;

	public GClass48 gclass48_0;

	public D0239F0F d0239F0F_0;

	public B891A187 b891A187_0;

	public AA26AA0A BC3A5BBC;

	public GClass50 F138DC03;

	public GClass59 gclass59_0;

	public event GDelegate25 Event_0
	{
		[CompilerGenerated]
		add
		{
			GDelegate25 gDelegate = gdelegate25_0;
			GDelegate25 gDelegate2;
			do
			{
				gDelegate2 = gDelegate;
				GDelegate25 value2 = (GDelegate25)Class607.B630A78B.object_0[752](gDelegate2, value);
				gDelegate = Interlocked.CompareExchange(ref gdelegate25_0, value2, gDelegate2);
			}
			while ((object)gDelegate != gDelegate2);
		}
		[CompilerGenerated]
		remove
		{
			GDelegate25 gDelegate = gdelegate25_0;
			GDelegate25 gDelegate2;
			do
			{
				gDelegate2 = gDelegate;
				GDelegate25 value2 = (GDelegate25)Class607.B630A78B.object_0[629](gDelegate2, value);
				gDelegate = Interlocked.CompareExchange(ref gdelegate25_0, value2, gDelegate2);
			}
			while ((object)gDelegate != gDelegate2);
		}
	}

	public event GClass112.E39CAE0B Event_1
	{
		[CompilerGenerated]
		add
		{
			GClass112.E39CAE0B e39CAE0B = e39CAE0B_0;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[752](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref e39CAE0B_0, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
		[CompilerGenerated]
		remove
		{
			GClass112.E39CAE0B e39CAE0B = e39CAE0B_0;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[629](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref e39CAE0B_0, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
	}

	public B42DC004 method_0()
	{
		return new B42DC004(e5964FB0_0);
	}

	public bool CA184E06(uint uint_0, uint[] uint_1)
	{
		for (int i = 0; i < uint_1.Length; i++)
		{
			method_2(Class607.B630A78B.object_0[21](uint_0 + i * 4), uint_1[i]);
		}
		return true;
	}

	public bool method_1(uint uint_0, byte[] E82A3617)
	{
		using (IEnumerator<int> enumerator = GClass112.smethod_30(0, E82A3617.Length, 4).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				List<byte> list = GClass112.smethod_14(E82A3617, current, 4).ToList();
				while (list.Count < 4)
				{
					list.Add(0);
				}
				method_2(Class607.B630A78B.object_0[21](uint_0 + current), GClass111.C3B9331C("<I", list.ToArray()).Cast<int>().ToArray());
			}
		}
		return true;
	}

	public bool method_2(uint uint_0, object A501EBBC)
	{
		if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.const_1)
		{
			return e5964FB0_0.gclass51_0.method_2(uint_0, A501EBBC);
		}
		if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.D03BC921)
		{
			return e5964FB0_0.gclass8_0.method_2(uint_0, A501EBBC);
		}
		List<uint> list = new List<uint>();
		if (GClass112.smethod_19(A501EBBC))
		{
			list.Add(Class607.B630A78B.object_0[40](A501EBBC));
		}
		else
		{
			list = (List<uint>)A501EBBC;
		}
		int num = 0;
		foreach (uint item in list)
		{
			if (method_3(Class607.B630A78B.object_0[21](uint_0 + num), item))
			{
				num += 4;
				continue;
			}
			return false;
		}
		return true;
	}

	public bool method_3(uint DC375707, uint uint_0)
	{
		e5964FB0_0.f7020D24_0.method_1(new byte[1] { 123 });
		e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { DC375707 }));
		e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { uint_0 }));
		if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] == 90)
		{
			return true;
		}
		return false;
	}

	public List<uint> method_4(uint uint_0, int BE0D3715 = 1)
	{
		if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.D03BC921)
		{
			return e5964FB0_0.gclass8_0.BDAA3A27(uint_0, BE0D3715);
		}
		if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.const_1)
		{
			return e5964FB0_0.gclass51_0.EE83B4B7(uint_0, BE0D3715);
		}
		List<uint> list = new List<uint>();
		using (IEnumerator<int> enumerator = GClass112.smethod_30(0, BE0D3715).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				uint num = method_5(Class607.B630A78B.object_0[21](uint_0 + current * 4));
				if (num != 0)
				{
					list.Add(num);
					if (BE0D3715 == 1)
					{
						return list;
					}
					continue;
				}
				return null;
			}
		}
		return list;
	}

	public uint method_5(uint uint_0)
	{
		if (e5964FB0_0.f7020D24_0.method_1(new byte[1] { 122 }))
		{
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { uint_0 }));
			F7020D24.GStruct60 gStruct = e5964FB0_0.f7020D24_0.method_6(4);
			uint result = Class607.B630A78B.object_0[40](GClass111.C3B9331C(">I", gStruct.byte_0)[0]);
			if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] == 90)
			{
				return result;
			}
		}
		return 0u;
	}

	public void method_6(B42DC004 D5852916)
	{
		byte[] array = null;
		if (gclass51_0.AA329715.D10D9B3F == null)
		{
		}
		if (array == null)
		{
			array = new byte[32];
		}
		D5852916.GClass27_0.A027F09F(array);
	}

	public bool A6AB45BB(bool bool_0)
	{
		string text = "unlock";
		if (!bool_0)
		{
			text = "lock";
		}
		e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[1217]("Perform functions related to {0} bootloader", text));
		Tuple<byte[], GClass37> tuple = gclass59_0.D60E1983(new D905A51F(0u, 0u, 0u));
		_ = tuple.Item1;
		GClass37 item = tuple.Item2;
		byte[] array = null;
		GClass37.B6BA8237 b6BA = default(GClass37.B6BA8237);
		foreach (GClass37.B6BA8237 item2 in item.list_0)
		{
			if (Class607.B630A78B.object_0[787](item2.string_2, "seccfg"))
			{
				b6BA = item2;
				array = B398B119(b6BA.D78F1C10 * gclass51_0.AA329715.uint_0, b6BA.ulong_1 * gclass51_0.AA329715.uint_0, "", "user").byte_0;
			}
		}
		if (array == null)
		{
			e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
			return false;
		}
		e39CAE0B_0?.Invoke("initializing seccfg :");
		if (!array.Take(4).SequenceEqual(GClass111.smethod_2("<I", new object[1] { 1296911693 })))
		{
			throw Class607.B630A78B.object_0[778]("Unknown seccfg partition header. Aborting unlock.");
		}
		B42DC004 b42DC = new B42DC004(e5964FB0_0);
		if (array.Take(12).SequenceEqual(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[47](), "AND_SECCFG_v")))
		{
			e39CAE0B_0?.Invoke("v3", EF1F389C.Success);
			GClass63 gClass = new GClass63(b42DC);
			e39CAE0B_0?.Invoke("excuting aes key arguments :");
			if (!gClass.BABD1F1C(array))
			{
				throw Class607.B630A78B.object_0[778]("Device has either already unlocked or the algorithm is unknown. Aborting.");
			}
			byte[] array2 = gClass.method_0(text);
			if (BD28DEB7(b6BA.D78F1C10 * e5964FB0_0.CF001212.uint_0, Class607.B630A78B.object_0[715](array2.Length), null, 0L, "user", array2))
			{
				e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
				DialogResult dialogResult = GClass110.C2AB1F9F("for completing unlock bootloader operation, some device required factory reset, do u want i do clear userdata?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
				if (dialogResult == DialogResult.Yes)
				{
					foreach (GClass37.B6BA8237 item3 in item.list_0)
					{
						string string_ = item3.string_2;
						string string_2 = string_;
						if (Class607.B630A78B.object_0[787](string_2, "metadata") || Class607.B630A78B.object_0[787](string_2, "md_udc") || Class607.B630A78B.object_0[787](string_2, "userdata"))
						{
							if (!method_16(item3.D78F1C10 * gclass51_0.AA329715.uint_0, item3.ulong_1 * gclass51_0.AA329715.uint_0))
							{
								e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
								return false;
							}
							e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
						}
					}
				}
				return true;
			}
			throw Class607.B630A78B.object_0[778]("Error on writing seccfg config to flash.");
		}
		IEnumerable<byte> first = array.Take(4);
		byte[] array3 = new byte[4];
		DEBEDD9C.smethod_0(array3, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		if (first.SequenceEqual(array3))
		{
			e39CAE0B_0?.Invoke("v4", EF1F389C.Success);
			GClass64 gClass2 = new GClass64(b42DC);
			e39CAE0B_0?.Invoke("excuting aes key arguments :");
			if (!gClass2.method_0(array))
			{
				throw Class607.B630A78B.object_0[778]("Device has either already unlocked or the algorithm is unknown. Aborting.");
			}
			byte[] array4 = gClass2.method_1(text);
			if (BD28DEB7(b6BA.D78F1C10 * e5964FB0_0.CF001212.uint_0, Class607.B630A78B.object_0[715](array4.Length), null, 0L, "user", array4))
			{
				e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
				DialogResult dialogResult2 = GClass110.C2AB1F9F("for completing unlock bootloader operation, some device required factory reset, do u want i do clear userdata?", "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
				if (dialogResult2 == DialogResult.Yes)
				{
					foreach (GClass37.B6BA8237 item4 in item.list_0)
					{
						string string_3 = item4.string_2;
						string string_4 = string_3;
						if (Class607.B630A78B.object_0[787](string_4, "metadata") || Class607.B630A78B.object_0[787](string_4, "md_udc") || Class607.B630A78B.object_0[787](string_4, "userdata"))
						{
							if (!method_16(item4.D78F1C10 * gclass51_0.AA329715.uint_0, item4.ulong_1 * gclass51_0.AA329715.uint_0))
							{
								e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
								return false;
							}
							e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
						}
					}
				}
				return true;
			}
			throw Class607.B630A78B.object_0[778]("Error on writing seccfg config to flash.");
		}
		throw Class607.B630A78B.object_0[778]("Unknown lockstate or no lockstate");
	}

	public Tuple<byte[], GClass37> AA2EF591()
	{
		GClass37 gClass = new GClass37();
		gClass.ulong_0 = gclass51_0.AA329715.ulong_0;
		gClass.BA28FF28 = Class607.B630A78B.object_0[1262](gclass51_0.AA329715.uint_0);
		List<GClass37.B6BA8237> list = new List<GClass37.B6BA8237>();
		F7020D24.GStruct60 gStruct = default(F7020D24.GStruct60);
		if (e5964FB0_0.f7020D24_0.method_1(new byte[1] { 165 }))
		{
			F7020D24.GStruct60 gStruct2 = e5964FB0_0.f7020D24_0.method_6(1);
			byte b = Class607.B630A78B.object_0[925](GClass111.C3B9331C(">B", gStruct2.byte_0)[0]);
			if (b == 90)
			{
				gStruct2 = e5964FB0_0.f7020D24_0.method_6(4);
				int num = Class607.B630A78B.object_0[84](GClass111.C3B9331C(">I", gStruct2.byte_0)[0]);
				if (e5964FB0_0.f7020D24_0.method_1(new byte[1] { 90 }))
				{
					gStruct = e5964FB0_0.f7020D24_0.method_6(num);
					if (e5964FB0_0.f7020D24_0.method_1(new byte[1] { 90 }))
					{
						if (gStruct.byte_0[72] == byte.MaxValue)
						{
							using IEnumerator<int> enumerator = GClass112.smethod_30(0, num, 96).GetEnumerator();
							while (Class607.B630A78B.object_0[212](enumerator))
							{
								int current = enumerator.Current;
								string bE8CA1B = Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[1096](), GClass112.smethod_14(gStruct.byte_0, current, 64));
								ulong num2 = Class607.B630A78B.object_0[750](GClass111.C3B9331C("<Q", GClass112.smethod_14(gStruct.byte_0, current + 64, 8))[0]);
								ulong ulong_ = Class607.B630A78B.object_0[750](GClass111.C3B9331C("<Q", GClass112.smethod_14(gStruct.byte_0, current + 72, 8))[0]);
								ulong num3 = Class607.B630A78B.object_0[750](GClass111.C3B9331C("<Q", GClass112.smethod_14(gStruct.byte_0, current + 80, 8))[0]);
								list.Add(new GClass37.B6BA8237
								{
									string_2 = Class607.B630A78B.object_0[95](bE8CA1B, new char[1]),
									string_1 = "1",
									D78F1C10 = num3 / gclass51_0.AA329715.uint_0,
									ulong_1 = num2 / gclass51_0.AA329715.uint_0,
									ulong_0 = ulong_
								});
							}
						}
						else
						{
							ulong num4 = Class607.B630A78B.object_0[750](GClass111.C3B9331C("<I", GClass112.smethod_14(gStruct.byte_0, 72, 4))[0]);
							if (num4 > 0L)
							{
								using IEnumerator<int> enumerator2 = GClass112.smethod_30(0, num, 88).GetEnumerator();
								while (Class607.B630A78B.object_0[212](enumerator2))
								{
									int current2 = enumerator2.Current;
									string bE8CA1B2 = Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[1096](), GClass112.smethod_14(gStruct.byte_0, current2, 64));
									ulong num5 = Class607.B630A78B.object_0[750](GClass111.C3B9331C("<Q", GClass112.smethod_14(gStruct.byte_0, current2 + 64, 8))[0]);
									ulong num6 = Class607.B630A78B.object_0[750](GClass111.C3B9331C("<Q", GClass112.smethod_14(gStruct.byte_0, current2 + 72, 8))[0]);
									num4 = Class607.B630A78B.object_0[750](GClass111.C3B9331C("<Q", GClass112.smethod_14(gStruct.byte_0, current2 + 80, 8))[0]);
									list.Add(new GClass37.B6BA8237
									{
										string_2 = Class607.B630A78B.object_0[95](bE8CA1B2, new char[1]),
										string_1 = "1",
										D78F1C10 = num6 / gclass51_0.AA329715.uint_0,
										ulong_1 = num5 / gclass51_0.AA329715.uint_0,
										ulong_0 = num4
									});
								}
							}
							else
							{
								using IEnumerator<int> enumerator3 = GClass112.smethod_30(0, num, 76).GetEnumerator();
								while (Class607.B630A78B.object_0[212](enumerator3))
								{
									int current3 = enumerator3.Current;
									string bE8CA1B3 = Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[1096](), GClass112.smethod_14(gStruct.byte_0, current3, 64));
									ulong num7 = Class607.B630A78B.object_0[750](GClass111.C3B9331C("<I", GClass112.smethod_14(gStruct.byte_0, current3 + 64, 4))[0]);
									ulong num8 = Class607.B630A78B.object_0[750](GClass111.C3B9331C("<I", GClass112.smethod_14(gStruct.byte_0, current3 + 68, 4))[0]);
									num4 = Class607.B630A78B.object_0[750](GClass111.C3B9331C("<I", GClass112.smethod_14(gStruct.byte_0, current3 + 72, 4))[0]);
									list.Add(new GClass37.B6BA8237
									{
										string_2 = Class607.B630A78B.object_0[95](bE8CA1B3, new char[1]),
										string_1 = "1",
										D78F1C10 = num8 / gclass51_0.AA329715.uint_0,
										ulong_1 = num7 / gclass51_0.AA329715.uint_0,
										ulong_0 = num4
									});
								}
							}
						}
						gClass.list_0 = list;
					}
				}
			}
		}
		return Tuple.Create(gStruct.byte_0, gClass);
	}

	public GClass43(E5964FB0 e5964FB0_1, GClass51 B0999A10)
	{
		new GClass128().F3BD1601(new object[3] { this, e5964FB0_1, B0999A10 }, 22517217);
	}

	public bool method_7(GClass70 gclass70_0)
	{
		return (bool)new GClass128().E6A33692(new object[2] { this, gclass70_0 }, 22075310);
	}

	public string EA177B99(int int_0)
	{
		if (dictionary_0.ContainsKey(int_0))
		{
			return dictionary_0[int_0];
		}
		return "unknown error";
	}

	public bool method_8()
	{
		e5964FB0_0.f7020D24_0.method_1(new byte[1] { Class607.B630A78B.object_0[75](GClass112.C78DEB29.A8054FBA.D4355B1E) });
		e5964FB0_0.f7020D24_0.method_1(new byte[1] { Class607.B630A78B.object_0[75](GClass112.C78DEB29.A8054FBA.int_4) });
		e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">H", new object[1] { (ushort)8 }));
		e5964FB0_0.f7020D24_0.method_1(new byte[1] { 0 });
		e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 1879572479 }));
		GClass112.C78DEB29.method_0(e5964FB0_0.int_1, gclass51_0.AA329715.string_1);
		e5964FB0_0.f7020D24_0.method_1(new byte[1] { Class607.B630A78B.object_0[75](GClass112.C78DEB29.int_0) });
		e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { GClass112.C78DEB29.EEAA3694 }));
		e5964FB0_0.f7020D24_0.method_1(new byte[1] { 1 });
		byte b = 1;
		if (e5964FB0_0.int_1 == 25987)
		{
			b = 0;
		}
		e5964FB0_0.f7020D24_0.method_1(new byte[1] { b });
		e5964FB0_0.f7020D24_0.method_1(new byte[1] { 2 });
		e5964FB0_0.f7020D24_0.method_1(new byte[1] { 0 });
		int num = 4;
		if (e5964FB0_0.int_1 == 26002)
		{
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 0 }));
		}
		else if (e5964FB0_0.int_1 == 25984 || e5964FB0_0.int_1 == 33123 || e5964FB0_0.int_1 == 33063)
		{
			if (e5964FB0_0.int_1 == 33063)
			{
				e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 0 }));
			}
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 1 }));
			byte[] object_ = GClass112.FF06B0AD("46460000000000000000000000000000ff000000");
			e5964FB0_0.f7020D24_0.method_1(object_);
		}
		else if (e5964FB0_0.int_1 == 25987 || e5964FB0_0.int_1 == 25993)
		{
			int num2 = 0;
			if (e5964FB0_0.int_1 == 25987)
			{
				num2 = 0;
			}
			else if (e5964FB0_0.int_1 == 25993)
			{
				num2 = 1;
			}
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { num2 }));
		}
		else if (e5964FB0_0.int_1 == 33063)
		{
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 0 }));
		}
		else if (e5964FB0_0.int_1 == 25986)
		{
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 1 }));
		}
		GClass112.smethod_28(350);
		F7020D24.GStruct60 gStruct = e5964FB0_0.f7020D24_0.method_6(num);
		if (gStruct.byte_0 == null || gStruct.byte_0.Length < 4)
		{
			e39CAE0B_0?.Invoke("Error: ");
			e39CAE0B_0?.Invoke(D09F0D3B.smethod_5(1075292u), EF1F389C.Error);
			return false;
		}
		List<byte> list = gStruct.byte_0.ToList();
		int num3 = Class607.B630A78B.object_0[84](GClass111.C3B9331C(">I", gStruct.byte_0)[0]);
		switch (num3)
		{
		case 0:
			return true;
		default:
			throw Class607.B630A78B.object_0[778](EA177B99(num3));
		case 3011:
			if (num == 4 && num3 == 3011)
			{
				gStruct = e5964FB0_0.f7020D24_0.method_6(4);
				list.AddRange(gStruct.byte_0);
				List<byte[]> list2 = new List<byte[]>();
				list2.Add(new byte[0]);
				list2.Add(new byte[0]);
				List<byte[]> list3 = list2;
				F7020D24.GStruct60 gStruct2 = e5964FB0_0.f7020D24_0.method_6(16);
				list3[0] = GClass112.smethod_14(gStruct2.byte_0, 0, 9);
				List<byte> list4 = GClass112.smethod_14(gStruct2.byte_0, 0, 4).Reverse().ToList();
				list4.AddRange(GClass112.smethod_14(gStruct2.byte_0, 4, 4).Reverse());
				list4.AddRange(GClass112.smethod_14(gStruct2.byte_0, 8, 4).Reverse());
				list4.AddRange(GClass112.smethod_14(gStruct2.byte_0, 12, 4).Reverse());
				list3[1] = GClass112.smethod_14(list4.ToArray(), 0, 9);
				e39CAE0B_0?.Invoke("dram config needed for :");
				e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", GClass112.smethod_27(list4.ToArray())), EF1F389C.Success);
				if (gclass51_0.AA329715.byte_1 == null || gclass51_0.AA329715.byte_1.Length == 0)
				{
					bool flag = false;
					string[] string_ = A3B08EB3.String_0;
					foreach (string string_2 in string_)
					{
						byte[] array = Class607.B630A78B.object_0[649](string_2);
						if (GClass112.smethod_24(array, list3[0], 0) != -1 || GClass112.smethod_24(array, list3[1], 0) != -1)
						{
							gclass51_0.AA329715.method_2(array);
							flag = true;
							break;
						}
					}
					if (!flag)
					{
						e39CAE0B_0?.Invoke("No preloader given. Operation may fail due to missing dram setup", EF1F389C.Error);
					}
				}
				F7020D24.GStruct60 gStruct3 = e5964FB0_0.f7020D24_0.method_6(4);
				num3 = Class607.B630A78B.object_0[84](GClass111.C3B9331C(">I", gStruct3.byte_0)[0]);
				if (gStruct.byte_0 == null || gStruct.byte_0.Length < 4)
				{
					e39CAE0B_0?.Invoke("Error: ");
					e39CAE0B_0?.Invoke(D09F0D3B.smethod_5(1075292u), EF1F389C.Error);
					return false;
				}
				if (num3 != 3012)
				{
					throw Class607.B630A78B.object_0[778](EA177B99(num3));
				}
				if (num3 == 3012)
				{
					e39CAE0B_0?.Invoke("Reading dram nand info :");
					gStruct3 = e5964FB0_0.f7020D24_0.method_6(2);
					int int_ = Class607.B630A78B.object_0[84](GClass111.C3B9331C(">H", gStruct3.byte_0)[0]);
					List<ushort> list5 = new List<ushort>();
					using (IEnumerator<int> enumerator = GClass112.smethod_30(0, int_).GetEnumerator())
					{
						while (Class607.B630A78B.object_0[212](enumerator))
						{
							_ = enumerator.Current;
							gStruct3 = e5964FB0_0.f7020D24_0.method_6(2);
							list5.Add(Class607.B630A78B.object_0[1181](GClass111.C3B9331C(">H", gStruct3.byte_0).Cast<ushort>().ToList()[0]));
						}
					}
					e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
					if (gclass51_0.AA329715.byte_1 == null || gclass51_0.AA329715.byte_1.Length == 0)
					{
						throw Class607.B630A78B.object_0[778]("Preloader needed due to dram config.");
					}
					e39CAE0B_0?.Invoke("Enabling dram: ");
					e5964FB0_0.f7020D24_0.method_1(new byte[1] { 232 });
					if (gclass51_0.AA329715.int_0 == 0)
					{
						e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { uint.MaxValue }));
					}
					else
					{
						e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { gclass51_0.AA329715.int_0 }));
					}
					gStruct3 = e5964FB0_0.f7020D24_0.method_6(1);
					if (gStruct3.byte_0[0] == 165)
					{
						throw Class607.B630A78B.object_0[778]("EMI Config not accepted :(");
					}
					if (gStruct3.byte_0[0] == 90)
					{
						e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
						e39CAE0B_0?.Invoke("Sending dram info :");
						int num4 = gclass51_0.AA329715.byte_1.Length;
						if (gclass51_0.AA329715.int_0 == 16 || gclass51_0.AA329715.int_0 == 20 || gclass51_0.AA329715.int_0 == 21 || gclass51_0.AA329715.int_0 == 15 || gclass51_0.AA329715.int_0 == 17)
						{
							gStruct3 = e5964FB0_0.f7020D24_0.method_6(4);
							e39CAE0B_0?.Invoke("RAM-Length :");
							num4 = Class607.B630A78B.object_0[84](GClass111.C3B9331C(">I", gStruct3.byte_0)[0]);
							e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[626](ref num4, "x")), EF1F389C.Error);
							e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { (byte)90 }));
							int num5 = gclass51_0.AA329715.byte_1.Length;
							e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { num5 }));
						}
						else if (gclass51_0.AA329715.int_0 == 11)
						{
							F7020D24.GStruct60 gStruct4 = e5964FB0_0.f7020D24_0.method_6(16);
							e39CAE0B_0?.Invoke("RAM-Info:");
							e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[1058](gStruct4.byte_0)), EF1F389C.Error);
							gStruct3 = e5964FB0_0.f7020D24_0.method_6(4);
							num4 = Class607.B630A78B.object_0[84](GClass111.C3B9331C(">I", gStruct3.byte_0)[0]);
							e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { (byte)90 }));
						}
						else if (gclass51_0.AA329715.int_0 == 13 || gclass51_0.AA329715.int_0 == 12)
						{
							F7020D24.GStruct60 gStruct5 = e5964FB0_0.f7020D24_0.method_6(4);
							num4 = Class607.B630A78B.object_0[84](GClass111.C3B9331C(">I", gStruct5.byte_0)[0]);
							e39CAE0B_0?.Invoke("RAM-Length:");
							e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[626](ref num4, "X")), EF1F389C.Error);
							e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { (byte)90 }));
							gclass51_0.AA329715.byte_1 = GClass112.smethod_14(gclass51_0.AA329715.byte_1, 0, num4);
							List<byte> list6 = GClass111.smethod_2(">I", new object[1] { 256 }).ToList();
							list6.AddRange(GClass112.smethod_14(gclass51_0.AA329715.byte_1, 4, num4 - 4));
							gclass51_0.AA329715.byte_1 = list6.ToArray();
						}
						else if (gclass51_0.AA329715.int_0 == 0)
						{
							F7020D24.GStruct60 gStruct6 = e5964FB0_0.f7020D24_0.method_6(4);
							num4 = Class607.B630A78B.object_0[84](GClass111.C3B9331C(">I", gStruct6.byte_0)[0]);
							e39CAE0B_0?.Invoke("RAM-Length:");
							e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[626](ref num4, "X")), EF1F389C.Error);
							e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { (byte)90 }));
							gclass51_0.AA329715.byte_1 = GClass112.smethod_14(gclass51_0.AA329715.byte_1, 0, num4);
							e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { num4 }));
						}
						e5964FB0_0.f7020D24_0.method_1(gclass51_0.AA329715.byte_1);
						gStruct3 = e5964FB0_0.f7020D24_0.method_6(2);
						ushort ushort_ = Class607.B630A78B.object_0[504](GClass111.C3B9331C(">H", gStruct3.byte_0)[0]);
						e39CAE0B_0?.Invoke("Checksum:");
						e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[720]("0000", Class607.B630A78B.object_0[102](ref ushort_, "x")).smethod_12(4)), EF1F389C.Success);
						e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { (byte)90 }));
						e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 2147483649u }));
						gStruct3 = e5964FB0_0.f7020D24_0.method_6(4);
						int int_2 = Class607.B630A78B.object_0[84](GClass111.C3B9331C(">I", gStruct3.byte_0)[0]);
						e39CAE0B_0?.Invoke("M_EXT_RAM_RET :");
						e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[626](ref int_2, "x")), EF1F389C.Success);
						if (int_2 != 0)
						{
							throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[720]("Preloader error:", EA177B99(int_2)));
						}
						byte ADBCA = e5964FB0_0.f7020D24_0.method_6(1).byte_0[0];
						e39CAE0B_0?.Invoke("M_EXT_RAM_TYPE :");
						e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[1273](ref ADBCA, "x")), EF1F389C.Success);
						byte ADBCA2 = e5964FB0_0.f7020D24_0.method_6(1).byte_0[0];
						e39CAE0B_0?.Invoke("M_EXT_RAM_CHIP_SELECT :");
						e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[1273](ref ADBCA2, "x")), EF1F389C.Success);
						gStruct3 = e5964FB0_0.f7020D24_0.method_6(8);
						ulong ulong_ = Class607.B630A78B.object_0[750](GClass111.C3B9331C(">Q", gStruct3.byte_0)[0]);
						e39CAE0B_0?.Invoke("M_EXT_RAM_SIZE : ");
						e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[1220](ref ulong_, "x")), EF1F389C.Success);
						if (gclass51_0.AA329715.int_0 == 13)
						{
							e5964FB0_0.f7020D24_0.method_6(4);
							e5964FB0_0.f7020D24_0.method_6(4);
							e5964FB0_0.f7020D24_0.method_6(4);
							e5964FB0_0.f7020D24_0.method_6(4);
							e5964FB0_0.f7020D24_0.method_6(4);
						}
					}
				}
			}
			return true;
		}
	}

	public bool method_9(GClass51.A4B06010 a4B06010_0 = GClass51.A4B06010.NORMAL)
	{
		ACB2BB3E(Class607.B630A78B.object_0[84](a4B06010_0));
		e5964FB0_0.f7020D24_0.DC1596BA();
		return true;
	}

	public bool ACB2BB3E(int int_0)
	{
		e5964FB0_0.f7020D24_0.method_1(new byte[1] { 217 });
		if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] == 90)
		{
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { int_0 }));
			if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] == 90)
			{
				return true;
			}
		}
		return false;
	}

	public bool method_10(GClass70 gclass70_0, byte[] byte_0, int int_0, int int_1 = 4096)
	{
		uint cD07EF = gclass70_0.E1105331.list_0[int_0].CD07EF94;
		uint uint_ = gclass70_0.E1105331.list_0[int_0].uint_1;
		e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { uint_ }));
		e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { cD07EF }));
		e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { int_1 }));
		F7020D24.GStruct60 gStruct = e5964FB0_0.f7020D24_0.method_6(1);
		if (gStruct.byte_0[0] == 90)
		{
			using (IEnumerator<int> enumerator = GClass112.smethod_30(0, Class607.B630A78B.object_0[1262](cD07EF), int_1).GetEnumerator())
			{
				while (Class607.B630A78B.object_0[212](enumerator))
				{
					int current = enumerator.Current;
					e5964FB0_0.f7020D24_0.method_1(GClass112.smethod_14(byte_0, current, int_1));
					gStruct = e5964FB0_0.f7020D24_0.method_6(1);
					if (gStruct.byte_0[0] != 90)
					{
						e39CAE0B_0?.Invoke("Error on sending brom stage :");
						e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[1058](gStruct.byte_0)), EF1F389C.Error);
						break;
					}
				}
			}
			GClass112.smethod_28(500);
			e5964FB0_0.f7020D24_0.method_1(new byte[1] { 90 });
			if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] == 90)
			{
				return true;
			}
		}
		else
		{
			e39CAE0B_0?.Invoke("Error on sending brom stage :");
			e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[1058](gStruct.byte_0)), EF1F389C.Error);
		}
		return false;
	}

	public bool FAB3EF3A()
	{
		F7020D24.GStruct60 gStruct = e5964FB0_0.f7020D24_0.method_6(28);
		if (gStruct.byte_0 == null)
		{
			return false;
		}
		DA968917 = new GClass46(gStruct.byte_0);
		gStruct = e5964FB0_0.f7020D24_0.method_6(17);
		gclass47_0 = new GClass47(gStruct.byte_0);
		uint num = gclass47_0.BA8AE29F;
		List<byte> list = new List<byte>();
		if (num == 0)
		{
			gclass48_0 = new GClass48(gStruct.byte_0);
			num = gclass48_0.D13F483A;
			list.AddRange(GClass112.smethod_14(gStruct.byte_0, gStruct.byte_0.Length - 4, 4));
			list.AddRange(e5964FB0_0.f7020D24_0.method_6(Class607.B630A78B.object_0[1262](num) * 2 - 4).byte_0);
		}
		else
		{
			list.AddRange(e5964FB0_0.f7020D24_0.method_6(Class607.B630A78B.object_0[1262](num) * 2).byte_0);
		}
		object[] source = GClass111.C3B9331C(Class607.B630A78B.object_0[720](">", Class607.B630A78B.object_0[275]('H', Class607.B630A78B.object_0[1262](num))), list.ToArray());
		gStruct = e5964FB0_0.f7020D24_0.method_6(9);
		gclass47_0.A2BCD3AF = new GClass49(gStruct.byte_0);
		if (gclass47_0.BA8AE29F == 0)
		{
			gclass48_0.List_0 = source.Cast<ushort>().ToList();
			gclass48_0.D216FC13 = new GClass49(gStruct.byte_0);
		}
		else
		{
			gclass47_0.C8906790 = source.Cast<ushort>().ToList();
			gclass47_0.A2BCD3AF = new GClass49(gStruct.byte_0);
		}
		d0239F0F_0 = new D0239F0F(e5964FB0_0.f7020D24_0.method_6(92).byte_0);
		b891A187_0 = new B891A187(e5964FB0_0.f7020D24_0.method_6(28).byte_0);
		BC3A5BBC = new AA26AA0A(e5964FB0_0.f7020D24_0.method_6(38).byte_0);
		F138DC03 = new GClass50(e5964FB0_0.f7020D24_0.method_6(10).byte_0);
		if (F138DC03.Byte_0[0] == 90)
		{
			return true;
		}
		if (F138DC03.A18610A9 == 90)
		{
			gStruct = e5964FB0_0.f7020D24_0.method_6(1);
			return true;
		}
		return false;
	}

	public bool DAB38510(byte byte_0 = 8)
	{
		e5964FB0_0.f7020D24_0.method_1(new byte[1] { 96 });
		if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] == 90)
		{
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2("B", new object[1] { byte_0 }));
			if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] == 90)
			{
				return true;
			}
		}
		return false;
	}

	public bool method_11()
	{
		if (e5964FB0_0.f7020D24_0.method_1(new byte[1] { 112 }) && e5964FB0_0.f7020D24_0.method_1(new byte[1] { 1 }))
		{
			F7020D24.GStruct60 gStruct = e5964FB0_0.f7020D24_0.method_6(1);
			if (gStruct.byte_0.Length != 0 && gStruct.byte_0[0] == 90)
			{
				return true;
			}
		}
		return false;
	}

	public byte FEA1F03E()
	{
		if (e5964FB0_0.f7020D24_0.method_1(new byte[1] { 114 }) && e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] == 90)
		{
			F7020D24.GStruct60 gStruct = e5964FB0_0.f7020D24_0.method_6(1);
			if (gStruct.byte_0.Length != 0)
			{
				return gStruct.byte_0[0];
			}
		}
		return 0;
	}

	public byte[] method_12(byte[] B10B581F)
	{
		int num = GClass67.smethod_2(B10B581F, GClass112.FF06B0AD("08B54FF45042"));
		if (num != -1)
		{
			byte[] array_ = GClass112.FF06B0AD("08B5002008BD");
			Class607.B630A78B.object_0[242](array_, 0, B10B581F, num, 6);
		}
		int num2 = GClass67.smethod_2(B10B581F, GClass112.FF06B0AD("30B585B003AB"));
		if (num2 != -1)
		{
			byte[] array = GClass112.FF06B0AD("70B54AF2C864C8F20404636A9847636A0546984706464FF00001286805F10405A36A9847A6F10106002EF6D15A202369BDE870401847");
			Class607.B630A78B.object_0[242](array, 0, B10B581F, num2, array.Length);
		}
		return B10B581F;
	}

	public bool method_13()
	{
		gclass51_0.bool_1 = false;
		e39CAE0B_0?.Invoke("Writing download agent :");
		using (FileStream fileStream = Class607.B630A78B.object_0[748](gclass51_0.AA329715.B0357487, FileMode.Open))
		{
			try
			{
				uint uint_ = gclass51_0.AA329715.E1105331.list_0[1].uint_0;
				uint cD07EF = gclass51_0.AA329715.E1105331.list_0[1].CD07EF94;
				uint uint_2 = gclass51_0.AA329715.E1105331.list_0[1].uint_1;
				_ = gclass51_0.AA329715.E1105331.list_0[1].uint_1;
				uint uint_3 = gclass51_0.AA329715.E1105331.list_0[1].uint_3;
				Class607.B630A78B.object_0[543](fileStream, uint_, SeekOrigin.Begin);
				byte[] array = new byte[cD07EF];
				Class607.B630A78B.object_0[53](fileStream, array, 0, array.Length);
				uint uint_4 = gclass51_0.AA329715.E1105331.list_0[2].uint_0;
				uint uint_5 = gclass51_0.AA329715.E1105331.list_0[2].uint_3;
				uint cD07EF2 = gclass51_0.AA329715.E1105331.list_0[2].CD07EF94;
				Class607.B630A78B.object_0[543](fileStream, uint_4, SeekOrigin.Begin);
				byte[] array2 = new byte[cD07EF2];
				Class607.B630A78B.object_0[53](fileStream, array2, 0, (int)cD07EF2);
				if (e5964FB0_0.genum23_0 == E5964FB0.GEnum23.const_0 || !gclass51_0.e5964FB0_0.D3A2FF25.bool_0)
				{
					var (num4, int_, num5) = gclass51_0.method_12(array, array2, Class607.B630A78B.object_0[1262](uint_3), Class607.B630A78B.object_0[1262](uint_5), gclass51_0.AA329715.A08FA093);
					if (num4 != -1)
					{
						array2 = method_12(array2);
						array = GClass67.A826F023(array, array2, num4, int_, num5);
						gclass51_0.bool_1 = true;
						List<byte> list = new List<byte>();
						list.AddRange(GClass112.smethod_14(array2, 0, num5));
						list.AddRange(GClass112.smethod_14(array2, num5, Class607.B630A78B.object_0[1262](uint_5)));
						gclass51_0.AA329715.byte_0 = list.ToArray();
					}
					else
					{
						gclass51_0.AA329715.byte_0 = array2;
					}
				}
				else
				{
					gclass51_0.AA329715.byte_0 = array2;
				}
				if (e5964FB0_0.A6028889(uint_2, (int)cD07EF, (int)uint_3, array))
				{
					e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
					e39CAE0B_0?.Invoke("Jumping Download Agent :");
					if (e5964FB0_0.ADB02D98(uint_2))
					{
						e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
						if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] != 192)
						{
							return false;
						}
						e39CAE0B_0?.Invoke("Reading nand info :");
						F7020D24.GStruct60 gStruct = e5964FB0_0.f7020D24_0.method_6(4);
						Class607.B630A78B.object_0[40](GClass111.C3B9331C(">I", gStruct.byte_0)[0]);
						F7020D24.GStruct60 gStruct2 = e5964FB0_0.f7020D24_0.method_6(2);
						int int_2 = Class607.B630A78B.object_0[84](GClass111.C3B9331C(">H", gStruct2.byte_0)[0]);
						List<ushort> list2 = new List<ushort>();
						using (IEnumerator<int> enumerator = GClass112.smethod_30(0, int_2).GetEnumerator())
						{
							while (Class607.B630A78B.object_0[212](enumerator))
							{
								_ = enumerator.Current;
								F7020D24.GStruct60 gStruct3 = e5964FB0_0.f7020D24_0.method_6(2);
								ushort item = Class607.B630A78B.object_0[504](GClass111.C3B9331C(">H", gStruct3.byte_0)[0]);
								list2.Add(item);
							}
						}
						e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
						e39CAE0B_0?.Invoke("Reading Emmc info : ");
						GClass111.C3B9331C(">I", e5964FB0_0.f7020D24_0.method_6(4).byte_0);
						List<uint> list3 = new List<uint>();
						using (IEnumerator<int> enumerator2 = GClass112.smethod_30(0, 4).GetEnumerator())
						{
							while (Class607.B630A78B.object_0[212](enumerator2))
							{
								_ = enumerator2.Current;
								F7020D24.GStruct60 gStruct4 = e5964FB0_0.f7020D24_0.method_6(4);
								uint item2 = Class607.B630A78B.object_0[40](GClass111.C3B9331C(">I", gStruct4.byte_0)[0]);
								list3.Add(item2);
							}
						}
						if (list2.Count > 0 && list2[0] != 0)
						{
							gclass51_0.AA329715.string_1 = "nand";
						}
						else if (list3.Count > 0 && list3[0] != 0)
						{
							gclass51_0.AA329715.string_1 = "emmc";
						}
						else
						{
							gclass51_0.AA329715.string_1 = "nor";
						}
						e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
						e5964FB0_0.f7020D24_0.method_1(new byte[1] { 90 });
						List<byte> list4 = new List<byte>();
						F7020D24.GStruct60 gStruct5 = e5964FB0_0.f7020D24_0.method_6(1);
						F7020D24.GStruct60 gStruct6 = e5964FB0_0.f7020D24_0.method_6(1);
						F7020D24.GStruct60 gStruct7 = e5964FB0_0.f7020D24_0.method_6(1);
						list4.AddRange(gStruct5.byte_0);
						list4.AddRange(gStruct6.byte_0);
						list4.AddRange(gStruct7.byte_0);
						e39CAE0B_0?.Invoke("ACK :");
						e39CAE0B_0?.Invoke(GClass112.smethod_27(list4.ToArray()), EF1F389C.Success);
						e39CAE0B_0?.Invoke("Setting stage 2 config : ");
						if (!method_8())
						{
							e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
							return false;
						}
						e39CAE0B_0?.Invoke("Uploading stage 2: ");
						if (method_10(gclass51_0.AA329715, array2, 2))
						{
							e39CAE0B_0?.Invoke("Ok", EF1F389C.Success);
							if (FAB3EF3A())
							{
								if (Class607.B630A78B.object_0[787](gclass51_0.AA329715.string_1, "nand"))
								{
									gclass51_0.AA329715.ulong_0 = gclass47_0.ulong_0;
								}
								else if (Class607.B630A78B.object_0[787](gclass51_0.AA329715.string_1, "emmc"))
								{
									gclass51_0.AA329715.ulong_0 = d0239F0F_0.C19D9CA1;
									if (gclass51_0.AA329715.ulong_0 == 0L)
									{
										gclass51_0.AA329715.ulong_0 = b891A187_0.BF254E16;
									}
								}
								else if (Class607.B630A78B.object_0[787](gclass51_0.AA329715.string_1, "nor"))
								{
									gclass51_0.AA329715.ulong_0 = DA968917.E20F5F3D;
								}
								if (FEA1F03E() == 0)
								{
									e39CAE0B_0?.Invoke("Reconnecting to preloader");
									method_11();
									e5964FB0_0.f7020D24_0.DC1596BA();
									GClass112.smethod_28(1500);
									e39CAE0B_0?.Invoke("Waiting for mediatek port (Timeout 3 minute) :");
									e5964FB0_0.E19342BD = GClass114.smethod_1(180000);
									if (!Class607.B630A78B.object_0[1205](e5964FB0_0.E19342BD.string_0))
									{
										e5964FB0_0.genum23_0 = (((!Class607.B630A78B.object_0[787](e5964FB0_0.E19342BD.string_3, "0E8D") || !Class607.B630A78B.object_0[787](e5964FB0_0.E19342BD.string_2, "0003")) && (!Class607.B630A78B.object_0[787](e5964FB0_0.E19342BD.string_3, "0FCE") || !Class607.B630A78B.object_0[787](e5964FB0_0.E19342BD.string_2, "F200"))) ? E5964FB0.GEnum23.const_1 : E5964FB0.GEnum23.const_0);
										e39CAE0B_0?.Invoke("Connected to preloader", EF1F389C.Success);
										e5964FB0_0.f7020D24_0.method_7(e5964FB0_0.E19342BD);
										FEA1F03E();
										return true;
									}
								}
							}
						}
						else
						{
							e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
						}
						return false;
					}
					e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
				}
				else
				{
					e39CAE0B_0?.Invoke("Failed", EF1F389C.Error);
				}
			}
			catch (Exception ex)
			{
				throw ex;
			}
			finally
			{
				Class607.B630A78B.object_0[587](fileStream);
				Class607.B630A78B.object_0[469](fileStream);
			}
		}
		return false;
	}

	public Tuple<ulong, byte> method_14(ulong ulong_0, string string_0)
	{
		ulong item = 0uL;
		byte b = 0;
		if (string_0 != null && string_0 != null)
		{
			switch (Class607.B630A78B.object_0[343](string_0))
			{
			case 3:
				break;
			case 4:
				goto IL_019a;
			case 5:
				goto IL_0201;
			default:
				goto IL_0295;
			}
			switch (Class607.B630A78B.object_0[1091](string_0, 2))
			{
			case '1':
				break;
			case '2':
				goto IL_00cb;
			case '3':
				goto IL_0110;
			case '4':
				goto IL_0155;
			default:
				goto IL_0295;
			}
			if (Class607.B630A78B.object_0[787](string_0, "gp1"))
			{
				item = Class607.B630A78B.object_0[743](ulong_0, d0239F0F_0.A80C739F[0]);
				b = 4;
				goto IL_02b4;
			}
		}
		goto IL_0295;
		IL_0110:
		if (!Class607.B630A78B.object_0[787](string_0, "gp3"))
		{
			goto IL_0295;
		}
		item = Class607.B630A78B.object_0[743](ulong_0, d0239F0F_0.A80C739F[2]);
		b = 6;
		goto IL_02b4;
		IL_02b4:
		return Tuple.Create(item, b);
		IL_0155:
		if (!Class607.B630A78B.object_0[787](string_0, "gp4"))
		{
			goto IL_0295;
		}
		item = Class607.B630A78B.object_0[743](ulong_0, d0239F0F_0.A80C739F[3]);
		b = 7;
		goto IL_02b4;
		IL_0295:
		item = Class607.B630A78B.object_0[743](ulong_0, d0239F0F_0.C19D9CA1);
		b = 8;
		goto IL_02b4;
		IL_019a:
		char c = Class607.B630A78B.object_0[1091](string_0, 0);
		if (c != 'r')
		{
			if (c == 'u' && Class607.B630A78B.object_0[787](string_0, "user"))
			{
			}
		}
		else if (Class607.B630A78B.object_0[787](string_0, "rpmb"))
		{
			b = 3;
			goto IL_02b4;
		}
		goto IL_0295;
		IL_00cb:
		if (!Class607.B630A78B.object_0[787](string_0, "gp2"))
		{
			goto IL_0295;
		}
		item = Class607.B630A78B.object_0[743](ulong_0, d0239F0F_0.A80C739F[1]);
		b = 5;
		goto IL_02b4;
		IL_0201:
		c = Class607.B630A78B.object_0[1091](string_0, 4);
		if (c != '1')
		{
			if (c != '2' || !Class607.B630A78B.object_0[787](string_0, "boot2"))
			{
				goto IL_0295;
			}
			item = Class607.B630A78B.object_0[743](ulong_0, d0239F0F_0.UInt64_1);
			b = 2;
		}
		else
		{
			if (!Class607.B630A78B.object_0[787](string_0, "boot1"))
			{
				goto IL_0295;
			}
			item = Class607.B630A78B.object_0[743](ulong_0, d0239F0F_0.UInt64_0);
			b = 1;
		}
		goto IL_02b4;
	}

	public bool BD28DEB7(ulong ulong_0, ulong ulong_1, string string_0 = "", long long_0 = 0L, string string_1 = null, byte[] byte_0 = null)
	{
		return (bool)new GClass128().B4154402(new object[7] { this, ulong_0, ulong_1, string_0, long_0, string_1, byte_0 }, 221223);
	}

	public bool method_15(ulong ulong_0, ulong ulong_1, string string_0 = "", long E38A8826 = 0L, string string_1 = null, byte[] EC3C5E83 = null)
	{
		Tuple<ulong, byte> tuple = method_14(ulong_1, string_1);
		ulong item = tuple.Item1;
		byte item2 = tuple.Item2;
		byte b = method_17();
		if (Class607.B630A78B.object_0[695](string_0))
		{
			using FileStream fileStream = Class607.B630A78B.object_0[123](string_0);
			ulong num = 0uL;
			item = Class607.B630A78B.object_0[743](Class607.B630A78B.object_0[243](Class607.B630A78B.object_0[61](fileStream)), item);
			if (item % 512L > 0L)
			{
				num = 512L - item % 512L;
				item += num;
			}
			try
			{
				Class607.B630A78B.object_0[543](fileStream, E38A8826, SeekOrigin.Begin);
				e5964FB0_0.f7020D24_0.B8295B87(98);
				e5964FB0_0.f7020D24_0.B8295B87(b);
				e5964FB0_0.f7020D24_0.B8295B87(item2);
				e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">Q", new object[1] { ulong_0 }));
				e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">Q", new object[1] { item }));
				e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 1048576 }));
				if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] != 90)
				{
					return false;
				}
				E38A8826 = 0L;
				while (Class607.B630A78B.object_0[243](E38A8826) < item)
				{
					e5964FB0_0.f7020D24_0.method_1(new byte[1] { 90 });
					int num2 = Class607.B630A78B.object_0[78](1048576, Class607.B630A78B.object_0[704](item - Class607.B630A78B.object_0[243](E38A8826)));
					byte[] array = new byte[num2];
					int num3 = Class607.B630A78B.object_0[53](fileStream, array, 0, num2);
					if (num3 < num2)
					{
						List<byte> list = new List<byte>();
						list.AddRange(array);
						list.AddRange(new byte[num]);
						array = list.ToArray();
					}
					e5964FB0_0.f7020D24_0.method_1(array);
					int num4 = array.Sum((byte byte_0) => (int)new GClass128().E6A33692(new object[2]
					{
						AB975AAB._003C_003E9,
						byte_0
					}, 22531702)) & 0xFFFF;
					e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">H", new object[1] { num4 }));
					if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] == 105)
					{
						gdelegate25_0?.Invoke(item, Class607.B630A78B.object_0[243](E38A8826));
						E38A8826 += num2;
						continue;
					}
					return false;
				}
			}
			finally
			{
				Class607.B630A78B.object_0[587](fileStream);
				Class607.B630A78B.object_0[469](fileStream);
			}
		}
		else if (EC3C5E83 != null)
		{
			e5964FB0_0.f7020D24_0.method_1(new byte[1] { 98 });
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">B", new object[1] { b }));
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">B", new object[1] { item2 }));
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">Q", new object[1] { ulong_0 }));
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">Q", new object[1] { item }));
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 1048576 }));
			if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] != 90)
			{
				return false;
			}
			E38A8826 = 0L;
			while (Class607.B630A78B.object_0[243](E38A8826) < item)
			{
				e5964FB0_0.f7020D24_0.method_1(new byte[1] { 90 });
				int num5 = Class607.B630A78B.object_0[78](1048576, Class607.B630A78B.object_0[704](item - Class607.B630A78B.object_0[243](E38A8826)));
				byte[] array2 = GClass112.smethod_14(EC3C5E83, Class607.B630A78B.object_0[1130](E38A8826), num5);
				e5964FB0_0.f7020D24_0.method_1(array2);
				int num6 = array2.Sum((byte byte_0) => byte_0) & 0xFFFF;
				e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">H", new object[1] { num6 }));
				if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] == 105)
				{
					gdelegate25_0?.Invoke(item, Class607.B630A78B.object_0[243](E38A8826));
					E38A8826 += num5;
					continue;
				}
				return false;
			}
		}
		return true;
	}

	public bool method_16(ulong ulong_0, ulong ulong_1, string string_0 = "", bool D59CB318 = true)
	{
		return (bool)new GClass128().EF8D5E3B(new object[5] { this, ulong_0, ulong_1, string_0, D59CB318 }, 21524886);
	}

	public byte method_17()
	{
		byte result = 1;
		string string_ = gclass51_0.AA329715.string_1;
		string string_2 = string_;
		if (!Class607.B630A78B.object_0[787](string_2, "nor"))
		{
			if (!Class607.B630A78B.object_0[787](string_2, "nand"))
			{
				if (!Class607.B630A78B.object_0[787](string_2, "ufs"))
				{
					if (Class607.B630A78B.object_0[787](string_2, "sdc"))
					{
						result = 2;
					}
				}
				else
				{
					result = 48;
				}
			}
			else
			{
				result = 16;
			}
		}
		else
		{
			result = 32;
		}
		return result;
	}

	public GClass51.GStruct57 B398B119(ulong B2B0033D, ulong ulong_0, string EA32031C, string string_0 = "")
	{
		GClass51.GStruct57 result = new GClass51.GStruct57
		{
			BD147F02 = false
		};
		Tuple<ulong, byte> tuple = method_14(ulong_0, string_0);
		FEA1F03E();
		ulong item = tuple.Item1;
		byte item2 = tuple.Item2;
		uint num = 0u;
		if (Class607.B630A78B.object_0[787](gclass51_0.AA329715.string_1, "emmc"))
		{
			DAB38510(item2);
			num = 1048576u;
			e5964FB0_0.f7020D24_0.method_1(new byte[1] { 214 });
			e5964FB0_0.f7020D24_0.method_1(new byte[1] { 12 });
			e5964FB0_0.f7020D24_0.method_1(new byte[1] { 2 });
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">Q", new object[1] { B2B0033D }));
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">Q", new object[1] { item }));
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 1048576u }));
			if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] != 90)
			{
				e5964FB0_0.f7020D24_0.method_1(new byte[1] { 165 });
				GClass111.C3B9331C("<I", e5964FB0_0.f7020D24_0.method_6(4).byte_0);
				return result;
			}
			gclass51_0.AA329715.ulong_1 = gclass51_0.AA329715.ulong_0;
		}
		else if (Class607.B630A78B.object_0[787](gclass51_0.AA329715.string_1, "nand"))
		{
			e5964FB0_0.f7020D24_0.method_1(new byte[1] { 223 });
			e5964FB0_0.f7020D24_0.method_1(new byte[1] { 12 });
			e5964FB0_0.f7020D24_0.method_1(new byte[1]);
			e5964FB0_0.f7020D24_0.method_1(new byte[1] { 1 });
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { B2B0033D }));
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { item }));
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 0 }));
			if (e5964FB0_0.f7020D24_0.method_6(1).byte_0[0] != 90)
			{
				return result;
			}
			F7020D24.GStruct60 gStruct = e5964FB0_0.f7020D24_0.method_6(4);
			gclass51_0.AA329715.uint_0 = Class607.B630A78B.object_0[40](GClass111.C3B9331C(">I", gStruct.byte_0)[0]);
			gStruct = e5964FB0_0.f7020D24_0.method_6(4);
			gclass51_0.AA329715.DB09DC88 = Class607.B630A78B.object_0[40](GClass111.C3B9331C(">I", gStruct.byte_0)[0]);
			gStruct = e5964FB0_0.f7020D24_0.method_6(4);
			num = Class607.B630A78B.object_0[40](GClass111.C3B9331C(">I", gStruct.byte_0)[0]);
			e5964FB0_0.f7020D24_0.method_1(GClass111.smethod_2(">I", new object[1] { 1 }));
			e5964FB0_0.f7020D24_0.method_6(4);
			gclass51_0.AA329715.ulong_1 = gclass51_0.AA329715.ulong_0 / gclass51_0.AA329715.uint_0 * (gclass51_0.AA329715.uint_0 + gclass51_0.AA329715.DB09DC88);
		}
		if (!Class607.B630A78B.object_0[1205](EA32031C))
		{
			GClass112.E209C304(Class607.B630A78B.object_0[321](EA32031C));
			using FileStream fileStream = Class607.B630A78B.object_0[792](EA32031C, FileMode.OpenOrCreate, FileAccess.ReadWrite);
			ulong num2 = item;
			while (num2 > 0L)
			{
				ulong num3 = num2;
				if (num2 > num)
				{
					num3 = num;
				}
				F7020D24.GStruct60 gStruct2 = e5964FB0_0.f7020D24_0.method_6(Class607.B630A78B.object_0[704](num3));
				Class607.B630A78B.object_0[132](fileStream, gStruct2.byte_0, 0, gStruct2.byte_0.Length);
				num2 -= num3;
				F7020D24.GStruct60 gStruct3 = e5964FB0_0.f7020D24_0.method_6(1);
				F7020D24.GStruct60 gStruct4 = e5964FB0_0.f7020D24_0.method_6(1);
				List<byte> list = gStruct3.byte_0.ToList();
				list.AddRange(gStruct4.byte_0);
				ushort ushort_ = Class607.B630A78B.object_0[504](GClass111.C3B9331C(">H", list.ToArray())[0]);
				e39CAE0B_0?.Invoke("Checksum :");
				e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[720]("0000", Class607.B630A78B.object_0[102](ref ushort_, "x")).smethod_12(4)), EF1F389C.Success);
				e5964FB0_0.f7020D24_0.method_1(new byte[1] { 90 });
				gdelegate25_0?.Invoke(item, Class607.B630A78B.object_0[405](item - num2));
			}
			result.BD147F02 = true;
			Class607.B630A78B.object_0[587](fileStream);
			Class607.B630A78B.object_0[469](fileStream);
			return result;
		}
		List<byte> list2 = new List<byte>();
		ulong num4 = item;
		while (num4 > 0L)
		{
			ulong num5 = num4;
			if (num4 > num)
			{
				num5 = num;
			}
			list2.AddRange(e5964FB0_0.f7020D24_0.method_6(Class607.B630A78B.object_0[704](num5)).byte_0);
			num4 -= num5;
			F7020D24.GStruct60 gStruct5 = e5964FB0_0.f7020D24_0.method_6(2);
			ushort ushort_2 = Class607.B630A78B.object_0[504](GClass111.C3B9331C(">H", gStruct5.byte_0)[0]);
			e39CAE0B_0?.Invoke("Checksum :");
			e39CAE0B_0?.Invoke(Class607.B630A78B.object_0[720]("0x", Class607.B630A78B.object_0[720]("0000", Class607.B630A78B.object_0[102](ref ushort_2, "x")).smethod_12(4)), EF1F389C.Success);
			e5964FB0_0.f7020D24_0.method_1(new byte[1] { 90 });
			gdelegate25_0?.Invoke(item, Class607.B630A78B.object_0[405](item - num4));
		}
		result.byte_0 = list2.ToArray();
		result.BD147F02 = true;
		return result;
	}
}
