using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;

namespace MotoKingPro
{
    /// <summary>
    /// Main application form for MotoKingPro - Mobile device repair and flashing tool
    /// Supports Qualcomm, MediaTek, Motorola devices and ADB operations
    /// </summary>
    public partial class MainForm : Form
    {
        #region Helper Classes for Operation Management

        /// <summary>
        /// Helper class for Qualcomm operations UI state management
        /// </summary>
        [CompilerGenerated]
        private sealed class QualcommOperationHelper
        {
            public MainForm ParentForm { get; set; }
            public bool IsOperationRunning { get; set; }

            internal void UpdateUIState()
            {
                // Enable/disable UI controls based on operation state
                ParentForm.tabSelector.Enabled = !IsOperationRunning;
                ParentForm.historyButton.Enabled = !IsOperationRunning;
                ParentForm.accountButton.Enabled = !IsOperationRunning;
            }

            internal void FocusQualcommTab()
            {
                // Focus on Qualcomm tab's text output
                ParentForm.qualcommPage.FocusTextOutput();
            }
        }

        /// <summary>
        /// Helper class for ADB/Services operations UI state management
        /// </summary>
        [CompilerGenerated]
        private sealed class ServicesOperationHelper
        {
            public MainForm ParentForm { get; set; }
            public bool IsOperationRunning { get; set; }

            internal void UpdateUIState()
            {
                // Enable/disable UI controls based on operation state
                ParentForm.tabSelector.Enabled = !IsOperationRunning;
                ParentForm.historyButton.Enabled = !IsOperationRunning;
                ParentForm.accountButton.Enabled = !IsOperationRunning;
            }

            internal void FocusServicesTab()
            {
                // Focus on Services tab's text output
                ParentForm.servicesPage.FocusTextOutput();
            }
        }

        /// <summary>
        /// Helper class for Motorola operations UI state management
        /// </summary>
        [CompilerGenerated]
        private sealed class MotorolaOperationHelper
        {
            public MainForm ParentForm { get; set; }
            public bool IsOperationRunning { get; set; }

            internal void UpdateUIState()
            {
                // Enable/disable UI controls based on operation state
                ParentForm.tabSelector.Enabled = !IsOperationRunning;
                ParentForm.historyButton.Enabled = !IsOperationRunning;
                ParentForm.accountButton.Enabled = !IsOperationRunning;
            }

            internal void FocusMotorolaTab()
            {
                // Focus on Motorola tab's text output
                ParentForm.motorolaPage.FocusTextOutput();
            }
        }

        /// <summary>
        /// Helper class for MediaTek operations UI state management
        /// </summary>
        [CompilerGenerated]
        private sealed class MediaTekOperationHelper
        {
            public MainForm ParentForm { get; set; }
            public bool IsOperationRunning { get; set; }

            internal void UpdateUIState()
            {
                // Enable/disable UI controls based on operation state
                ParentForm.tabSelector.Enabled = !IsOperationRunning;
                ParentForm.historyButton.Enabled = !IsOperationRunning;
                ParentForm.accountButton.Enabled = !IsOperationRunning;
            }

            internal void FocusMediaTekTab()
            {
                // Focus on MediaTek tab's text output
                ParentForm.mediaTekPage.FocusTextOutput();
            }
        }

        #endregion

        #region Fields

        // Mouse drag support for borderless window
        private Point lastMousePosition = new Point();
        
        // Custom control for device status display
        private DeviceStatusControl deviceStatusControl = new DeviceStatusControl();

        // Operation tracking objects for each platform
        private OperationTracker servicesOperation;      // ADB/Services operations
        private OperationTracker qualcommOperation;      // Qualcomm operations  
        private OperationTracker mediaTekOperation;      // MediaTek operations
        private OperationTracker motorolaOperation;      // Motorola operations

        private bool isClosingForced = false;
        private IContainer components = null;

        #endregion

        #region UI Controls

        // Main UI Controls
        private Panel navigationPanel;
        private Button maximizeButton;
        private Button minimizeButton;
        private Button closeButton;
        private CustomLabel titleLabel;
        private TabSelector tabSelector;
        private CustomTabControl mainTabControl;
        
        // Tab Pages
        private TabPage motorolaTabPage;
        private TabPage qualcommTabPage;
        private TabPage servicesTabPage;
        private TabPage mediaTekTabPage;
        
        // Platform-specific pages
        private MediaTekPage mediaTekPage;
        private QualcommPage qualcommPage;
        private MotorolaPage motorolaPage;
        private Button historyButton;
        private ServicesPage servicesPage;
        private Panel mainContentPanel;
        private Button accountButton;
        private Label versionLabel;
        private Button signOutButton;

        #endregion

        #region Constructor

        public MainForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
            SetupEventHandlers();
            LoadApplicationSettings();
        }

        #endregion

        #region Initialization Methods

        private void InitializeCustomComponents()
        {
            // Initialize theme and styling
            new ThemeManager().ApplyTheme(this);
            
            // Setup platform-specific pages
            qualcommPage.SetParentForm(this);
            motorolaPage.SetParentForm(this);
            servicesPage.SetParentForm(this);
            
            // Setup event handlers for operations
            qualcommPage.OperationStateChanged += OnQualcommOperationStateChanged;
            mediaTekPage.OperationStateChanged += OnMediaTekOperationStateChanged;
            motorolaPage.OperationStateChanged += OnMotorolaOperationStateChanged;
            servicesPage.OperationStateChanged += OnServicesOperationStateChanged;
            
            // Add device status control to main panel
            mainContentPanel.Controls.Add(deviceStatusControl);
            deviceStatusControl.Dock = DockStyle.Fill;
            deviceStatusControl.Visible = false;
            
            // Set version label
            versionLabel.Text = ApplicationInfo.GetVersionString();
        }

        private void SetupEventHandlers()
        {
            // Window drag events
            navigationPanel.MouseDown += OnNavigationPanelMouseDown;
            navigationPanel.MouseMove += OnNavigationPanelMouseMove;
            navigationPanel.MouseUp += OnNavigationPanelMouseUp;
            titleLabel.MouseDown += OnNavigationPanelMouseDown;
            titleLabel.MouseMove += OnNavigationPanelMouseMove;
            titleLabel.MouseUp += OnNavigationPanelMouseUp;
        }

        private void LoadApplicationSettings()
        {
            // Load auto-loader files
            LoadAutoLoaderFiles();
            
            // Initialize MediaTek page
            mediaTekPage.InitializeAutoLoaders();
        }

        #endregion

        #region Operation Event Handlers

        private void OnServicesOperationStateChanged(bool isRunning, string operationName)
        {
            var helper = new ServicesOperationHelper 
            { 
                ParentForm = this, 
                IsOperationRunning = isRunning 
            };
            
            servicesPage.SetOperationState(helper.IsOperationRunning);
            this.InvokeIfRequired(() => helper.UpdateUIState());
            
            if (helper.IsOperationRunning)
            {
                DateTime startTime = DateTime.Now;
                servicesOperation = new OperationTracker
                {
                    StartedAt = startTime,
                    OperationName = operationName,
                    Tab = "Services"
                };
                servicesPage.ClearLog();
                this.InvokeIfRequired(() => helper.FocusServicesTab());
            }
            else
            {
                DateTime endTime = DateTime.Now;
                servicesPage.LogMessage("Operation: ");
                servicesPage.LogMessage(operationName, LogLevel.Warning, false, true);
                servicesPage.LogMessage("Elapsed time: ");
                TimeSpan elapsed = endTime - servicesOperation.StartedAt;
                servicesPage.LogMessage(elapsed.ToString(@"hh\:mm\:ss"), LogLevel.Warning, false, true);
                servicesOperation.EndedAt = endTime;
                servicesOperation.Items = servicesPage.GetLogItems();
                OperationHistory.Instance.Operations.Add(servicesOperation);
                OperationHistory.SaveToFile();
            }
        }

        #endregion

        #region Window Management

        private void OnNavigationPanelMouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                lastMousePosition = new Point(e.X, e.Y);
            }
        }

        private void OnNavigationPanelMouseMove(object sender, MouseEventArgs e)
        {
            if (lastMousePosition != Point.Empty)
            {
                this.Location = new Point(
                    this.Left + e.X - lastMousePosition.X,
                    this.Top + e.Y - lastMousePosition.Y);
            }
        }

        private void OnNavigationPanelMouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                lastMousePosition = Point.Empty;
            }
        }

        #endregion

        #region Helper Methods

        private void LoadAutoLoaderFiles()
        {
            string autoLoaderPath = Path.Combine(ApplicationSettings.ApplicationPath, @"bin\loader\auto");
            string[] files = Directory.GetFiles(autoLoaderPath, "*", SearchOption.AllDirectories);
            
            foreach (string file in files)
            {
                string fileName = Path.GetFileName(file);
                ApplicationSettings.AutoLoaderFiles[fileName] = file;
            }
        }

        #endregion
    }
}
