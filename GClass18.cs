using System.Collections.Generic;
using System.Linq;

public class GClass18
{
	public uint C682D094;

	public int int_0;

	public GClass19 gclass19_0;

	public GDelegate1 C0051235;

	public GDelegate2 gdelegate2_0;

	public int BA38C2AE = 0;

	public int int_1 = 1;

	public int int_2 = 2;

	public int A0316329 = 3;

	public int BF36910E = 4;

	public int int_3 = 5;

	public int ABB8FE83 = 8;

	public int int_4 = 9;

	public int B5870F86 = 10;

	public int int_5 = 11;

	public int D9805333 = 12;

	public int F609B9BA = 13;

	public int int_6 = 14;

	public int C92DDC3A = 15;

	public int int_7 = 30;

	public int CA061B0C = 31;

	public int int_8 = 32;

	public int int_9 = 33;

	public int CE14C0B9 = 34;

	public int int_10 = 35;

	public int A3120C2A = 36;

	public int int_11 = 37;

	public int B5ACF934 = 38;

	public int int_12 = 39;

	public int int_13 = 40;

	public int A71DE22F = 54;

	public int int_14 = 116;

	public int D9891C06 = 117;

	public int int_15 = 118;

	public int int_16 = 119;

	public int DD350433 = 120;

	public int int_17 = 121;

	public int CC1DFAB0 = 122;

	public int int_18 = 123;

	public int int_19 = 124;

	public int int_20 = 125;

	public int CE91248B = 126;

	public int EBB5BC21 = 40;

	public int D7184BB9 = 41;

	public int int_21 = 42;

	public int FF129585 = 43;

	public int int_22 = 44;

	public int int_23 = 45;

	public int int_24 = 46;

	public int A21AA0A9 = 48;

	public int D48FED10 = 49;

	public int int_25 = 50;

	public int int_26 = 51;

	public int E6A74BB7 = 52;

	public int EFA58C36 = 53;

	public int int_27 = 55;

	public int int_28 = 80;

	public int int_29 = 81;

	public int int_30 = 82;

	public int int_31 = 83;

	public int B69E0583 = 84;

	public int int_32 = 85;

	public int C413281E = 88;

	public int int_33 = 89;

	public int CD939D28 = 90;

	public int int_34 = 91;

	public int int_35 = 92;

	public int A18F3E3B = 110;

	public int int_36 = 111;

	public int E78E0502 = 16;

	public int int_37 = 17;

	public int int_38 = 64;

	public int int_39 = 65;

	public int int_40 = 66;

	public int CE00A53C = 67;

	public int F2AEC8B2 = 94;

	public int A88FC0B6 = 107;

	public int int_41 = 113;

	public int D9BE8333 = 129;

	public int B12EF79C = 140;

	public int int_42 = 135;

	public int int_43 = 136;

	public GClass18(GClass11 gclass11_0)
	{
		Class607.B630A78B.object_0[571](this);
		C682D094 = gclass11_0.uint_1;
		int_0 = gclass11_0.int_0;
		gclass19_0 = new GClass19(gclass11_0);
		C0051235 = gclass11_0.gdelegate1_0;
		gdelegate2_0 = gclass11_0.F2B1FB97;
	}

	public byte[] method_0(uint D0B7101B, bool bool_0 = false, uint uint_0 = 18u, uint uint_1 = 26u)
	{
		method_1(bool_0, D0B7101B, 0u, 16u, uint_0, uint_1);
		List<uint> list = C0051235(C682D094 + gclass19_0.uint_10 + 104, 4);
		List<byte> list2 = new List<byte>();
		foreach (uint item in list)
		{
			list2.AddRange(GClass111.smethod_2("<I", new object[1] { item }));
		}
		return list2.ToArray();
	}

	public bool F68A5C27(uint BD08C397, byte[] D2B14FA9, byte[] A006703C = null, bool bool_0 = false)
	{
		uint num = 18u;
		int num2 = 22;
		uint num3 = 26u;
		if (A006703C == null)
		{
			A006703C = GClass112.FF06B0AD("4dd12bdf0ec7d26c482490b3482a1b1f");
		}
		if (D2B14FA9.Length != 16)
		{
			throw Class607.B630A78B.object_0[778]("data must be 16 bytes");
		}
		byte[] aA23FA = A006703C;
		List<uint> list = new List<uint>();
		using (IEnumerator<int> enumerator = GClass112.smethod_30(0, 4).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				uint num4 = Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(D2B14FA9, current * 4, 4))[0]);
				uint num5 = Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(aA23FA, current * 4, 4))[0]);
				list.Add(num4 ^ num5);
			}
		}
		gdelegate2_0(Class607.B630A78B.object_0[1200](C682D094 + gclass19_0.uint_10 + num * 4), new List<uint> { 0u, 0u, 0u, 0u });
		gdelegate2_0(Class607.B630A78B.object_0[21](C682D094 + gclass19_0.uint_10 + num2 * 4), new List<uint> { 0u, 0u, 0u, 0u });
		gdelegate2_0(Class607.B630A78B.object_0[1200](C682D094 + gclass19_0.uint_10 + num3 * 4), new List<uint> { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u });
		gdelegate2_0(Class607.B630A78B.object_0[1200](C682D094 + gclass19_0.uint_10 + num3 * 4), list);
		uint uint_ = 0u;
		if (int_0 == 33138)
		{
			uint_ = 55368u;
		}
		return method_1(bool_0, uint_, BD08C397, 16u, num, num3);
	}

	public bool method_1(bool bool_0, uint uint_0, uint DBB07CAA, uint uint_1, uint C72C46B5 = 18u, uint FE8FF92B = 26u)
	{
		uint num = uint_1 / 16;
		if (num % 16 != 0)
		{
			num++;
		}
		gclass19_0.uint_11 = uint_0;
		gclass19_0.uint_12 = DBB07CAA;
		gclass19_0.uint_13 = num;
		gclass19_0.B306021C = C72C46B5;
		gclass19_0.F1A9E6B0 = FE8FF92B;
		gclass19_0.uint_15 = FE8FF92B;
		gclass19_0.AB91DA13("GCPU_REG_MEM_P0", gclass19_0.uint_11);
		gclass19_0.AB91DA13("GCPU_REG_MEM_P1", gclass19_0.uint_12);
		gclass19_0.AB91DA13("GCPU_REG_MEM_P2", gclass19_0.uint_13);
		gclass19_0.AB91DA13("GCPU_REG_MEM_P4", gclass19_0.B306021C);
		gclass19_0.AB91DA13("GCPU_REG_MEM_P5", gclass19_0.F1A9E6B0);
		gclass19_0.AB91DA13("GCPU_REG_MEM_P6", gclass19_0.uint_15);
		if (F9B1EA0E(bool_0) != 0)
		{
			return false;
		}
		return true;
	}

	public byte[] method_2(byte[] byte_0, bool bool_0 = false, uint uint_0 = 18u, uint D51CABAC = 26u, uint uint_1 = 48u)
	{
		byte[] array = A50FB49D(48u);
		if (array != null && array.Length != 0)
		{
			F897EA30(uint_0, byte_0);
			if (bool_0)
			{
				if (!method_4(uint_1, uint_0, D51CABAC))
				{
					return method_3(D51CABAC, 16);
				}
			}
			else if (!E29883A2(uint_1, uint_0, D51CABAC))
			{
				return method_3(D51CABAC, 16);
			}
		}
		return null;
	}

	public bool E29883A2(uint uint_0, uint B7AD298C, uint D1B0D780)
	{
		return (bool)new GClass128().E8AA1C3C(new object[4] { this, uint_0, B7AD298C, D1B0D780 }, 195360);
	}

	public byte[] method_3(uint uint_0, int AFB6C715)
	{
		List<byte> list = new List<byte>();
		using (IEnumerator<int> enumerator = GClass112.smethod_30(0, AFB6C715, 4).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				List<uint> list2 = C0051235(Class607.B630A78B.object_0[21](C682D094 + gclass19_0.uint_10 + current + uint_0 * 4));
				list.AddRange(GClass111.smethod_2("<I", list2.ToArray().Cast<object>().ToArray()));
			}
		}
		return list.ToArray();
	}

	public int F9B1EA0E(bool bool_0 = false, string B99CFBB3 = "cbc", bool F71E3BBF = true)
	{
		int a711A = CE91248B;
		if (bool_0)
		{
			if (Class607.B630A78B.object_0[787](B99CFBB3, "ecb"))
			{
				a711A = ((!F71E3BBF) ? int_17 : int_16);
			}
			else if (Class607.B630A78B.object_0[787](B99CFBB3, "cbc"))
			{
				a711A = ((!F71E3BBF) ? int_20 : int_20);
			}
		}
		else if (Class607.B630A78B.object_0[787](B99CFBB3, "ecb"))
		{
			a711A = ((!F71E3BBF) ? DD350433 : int_15);
		}
		else if (Class607.B630A78B.object_0[787](B99CFBB3, "cbc"))
		{
			a711A = ((!F71E3BBF) ? int_19 : CE91248B);
		}
		return method_5(Class607.B630A78B.object_0[836](a711A));
	}

	public bool method_4(uint uint_0, uint uint_1, uint uint_2)
	{
		return (bool)new GClass128().AD84C1A2(new object[4] { this, uint_0, uint_1, uint_2 }, 1240872);
	}

	public byte[] A50FB49D(uint B40755B7)
	{
		gclass19_0.uint_11 = 88u;
		gclass19_0.uint_12 = B40755B7;
		gclass19_0.uint_13 = 4u;
		gclass19_0.AB91DA13("GCPU_REG_MEM_P0", gclass19_0.uint_11);
		gclass19_0.AB91DA13("GCPU_REG_MEM_P1", gclass19_0.uint_12);
		gclass19_0.AB91DA13("GCPU_REG_MEM_P2", gclass19_0.uint_13);
		if (method_5(112u) != 0)
		{
			throw Class607.B630A78B.object_0[778]("failed to call the function!");
		}
		List<uint> list = C0051235(C682D094 + gclass19_0.uint_10 + 104, 4);
		List<byte> list2 = new List<byte>();
		foreach (uint item in list)
		{
			list2.AddRange(GClass111.smethod_2("<I", new object[1] { item }));
		}
		return list2.ToArray();
	}

	public List<uint> C91AC519(byte[] byte_0)
	{
		List<byte> list = byte_0.ToList();
		List<uint> list2 = new List<uint>();
		if (byte_0.Length % 4 != 0)
		{
			list.AddRange(new byte[4 - byte_0.Length % 4]);
		}
		using (IEnumerator<int> enumerator = GClass112.smethod_30(0, list.Count, 4).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				list2.Add(Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(list.ToArray(), current, 4))[0]));
			}
		}
		return list2;
	}

	public void F897EA30(uint F9A22631, byte[] byte_0)
	{
		if (C682D094 == 0)
		{
			return;
		}
		List<uint> list = C91AC519(byte_0);
		int num = 0;
		foreach (uint item in list)
		{
			gdelegate2_0(Class607.B630A78B.object_0[21](C682D094 + gclass19_0.uint_10 + num + F9A22631 * 4), item);
			num += 4;
		}
	}

	public int method_5(uint uint_0, uint uint_1 = 0u)
	{
		int num = -1;
		gclass19_0.D5A4C11B = 3u;
		gclass19_0.C1BAF330 = 3u;
		gclass19_0.uint_10 = uint_0;
		gclass19_0.uint_2 = uint_1;
		gclass19_0.AB91DA13("GCPU_REG_INT_CLR", gclass19_0.D5A4C11B);
		gclass19_0.AB91DA13("GCPU_REG_INT_EN", gclass19_0.C1BAF330);
		gclass19_0.AB91DA13("GCPU_REG_MEM_CMD", gclass19_0.uint_10);
		gclass19_0.AB91DA13("GCPU_REG_PC_CTL", gclass19_0.uint_2);
		while (gclass19_0.method_0("GCPU_REG_INT_SET") != 0)
		{
		}
		uint num2 = gclass19_0.method_0("GCPU_REG_INT_SET");
		if ((num2 & 2) != 0)
		{
			num2 = gclass19_0.method_0("GCPU_REG_INT_SET");
			if ((num2 & 1) != 0)
			{
				while (gclass19_0.method_0("GCPU_REG_INT_SET") != 0)
				{
				}
			}
			num = -1;
			gclass19_0.AB91DA13("GCPU_REG_INT_CLR", gclass19_0.D5A4C11B);
		}
		else
		{
			while ((gclass19_0.method_0("GCPU_REG_DRAM_MON") & 1) != 0)
			{
			}
			num = 0;
			gclass19_0.AB91DA13("GCPU_REG_INT_CLR", gclass19_0.D5A4C11B);
		}
		return num;
	}
}
