using System;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using SharpAdbClient;

public struct GStruct98
{
	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private DeviceData deviceData_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string A9280C3E;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_1;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string EB372013;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_2;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_3;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_4;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_5;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string DF360921;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string AD9BFF91;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string E80D8D87;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string F420C1A8;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_6;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_7;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string B7846415;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string D48F9985;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string A58F1908;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_8;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_9;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_10;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_11;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string C50F0A05;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string D7B162A4;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_12;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string B4B6FD91;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string C991658B;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_13;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string A23B580F;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string AB365981;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string E302A512;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_14;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string DB18C03E;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_15;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string E320432D;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_16;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string B0232A30;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string FF2420B5;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private bool bool_0;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_17;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private bool bool_1;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private bool AFBCF095;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_18;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_19;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string DE81C9B8;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_20;

	public DeviceData Device
	{
		[CompilerGenerated]
		get
		{
			return deviceData_0;
		}
		[CompilerGenerated]
		set
		{
			deviceData_0 = value;
		}
	}

	public string device
	{
		[CompilerGenerated]
		get
		{
			return A9280C3E;
		}
		[CompilerGenerated]
		set
		{
			A9280C3E = value;
		}
	}

	public string Brand
	{
		[CompilerGenerated]
		get
		{
			return string_0;
		}
		[CompilerGenerated]
		set
		{
			string_0 = value;
		}
	}

	public string manufacturer
	{
		[CompilerGenerated]
		get
		{
			return string_1;
		}
		[CompilerGenerated]
		set
		{
			string_1 = value;
		}
	}

	public string Model
	{
		[CompilerGenerated]
		get
		{
			return EB372013;
		}
		[CompilerGenerated]
		set
		{
			EB372013 = value;
		}
	}

	public string BuildNumber
	{
		[CompilerGenerated]
		get
		{
			return string_2;
		}
		[CompilerGenerated]
		set
		{
			string_2 = value;
		}
	}

	public string OsVersion
	{
		[CompilerGenerated]
		get
		{
			return string_3;
		}
		[CompilerGenerated]
		set
		{
			string_3 = value;
		}
	}

	public string SDKVersion
	{
		[CompilerGenerated]
		get
		{
			return string_4;
		}
		[CompilerGenerated]
		set
		{
			string_4 = value;
		}
	}

	public string ModelID
	{
		[CompilerGenerated]
		get
		{
			return string_5;
		}
		[CompilerGenerated]
		set
		{
			string_5 = value;
		}
	}

	public string CustCVersion
	{
		[CompilerGenerated]
		get
		{
			return DF360921;
		}
		[CompilerGenerated]
		set
		{
			DF360921 = value;
		}
	}

	public string CustDVersion
	{
		[CompilerGenerated]
		get
		{
			return AD9BFF91;
		}
		[CompilerGenerated]
		set
		{
			AD9BFF91 = value;
		}
	}

	public string Processor
	{
		[CompilerGenerated]
		get
		{
			return E80D8D87;
		}
		[CompilerGenerated]
		set
		{
			E80D8D87 = value;
		}
	}

	public string Chipset
	{
		[CompilerGenerated]
		get
		{
			return F420C1A8;
		}
		[CompilerGenerated]
		set
		{
			F420C1A8 = value;
		}
	}

	public string Platform
	{
		[CompilerGenerated]
		get
		{
			return string_6;
		}
		[CompilerGenerated]
		set
		{
			string_6 = value;
		}
	}

	public string HardwareVersion
	{
		[CompilerGenerated]
		get
		{
			return string_7;
		}
		[CompilerGenerated]
		set
		{
			string_7 = value;
		}
	}

	public string USBConfig
	{
		[CompilerGenerated]
		get
		{
			return B7846415;
		}
		[CompilerGenerated]
		set
		{
			B7846415 = value;
		}
	}

	public string TimezoneID
	{
		[CompilerGenerated]
		get
		{
			return D48F9985;
		}
		[CompilerGenerated]
		set
		{
			D48F9985 = value;
		}
	}

	public string OperatorReady
	{
		[CompilerGenerated]
		get
		{
			return A58F1908;
		}
		[CompilerGenerated]
		set
		{
			A58F1908 = value;
		}
	}

	public string BLVersion
	{
		[CompilerGenerated]
		get
		{
			return string_8;
		}
		[CompilerGenerated]
		set
		{
			string_8 = value;
		}
	}

	public string APVersion
	{
		[CompilerGenerated]
		get
		{
			return string_9;
		}
		[CompilerGenerated]
		set
		{
			string_9 = value;
		}
	}

	public string CPVersion
	{
		[CompilerGenerated]
		get
		{
			return string_10;
		}
		[CompilerGenerated]
		set
		{
			string_10 = value;
		}
	}

	public string CSCVersion
	{
		[CompilerGenerated]
		get
		{
			return string_11;
		}
		[CompilerGenerated]
		set
		{
			string_11 = value;
		}
	}

	public string FirmwareCountry
	{
		[CompilerGenerated]
		get
		{
			return C50F0A05;
		}
		[CompilerGenerated]
		set
		{
			C50F0A05 = value;
		}
	}

	public string language
	{
		[CompilerGenerated]
		get
		{
			return D7B162A4;
		}
		[CompilerGenerated]
		set
		{
			D7B162A4 = value;
		}
	}

	public string Local
	{
		[CompilerGenerated]
		get
		{
			return string_12;
		}
		[CompilerGenerated]
		set
		{
			string_12 = value;
		}
	}

	public string KnoxGuardState
	{
		[CompilerGenerated]
		get
		{
			return B4B6FD91;
		}
		[CompilerGenerated]
		set
		{
			B4B6FD91 = value;
		}
	}

	public string SecurityPath
	{
		[CompilerGenerated]
		get
		{
			return C991658B;
		}
		[CompilerGenerated]
		set
		{
			C991658B = value;
		}
	}

	public string SecureLevel
	{
		[CompilerGenerated]
		get
		{
			return string_13;
		}
		[CompilerGenerated]
		set
		{
			string_13 = value;
		}
	}

	public unsafe string EncryptionState
	{
		[CompilerGenerated]
		get
		{
			GClass128 gClass = new GClass128();
			object[] array = new object[1];
			TypedReference typedReference = __makeref(this);
			array[0] = (nint)(&typedReference);
			return (string)gClass.DFB12B0F(array, 469327);
		}
		[CompilerGenerated]
		set
		{
			GClass128 gClass = new GClass128();
			object[] array = new object[2];
			TypedReference typedReference = __makeref(this);
			array[0] = (nint)(&typedReference);
			array[1] = value;
			gClass.DFB12B0F(array, 536614);
		}
	}

	public string BootloaderVersion
	{
		[CompilerGenerated]
		get
		{
			return AB365981;
		}
		[CompilerGenerated]
		set
		{
			AB365981 = value;
		}
	}

	public string RilSerialNumber
	{
		[CompilerGenerated]
		get
		{
			return E302A512;
		}
		[CompilerGenerated]
		set
		{
			E302A512 = value;
		}
	}

	public string SerialNumber
	{
		[CompilerGenerated]
		get
		{
			return string_14;
		}
		[CompilerGenerated]
		set
		{
			string_14 = value;
		}
	}

	public string IMEI
	{
		[CompilerGenerated]
		get
		{
			return DB18C03E;
		}
		[CompilerGenerated]
		set
		{
			DB18C03E = value;
		}
	}

	public string IMEI1
	{
		[CompilerGenerated]
		get
		{
			return string_15;
		}
		[CompilerGenerated]
		set
		{
			string_15 = value;
		}
	}

	public string IMEI2
	{
		[CompilerGenerated]
		get
		{
			return E320432D;
		}
		[CompilerGenerated]
		set
		{
			E320432D = value;
		}
	}

	public string BootloaderState
	{
		[CompilerGenerated]
		get
		{
			return string_16;
		}
		[CompilerGenerated]
		set
		{
			string_16 = value;
		}
	}

	public string KernelStatus
	{
		[CompilerGenerated]
		get
		{
			return B0232A30;
		}
		[CompilerGenerated]
		set
		{
			B0232A30 = value;
		}
	}

	public string SimCount
	{
		[CompilerGenerated]
		get
		{
			return FF2420B5;
		}
		[CompilerGenerated]
		set
		{
			FF2420B5 = value;
		}
	}

	public bool RootStatus
	{
		[CompilerGenerated]
		get
		{
			return bool_0;
		}
		[CompilerGenerated]
		set
		{
			bool_0 = value;
		}
	}

	public string RootVersion
	{
		[CompilerGenerated]
		get
		{
			return string_17;
		}
		[CompilerGenerated]
		set
		{
			string_17 = value;
		}
	}

	public bool IsMediatek
	{
		[CompilerGenerated]
		get
		{
			return bool_1;
		}
		[CompilerGenerated]
		set
		{
			bool_1 = value;
		}
	}

	public bool IsQualcomm
	{
		[CompilerGenerated]
		get
		{
			return AFBCF095;
		}
		[CompilerGenerated]
		set
		{
			AFBCF095 = value;
		}
	}

	public string String_0
	{
		[CompilerGenerated]
		get
		{
			return string_18;
		}
		[CompilerGenerated]
		set
		{
			string_18 = value;
		}
	}

	public string String_1
	{
		[CompilerGenerated]
		get
		{
			return string_19;
		}
		[CompilerGenerated]
		set
		{
			string_19 = value;
		}
	}

	public string String_2
	{
		[CompilerGenerated]
		get
		{
			return DE81C9B8;
		}
		[CompilerGenerated]
		set
		{
			DE81C9B8 = value;
		}
	}

	public string String_3
	{
		[CompilerGenerated]
		get
		{
			return string_20;
		}
		[CompilerGenerated]
		set
		{
			string_20 = value;
		}
	}
}
