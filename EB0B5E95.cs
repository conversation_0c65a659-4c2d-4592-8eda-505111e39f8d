using System.Drawing;

public static class EB0B5E95
{
	public static Color smethod_0(this int int_0)
	{
		return Class607.B630A78B.object_0[235]((int_0 & 0xFF0000) >> 16, (int_0 & 0xFF00) >> 8, int_0 & 0xFF);
	}

	public static Color A5AE579A(this Color color_0)
	{
		return Class607.B630A78B.object_0[235](Class607.B630A78B.object_0[512](ref color_0), Class607.B630A78B.object_0[524](ref color_0), Class607.B630A78B.object_0[116](ref color_0));
	}

	public static int smethod_1(this int FA92ABB8)
	{
		return (int)((double)FA92ABB8 / 100.0 * 255.0);
	}
}
