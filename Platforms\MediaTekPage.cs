using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using MotoKingPro.Core;
using MotoKingPro.UI;

namespace MotoKingPro.Platforms
{
    /// <summary>
    /// MediaTek platform page (C220CEB6 -> MediaTekPage)
    /// Handles MediaTek devices with SP Flash Tool integration
    /// </summary>
    public partial class MediaTekPage : UserControl
    {
        #region Events
        public event Action<bool, string> OperationStateChanged;
        public event Action<string, LogLevel, bool, bool> LogMessage;
        #endregion

        #region Fields
        private MainForm parentForm;
        private MediaTekManager mediaTekManager;
        private LogRichTextBox textOutput;
        private bool isOperationRunning = false;
        #endregion

        #region UI Controls
        private Panel mainPanel;
        private GroupBox deviceInfoGroup;
        private GroupBox operationsGroup;
        private GroupBox flashGroup;
        
        private Button connectButton;
        private Button disconnectButton;
        private Button readInfoButton;
        private Button formatButton;
        private Button downloadButton;
        private Button rebootButton;
        
        private ComboBox authBypassCombo;
        private Button authBypassButton;
        private TextBox scatterFileTextBox;
        private Button browseScatterButton;
        private CheckedListBox romListBox;
        
        private Label deviceModelLabel;
        private Label chipsetLabel;
        private Label connectionStatusLabel;
        private Label preloaderLabel;
        #endregion

        #region Constructor
        public MediaTekPage()
        {
            InitializeComponent();
            InitializeCustomComponents();
            SetupEventHandlers();
        }
        #endregion

        #region Initialization
        private void InitializeComponent()
        {
            mainPanel = new Panel { Dock = DockStyle.Fill, BackColor = Color.FromArgb(45, 45, 48) };
            
            // Device info group
            deviceInfoGroup = new GroupBox 
            { 
                Text = "Device Information", 
                ForeColor = Color.White,
                Size = new Size(300, 120),
                Location = new Point(10, 10)
            };
            
            deviceModelLabel = new Label { Text = "Model: Not Connected", ForeColor = Color.White, Location = new Point(10, 20) };
            chipsetLabel = new Label { Text = "Chipset: Unknown", ForeColor = Color.White, Location = new Point(10, 40) };
            preloaderLabel = new Label { Text = "Preloader: Unknown", ForeColor = Color.White, Location = new Point(10, 60) };
            connectionStatusLabel = new Label { Text = "Status: Disconnected", ForeColor = Color.Red, Location = new Point(10, 80) };
            
            deviceInfoGroup.Controls.AddRange(new Control[] { deviceModelLabel, chipsetLabel, preloaderLabel, connectionStatusLabel });
            
            // Operations group
            operationsGroup = new GroupBox 
            { 
                Text = "Operations", 
                ForeColor = Color.White,
                Size = new Size(300, 180),
                Location = new Point(10, 140)
            };
            
            connectButton = new Button { Text = "Connect Device", Size = new Size(120, 30), Location = new Point(10, 20) };
            disconnectButton = new Button { Text = "Disconnect", Size = new Size(120, 30), Location = new Point(140, 20), Enabled = false };
            readInfoButton = new Button { Text = "Read Device Info", Size = new Size(120, 30), Location = new Point(10, 60), Enabled = false };
            
            authBypassCombo = new ComboBox 
            { 
                Size = new Size(120, 25), 
                Location = new Point(10, 100),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Enabled = false
            };
            authBypassCombo.Items.AddRange(new[] { "None", "SLA/DAA", "Kamakiri", "Brom" });
            authBypassCombo.SelectedIndex = 0;
            
            authBypassButton = new Button { Text = "Auth Bypass", Size = new Size(120, 30), Location = new Point(140, 100), Enabled = false };
            
            formatButton = new Button { Text = "Format", Size = new Size(80, 30), Location = new Point(10, 140), Enabled = false };
            downloadButton = new Button { Text = "Download", Size = new Size(80, 30), Location = new Point(100, 140), Enabled = false };
            rebootButton = new Button { Text = "Reboot", Size = new Size(80, 30), Location = new Point(190, 140), Enabled = false };
            
            operationsGroup.Controls.AddRange(new Control[] 
            { 
                connectButton, disconnectButton, readInfoButton, 
                authBypassCombo, authBypassButton,
                formatButton, downloadButton, rebootButton
            });
            
            // Flash group
            flashGroup = new GroupBox 
            { 
                Text = "Flash Files", 
                ForeColor = Color.White,
                Size = new Size(300, 200),
                Location = new Point(320, 10)
            };
            
            Label scatterLabel = new Label { Text = "Scatter File:", ForeColor = Color.White, Location = new Point(10, 20) };
            scatterFileTextBox = new TextBox 
            { 
                Size = new Size(200, 25), 
                Location = new Point(10, 40),
                BackColor = Color.FromArgb(30, 30, 30),
                ForeColor = Color.White
            };
            browseScatterButton = new Button { Text = "Browse", Size = new Size(70, 25), Location = new Point(220, 40) };
            
            Label romLabel = new Label { Text = "ROM Files:", ForeColor = Color.White, Location = new Point(10, 75) };
            romListBox = new CheckedListBox 
            { 
                Size = new Size(280, 100), 
                Location = new Point(10, 95),
                BackColor = Color.FromArgb(30, 30, 30),
                ForeColor = Color.White,
                CheckOnClick = true
            };
            
            flashGroup.Controls.AddRange(new Control[] { scatterLabel, scatterFileTextBox, browseScatterButton, romLabel, romListBox });
            
            // Text output
            textOutput = new LogRichTextBox 
            { 
                Size = new Size(610, 200), 
                Location = new Point(10, 330),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };
            
            mainPanel.Controls.AddRange(new Control[] { deviceInfoGroup, operationsGroup, flashGroup, textOutput });
            Controls.Add(mainPanel);
        }

        private void InitializeCustomComponents()
        {
            mediaTekManager = new MediaTekManager();
            
            // Log initial messages
            LogMessage("MediaTek Platform", LogLevel.Word, true, true);
            LogMessage("Version: " + ApplicationInfo.GetVersionString(), LogLevel.Success);
            LogMessage("Selected Tab: Mediatek", LogLevel.Success, true, true);
            LogMessage("Ready for device connection...", LogLevel.Info);
        }

        private void SetupEventHandlers()
        {
            connectButton.Click += ConnectButton_Click;
            disconnectButton.Click += DisconnectButton_Click;
            readInfoButton.Click += ReadInfoButton_Click;
            authBypassButton.Click += AuthBypassButton_Click;
            formatButton.Click += FormatButton_Click;
            downloadButton.Click += DownloadButton_Click;
            rebootButton.Click += RebootButton_Click;
            browseScatterButton.Click += BrowseScatterButton_Click;
            
            mediaTekManager.OperationStateChanged += OnOperationStateChanged;
            mediaTekManager.LogMessage += OnLogMessage;
        }
        #endregion

        #region Public Methods
        public void SetParentForm(MainForm form)
        {
            parentForm = form;
        }

        public void FocusTextOutput()
        {
            textOutput?.Focus();
        }

        public void ClearLog()
        {
            textOutput?.ClearLog();
        }

        public List<LogItem> GetLogItems()
        {
            return new List<LogItem>();
        }

        public void SetOperationState(bool isRunning)
        {
            isOperationRunning = isRunning;
            UpdateUIState();
        }

        public void InitializeAutoLoaders()
        {
            // Load auto-loader files for MediaTek
            LogMessage("Loading MediaTek auto-loaders...", LogLevel.Info);
        }
        #endregion

        #region Event Handlers
        private void ConnectButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Connect MediaTek Device");
            
            bool success = mediaTekManager.ConnectDevice();
            if (success)
            {
                connectionStatusLabel.Text = "Status: Connected";
                connectionStatusLabel.ForeColor = Color.Green;
                EnableConnectedControls(true);
                LogMessage("MediaTek device connected successfully", LogLevel.Success);
            }
            else
            {
                LogMessage("Failed to connect MediaTek device", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Connect MediaTek Device");
        }

        private void DisconnectButton_Click(object sender, EventArgs e)
        {
            mediaTekManager.DisconnectDevice();
            connectionStatusLabel.Text = "Status: Disconnected";
            connectionStatusLabel.ForeColor = Color.Red;
            EnableConnectedControls(false);
            LogMessage("Device disconnected", LogLevel.Warning);
        }

        private void ReadInfoButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Read Device Information");
            
            var deviceInfo = mediaTekManager.ReadDeviceInfo();
            if (deviceInfo != null)
            {
                deviceModelLabel.Text = $"Model: {deviceInfo.Model}";
                chipsetLabel.Text = $"Chipset: {deviceInfo.Chipset}";
                LogMessage($"Device Model: {deviceInfo.Model}", LogLevel.Success);
                LogMessage($"Chipset: {deviceInfo.Chipset}", LogLevel.Success);
            }
            
            OnOperationStateChanged(false, "Read Device Information");
        }

        private void AuthBypassButton_Click(object sender, EventArgs e)
        {
            string method = authBypassCombo.SelectedItem.ToString();
            OnOperationStateChanged(true, $"Auth Bypass: {method}");
            
            bool success = mediaTekManager.PerformAuthBypass(method);
            LogMessage(success ? $"Auth bypass ({method}) successful" : $"Auth bypass ({method}) failed", 
                success ? LogLevel.Success : LogLevel.Error);
            
            OnOperationStateChanged(false, $"Auth Bypass: {method}");
        }

        private void FormatButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Format Device");
            
            bool success = mediaTekManager.FormatDevice();
            LogMessage(success ? "Device format completed" : "Device format failed", 
                success ? LogLevel.Success : LogLevel.Error);
            
            OnOperationStateChanged(false, "Format Device");
        }

        private void DownloadButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(scatterFileTextBox.Text))
            {
                LogMessage("Please select a scatter file first", LogLevel.Error);
                return;
            }

            OnOperationStateChanged(true, "Download Firmware");
            
            var selectedFiles = new List<string>();
            foreach (var item in romListBox.CheckedItems)
            {
                selectedFiles.Add(item.ToString());
            }
            
            bool success = mediaTekManager.DownloadFirmware(scatterFileTextBox.Text, selectedFiles);
            LogMessage(success ? "Firmware download completed" : "Firmware download failed", 
                success ? LogLevel.Success : LogLevel.Error);
            
            OnOperationStateChanged(false, "Download Firmware");
        }

        private void RebootButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Reboot Device");
            
            bool success = mediaTekManager.RebootDevice("Normal");
            LogMessage(success ? "Device reboot initiated" : "Device reboot failed", 
                success ? LogLevel.Success : LogLevel.Error);
            
            OnOperationStateChanged(false, "Reboot Device");
        }

        private void BrowseScatterButton_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog ofd = new OpenFileDialog())
            {
                ofd.Filter = "Scatter Files (*.txt)|*.txt|All Files (*.*)|*.*";
                ofd.Title = "Select Scatter File";
                
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    scatterFileTextBox.Text = ofd.FileName;
                    LoadScatterFile(ofd.FileName);
                }
            }
        }

        private void OnOperationStateChanged(bool isRunning, string operationName)
        {
            OperationStateChanged?.Invoke(isRunning, operationName);
        }

        private void OnLogMessage(string message, LogLevel level, bool newLine, bool bold)
        {
            LogMessage?.Invoke(message, level, newLine, bold);
            textOutput.LogMessage(message, level, newLine, bold);
        }
        #endregion

        #region Helper Methods
        private void EnableConnectedControls(bool enabled)
        {
            disconnectButton.Enabled = enabled;
            readInfoButton.Enabled = enabled;
            authBypassCombo.Enabled = enabled;
            authBypassButton.Enabled = enabled;
            formatButton.Enabled = enabled;
            downloadButton.Enabled = enabled;
            rebootButton.Enabled = enabled;
            
            connectButton.Enabled = !enabled;
        }

        private void UpdateUIState()
        {
            // Update UI based on operation state
        }

        private void LogMessage(string message, LogLevel level = LogLevel.Info, bool newLine = true, bool bold = false)
        {
            textOutput?.LogMessage(message, level, newLine, bold);
        }

        private void LoadScatterFile(string filePath)
        {
            try
            {
                romListBox.Items.Clear();
                
                // Simulate loading scatter file and populating ROM list
                romListBox.Items.Add("preloader.img", true);
                romListBox.Items.Add("boot.img", true);
                romListBox.Items.Add("recovery.img", true);
                romListBox.Items.Add("system.img", true);
                romListBox.Items.Add("userdata.img", false);
                romListBox.Items.Add("cache.img", false);
                
                LogMessage($"Scatter file loaded: {System.IO.Path.GetFileName(filePath)}", LogLevel.Success);
                LogMessage($"Found {romListBox.Items.Count} ROM files", LogLevel.Info);
            }
            catch (Exception ex)
            {
                LogMessage($"Failed to load scatter file: {ex.Message}", LogLevel.Error);
            }
        }
        #endregion
    }

    /// <summary>
    /// MediaTek device manager for SP Flash Tool operations
    /// </summary>
    public class MediaTekManager : BasePlatformManager
    {
        private DeviceConnectionState connectionState = DeviceConnectionState.Disconnected;
        private DeviceInfo currentDevice;

        public override bool ConnectDevice()
        {
            try
            {
                OnLogMessage("Attempting to connect MediaTek device...", LogLevel.Info);
                OnLogMessage("Waiting for device in download mode...", LogLevel.Info);

                System.Threading.Thread.Sleep(1500);

                connectionState = DeviceConnectionState.DownloadMode;
                currentDevice = new DeviceInfo
                {
                    Model = "MediaTek Device",
                    Manufacturer = "MediaTek",
                    Chipset = "MT6765",
                    Platform = PlatformType.MediaTek,
                    SerialNumber = "MTK123456789"
                };

                OnLogMessage("MediaTek device connected in download mode", LogLevel.Success);
                return true;
            }
            catch (Exception ex)
            {
                OnLogMessage($"Connection failed: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        public override bool DisconnectDevice()
        {
            connectionState = DeviceConnectionState.Disconnected;
            currentDevice = null;
            OnLogMessage("Device disconnected", LogLevel.Warning);
            return true;
        }

        public override DeviceConnectionState GetConnectionState()
        {
            return connectionState;
        }

        public DeviceInfo ReadDeviceInfo()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return null;

            OnLogMessage("Reading MediaTek device information...", LogLevel.Info);
            System.Threading.Thread.Sleep(800);

            return currentDevice;
        }

        public bool PerformAuthBypass(string method)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage($"Performing authentication bypass using {method}...", LogLevel.Info);

            switch (method.ToLower())
            {
                case "kamakiri":
                    OnLogMessage("Exploiting Kamakiri vulnerability...", LogLevel.Warning);
                    System.Threading.Thread.Sleep(3000);
                    break;
                case "sla/daa":
                    OnLogMessage("Bypassing SLA/DAA authentication...", LogLevel.Warning);
                    System.Threading.Thread.Sleep(2000);
                    break;
                case "brom":
                    OnLogMessage("Using BROM exploit...", LogLevel.Warning);
                    System.Threading.Thread.Sleep(2500);
                    break;
                default:
                    OnLogMessage("No authentication bypass selected", LogLevel.Info);
                    return true;
            }

            OnLogMessage($"Authentication bypass ({method}) completed successfully", LogLevel.Success);
            return true;
        }

        public bool FormatDevice()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage("Starting device format operation...", LogLevel.Warning);
            OnLogMessage("WARNING: This will erase all data on the device!", LogLevel.Error);
            System.Threading.Thread.Sleep(4000);
            OnLogMessage("Format operation completed", LogLevel.Success);
            return true;
        }

        public bool DownloadFirmware(string scatterFile, List<string> selectedFiles)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage($"Starting firmware download using scatter: {System.IO.Path.GetFileName(scatterFile)}", LogLevel.Info);
            OnLogMessage($"Selected {selectedFiles.Count} files for download", LogLevel.Info);

            foreach (string file in selectedFiles)
            {
                OnLogMessage($"Downloading: {file}", LogLevel.Info);
                System.Threading.Thread.Sleep(1000);
                OnLogMessage($"✓ {file} downloaded successfully", LogLevel.Success);
            }

            OnLogMessage("Firmware download completed successfully", LogLevel.Success);
            return true;
        }

        public bool RebootDevice(string mode)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage($"Rebooting device to {mode} mode...", LogLevel.Info);
            System.Threading.Thread.Sleep(1000);
            OnLogMessage("Device reboot initiated", LogLevel.Success);
            return true;
        }
    }
}
