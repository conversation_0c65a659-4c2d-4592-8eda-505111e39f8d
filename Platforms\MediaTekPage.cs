using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using System.Threading;
using System.Threading.Tasks;
using System.IO;
using System.Linq;
using MotoKingPro.Core;
using MotoKingPro.UI;
using MotoKingPro.MediaTek;

namespace MotoKingPro.Platforms
{
    /// <summary>
    /// MediaTek platform page (C220CEB6 -> MediaTekPage)
    /// Handles MediaTek devices with SP Flash Tool integration
    /// </summary>
    public partial class MediaTekPage : UserControl
    {
        #region Events
        public event Action<bool, string> OperationStateChanged;
        public event Action<string, LogLevel, bool, bool> LogMessage;
        #endregion

        #region Fields
        private MainForm parentForm;
        private MediaTekManager mediaTekManager;
        private LogRichTextBox textOutput;
        private bool isOperationRunning = false;
        private ScatterFileManager scatterManager;
        private UsbManager usbManager;
        private AuthenticationBypass authBypass;
        private PayloadManager payloadManager;
        private CancellationTokenSource cancellationTokenSource;
        #endregion

        #region UI Controls
        private Panel mainPanel;
        private GroupBox deviceInfoGroup;
        private GroupBox operationsGroup;
        private GroupBox flashGroup;
        
        private Button connectButton;
        private Button disconnectButton;
        private Button readInfoButton;
        private Button formatButton;
        private Button downloadButton;
        private Button rebootButton;
        
        private ComboBox authBypassCombo;
        private Button authBypassButton;
        private TextBox scatterFileTextBox;
        private Button browseScatterButton;
        private CheckedListBox romListBox;
        
        private Label deviceModelLabel;
        private Label chipsetLabel;
        private Label connectionStatusLabel;
        private Label preloaderLabel;
        #endregion

        #region Constructor
        public MediaTekPage()
        {
            InitializeComponent();
            InitializeCustomComponents();
            SetupEventHandlers();
        }
        #endregion

        #region Initialization
        private void InitializeComponent()
        {
            mainPanel = new Panel { Dock = DockStyle.Fill, BackColor = Color.FromArgb(45, 45, 48) };
            
            // Device info group
            deviceInfoGroup = new GroupBox 
            { 
                Text = "Device Information", 
                ForeColor = Color.White,
                Size = new Size(300, 120),
                Location = new Point(10, 10)
            };
            
            deviceModelLabel = new Label { Text = "Model: Not Connected", ForeColor = Color.White, Location = new Point(10, 20) };
            chipsetLabel = new Label { Text = "Chipset: Unknown", ForeColor = Color.White, Location = new Point(10, 40) };
            preloaderLabel = new Label { Text = "Preloader: Unknown", ForeColor = Color.White, Location = new Point(10, 60) };
            connectionStatusLabel = new Label { Text = "Status: Disconnected", ForeColor = Color.Red, Location = new Point(10, 80) };
            
            deviceInfoGroup.Controls.AddRange(new Control[] { deviceModelLabel, chipsetLabel, preloaderLabel, connectionStatusLabel });
            
            // Operations group
            operationsGroup = new GroupBox 
            { 
                Text = "Operations", 
                ForeColor = Color.White,
                Size = new Size(300, 180),
                Location = new Point(10, 140)
            };
            
            connectButton = new Button { Text = "Connect Device", Size = new Size(120, 30), Location = new Point(10, 20) };
            disconnectButton = new Button { Text = "Disconnect", Size = new Size(120, 30), Location = new Point(140, 20), Enabled = false };
            readInfoButton = new Button { Text = "Read Device Info", Size = new Size(120, 30), Location = new Point(10, 60), Enabled = false };
            
            authBypassCombo = new ComboBox 
            { 
                Size = new Size(120, 25), 
                Location = new Point(10, 100),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Enabled = false
            };
            authBypassCombo.Items.AddRange(new[] { "None", "SLA/DAA", "Kamakiri", "Brom" });
            authBypassCombo.SelectedIndex = 0;
            
            authBypassButton = new Button { Text = "Auth Bypass", Size = new Size(120, 30), Location = new Point(140, 100), Enabled = false };
            
            formatButton = new Button { Text = "Format", Size = new Size(80, 30), Location = new Point(10, 140), Enabled = false };
            downloadButton = new Button { Text = "Download", Size = new Size(80, 30), Location = new Point(100, 140), Enabled = false };
            rebootButton = new Button { Text = "Reboot", Size = new Size(80, 30), Location = new Point(190, 140), Enabled = false };
            
            operationsGroup.Controls.AddRange(new Control[] 
            { 
                connectButton, disconnectButton, readInfoButton, 
                authBypassCombo, authBypassButton,
                formatButton, downloadButton, rebootButton
            });
            
            // Flash group
            flashGroup = new GroupBox 
            { 
                Text = "Flash Files", 
                ForeColor = Color.White,
                Size = new Size(300, 200),
                Location = new Point(320, 10)
            };
            
            Label scatterLabel = new Label { Text = "Scatter File:", ForeColor = Color.White, Location = new Point(10, 20) };
            scatterFileTextBox = new TextBox 
            { 
                Size = new Size(200, 25), 
                Location = new Point(10, 40),
                BackColor = Color.FromArgb(30, 30, 30),
                ForeColor = Color.White
            };
            browseScatterButton = new Button { Text = "Browse", Size = new Size(70, 25), Location = new Point(220, 40) };
            
            Label romLabel = new Label { Text = "ROM Files:", ForeColor = Color.White, Location = new Point(10, 75) };
            romListBox = new CheckedListBox 
            { 
                Size = new Size(280, 100), 
                Location = new Point(10, 95),
                BackColor = Color.FromArgb(30, 30, 30),
                ForeColor = Color.White,
                CheckOnClick = true
            };
            
            flashGroup.Controls.AddRange(new Control[] { scatterLabel, scatterFileTextBox, browseScatterButton, romLabel, romListBox });
            
            // Text output
            textOutput = new LogRichTextBox 
            { 
                Size = new Size(610, 200), 
                Location = new Point(10, 330),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };
            
            mainPanel.Controls.AddRange(new Control[] { deviceInfoGroup, operationsGroup, flashGroup, textOutput });
            Controls.Add(mainPanel);
        }

        private void InitializeCustomComponents()
        {
            mediaTekManager = new MediaTekManager();
            scatterManager = new ScatterFileManager();
            usbManager = new UsbManager();
            authBypass = new AuthenticationBypass();
            payloadManager = new PayloadManager();
            cancellationTokenSource = new CancellationTokenSource();

            // Setup event handlers
            scatterManager.LogMessage += (s, msg) => LogToOutput(msg, LogLevel.Info);
            usbManager.LogMessage += (s, msg) => LogToOutput(msg, LogLevel.Info);
            authBypass.LogMessage += (s, msg) => LogToOutput(msg, LogLevel.Info);
            authBypass.ProgressUpdate += (s, progress) => UpdateProgress(progress);
            payloadManager.LogMessage += (s, msg) => LogToOutput(msg, LogLevel.Info);

            // Log initial messages
            LogToOutput("MediaTek Platform", LogLevel.Word, true, true);
            LogToOutput("Version: " + ApplicationInfo.GetVersionString(), LogLevel.Success);
            LogToOutput("Selected Tab: Mediatek", LogLevel.Success, true, true);
            LogToOutput("Ready for device connection...", LogLevel.Info);

            // Load available payloads and preloaders
            LoadAvailableResources();

            // Scan for devices
            RefreshDeviceList();
        }

        private void SetupEventHandlers()
        {
            connectButton.Click += ConnectButton_Click;
            disconnectButton.Click += DisconnectButton_Click;
            readInfoButton.Click += ReadInfoButton_Click;
            authBypassButton.Click += AuthBypassButton_Click;
            formatButton.Click += FormatButton_Click;
            downloadButton.Click += DownloadButton_Click;
            rebootButton.Click += RebootButton_Click;
            browseScatterButton.Click += BrowseScatterButton_Click;
            
            mediaTekManager.OperationStateChanged += OnOperationStateChanged;
            mediaTekManager.LogMessage += OnLogMessage;
        }
        #endregion

        #region Public Methods
        public void SetParentForm(MainForm form)
        {
            parentForm = form;
        }

        public void FocusTextOutput()
        {
            textOutput?.Focus();
        }

        public void ClearLog()
        {
            textOutput?.ClearLog();
        }

        public List<LogItem> GetLogItems()
        {
            return new List<LogItem>();
        }

        public void SetOperationState(bool isRunning)
        {
            isOperationRunning = isRunning;
            UpdateUIState();
        }

        public void InitializeAutoLoaders()
        {
            // Load auto-loader files for MediaTek
            LogMessage("Loading MediaTek auto-loaders...", LogLevel.Info);
        }
        #endregion

        #region Event Handlers
        private async void ConnectButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Connect MediaTek Device");

            try
            {
                // Scan for MediaTek devices
                var devices = usbManager.GetConnectedMediaTekDevices();
                if (!devices.Any())
                {
                    LogMessage("No MediaTek devices found", LogLevel.Error);
                    LogMessage("Please ensure device is in download mode", LogLevel.Info);
                    return;
                }

                var device = devices.First();
                LogMessage($"Found device: {device}", LogLevel.Success);

                // Connect to device
                bool success = await mediaTekManager.ConnectDevice(device);
                if (success)
                {
                    connectionStatusLabel.Text = "Status: Connected";
                    connectionStatusLabel.ForeColor = Color.Green;
                    EnableConnectedControls(true);
                    LogMessage("MediaTek device connected successfully", LogLevel.Success);

                    // Read device information
                    await ReadDeviceInformation();
                }
                else
                {
                    LogMessage("Failed to connect MediaTek device", LogLevel.Error);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Connection error: {ex.Message}", LogLevel.Error);
            }
            finally
            {
                OnOperationStateChanged(false, "Connect MediaTek Device");
            }
        }

        private void DisconnectButton_Click(object sender, EventArgs e)
        {
            mediaTekManager.DisconnectDevice();
            connectionStatusLabel.Text = "Status: Disconnected";
            connectionStatusLabel.ForeColor = Color.Red;
            EnableConnectedControls(false);
            LogMessage("Device disconnected", LogLevel.Warning);
        }

        private void ReadInfoButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Read Device Information");
            
            var deviceInfo = mediaTekManager.ReadDeviceInfo();
            if (deviceInfo != null)
            {
                deviceModelLabel.Text = $"Model: {deviceInfo.Model}";
                chipsetLabel.Text = $"Chipset: {deviceInfo.Chipset}";
                LogMessage($"Device Model: {deviceInfo.Model}", LogLevel.Success);
                LogMessage($"Chipset: {deviceInfo.Chipset}", LogLevel.Success);
            }
            
            OnOperationStateChanged(false, "Read Device Information");
        }

        private async void AuthBypassButton_Click(object sender, EventArgs e)
        {
            string method = authBypassCombo.SelectedItem.ToString();
            OnOperationStateChanged(true, $"Auth Bypass: {method}");

            try
            {
                var device = mediaTekManager.GetCurrentDevice();
                if (device == null)
                {
                    LogMessage("No device connected", LogLevel.Error);
                    return;
                }

                LogMessage($"Starting {method} authentication bypass...", LogLevel.Warning);

                bool success = method.ToLower() switch
                {
                    "kamakiri" => await authBypass.PerformKamakiriExploit(device, cancellationTokenSource.Token),
                    "sla/daa" => await authBypass.PerformSlaBypass(device, cancellationTokenSource.Token),
                    "brom" => await authBypass.PerformBromExploit(device, cancellationTokenSource.Token),
                    _ => false
                };

                LogMessage(success ? $"Auth bypass ({method}) successful" : $"Auth bypass ({method}) failed",
                    success ? LogLevel.Success : LogLevel.Error);
            }
            catch (Exception ex)
            {
                LogMessage($"Auth bypass error: {ex.Message}", LogLevel.Error);
            }
            finally
            {
                OnOperationStateChanged(false, $"Auth Bypass: {method}");
            }
        }

        private async void FormatButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Format Device");

            try
            {
                bool success = await mediaTekManager.FormatDevice();
                LogMessage(success ? "Device format completed" : "Device format failed",
                    success ? LogLevel.Success : LogLevel.Error);
            }
            catch (Exception ex)
            {
                LogMessage($"Format error: {ex.Message}", LogLevel.Error);
            }
            finally
            {
                OnOperationStateChanged(false, "Format Device");
            }
        }

        private async void DownloadButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(scatterFileTextBox.Text))
            {
                LogMessage("Please select a scatter file first", LogLevel.Error);
                return;
            }

            if (!scatterManager.IsLoaded)
            {
                LogMessage("Please load a valid scatter file first", LogLevel.Error);
                return;
            }

            OnOperationStateChanged(true, "Download Firmware");

            try
            {
                var selectedEntries = new List<RomEntry>();
                foreach (var item in romListBox.CheckedItems)
                {
                    if (item is RomEntry entry)
                    {
                        selectedEntries.Add(entry);
                    }
                }

                if (!selectedEntries.Any())
                {
                    LogMessage("No ROM files selected for download", LogLevel.Warning);
                    return;
                }

                bool success = await mediaTekManager.DownloadFirmware(scatterManager, selectedEntries);
                LogMessage(success ? "Firmware download completed" : "Firmware download failed",
                    success ? LogLevel.Success : LogLevel.Error);
            }
            catch (Exception ex)
            {
                LogMessage($"Download error: {ex.Message}", LogLevel.Error);
            }
            finally
            {
                OnOperationStateChanged(false, "Download Firmware");
            }
        }

        private async void RebootButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Reboot Device");

            try
            {
                bool success = await mediaTekManager.RebootDevice("Normal");
                LogMessage(success ? "Device reboot initiated" : "Device reboot failed",
                    success ? LogLevel.Success : LogLevel.Error);
            }
            catch (Exception ex)
            {
                LogMessage($"Reboot error: {ex.Message}", LogLevel.Error);
            }
            finally
            {
                OnOperationStateChanged(false, "Reboot Device");
            }
        }

        private void BrowseScatterButton_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog ofd = new OpenFileDialog())
            {
                ofd.Filter = "Scatter Files (*.txt)|*.txt|All Files (*.*)|*.*";
                ofd.Title = "Select Scatter File";
                
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    scatterFileTextBox.Text = ofd.FileName;
                    LoadScatterFile(ofd.FileName);
                }
            }
        }

        private void OnOperationStateChanged(bool isRunning, string operationName)
        {
            OperationStateChanged?.Invoke(isRunning, operationName);
        }

        private void OnLogMessage(string message, LogLevel level, bool newLine, bool bold)
        {
            LogMessage?.Invoke(message, level, newLine, bold);
            textOutput.LogMessage(message, level, newLine, bold);
        }
        #endregion

        #region Helper Methods
        private void EnableConnectedControls(bool enabled)
        {
            disconnectButton.Enabled = enabled;
            readInfoButton.Enabled = enabled;
            authBypassCombo.Enabled = enabled;
            authBypassButton.Enabled = enabled;
            formatButton.Enabled = enabled;
            downloadButton.Enabled = enabled;
            rebootButton.Enabled = enabled;
            
            connectButton.Enabled = !enabled;
        }

        private void UpdateUIState()
        {
            // Update UI based on operation state
        }

        private void LoadAvailableResources()
        {
            try
            {
                // Load payloads
                var payloads = payloadManager.GetAvailablePayloads();
                LogMessage($"Loaded {payloads.Count} payload files", LogLevel.Info);

                // Load preloaders
                var preloaders = payloadManager.GetAvailablePreloaders();
                LogMessage($"Loaded {preloaders.Count} preloader files", LogLevel.Info);

                // Load DA files
                var daFiles = payloadManager.GetAvailableDownloadAgents();
                LogMessage($"Loaded {daFiles.Count} DA files", LogLevel.Info);

                // Load loaders
                var loaders = payloadManager.GetAvailableLoaders();
                LogMessage($"Loaded {loaders.Count} loader files", LogLevel.Info);
            }
            catch (Exception ex)
            {
                LogMessage($"Error loading resources: {ex.Message}", LogLevel.Error);
            }
        }

        private void RefreshDeviceList()
        {
            try
            {
                var devices = usbManager.GetConnectedMediaTekDevices();
                LogMessage($"Found {devices.Count} MediaTek device(s)", LogLevel.Info);

                foreach (var device in devices)
                {
                    LogMessage($"Device: {device}", LogLevel.Info);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error refreshing device list: {ex.Message}", LogLevel.Error);
            }
        }

        private void UpdateProgress(int progress)
        {
            // Update progress bar or status
            this.InvokeIfRequired(() =>
            {
                // Update UI progress indicator
                LogMessage($"Progress: {progress}%", LogLevel.Info);
            });
        }

        private async Task ReadDeviceInformation()
        {
            try
            {
                var deviceInfo = mediaTekManager.ReadDeviceInfo();
                if (deviceInfo != null)
                {
                    deviceModelLabel.Text = $"Model: {deviceInfo.Model}";
                    chipsetLabel.Text = $"Chipset: {deviceInfo.Chipset}";
                    preloaderLabel.Text = $"Preloader: Connected";
                    LogMessage($"Device Model: {deviceInfo.Model}", LogLevel.Success);
                    LogMessage($"Chipset: {deviceInfo.Chipset}", LogLevel.Success);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error reading device info: {ex.Message}", LogLevel.Error);
            }
        }

        private void LogToOutput(string message, LogLevel level = LogLevel.Info, bool newLine = true, bool bold = false)
        {
            textOutput?.LogMessage(message, level, newLine, bold);
        }

        private void LoadScatterFile(string filePath)
        {
            try
            {
                romListBox.Items.Clear();

                // Load scatter file using ScatterFileManager
                if (scatterManager.LoadScatterFile(filePath))
                {
                    // Populate ROM list with actual entries from scatter file
                    foreach (var romEntry in scatterManager.RomEntries)
                    {
                        bool isChecked = romEntry.IsDownload && romEntry.IsCriticalPartition;
                        romListBox.Items.Add(romEntry, isChecked);
                    }

                    // Validate ROM files
                    bool allValid = scatterManager.ValidateRomFiles();
                    if (!allValid)
                    {
                        LogMessage("Some ROM files are missing", LogLevel.Warning);
                    }

                    LogMessage($"Scatter file loaded: {Path.GetFileName(filePath)}", LogLevel.Success);
                    LogMessage($"Platform: {scatterManager.ScatterInfo.Platform}", LogLevel.Info);
                    LogMessage($"Found {scatterManager.RomEntries.Count} ROM entries", LogLevel.Info);
                }
                else
                {
                    LogMessage("Failed to parse scatter file", LogLevel.Error);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Failed to load scatter file: {ex.Message}", LogLevel.Error);
            }
        }
        #endregion
    }

    /// <summary>
    /// MediaTek device manager for SP Flash Tool operations using FlashToolLib.dll
    /// </summary>
    public class MediaTekManager : BasePlatformManager
    {
        #region Fields
        private DeviceConnectionState connectionState = DeviceConnectionState.Disconnected;
        private DeviceInfo currentDevice;
        private MediaTekDevice connectedDevice;
        private IntPtr flashToolHandle = IntPtr.Zero;
        private IntPtr downloadHandle = IntPtr.Zero;
        private UsbManager usbManager;
        private bool isAuthenticated = false;
        #endregion

        #region Constructor
        public MediaTekManager()
        {
            usbManager = new UsbManager();
            usbManager.LogMessage += (s, msg) => OnLogMessage(msg, LogLevel.Info);
        }
        #endregion

        #region Public Methods
        public async Task<bool> ConnectDevice(MediaTekDevice device)
        {
            try
            {
                OnLogMessage("Attempting to connect MediaTek device...", LogLevel.Info);
                OnLogMessage($"Device: {device}", LogLevel.Info);

                connectedDevice = device;

                // Open USB device
                if (!usbManager.OpenDevice(device))
                {
                    OnLogMessage("Failed to open USB device", LogLevel.Error);
                    return false;
                }

                // Connect using FlashToolLib
                var flashtoolArg = new FlashToolLibrary.FLASHTOOL_ARG();
                var connectArg = new FlashToolLibrary.FLASHTOOL_CONNECT_ARG();
                var bromInfo = new FlashToolLibrary.BROM_INFO();

                int result = FlashToolLibrary.FlashTool_Connect_BROM(
                    0, // COM port (0 for USB)
                    ref flashtoolArg,
                    ref flashToolHandle,
                    IntPtr.Zero, // Stop flag
                    true // USB mode
                );

                if (result == FlashToolLibrary.S_DONE)
                {
                    connectionState = DeviceConnectionState.DownloadMode;

                    // Read device information
                    await ReadDeviceInformation();

                    OnLogMessage("MediaTek device connected successfully", LogLevel.Success);
                    return true;
                }
                else
                {
                    OnLogMessage($"FlashTool connection failed: {FlashToolLibrary.GetErrorMessage(result)}", LogLevel.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnLogMessage($"Connection failed: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        public override bool ConnectDevice()
        {
            // Get first available MediaTek device
            var devices = usbManager.GetConnectedMediaTekDevices();
            if (devices.Any())
            {
                return ConnectDevice(devices.First()).Result;
            }
            return false;
        }

        public override bool DisconnectDevice()
        {
            try
            {
                // Disconnect FlashTool
                if (flashToolHandle != IntPtr.Zero)
                {
                    FlashToolLibrary.FlashTool_Disconnect(ref flashToolHandle);
                    flashToolHandle = IntPtr.Zero;
                }

                // Close USB device
                if (connectedDevice != null)
                {
                    usbManager.CloseDevice(connectedDevice);
                    connectedDevice = null;
                }

                connectionState = DeviceConnectionState.Disconnected;
                currentDevice = null;
                isAuthenticated = false;

                OnLogMessage("Device disconnected", LogLevel.Warning);
                return true;
            }
            catch (Exception ex)
            {
                OnLogMessage($"Disconnect error: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        public override DeviceConnectionState GetConnectionState()
        {
            return connectionState;
        }

        public MediaTekDevice GetCurrentDevice()
        {
            return connectedDevice;
        }

        private async Task ReadDeviceInformation()
        {
            try
            {
                OnLogMessage("Reading device information...", LogLevel.Info);

                // Get platform information
                var platformInfo = new FlashToolLibrary.PLATFORM_INFO();
                int result = FlashToolLibrary.DL_GetPlatformInfo(flashToolHandle, ref platformInfo);

                if (result == FlashToolLibrary.S_DONE)
                {
                    currentDevice = new DeviceInfo
                    {
                        Model = connectedDevice.ProductName,
                        Manufacturer = "MediaTek",
                        Chipset = platformInfo.m_szChipName,
                        Platform = PlatformType.MediaTek,
                        SerialNumber = connectedDevice.SerialNumber,
                        AndroidVersion = "Unknown"
                    };

                    OnLogMessage($"Chipset: {platformInfo.m_szChipName}", LogLevel.Success);
                    OnLogMessage($"Storage: {platformInfo.m_storage_type}", LogLevel.Info);
                }
                else
                {
                    OnLogMessage($"Failed to read platform info: {FlashToolLibrary.GetErrorMessage(result)}", LogLevel.Warning);
                }
            }
            catch (Exception ex)
            {
                OnLogMessage($"Error reading device info: {ex.Message}", LogLevel.Error);
            }
        }

        public DeviceInfo ReadDeviceInfo()
        {
            return currentDevice;
        }

        public async Task<bool> FormatDevice()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            try
            {
                OnLogMessage("Starting device format operation...", LogLevel.Warning);
                OnLogMessage("WARNING: This will erase all data on the device!", LogLevel.Error);

                var formatArg = new FlashToolLibrary.FLASHTOOL_FORMAT_ARG
                {
                    m_format_handle = IntPtr.Zero,
                    m_p_stopflag = IntPtr.Zero,
                    m_format_begin_addr = 0,
                    m_format_length = 0xFFFFFFFF, // Format entire device
                    m_part_id = 0
                };

                var bromInfo = new FlashToolLibrary.BROM_INFO();

                int result = FlashToolLibrary.FlashTool_Format(flashToolHandle, ref formatArg, ref bromInfo);

                if (result == FlashToolLibrary.S_DONE)
                {
                    OnLogMessage("Device format completed successfully", LogLevel.Success);
                    return true;
                }
                else
                {
                    OnLogMessage($"Format failed: {FlashToolLibrary.GetErrorMessage(result)}", LogLevel.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnLogMessage($"Format error: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        public async Task<bool> DownloadFirmware(ScatterFileManager scatterManager, List<RomEntry> selectedEntries)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            try
            {
                OnLogMessage($"Starting firmware download using scatter: {Path.GetFileName(scatterManager.ScatterFilePath)}", LogLevel.Info);
                OnLogMessage($"Selected {selectedEntries.Count} files for download", LogLevel.Info);

                // Create download handle
                int result = FlashToolLibrary.DL_Create(ref downloadHandle);
                if (result != FlashToolLibrary.S_DONE)
                {
                    OnLogMessage($"Failed to create download handle: {FlashToolLibrary.GetErrorMessage(result)}", LogLevel.Error);
                    return false;
                }

                // Load scatter file
                var scatterInfo = new FlashToolLibrary.SCATTER_Head_Info();
                result = FlashToolLibrary.DL_LoadScatter(downloadHandle, scatterManager.ScatterFilePath, ref scatterInfo);
                if (result != FlashToolLibrary.S_DONE)
                {
                    OnLogMessage($"Failed to load scatter file: {FlashToolLibrary.GetErrorMessage(result)}", LogLevel.Error);
                    return false;
                }

                // Configure download entries
                foreach (var entry in selectedEntries)
                {
                    OnLogMessage($"Configuring: {entry.DisplayName}", LogLevel.Info);
                    // Set enable/disable for each entry
                    FlashToolLibrary.DL_SetEnableAttr(downloadHandle, (ushort)entry.PartitionIndex, entry.IsEnabled);
                }

                // Perform download
                var downloadArg = new FlashToolLibrary.FLASHTOOL_DOWNLOAD_ARG
                {
                    m_dl_handle = downloadHandle,
                    m_p_stopflag = IntPtr.Zero
                };

                var bromInfo = new FlashToolLibrary.BROM_INFO();
                result = FlashToolLibrary.FlashTool_Download(flashToolHandle, ref downloadArg, ref bromInfo);

                if (result == FlashToolLibrary.S_DONE)
                {
                    OnLogMessage("Firmware download completed successfully", LogLevel.Success);
                    return true;
                }
                else
                {
                    OnLogMessage($"Download failed: {FlashToolLibrary.GetErrorMessage(result)}", LogLevel.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnLogMessage($"Download error: {ex.Message}", LogLevel.Error);
                return false;
            }
            finally
            {
                // Cleanup download handle
                if (downloadHandle != IntPtr.Zero)
                {
                    FlashToolLibrary.DL_Destroy(ref downloadHandle);
                    downloadHandle = IntPtr.Zero;
                }
            }
        }

        public async Task<bool> RebootDevice(string mode)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            try
            {
                OnLogMessage($"Rebooting device to {mode} mode...", LogLevel.Info);

                int result = FlashToolLibrary.FlashTool_Reboot(flashToolHandle, IntPtr.Zero);

                if (result == FlashToolLibrary.S_DONE)
                {
                    OnLogMessage("Device reboot initiated", LogLevel.Success);
                    return true;
                }
                else
                {
                    OnLogMessage($"Reboot failed: {FlashToolLibrary.GetErrorMessage(result)}", LogLevel.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                OnLogMessage($"Reboot error: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        #endregion
    }
}
