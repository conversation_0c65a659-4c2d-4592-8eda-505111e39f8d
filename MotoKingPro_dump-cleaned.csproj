<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">
  <PropertyGroup>
    <AssemblyName>MotoKingPro</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <OutputType>WinExe</OutputType>
    <UseWindowsForms>True</UseWindowsForms>
    <TargetFramework>net472</TargetFramework>
    <Prefer32Bit>True</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>app.ico</ApplicationIcon>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup />
  <ItemGroup>
    <Reference Include="System.Numerics" />
    <Reference Include="LibUsbDotNet.LibUsbDotNet" />
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\KsDumper11.v1.3.4\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Memory" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Core" />
    <Reference Include="SharpAdbClient" />
    <Reference Include="zxing" />
    <Reference Include="ICSharpCode.SharpZipLib" />
    <Reference Include="System.Management" />
    <Reference Include="K4os.Compression.LZ4.Streams" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Microsoft.Extensions.Logging.Abstractions" />
  </ItemGroup>
</Project>