using System;
using System.Collections.Generic;
using System.Runtime.ExceptionServices;
using System.Runtime.InteropServices;
using System.Security;

public class GClass32
{
	public struct GStruct1
	{
		public string AE190F3F;

		public string string_0;

		public string C32299A7;

		public string string_1;

		public string E32F1637;

		public string string_2;

		public string B63759B3;

		public string string_3;

		public string F03E0AAD;

		public string string_4;

		public string string_5;

		public string string_6;

		public string F832A212;

		public string string_7;
	}

	public struct GStruct2
	{
		public string string_0;

		public string string_1;

		public string string_2;

		public string C2A56590;

		public string DCACAD8A;

		public string string_3;

		public List<GStruct1> EE9D653C;
	}

	public enum F8BA6731 : uint
	{
		const_0 = 0u,
		E00F401D = 0u,
		const_2 = 1000u,
		const_3 = 1000u,
		AB059089 = 1001u,
		F6243731 = 1002u,
		const_6 = 1003u,
		const_7 = 1004u,
		const_8 = 1005u,
		E50D11BD = 1006u,
		const_10 = 1007u,
		const_11 = 1008u,
		A1A14EA7 = 1009u,
		const_13 = 1010u,
		const_14 = 1011u,
		const_15 = 1012u,
		B2AC7FAB = 1013u,
		const_17 = 1014u,
		const_18 = 1015u,
		C610243B = 1016u,
		const_20 = 1017u,
		const_21 = 1018u,
		C1B37837 = 1019u,
		const_23 = 1020u,
		const_24 = 1021u,
		const_25 = 1022u,
		F8B34E35 = 1023u,
		const_27 = 1024u,
		FAB66FB9 = 1025u,
		EA9601B1 = 1026u,
		const_30 = 1027u,
		const_31 = 1028u,
		D49E758D = 1029u,
		E989F00D = 1030u,
		const_34 = 1031u,
		EE352CA3 = 1032u,
		const_36 = 1033u,
		C808AA1B = 1034u,
		E621B08B = 1035u,
		const_39 = 1036u,
		const_40 = 1037u,
		const_41 = 1038u,
		const_42 = 1039u,
		const_43 = 1040u,
		const_44 = 1041u,
		A6BAAA05 = 1042u,
		DCA6FAAB = 1043u,
		const_47 = 2000u,
		F622CB23 = 2000u,
		const_49 = 2001u,
		A81E831C = 2002u,
		const_51 = 2003u,
		const_52 = 2004u,
		B19B489B = 2005u,
		F71FA5A9 = 2006u,
		D037509D = 2007u,
		const_56 = 2008u,
		const_57 = 2009u,
		const_58 = 2010u,
		const_59 = 2011u,
		const_60 = 2012u,
		B580EC14 = 2013u,
		const_62 = 2014u,
		const_63 = 2015u,
		const_64 = 2016u,
		D5012A98 = 2017u,
		const_66 = 2018u,
		ED09788F = 2019u,
		F22FF2B5 = 2020u,
		const_69 = 2021u,
		F929AA2C = 2022u,
		F3181D96 = 2023u,
		const_72 = 2024u,
		FD075B8F = 2025u,
		const_74 = 2026u,
		BA0B3890 = 2027u,
		const_76 = 2028u,
		FCA87BAF = 2029u,
		const_78 = 2030u,
		FFB549AC = 2031u,
		const_80 = 2032u,
		const_81 = 2033u,
		CEA15B07 = 2034u,
		const_83 = 2035u,
		const_84 = 2036u,
		F802D4BC = 2037u,
		EFBE1229 = 2038u,
		EFBFAC9D = 2039u,
		F0024E9E = 2040u,
		const_89 = 2041u,
		const_90 = 2042u,
		C6A41A1C = 2043u,
		const_92 = 2044u,
		const_93 = 2045u,
		const_94 = 2046u,
		F72A352A = 2047u,
		FDBFD698 = 2048u,
		C20E8F31 = 2049u,
		CB078906 = 3000u,
		const_99 = 3000u,
		const_100 = 3001u,
		const_101 = 3002u,
		DE9D322F = 3003u,
		EA10F199 = 3004u,
		E6340339 = 3005u,
		const_105 = 3006u,
		const_106 = 3007u,
		const_107 = 3008u,
		AC8C0C2C = 3009u,
		C0945320 = 3010u,
		const_110 = 3011u,
		const_111 = 3012u,
		C399763B = 3013u,
		const_113 = 3014u,
		const_114 = 3015u,
		BC344A2E = 3016u,
		FF34B906 = 3017u,
		const_117 = 3018u,
		CA095E07 = 3019u,
		const_119 = 3020u,
		const_120 = 3021u,
		const_121 = 3022u,
		B91631B3 = 3023u,
		const_123 = 3024u,
		F6BF1C9F = 3025u,
		const_125 = 3026u,
		AC339EAD = 3027u,
		F937CF99 = 3028u,
		DC955932 = 3029u,
		D0ABBDAB = 3030u,
		const_130 = 3031u,
		const_131 = 3032u,
		BB8E28B8 = 3033u,
		E0A47710 = 3034u,
		B686E9B4 = 3035u,
		const_135 = 3036u,
		const_136 = 3037u,
		const_137 = 3038u,
		const_138 = 3039u,
		const_139 = 3040u,
		const_140 = 3041u,
		const_141 = 3042u,
		const_142 = 3043u,
		const_143 = 3044u,
		const_144 = 3045u,
		FEA10282 = 3046u,
		const_146 = 3047u,
		A00BB40E = 3048u,
		const_148 = 3049u,
		const_149 = 3050u,
		A79C2933 = 3051u,
		const_151 = 3052u,
		const_152 = 3053u,
		const_153 = 3054u,
		B10323A2 = 3055u,
		C738C616 = 3056u,
		D1009B29 = 3057u,
		C983703A = 3058u,
		const_158 = 3059u,
		const_159 = 3060u,
		const_160 = 3061u,
		const_161 = 3062u,
		D9A74E00 = 3063u,
		const_163 = 3064u,
		E1924395 = 3065u,
		const_165 = 3066u,
		A191703B = 3067u,
		const_167 = 3068u,
		D19EE398 = 3069u,
		const_169 = 3070u,
		const_170 = 3071u,
		EEB87E8C = 3072u,
		E9933193 = 3073u,
		const_173 = 3074u,
		const_174 = 3075u,
		const_175 = 3076u,
		C31C6520 = 3077u,
		const_177 = 3078u,
		const_178 = 3079u,
		D801828B = 3080u,
		D2A3172E = 3081u,
		const_181 = 3082u,
		A831133D = 3083u,
		const_183 = 3084u,
		A5BA83B5 = 3085u,
		const_185 = 3086u,
		const_186 = 3087u,
		A09ABE91 = 3088u,
		const_188 = 3089u,
		CB8CF303 = 3090u,
		const_190 = 3091u,
		const_191 = 3092u,
		D11121B9 = 3093u,
		D29BF92F = 3094u,
		F7386918 = 3095u,
		const_195 = 3096u,
		const_196 = 3097u,
		const_197 = 3098u,
		BF076624 = 3099u,
		const_199 = 3100u,
		const_200 = 3101u,
		A01BC89A = 3102u,
		CFABD08B = 3103u,
		const_203 = 3104u,
		const_204 = 3105u,
		DDA43E90 = 3106u,
		FE1F779D = 3107u,
		const_207 = 3108u,
		const_208 = 3109u,
		const_209 = 3110u,
		D994EA88 = 3111u,
		const_211 = 3112u,
		const_212 = 3113u,
		const_213 = 3114u,
		EAA2BC95 = 3115u,
		const_215 = 3116u,
		E096B239 = 3117u,
		const_217 = 3118u,
		F536C110 = 3119u,
		const_219 = 3120u,
		const_220 = 3121u,
		const_221 = 3122u,
		const_222 = 3123u,
		BC876125 = 3124u,
		const_224 = 3125u,
		const_225 = 3126u,
		A3087F08 = 3127u,
		const_227 = 3128u,
		const_228 = 3129u,
		const_229 = 3130u,
		const_230 = 3131u,
		const_231 = 3132u,
		CF8A68A7 = 3133u,
		const_233 = 3134u,
		const_234 = 3135u,
		const_235 = 3136u,
		const_236 = 3137u,
		const_237 = 3138u,
		C7A16904 = 3139u,
		const_239 = 3140u,
		F59098A3 = 3141u,
		CABAEEB5 = 3142u,
		D88CFABA = 3143u,
		C216CBB2 = 3144u,
		E88E0992 = 3145u,
		FD338489 = 3146u,
		const_246 = 3147u,
		const_247 = 3148u,
		const_248 = 3149u,
		const_249 = 3150u,
		const_250 = 3151u,
		A6B0139C = 3152u,
		B4A9A604 = 3153u,
		const_253 = 3154u,
		B883189F = 3155u,
		FDA83C3C = 3156u,
		const_256 = 3157u,
		DB1EDDBD = 3158u,
		FE1E30BB = 3159u,
		const_259 = 3160u,
		D6BA1305 = 3161u,
		const_261 = 3162u,
		const_262 = 3163u,
		const_263 = 3164u,
		A5BBF8AE = 3165u,
		const_265 = 3166u,
		EFAB4F93 = 3167u,
		ABA2C22E = 3168u,
		const_268 = 3169u,
		const_269 = 3170u,
		const_270 = 3171u,
		const_271 = 3172u,
		const_272 = 3173u,
		const_273 = 3174u,
		const_274 = 3175u,
		B4AD8636 = 3176u,
		DC93CA8E = 3177u,
		const_277 = 3178u,
		B826CC11 = 3179u,
		const_279 = 3180u,
		const_280 = 3181u,
		const_281 = 3182u,
		D38E2BA5 = 3183u,
		const_283 = 3184u,
		const_284 = 3185u,
		const_285 = 3186u,
		C6128894 = 4000u,
		const_287 = 4000u,
		const_288 = 4001u,
		const_289 = 4002u,
		const_290 = 4003u,
		const_291 = 4004u,
		BE8D42A7 = 4005u,
		A4347D23 = 4006u,
		const_294 = 4007u,
		const_295 = 4008u,
		E01B9237 = 4009u,
		AFB6289E = 4010u,
		B184143B = 4011u,
		const_299 = 4012u,
		CBBE59A1 = 4013u,
		const_301 = 4014u,
		EB30F1BC = 4015u,
		const_303 = 4016u,
		const_304 = 4017u,
		C0225927 = 4018u,
		const_306 = 4019u,
		const_307 = 4020u,
		const_308 = 4021u,
		C3AEB42E = 4022u,
		const_310 = 4023u,
		E19CD1A4 = 4024u,
		const_312 = 4025u,
		const_313 = 4026u,
		const_314 = 4027u,
		const_315 = 4028u,
		A7B9B9B1 = 4029u,
		BB345BA8 = 4030u,
		const_318 = 4031u,
		const_319 = 4032u,
		const_320 = 4033u,
		EA016A2E = 4034u,
		const_322 = 4035u,
		E79C2B90 = 4036u,
		const_324 = 4037u,
		A8875EA6 = 4038u,
		AF061401 = 4039u,
		const_327 = 4040u,
		const_328 = 4041u,
		DD315729 = 4042u,
		const_330 = 4043u,
		const_331 = 4044u,
		const_332 = 4045u,
		const_333 = 4046u,
		const_334 = 4047u,
		A72A4318 = 4048u,
		const_336 = 4049u,
		const_337 = 4050u,
		CEAD2C23 = 4051u,
		const_339 = 4052u,
		const_340 = 4053u,
		const_341 = 4054u,
		const_342 = 4055u,
		const_343 = 4056u,
		const_344 = 4057u,
		const_345 = 4058u,
		const_346 = 4059u,
		const_347 = 4060u,
		const_348 = 5000u,
		D11AAD1D = 5000u,
		const_350 = 5001u,
		const_351 = 5002u,
		const_352 = 5003u,
		const_353 = 5004u,
		const_354 = 5005u,
		const_355 = 5006u,
		const_356 = 5007u,
		const_357 = 5008u,
		A9949517 = 5009u,
		const_359 = 5010u,
		const_360 = 5011u,
		D4A31881 = 5012u,
		const_362 = 5013u,
		const_363 = 5014u,
		const_364 = 5015u,
		const_365 = 5016u,
		F916D683 = 5017u,
		E2380B06 = 5018u,
		F1A0CD08 = 5019u,
		const_369 = 5020u,
		const_370 = 5021u,
		const_371 = 5022u,
		DC2DAB93 = 5023u,
		E0B91C27 = 5024u,
		const_374 = 5025u,
		const_375 = 5026u,
		const_376 = 5027u,
		D615B19A = 5028u,
		B3185A19 = 5029u,
		const_379 = 5030u,
		FC8D0106 = 5031u,
		E53AD525 = 5032u,
		const_382 = 5033u,
		FB96AB2D = 5034u,
		const_384 = 5035u,
		FF909F1B = 5036u,
		BA918480 = 5037u,
		CA1FA189 = 5038u,
		FB314FB8 = 5039u,
		C3369D31 = 5040u,
		const_390 = 5041u,
		const_391 = 5042u,
		const_392 = 5043u,
		B58D0BA1 = 5044u,
		const_394 = 5045u,
		const_395 = 5046u,
		const_396 = 5047u,
		const_397 = 5048u,
		const_398 = 5049u,
		ED1462B1 = 5050u,
		A0897513 = 5051u,
		const_401 = 5052u,
		const_402 = 5053u,
		const_403 = 5054u,
		const_404 = 5055u,
		DF221509 = 5056u,
		F2BB6199 = 5057u,
		FE3BD038 = 5058u,
		F3A6B8A4 = 5059u,
		D78E5A16 = 5060u,
		const_410 = 5061u,
		const_411 = 5062u,
		const_412 = 5063u,
		const_413 = 5064u,
		E70D511E = 5065u,
		DA9326BC = 5066u,
		FE88019A = 5067u,
		DD3D093C = 5068u,
		const_418 = 5069u,
		const_419 = 5070u,
		A61D9381 = 5071u,
		const_421 = 5072u,
		const_422 = 5073u,
		const_423 = 5074u,
		const_424 = 5075u,
		A010D124 = 5076u,
		const_426 = 5077u,
		CF0FE51C = 5078u,
		const_428 = 5079u,
		const_429 = 5080u,
		F1A8181A = 5081u,
		E786EA82 = 5082u,
		BB86CE0C = 5083u,
		FE3A67B5 = 5084u,
		D78C8287 = 5085u,
		const_435 = 5086u,
		B9AC5B92 = 6000u,
		const_437 = 6000u,
		const_438 = 6001u,
		A190D530 = 6002u,
		const_440 = 6003u,
		const_441 = 6004u,
		const_442 = 6005u,
		const_443 = 6006u,
		B4A87DB5 = 6007u,
		const_445 = 6008u,
		const_446 = 6009u,
		const_447 = 6010u,
		const_448 = 6011u,
		const_449 = 6012u,
		const_450 = 6013u,
		BB8968B1 = 6014u,
		const_452 = 6015u,
		const_453 = 6016u,
		C90DB40B = 6017u,
		const_455 = 6018u,
		const_456 = 6019u,
		B4B00805 = 6020u,
		const_458 = 6021u,
		const_459 = 6022u,
		const_460 = 6023u,
		const_461 = 6024u,
		B5AB8B9C = 6025u,
		F1B74302 = 6026u,
		const_464 = 6027u,
		const_465 = 6028u,
		AD922020 = 6029u,
		const_467 = 6030u,
		const_468 = 6031u,
		const_469 = 6032u,
		const_470 = 6033u,
		const_471 = 6034u,
		const_472 = 6035u,
		C83530BF = 6036u,
		const_474 = 6037u,
		const_475 = 6038u,
		E58EDFAD = 6039u,
		const_477 = 6040u,
		const_478 = 6041u,
		const_479 = 6042u,
		D6067E3A = 6043u,
		DC3F9B32 = 6044u,
		const_482 = 6045u,
		const_483 = 6046u,
		const_484 = 6047u,
		F5318C39 = 6048u,
		const_486 = 6049u,
		const_487 = 6050u,
		const_488 = 6051u,
		const_489 = 6052u,
		D8304EB1 = 6053u,
		FBB8583D = 6054u,
		const_492 = 6055u,
		D4A7CB80 = 6056u,
		D439B602 = 6057u,
		const_495 = 6058u,
		B83213B2 = 6059u,
		const_497 = 6060u,
		const_498 = 6061u,
		const_499 = 6062u,
		D9BBA011 = 6063u,
		const_501 = 6064u,
		const_502 = 6065u,
		const_503 = 6066u,
		const_504 = 6067u,
		F7269FA2 = 6068u,
		const_506 = 6069u,
		const_507 = 6070u,
		CF112EAE = 6071u,
		const_509 = 6072u,
		CF996235 = 6073u,
		F42B1BB4 = 6074u,
		const_512 = 6075u,
		const_513 = 6076u,
		const_514 = 6077u,
		BBB67FB1 = 6078u,
		const_516 = 6079u,
		BB988303 = 6080u,
		const_518 = 6081u,
		const_519 = 6082u,
		FBA6428D = 6083u,
		D9A74DAE = 6084u,
		F8AEC897 = 6085u,
		const_523 = 6086u,
		B2AAC110 = 6087u,
		const_525 = 6088u,
		const_526 = 6089u,
		B48FE394 = 6090u,
		D9094023 = 6091u,
		const_529 = 6092u,
		const_530 = 6093u,
		const_531 = 6094u,
		D2BB0984 = 6095u,
		B33D07B1 = 6096u,
		const_534 = 6097u,
		const_535 = 6098u,
		const_536 = 6099u,
		const_537 = 6100u,
		const_538 = 6101u,
		D91CCA04 = 6102u,
		const_540 = 6103u,
		const_541 = 6104u,
		B2348BAB = 6105u,
		const_543 = 6106u,
		A015FF19 = 6107u,
		const_545 = 6108u,
		const_546 = 6109u,
		const_547 = 6110u,
		const_548 = 6111u,
		E79FD78C = 6112u,
		D08A5ABD = 6113u,
		F112DA8C = 6114u,
		DBBCA516 = 6115u,
		const_553 = 6116u,
		EF2D2632 = 6117u,
		D6823B9C = 6118u,
		const_556 = 6119u,
		const_557 = 6120u,
		A1A0B0BC = 6121u,
		ADA76C18 = 6122u,
		E6879D8B = 6123u,
		const_561 = 6124u,
		const_562 = 6125u,
		const_563 = 6126u,
		const_564 = 6127u,
		const_565 = 6128u,
		B68D08B1 = 6129u,
		DB105AA1 = 7000u,
		const_568 = 7000u,
		const_569 = 7001u,
		const_570 = 7002u,
		BE851412 = 7003u,
		const_572 = 7004u,
		const_573 = 10001u,
		const_574 = 10002u,
		const_575 = 10003u,
		F003B119 = 10004u,
		const_577 = 10005u,
		const_578 = 10006u,
		C983E732 = 10007u,
		const_580 = 10008u,
		const_581 = 3221291009u,
		const_582 = 3221291010u,
		FB02282B = 3221291011u,
		const_584 = 3221291012u,
		const_585 = 3221291013u,
		DD3A0616 = 3221291014u,
		const_587 = 3221291015u,
		B02EFBA5 = 3221291016u,
		const_589 = 3221291017u,
		const_590 = 3221291018u,
		const_591 = 3221291019u,
		const_592 = 3221291020u,
		F897D5AC = 3221291021u,
		const_594 = 3221291022u,
		const_595 = 3221291023u,
		const_596 = 3221291024u,
		DE871532 = 3221291025u,
		D8B4671E = 3221291026u,
		const_599 = 3221291027u,
		const_600 = 3221553153u,
		DCA6782C = 3221553154u,
		const_602 = 3221553155u,
		DF22C998 = 3221553156u,
		const_604 = 3221553157u,
		AB87D5A8 = 3221553158u,
		const_606 = 3221553159u,
		EA96F11D = 3221553160u,
		const_608 = 3221553161u,
		const_609 = 3221553162u,
		A8058C8F = 3221553163u,
		const_611 = 3221553164u,
		const_612 = 3221553165u,
		F0A61B99 = 3221618689u,
		const_614 = 3221618690u,
		const_615 = 3221618691u,
		const_616 = 3221618692u,
		EA20AD04 = 3221618693u,
		E10409B7 = 3221618694u,
		const_619 = 1074135041u,
		const_620 = 3221749761u,
		const_621 = 3221684225u,
		const_622 = 3221684226u,
		const_623 = 3221684227u,
		const_624 = 3221684228u,
		const_625 = 3221684229u,
		A5B41ABE = 3221422081u,
		const_627 = 3221422082u,
		E11FE614 = 3221422083u,
		B4BEF18B = 3221422084u,
		DDB2A52B = 3221422085u,
		const_631 = 3221422086u,
		const_632 = 3221422087u,
		B1AFAF0A = 3221422088u,
		const_634 = 3221422089u,
		const_635 = 3221422090u,
		const_636 = 3221422091u,
		const_637 = 3221422092u,
		DA303229 = 3221422093u,
		E68D8E19 = 3221422094u,
		const_640 = 3221422095u,
		const_641 = 3221422096u,
		F22343A0 = 3221422097u,
		E68E9F1D = 3221422098u,
		const_644 = 3221422099u,
		const_645 = 3221422100u,
		const_646 = 3221422101u,
		const_647 = 3221422102u,
		const_648 = 3221422103u,
		const_649 = 3221422104u,
		const_650 = 3221422105u,
		const_651 = 3221422106u,
		const_652 = 3221422107u,
		const_653 = 3221422108u,
		const_654 = 3221422109u,
		const_655 = 1074003969u,
		DBBC1DA2 = 1074003970u,
		const_657 = 1074003971u,
		const_658 = 1074003972u,
		const_659 = 1074003973u,
		const_660 = 1074003974u,
		E08ADB88 = 3221487617u,
		E5226208 = 3221487618u,
		AE931C16 = 3221487619u,
		const_664 = 3221487620u,
		const_665 = 3221487621u,
		B7A26E85 = 3221487622u,
		const_667 = 3221487623u,
		const_668 = 3221487624u,
		CA96FD14 = 3221487625u,
		const_670 = 3221487626u,
		const_671 = 3221487627u,
		const_672 = 3221487628u,
		const_673 = 3221487629u,
		const_674 = 3221487630u,
		const_675 = 3221487664u,
		const_676 = 3221487680u,
		const_677 = 3221487681u,
		const_678 = 3221487682u,
		const_679 = 3221487683u,
		C83109A6 = 3221487684u,
		const_681 = 3221487685u,
		AD29FA21 = 3221487686u,
		const_683 = 3221487696u,
		AD0EBD8E = 3221487872u,
		const_685 = 3221487874u,
		FC02BBA9 = 3221488128u,
		const_687 = 3221488129u,
		DC057F9B = 3221488130u,
		C8BB6B37 = 3221488131u,
		DF08A69E = 3221488132u,
		BF919809 = 3221488133u,
		C8BEE83A = 3221488134u,
		const_693 = 3221488135u,
		const_694 = 3221488136u,
		const_695 = 3221488137u,
		const_696 = 3221488138u,
		E929C3A0 = 3221488139u,
		const_698 = 3221488140u,
		E720B40F = 3221488141u,
		const_700 = 3221488142u,
		A69EE22A = 3221488143u,
		const_702 = 3221488144u,
		const_703 = 3221487872u,
		const_704 = 1074003970u,
		CC8188AC = 3221487874u,
		const_706 = 1074003971u,
		const_707 = 3221487872u,
		C2B36D9B = 1074003970u,
		const_709 = 3221487874u,
		const_710 = 1074003971u,
		const_711 = 3221356545u,
		const_712 = 3221356546u,
		const_713 = 3221356547u,
		B50326B2 = 3221356548u,
		E53F9028 = 3221356549u,
		const_716 = 3221356550u,
		E5B71EB8 = 3221356551u,
		const_718 = 3221356552u,
		const_719 = 3221356553u,
		const_720 = 3221356554u,
		const_721 = 3221356555u,
		const_722 = 3221356556u,
		A53EAC21 = 3221356557u,
		const_724 = 3221356558u,
		B9355AAC = 3221356559u,
		const_726 = 3221356560u,
		C715600D = 3221356561u,
		const_728 = 3221356562u,
		const_729 = 3221356563u,
		CF807084 = 3221356564u,
		const_731 = 3221356565u,
		E2A3C70B = 3221356566u,
		E00E7D09 = 3221356567u,
		const_734 = 3221356568u,
		const_735 = 3221356569u,
		AE1C9C99 = 3221356570u,
		const_737 = 3221356571u,
		const_738 = 3221356572u,
		const_739 = 3221356573u,
		const_740 = 3221356574u,
		const_741 = 3221356575u,
		const_742 = 3221356576u,
		BD90A620 = 3221356577u,
		const_744 = 3221356578u,
		const_745 = 3221356579u,
		const_746 = 3221356580u,
		const_747 = 3221356581u,
		const_748 = 3221356582u,
		const_749 = 3221356583u,
		const_750 = 3221356584u,
		ABB2118A = 3221356585u,
		A237D734 = 3221356586u,
		const_753 = 3221356587u,
		const_754 = 3221356588u,
		const_755 = 3221356589u,
		F2827E24 = 3221356590u,
		A58F2F21 = 3221356591u,
		const_758 = 3221356592u,
		const_759 = 3221356593u,
		const_760 = 3221356594u,
		const_761 = 3221356595u,
		C5B88E37 = 3221356596u,
		const_763 = 3221356597u,
		const_764 = 3221356598u,
		const_765 = 3221356599u,
		const_766 = 3221356600u,
		D32602A4 = 3221356601u,
		const_768 = 3221356608u,
		const_769 = 3221356609u,
		const_770 = 3221356610u,
		const_771 = 3221356611u,
		A52AF106 = 3221356612u,
		BA15F492 = 3221356613u,
		const_774 = 3221356614u,
		const_775 = 3221356615u,
		const_776 = 3221356616u,
		F01337BA = 3221356617u,
		A92B0991 = 3221356618u,
		const_779 = 3221356619u,
		const_780 = 3221356620u,
		const_781 = 3221356621u,
		const_782 = 3221356622u,
		const_783 = 3221356623u,
		A80A0C98 = 3221356624u,
		const_785 = 3221356625u,
		BB048B91 = 3221356626u,
		const_787 = 3221356627u,
		F0239116 = 2147483647u
	}

	public enum GEnum2
	{
		const_0 = 0,
		const_1 = 1,
		BDBD04B6 = 2,
		const_3 = 4,
		const_4 = 5,
		const_5 = 6,
		const_6 = 7,
		const_7 = 8,
		EB9B5580 = 9,
		const_9 = 10,
		ED01C03B = 11,
		const_11 = 12,
		BA35D422 = 13,
		const_13 = 14,
		BE23FB16 = 15,
		const_15 = 16,
		const_16 = 17,
		C1985B99 = 18,
		const_18 = 19,
		FE880016 = 20,
		const_20 = 21,
		D5370027 = 22,
		const_22 = 23,
		B7092D8E = 24,
		EB1DE700 = 25,
		const_25 = 26,
		const_26 = 27,
		const_27 = 28,
		const_28 = 29,
		const_29 = 30,
		const_30 = 32,
		const_31 = 33,
		const_32 = 34,
		const_33 = 35,
		BC91EB14 = 128,
		D09B4B09 = 129,
		const_36 = 130,
		const_37 = 131,
		C9BB1698 = 132,
		const_39 = 133,
		FE03D2B6 = 134,
		A8817F91 = 135,
		const_42 = 136,
		F7226A8A = 137,
		const_44 = 138,
		const_45 = 139,
		const_46 = 140,
		const_47 = 141,
		F22A7E3B = 142,
		const_49 = 143,
		const_50 = 144,
		const_51 = 145,
		D632E734 = 146,
		F7867927 = 147,
		const_54 = 148,
		FF067299 = 149,
		const_56 = 150,
		const_57 = 151,
		AD884E33 = 152,
		const_59 = 153,
		const_60 = 154,
		C69D2732 = 135,
		const_62 = 155,
		const_63 = 156,
		EDB28181 = 157,
		const_65 = 158,
		const_66 = 159,
		E8043021 = 160,
		D3836CAF = 161,
		CF94D92F = 180,
		const_70 = 181,
		const_71 = 182,
		EE2A8632 = 183,
		D5879033 = 184,
		const_74 = 185,
		C8B1C736 = 186,
		E00A592E = 187,
		const_77 = 188,
		ED81AD1F = 189,
		const_79 = 190,
		F6B2E006 = 191,
		const_81 = 192,
		const_82 = 193,
		B5ACF22D = 194,
		const_84 = 195,
		const_85 = 196,
		const_86 = 197,
		const_87 = 198,
		const_88 = 199,
		const_89 = 200,
		E2921835 = 201,
		C6BC539C = 202,
		const_92 = 203,
		const_93 = 204,
		const_94 = 205,
		const_95 = 206,
		F0884E84 = 207,
		E396F49E = 208,
		const_98 = 209,
		B428AE13 = 210,
		F58A3498 = 211,
		A02B031A = 212,
		const_102 = 213,
		const_103 = 214,
		DBA51033 = 215,
		ED3D278B = 216,
		B91C791D = 254,
		D724E4A3 = 255
	}

	public enum GEnum3
	{
		const_0,
		D223CA94
	}

	public enum GEnum4
	{
		EB22A422,
		F21F75A1,
		C081809F,
		const_3
	}

	public enum A115CC23
	{
		const_0 = 0,
		const_1 = 1,
		E234DF89 = 2,
		A08A7516 = 3,
		const_4 = 4,
		const_5 = 5,
		ABBF471D = 6,
		const_7 = 7,
		E7A47206 = 8,
		const_9 = 8,
		const_10 = 9
	}

	public enum D2A9FC0A
	{
		const_0,
		const_1,
		const_2,
		const_3
	}

	public enum B8A22E0D
	{
		const_0 = 1,
		const_1 = 7,
		const_2 = 8,
		const_3 = 9,
		B735401D = 16,
		const_5 = 17,
		const_6 = 18,
		E7290F98 = 19,
		const_8 = 32,
		const_9 = 255
	}

	[StructLayout(LayoutKind.Sequential, Size = 1)]
	public struct GStruct3
	{
	}

	public struct GStruct4
	{
		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
		public char[] char_0;

		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
		public char[] char_1;

		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
		public char[] char_2;

		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 128)]
		public char[] ABBF2FAB;

		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
		public char[] A6940B99;

		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
		public char[] char_3;

		public uint uint_0;

		public bool bool_0;

		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
		public char[] char_4;

		public bool bool_1;

		public bool bool_2;
	}

	public struct GStruct5
	{
		public bool bool_0;

		public uint uint_0;

		public uint uint_1;

		public uint AEA32687;

		public uint DC06FC0F;

		public uint uint_2;

		public uint FB32F635;

		public uint uint_3;

		public uint uint_4;

		public uint FF1ABC26;

		public uint uint_5;

		public uint uint_6;

		public uint uint_7;

		public uint EA36F60C;

		public uint C6A66B10;

		public uint uint_8;

		public uint uint_9;

		public uint uint_10;

		public uint uint_11;

		public uint uint_12;

		public uint EA1B7521;

		public uint uint_13;

		public uint E6141C1B;

		public uint uint_14;

		public uint uint_15;

		public uint uint_16;

		public uint uint_17;

		public uint D5A5A797;

		public uint C213052B;

		public uint DAA441AD;

		public uint uint_18;

		public uint A42BFE3E;

		public uint uint_19;

		public uint F508E93B;

		public uint uint_20;

		public uint uint_21;

		public uint E0215619;

		public uint uint_22;

		public uint B111AD10;

		public uint uint_23;

		public GStruct6 gstruct6_0;

		public GStruct7 gstruct7_0;
	}

	public struct GStruct6
	{
		public uint uint_0;

		public uint uint_1;

		public uint D9B63636;

		public uint uint_2;

		public uint B5BF1995;

		public uint uint_3;

		public uint uint_4;

		public uint uint_5;

		public uint D603F1AB;

		public uint F3B559A8;

		public uint E2029309;

		public uint AD8303A7;

		public uint C031DC11;

		public uint E721DC80;

		public uint F8B98222;
	}

	public enum F299BF3A
	{
		const_0 = 1,
		const_1 = 2,
		const_2 = 3,
		CEA07C9E = 4,
		const_4 = 5,
		const_5 = 254,
		const_6 = 255
	}

	public struct GStruct7
	{
		public uint E8B3C127;

		public uint uint_0;

		public uint uint_1;

		public uint E52BA89A;

		public uint uint_2;

		public uint B4A1E229;

		public uint uint_3;

		public uint AC177D15;

		public uint A7873D8E;

		public uint uint_4;

		public uint uint_5;

		public uint A511070C;

		public uint C636CD04;

		public uint uint_6;

		public uint uint_7;

		public uint E41EAA81;
	}

	public delegate int GDelegate3(IntPtr BA2E4BBE, string string_0);

	public delegate int AA141FB9(byte byte_0, ulong A0980B8A, ulong ulong_0, IntPtr A089B714);

	public struct GStruct8
	{
		public ushort ushort_0;

		public uint uint_0;

		public GDelegate3 FAA613B7;

		public IntPtr F5834D89;

		public AA141FB9 aa141FB9_0;

		public IntPtr EE93652E;

		public IntPtr intptr_0;
	}

	public struct GStruct9
	{
		public byte byte_0;

		public byte C42E898B;

		public byte byte_1;

		public byte byte_2;

		public GEnum2 A6A736AE;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 32)]
		public string E3301C13;

		public ushort ushort_0;

		public ushort ushort_1;

		public ushort ushort_2;

		public F299BF3A CAA976A0;

		public byte byte_3;

		public byte byte_4;

		public F8BA6731 f8BA6731_0;

		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 2)]
		public A115CC23[] a115CC23_0;

		public ushort ushort_3;

		public uint uint_0;

		public ushort C93E033C;

		public ushort F19BDB24;

		public ushort ushort_4;

		public ushort ushort_5;

		public F8BA6731 f8BA6731_1;

		public uint uint_1;

		public F8BA6731 A616FDA0;

		public A115CC23 a115CC23_1;

		public ushort ushort_6;

		public ulong ulong_0;

		public ushort C736C60B;

		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 16)]
		public ushort[] BC282C24;

		public ushort ushort_7;

		public ushort ushort_8;

		public ushort ushort_9;

		public byte byte_5;

		public byte byte_6;

		public byte D38DA5AE;

		public F8BA6731 C026F613;

		public uint A7352503;

		public F8BA6731 f8BA6731_2;

		public GEnum4 genum4_0;

		public A115CC23 a115CC23_2;

		public ulong ulong_1;

		public byte byte_7;

		public F8BA6731 f8BA6731_3;

		public F8BA6731 f8BA6731_4;

		public uint D21CB610;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
		public string string_0;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
		public string string_1;

		private IntPtr intptr_0;

		public F8BA6731 BB9F2EAF;

		public ulong C4AF1418;

		public ulong ulong_2;

		public ulong A9A821AA;

		public ulong ulong_3;

		public ulong ulong_4;

		public ulong ulong_5;

		public ulong ulong_6;

		public ulong ulong_7;

		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
		public uint[] AE257089;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 8)]
		public string BC8C21AD;

		public F8BA6731 f8BA6731_5;

		public ulong ulong_8;

		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
		public uint[] B9B7CB0F;

		public F8BA6731 C791EEA6;

		public ulong ulong_9;

		public ulong ulong_10;

		public ulong ulong_11;

		public ushort ushort_10;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 17)]
		public string B7A6F597;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 5)]
		public string B2975A24;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 129)]
		public string C1B201BB;
	}

	public enum GEnum5
	{
		A3345328,
		F7B340B7,
		EE0A7B25,
		BA9BB4BE,
		const_4,
		const_5,
		DCB14B96,
		const_7
	}

	public struct AA12E401
	{
		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 64)]
		public string string_0;

		public ulong ulong_0;

		public GEnum3 genum3_0;

		public ulong BE815025;

		public ulong ulong_1;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
		public string AE1B8782;

		public ulong ulong_2;

		public ushort ushort_0;

		public B8A22E0D b8A22E0D_0;

		public bool bool_0;

		public bool bool_1;

		public bool bool_2;

		public GEnum5 genum5_0;

		public uint uint_0;

		public ulong ulong_3;

		public bool BB839484;
	}

	public struct GStruct10
	{
		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
		public string string_0;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
		public string string_1;

		public GEnum2 BAAE01BE;

		public ABA18D2C.GEnum6 genum6_0;
	}

	public static IntPtr intptr_0;

	public static IntPtr B0AD49A4;

	private static IntPtr C810E395;

	private static IntPtr BC887AB2;

	private static IntPtr D139808E;

	private static IntPtr intptr_1;

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_Create(ref IntPtr A623D223);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_Rom_UnloadAll(IntPtr intptr_2);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_Destroy(ref IntPtr intptr_2);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_LoadScatter(IntPtr intptr_2, byte[] byte_0, byte[] E6A0A8AF);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_BL_Load(IntPtr DB14781B, ref byte[] D80281A3);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_BL_EXT_Load(IntPtr intptr_2, ref byte[] byte_0);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_AutoLoadRomImages(IntPtr intptr_2, byte[] byte_0);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_Rom_SetEnableAttr(IntPtr intptr_2, ushort F2AA9D08, bool bool_0);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_SetChecksumLevel(IntPtr intptr_2, D2A9FC0A C308CB0A);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_VerifyROMMemBuf(IntPtr BA92451E, ref GStruct8 gstruct8_0, ref GStruct3 DD28091E);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_CreateList(ref IntPtr D926ABBC);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_DestroyList(ref IntPtr intptr_2);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_AddHandleToList(IntPtr FA14EA3E, IntPtr intptr_2);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	public static extern int DL_GetScatterVersion(IntPtr intptr_2, ref string string_0);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	public static extern int DL_GetScatterInfo(IntPtr D58EF738, ref GStruct4 CCB86B84);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int FlashTool_GetDLHandle(IntPtr intptr_2, ref IntPtr E60A5AA8);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_Rom_Load(IntPtr CFB2BAA2, ushort ushort_0, byte[] byte_0);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern int DL_Rom_Unload(IntPtr intptr_2, ushort ushort_0);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	private static extern IntPtr DL_GetDRAMSetting(IntPtr DABB8737, ref GStruct5 gstruct5_0, ref GStruct9 gstruct9_0);

	[SecurityCritical]
	[HandleProcessCorruptedStateExceptions]
	public static bool smethod_0(ushort ushort_0, string string_0)
	{
		if (DL_Rom_Unload(intptr_0, ushort_0) == 0)
		{
			int num = DL_Rom_Load(intptr_0, ushort_0, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), string_0));
			if (num == 0)
			{
				return true;
			}
			GClass29.smethod_0(num);
		}
		return false;
	}

	[SecurityCritical]
	[HandleProcessCorruptedStateExceptions]
	public static bool smethod_1(ushort C43D2387, string F70CA41C)
	{
		if (DL_Rom_Load(intptr_0, C43D2387, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), F70CA41C)) == 0)
		{
			return true;
		}
		return false;
	}

	public static bool smethod_2(string string_0)
	{
		byte[] byte_ = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), string_0);
		if (DL_BL_EXT_Load(intptr_0, ref byte_) == 0)
		{
			return true;
		}
		return false;
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static bool smethod_3(ushort ushort_0, bool bool_0)
	{
		if (DL_Rom_SetEnableAttr(intptr_0, ushort_0, bool_0) == 0)
		{
			return true;
		}
		return false;
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static void C48B5409()
	{
		IntPtr A623D = default(IntPtr);
		IntPtr A623D2 = default(IntPtr);
		IntPtr D926ABBC = default(IntPtr);
		int num = -1;
		string aBB24F = "D:\\刷机包\\G7\\begonia_factory_images_FACTORY-BEGONIA-1123_9.0\\images\\MT6785_Android_scatter.txt";
		num = DL_Create(ref A623D);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
		num = DL_Create(ref A623D2);
		if (num != 0)
		{
			string f097F68F2 = GClass29.smethod_0(num);
			f097F68F2 = GClass29.smethod_1(f097F68F2, num);
		}
		num = DL_CreateList(ref D926ABBC);
		if (num != 0)
		{
			string f097F68F3 = GClass29.smethod_0(num);
			f097F68F3 = GClass29.smethod_1(f097F68F3, num);
		}
		num = DL_LoadScatter(A623D, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), aBB24F), null);
		if (num != 0)
		{
			string f097F68F4 = GClass29.smethod_0(num);
			f097F68F4 = GClass29.smethod_1(f097F68F4, num);
		}
		num = DL_AutoLoadRomImages(A623D, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), aBB24F));
		if (num != 0)
		{
			string f097F68F5 = GClass29.smethod_0(num);
			f097F68F5 = GClass29.smethod_1(f097F68F5, num);
		}
		num = DL_LoadScatter(A623D2, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), aBB24F), null);
		if (num != 0)
		{
			string f097F68F6 = GClass29.smethod_0(num);
			f097F68F6 = GClass29.smethod_1(f097F68F6, num);
		}
		num = DL_AutoLoadRomImages(A623D2, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), aBB24F));
		if (num != 0)
		{
			string f097F68F7 = GClass29.smethod_0(num);
			f097F68F7 = GClass29.smethod_1(f097F68F7, num);
		}
		num = DL_AddHandleToList(D926ABBC, A623D);
		if (num != 0)
		{
			string f097F68F8 = GClass29.smethod_0(num);
			f097F68F8 = GClass29.smethod_1(f097F68F8, num);
		}
		num = DL_AddHandleToList(D926ABBC, A623D2);
		if (num != 0)
		{
			string f097F68F9 = GClass29.smethod_0(num);
			f097F68F9 = GClass29.smethod_1(f097F68F9, num);
		}
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static void D81DDB0C()
	{
		string string_ = Class607.B630A78B.object_0[720](GClass112.********, "\\bin\\lib\\FlashtoollibEx.dll");
		string string_2 = Class607.B630A78B.object_0[720](GClass112.********, "\\bin\\lib\\FlashToolLib.v1.dll");
		string string_3 = Class607.B630A78B.object_0[720](GClass112.********, "\\bin\\lib\\FlashToolLib.dll");
		string string_4 = Class607.B630A78B.object_0[720](GClass112.********, "\\bin\\lib\\SLA_Challenge.dll");
		if (Class607.B630A78B.object_0[1182](C810E395, Class607.B630A78B.object_0[120]()))
		{
			C810E395 = GClass28.LoadLibrary(string_2);
		}
		if (Class607.B630A78B.object_0[1182](BC887AB2, Class607.B630A78B.object_0[120]()))
		{
			BC887AB2 = GClass28.LoadLibrary(string_);
		}
		if (Class607.B630A78B.object_0[1182](D139808E, Class607.B630A78B.object_0[120]()))
		{
			D139808E = GClass28.LoadLibrary(string_4);
		}
		if (Class607.B630A78B.object_0[1182](intptr_1, Class607.B630A78B.object_0[120]()))
		{
			intptr_1 = GClass28.LoadLibrary(string_3);
		}
		int num = -1;
		num = DL_Create(ref intptr_0);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
	}

	public static void C691B932()
	{
		int num = DL_Rom_UnloadAll(intptr_0);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static void AC30E4A1()
	{
		int num = -1;
		if (Class607.B630A78B.object_0[76](ref intptr_0) != 0L)
		{
			num = DL_Rom_UnloadAll(intptr_0);
			if (num != 0)
			{
				string f097F68F = GClass29.smethod_0(num);
				f097F68F = GClass29.smethod_1(f097F68F, num);
			}
			num = DL_Destroy(ref intptr_0);
			if (num != 0)
			{
				string f097F68F2 = GClass29.smethod_0(num);
				f097F68F2 = GClass29.smethod_1(f097F68F2, num);
			}
		}
	}

	[SecurityCritical]
	[HandleProcessCorruptedStateExceptions]
	public static bool smethod_4(D2A9FC0A D5B07924)
	{
		if (DL_SetChecksumLevel(intptr_0, D5B07924) == 0)
		{
			return true;
		}
		return false;
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static void smethod_5(int int_0, string string_0)
	{
		int num = DL_LoadScatter(intptr_0, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), string_0), null);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
	}

	[SecurityCritical]
	[HandleProcessCorruptedStateExceptions]
	public static void smethod_6(int int_0, string FFB8DB24)
	{
		int num = -1;
		num = DL_AutoLoadRomImages(intptr_0, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), FFB8DB24));
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static GStruct10 smethod_7(int int_0)
	{
		int num = -1;
		GStruct10 gstruct10_ = default(GStruct10);
		num = ABA18D2C.DL_GetPlatformInfo(intptr_0, ref gstruct10_);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
		return gstruct10_;
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static GStruct4 smethod_8()
	{
		GStruct4 CCB86B = default(GStruct4);
		DL_GetScatterInfo(intptr_0, ref CCB86B);
		return CCB86B;
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static List<AA12E401> smethod_9(int F19EB21E)
	{
		List<AA12E401> list = new List<AA12E401>();
		int num = -1;
		AA12E401[] array = new AA12E401[128];
		int b019AD = Class607.B630A78B.object_0[146](Class607.B630A78B.object_0[6](typeof(AA12E401).TypeHandle)) * 128;
		IntPtr BA = Class607.B630A78B.object_0[263](b019AD);
		ushort ushort_ = 0;
		num = ABA18D2C.DL_GetCount(intptr_0, out ushort_);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
		num = ABA18D2C.DL_Rom_GetInfoAll(intptr_0, BA, 128);
		if (num != 0)
		{
			string f097F68F2 = GClass29.smethod_0(num);
			f097F68F2 = GClass29.smethod_1(f097F68F2, num);
		}
		int i = 0;
		IntPtr FB944B = default(IntPtr);
		for (int num2 = ushort_ - 1; i <= num2; i++)
		{
			Class607.B630A78B.object_0[855](ref FB944B, Class607.B630A78B.object_0[76](ref BA) + Class607.B630A78B.object_0[146](Class607.B630A78B.object_0[6](typeof(AA12E401).TypeHandle)) * i);
			array[i] = (AA12E401)Class607.B630A78B.object_0[378](FB944B, Class607.B630A78B.object_0[6](typeof(AA12E401).TypeHandle));
			list.Add(new AA12E401
			{
				string_0 = array[i].string_0,
				ulong_0 = array[i].ulong_0,
				genum3_0 = array[i].genum3_0,
				BE815025 = array[i].BE815025,
				ulong_1 = array[i].ulong_1,
				AE1B8782 = array[i].AE1B8782,
				ulong_2 = array[i].ulong_2,
				ushort_0 = array[i].ushort_0,
				b8A22E0D_0 = array[i].b8A22E0D_0,
				bool_0 = array[i].bool_0,
				bool_1 = array[i].bool_1,
				bool_2 = array[i].bool_2,
				genum5_0 = array[i].genum5_0,
				uint_0 = array[i].uint_0,
				ulong_3 = array[i].ulong_3,
				BB839484 = array[i].BB839484
			});
		}
		Class607.B630A78B.object_0[366](BA);
		return list;
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static void smethod_10()
	{
		int num = -1;
		num = DL_CreateList(ref B0AD49A4);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static void CD8942AD()
	{
		int num = -1;
		num = DL_DestroyList(ref B0AD49A4);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static void smethod_11(int int_0)
	{
		int num = -1;
		num = DL_AddHandleToList(B0AD49A4, intptr_0);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
	}

	public GClass32()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
