using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Xml;
using Microsoft.Win32.SafeHandles;

public class GClass121
{
	public static SafeFileHandle OpenWritePort = Class607.B630A78B.object_0[637](Class607.B630A78B.object_0[120](), CA1AC71F: false);

	public string F8A2880B;

	public byte[] byte_0;

	public bool EA0E5893;

	public bool bool_0;

	public string string_0 = "";

	public int int_0;

	public int int_1;

	public string string_1 = "";

	public bool bool_1;

	public bool bool_2;

	public string E69BB901 = "";

	public bool C787F32B;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private GClass112.E39CAE0B AD9F3305;

	public int DF179F9A;

	public string DA2A3883 = "error: only nop and sig tag can be recevied before authentication";

	public bool Boolean_0
	{
		get
		{
			if (OpenWritePort == null || Class607.B630A78B.object_0[1087](OpenWritePort) || Class607.B630A78B.object_0[3](OpenWritePort))
			{
				return false;
			}
			return true;
		}
	}

	public event GClass112.E39CAE0B D7142E3D
	{
		[CompilerGenerated]
		add
		{
			GClass112.E39CAE0B e39CAE0B = AD9F3305;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[752](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref AD9F3305, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
		[CompilerGenerated]
		remove
		{
			GClass112.E39CAE0B e39CAE0B = AD9F3305;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[629](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref AD9F3305, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
	}

	[DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
	public static extern SafeFileHandle CreateFile(string string_2, int int_2, uint CAA335A7, IntPtr intptr_0, uint A32CB12B, uint DE91EF20, IntPtr intptr_1);

	[DllImport("kernel32.dll", SetLastError = true)]
	public static extern bool CloseHandle(SafeFileHandle safeFileHandle_0);

	[DllImport("kernel32", CharSet = CharSet.Auto, SetLastError = true)]
	internal static extern int ReadFile(SafeFileHandle safeFileHandle_0, [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] byte[] byte_1, int int_2, ref int int_3, IntPtr intptr_0);

	[DllImport("kernel32.dll", SetLastError = true)]
	internal static extern int WriteFile(SafeFileHandle safeFileHandle_0, byte[] BF25E498, int int_2, out int int_3, IntPtr F90EF489);

	[DllImport("kernel32", CharSet = CharSet.Auto, SetLastError = true)]
	internal static extern int GetFileSize(SafeFileHandle safeFileHandle_0, ref long long_0);

	[DllImport("kernel32", CharSet = CharSet.Auto, EntryPoint = "ReadFile", SetLastError = true)]
	internal static extern int ReadFile_1(SafeFileHandle safeFileHandle_0, IntPtr intptr_0, int int_2, ref int int_3, IntPtr D9B457A3);

	[DllImport("Kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
	internal static extern bool PurgeComm(SafeFileHandle safeFileHandle_0, uint uint_0);

	public bool method_0(string string_2)
	{
		F8A2880B = string_2;
		if (OpenWritePort != null && Boolean_0)
		{
			method_4();
		}
		OpenWritePort = Class607.B630A78B.object_0[637](Class607.B630A78B.object_0[120](), CA1AC71F: true);
		OpenWritePort = CreateFile(Class607.B630A78B.object_0[720]("\\\\.\\", string_2), -1073741824, 3u, Class607.B630A78B.object_0[120](), 3u, 128u, Class607.B630A78B.object_0[120]());
		return !Class607.B630A78B.object_0[1087](OpenWritePort);
	}

	public bool method_1()
	{
		if (OpenWritePort != null && Boolean_0)
		{
			method_4();
		}
		OpenWritePort = Class607.B630A78B.object_0[637](Class607.B630A78B.object_0[120](), CA1AC71F: true);
		OpenWritePort = CreateFile(Class607.B630A78B.object_0[720]("\\\\.\\", F8A2880B), -1073741824, 3u, Class607.B630A78B.object_0[120](), 3u, 128u, Class607.B630A78B.object_0[120]());
		return Boolean_0;
	}

	public void BB3C471E()
	{
		try
		{
			PurgeComm(OpenWritePort, 8u);
		}
		catch
		{
		}
	}

	public void E8843913()
	{
		try
		{
			PurgeComm(OpenWritePort, 4u);
		}
		catch
		{
		}
	}

	public void AE103735(string string_2)
	{
		byte[] a797DF2D = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1124](), string_2);
		method_6(a797DF2D);
	}

	public bool method_2(string string_2)
	{
		List<byte> list = new List<byte>();
		for (int i = 0; i < Class607.B630A78B.object_0[342](string_2); i += 2)
		{
			string d5854B = Class607.B630A78B.object_0[31](string_2, i, 2);
			list.Add(Class607.B630A78B.object_0[1285](d5854B, NumberStyles.AllowHexSpecifier));
		}
		byte[] array = list.ToArray();
		string string_3 = Class607.B630A78B.object_0[1217](B20B4631.C69D829E, array.Length);
		AE103735(string_3);
		if (!method_8())
		{
			Class607.B630A78B.object_0[294]("Signature failed!");
			return true;
		}
		StringBuilder object_ = Class607.B630A78B.object_0[1086]();
		StringBuilder object_2 = Class607.B630A78B.object_0[1086]();
		byte[] array2 = array;
		for (int j = 0; j < array2.Length; j++)
		{
			byte ADBCA = array2[j];
			Class607.B630A78B.object_0[426](object_, Class607.B630A78B.object_0[720](Class607.B630A78B.object_0[446](ref ADBCA), " "));
			Class607.B630A78B.object_0[426](object_2, Class607.B630A78B.object_0[1140]("0x", Class607.B630A78B.object_0[1273](ref ADBCA, "X2"), " "));
		}
		method_6(array);
		byte[] array3 = method_3(500);
		if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[1124](), array3, 0, array3.Length), "ERROR:"))
		{
			return false;
		}
		return true;
	}

	public byte[] method_3(int F825862B = 0)
	{
		try
		{
			if (GClass112.E31AABAB)
			{
				throw Class607.B630A78B.object_0[778]("Stopped by user!!");
			}
			if (F825862B > 0)
			{
				Class607.B630A78B.object_0[299](F825862B);
			}
			byte[] array = new byte[524288];
			int int_ = 0;
			ReadFile(OpenWritePort, array, array.Length, ref int_, Class607.B630A78B.object_0[120]());
			int num = 4;
			do
			{
				if (num-- >= 0 && int_ == 0)
				{
					GClass112.smethod_28(10);
					array = new byte[524288];
					ReadFile(OpenWritePort, array, array.Length, ref int_, Class607.B630A78B.object_0[120]());
					continue;
				}
				if (GClass112.E31AABAB)
				{
					throw Class607.B630A78B.object_0[778]("Stopped by user!!");
				}
				return array.Take(int_).ToArray();
			}
			while (!GClass112.E31AABAB);
			throw Class607.B630A78B.object_0[778]("Stopped by user!!");
		}
		finally
		{
			GClass112.E31AABAB = false;
		}
	}

	public void method_4()
	{
		try
		{
			Class607.B630A78B.object_0[419](OpenWritePort);
			Class607.B630A78B.object_0[621](OpenWritePort);
		}
		catch (Exception)
		{
		}
	}

	public string method_5(string string_2)
	{
		AE103735(string_2);
		byte[] array = method_3(500);
		return Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[1124](), array, 0, array.Length);
	}

	public void method_6(byte[] A797DF2D)
	{
		try
		{
			if (GClass112.E31AABAB)
			{
				throw Class607.B630A78B.object_0[778]("Stopped by user!!");
			}
			WriteFile(OpenWritePort, A797DF2D, A797DF2D.Length, out var _, Class607.B630A78B.object_0[120]());
		}
		finally
		{
			GClass112.E31AABAB = false;
		}
	}

	public byte[] method_7()
	{
		byte[] array = C59B872B();
		if (array != null && array.Length != 0)
		{
			method_10(array);
		}
		return array;
	}

	public bool method_8()
	{
		try
		{
			while (true)
			{
				byte[] array = method_3();
				string object_ = Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[1124](), array, 0, array.Length);
				if (Class607.B630A78B.object_0[1240](object_, "\"ACK\""))
				{
					break;
				}
				if (Class607.B630A78B.object_0[1240](object_, "\"NAK\""))
				{
				}
			}
			return true;
		}
		catch (Exception ex)
		{
			throw ex;
		}
	}

	public string method_9()
	{
		try
		{
			byte[] array = method_3();
			string object_ = Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[1124](), array, 0, array.Length);
			if (Class607.B630A78B.object_0[1240](object_, "\"ACK\""))
			{
				return "ack";
			}
			if (Class607.B630A78B.object_0[1240](object_, "Only nop and sig tag"))
			{
				return "auth";
			}
		}
		catch (Exception ex)
		{
			throw ex;
		}
		return Class607.B630A78B.object_0[170](bool_0: false);
	}

	public byte[] D4B8C097()
	{
		byte[] array = C59B872B();
		if (array == null)
		{
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[720]("can not read from port ", F8A2880B));
		}
		if (array.Length != 0)
		{
			method_10(array);
		}
		return array;
	}

	public void CC313E0C()
	{
		if (Boolean_0)
		{
			BB3C471E();
			E8843913();
		}
	}

	public byte[] F99B7510()
	{
		return method_3();
	}

	private byte[] C59B872B()
	{
		int num = 10;
		byte_0 = null;
		byte_0 = F99B7510();
		while (num-- >= 0 && byte_0 == null)
		{
			GClass112.smethod_28(50);
			byte_0 = F99B7510();
		}
		return byte_0;
	}

	private string[] method_10(byte[] byte_1)
	{
		return method_15(byte_1, bool_3: false);
	}

	private int method_11(string B13F4CBA, string B3BC8714)
	{
		if (Class607.B630A78B.object_0[1240](B13F4CBA, B3BC8714))
		{
			string fFA17C = Class607.B630A78B.object_0[99](B13F4CBA, B3BC8714, "");
			return (Class607.B630A78B.object_0[342](B13F4CBA) - Class607.B630A78B.object_0[342](fFA17C)) / Class607.B630A78B.object_0[342](B3BC8714);
		}
		return 0;
	}

	public bool method_12(string string_2, bool bool_3)
	{
		byte[] a797DF2D = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), string_2);
		method_6(a797DF2D);
		if (bool_3)
		{
			return method_14(bool_3);
		}
		return false;
	}

	public List<XmlDocument> method_13(bool bool_3)
	{
		List<XmlDocument> list = new List<XmlDocument>();
		byte[] c238F2BE = C59B872B();
		string[] array = method_15(c238F2BE, bool_3);
		if (array.Length >= 2)
		{
			string[] source = Class607.B630A78B.object_0[87](array[1], "\\<\\?xml");
			foreach (string item in source.ToList())
			{
				if (!Class607.B630A78B.object_0[1205](item))
				{
					if (Class607.B630A78B.object_0[1145](Class607.B630A78B.object_0[1050](item), DA2A3883) >= 0)
					{
						C787F32B = true;
					}
					XmlDocument xmlDocument = Class607.B630A78B.object_0[1104]();
					Class607.B630A78B.object_0[963](xmlDocument, Class607.B630A78B.object_0[720]("<?xml ", item));
					list.Add(xmlDocument);
				}
			}
			return list;
		}
		return list;
	}

	public bool method_14(bool B3AE403F)
	{
		bool flag = false;
		if (!B3AE403F)
		{
			return C59B872B() != null;
		}
		int num = 2;
		if (B3AE403F)
		{
			num = 16;
		}
		while (num-- > 0 && !flag)
		{
			List<XmlDocument> list = method_13(B3AE403F);
			_ = list.Count;
			foreach (XmlDocument item in list)
			{
				XmlNode object_ = Class607.B630A78B.object_0[791](item, "data");
				XmlNodeList object_2 = Class607.B630A78B.object_0[539](object_);
				XmlElement xmlElement = null;
				{
					IEnumerator object_3 = Class607.B630A78B.object_0[391](object_2);
					try
					{
						while (Class607.B630A78B.object_0[212](object_3))
						{
							XmlNode xmlNode = (XmlNode)Class607.B630A78B.object_0[107](object_3);
							xmlElement = (XmlElement)xmlNode;
							if (Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[1050](Class607.B630A78B.object_0[531](xmlElement)), "sig"))
							{
								string_0 = Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[1169](xmlElement), "blob", "sig");
							}
							{
								IEnumerator object_4 = Class607.B630A78B.object_0[954](Class607.B630A78B.object_0[160](xmlElement));
								try
								{
									while (Class607.B630A78B.object_0[212](object_4))
									{
										XmlAttribute object_5 = (XmlAttribute)Class607.B630A78B.object_0[107](object_4);
										if (Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[1050](Class607.B630A78B.object_0[531](object_5)), "maxpayloadsizetotargetinbytes"))
										{
											int_0 = Class607.B630A78B.object_0[754](Class607.B630A78B.object_0[769](object_5)) / int_1;
										}
										if (Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[1050](Class607.B630A78B.object_0[769](object_5)), "ack"))
										{
											flag = true;
										}
										if (Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[769](object_5), "WARN: NAK: MaxPayloadSizeToTargetInBytes sent by host 1048576 larger than supported 16384"))
										{
											flag = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x40 0x10 "))
										{
											EA0E5893 = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x31 0x10 "))
										{
											EA0E5893 = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x02 0x10 "))
										{
											EA0E5893 = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x00 0x10 "))
										{
											bool_0 = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x51 0x10 "))
										{
											bool_0 = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "0xC3 0x00 0x32 0x10 "))
										{
											bool_0 = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "UFS Inquiry Command Output"))
										{
											AD9F3305?.Invoke("Storage Info : ");
											AD9F3305?.Invoke(Class607.B630A78B.object_0[769](object_5), EF1F389C.Success, BB24BF3C: false, bool_0: true);
											string_1 = Class607.B630A78B.object_0[769](object_5);
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "INFO: quick_reset") && !bool_1)
										{
											AD9F3305?.Invoke("quick reset : ");
											AD9F3305?.Invoke(Class607.B630A78B.object_0[769](object_5), EF1F389C.Success, BB24BF3C: false, bool_0: true);
											bool_1 = true;
										}
										if (Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[769](object_5), "Chip serial num") && !bool_2)
										{
											AD9F3305?.Invoke("Chip serial number : ");
											bool_2 = true;
											int num2 = Class607.B630A78B.object_0[1129](Class607.B630A78B.object_0[769](object_5), '(') + 1;
											int num3 = Class607.B630A78B.object_0[1129](Class607.B630A78B.object_0[769](object_5), ')');
											string text = Class607.B630A78B.object_0[31](Class607.B630A78B.object_0[769](object_5), num2, num3 - num2);
											if (Class607.B630A78B.object_0[342](text) < 10)
											{
												int num4 = 10 - Class607.B630A78B.object_0[342](text);
												string object_6 = "00000000";
												text = Class607.B630A78B.object_0[1140](Class607.B630A78B.object_0[31](text, 0, 2), Class607.B630A78B.object_0[31](object_6, 0, num4), Class607.B630A78B.object_0[31](text, 2, Class607.B630A78B.object_0[342](text) - 2));
											}
											E69BB901 = text;
											AD9F3305?.Invoke(E69BB901, EF1F389C.Success, BB24BF3C: false, bool_0: true);
										}
									}
								}
								finally
								{
									IDisposable disposable2 = object_4 as IDisposable;
									if (disposable2 != null)
									{
										disposable2.Dispose();
									}
								}
							}
						}
					}
					finally
					{
						IDisposable disposable = object_3 as IDisposable;
						if (disposable != null)
						{
							disposable.Dispose();
						}
					}
				}
			}
			if (B3AE403F)
			{
				GClass112.smethod_28(50);
			}
		}
		return flag;
	}

	public bool FF876814(out string string_2, int B7BB218C)
	{
		string_2 = null;
		byte[] c238F2BE = C59B872B();
		string[] array = method_15(c238F2BE, bool_3: true);
		string b3BC = "<response value=\"ACK\"";
		int num = 10;
		while ((array.Length != 2 || Class607.B630A78B.object_0[1145](array[1], b3BC) < 0) && num-- >= 0)
		{
			GClass112.smethod_28(10);
			c238F2BE = C59B872B();
			array = method_15(c238F2BE, bool_3: true);
		}
		if (array.Length == 2 && Class607.B630A78B.object_0[1145](array[1], b3BC) >= 0)
		{
			int i = method_11(array[1], b3BC);
			num = 10;
			for (; i < B7BB218C * 2; i += method_11(array[1], b3BC))
			{
				if (num-- <= 0)
				{
					break;
				}
				GClass112.smethod_28(10);
				c238F2BE = C59B872B();
				array = method_15(c238F2BE, bool_3: true);
			}
			if (B7BB218C * 2 > i)
			{
				throw Class607.B630A78B.object_0[778]("ACK count don't match!");
			}
			CC313E0C();
			DF179F9A = 0;
			return true;
		}
		string_2 = array[1];
		return false;
	}

	private string[] method_15(byte[] C238F2BE, bool bool_3)
	{
		if (C238F2BE == null)
		{
			return new string[2] { "", "" };
		}
		StringBuilder stringBuilder = Class607.B630A78B.object_0[1086]();
		StringBuilder stringBuilder2 = Class607.B630A78B.object_0[1086]();
		Class607.B630A78B.object_0[1086]();
		Class607.B630A78B.object_0[1086]();
		for (int i = 0; i < C238F2BE.Length; i++)
		{
			object obj = Class607.B630A78B.object_0[426];
			char char_ = Class607.B630A78B.object_0[972](C238F2BE[i]);
			obj(stringBuilder2, Class607.B630A78B.object_0[1219](ref char_));
		}
		return new string[2]
		{
			stringBuilder.ToString(),
			stringBuilder2.ToString()
		};
	}

	public GClass121()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
