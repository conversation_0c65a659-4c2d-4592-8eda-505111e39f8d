using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.IO;
using System.Linq;

public class GClass37
{
	public enum GEnum24 : uint
	{
		[Description("EFI_UNUSED")]
		EFI_UNUSED = 0u,
		[Description("EFI_MBR")]
		EFI_MBR = 38661697u,
		[Description("EFI_SYSTEM")]
		EFI_SYSTEM = 3240784680u,
		[Description("EFI_BIOS_BOOT")]
		EFI_BIOS_BOOT = 560488776u,
		[Description("EFI_IFFS")]
		EFI_IFFS = 3552568030u,
		[Description("EFI_SONY_BOOT")]
		EFI_SONY_BOOT = 4093744946u,
		[Description("EFI_LENOVO_BOOT")]
		EFI_LENOVO_BOOT = 3217010663u,
		[Description("EFI_MSR")]
		EFI_MSR = 3821658902u,
		[Description("EFI_BASIC_DATA")]
		EFI_BASIC_DATA = 3956318370u,
		[Description("EFI_LDM_META")]
		EFI_LDM_META = 1476970666u,
		[Description("EFI_LDM")]
		EFI_LDM = 2946195616u,
		[Description("EFI_RECOVERY")]
		EFI_RECOVERY = 3734289316u,
		[Description("EFI_GPFS")]
		EFI_GPFS = 934280336u,
		[Description("EFI_STORAGE_SPACES")]
		EFI_STORAGE_SPACES = 3881611151u,
		[Description("EFI_HPUX_DATA")]
		EFI_HPUX_DATA = 1971932190u,
		[Description("EFI_HPUX_SERVICE")]
		EFI_HPUX_SERVICE = 3802261288u,
		[Description("EFI_LINUX_DAYA")]
		EFI_LINUX_DAYA = 264650159u,
		[Description("EFI_LINUX_RAID")]
		EFI_LINUX_RAID = 2711455759u,
		[Description("EFI_LINUX_ROOT32")]
		EFI_LINUX_ROOT32 = 1145541952u,
		[Description("EFI_LINUX_ROOT64")]
		EFI_LINUX_ROOT64 = 1332264163u,
		[Description("EFI_LINUX_ROOT_ARM32")]
		EFI_LINUX_ROOT_ARM32 = 1775949584u,
		[Description("EFI_LINUX_ROOT_ARM64")]
		EFI_LINUX_ROOT_ARM64 = 3105992773u,
		[Description("EFI_LINUX_SWAP")]
		EFI_LINUX_SWAP = 106429805u,
		[Description("EFI_LINUX_LVM")]
		EFI_LINUX_LVM = 3872838521u,
		[Description("EFI_LINUX_HOME")]
		EFI_LINUX_HOME = 2470103009u,
		[Description("EFI_LINUX_SRV")]
		EFI_LINUX_SRV = 999261221u,
		[Description("EFI_LINUX_DM_CRYPT")]
		EFI_LINUX_DM_CRYPT = 2147403209u,
		[Description("EFI_LINUX_LUKS")]
		EFI_LINUX_LUKS = 3397221579u,
		[Description("EFI_LINUX_RESERVED")]
		EFI_LINUX_RESERVED = 2376479545u,
		[Description("EFI_FREEBSD_BOOT")]
		EFI_FREEBSD_BOOT = 2210229149u,
		[Description("EFI_FREEBSD_DATA")]
		EFI_FREEBSD_DATA = 1366195380u,
		[Description("EFI_FREEBSD_SWAP")]
		EFI_FREEBSD_SWAP = 1366195381u,
		[Description("EFI_FREEBSD_UFS")]
		EFI_FREEBSD_UFS = 1366195382u,
		[Description("EFI_FREEBSD_VINUM")]
		EFI_FREEBSD_VINUM = 1366195384u,
		[Description("EFI_FREEBSD_ZFS")]
		EFI_FREEBSD_ZFS = 1366195386u,
		[Description("EFI_OSX_HFS")]
		EFI_OSX_HFS = 1212568320u,
		[Description("EFI_OSX_UFS")]
		EFI_OSX_UFS = 1430672128u,
		[Description("EFI_OSX_ZFS")]
		EFI_OSX_ZFS = 1787399363u,
		[Description("EFI_OSX_RAID")]
		EFI_OSX_RAID = 1380010308u,
		[Description("EFI_OSX_RAID_OFFLINE")]
		EFI_OSX_RAID_OFFLINE = 1380010308u,
		[Description("EFI_OSX_RECOVERY")]
		EFI_OSX_RECOVERY = 1114599284u,
		[Description("EFI_OSX_LABEL")]
		EFI_OSX_LABEL = 1281450597u,
		[Description("EFI_OSX_TV_RECOVERY")]
		EFI_OSX_TV_RECOVERY = 1382376303u,
		[Description("EFI_OSX_CORE_STORAGE")]
		EFI_OSX_CORE_STORAGE = 1400139634u,
		[Description("EFI_SOLARIS_BOOT")]
		EFI_SOLARIS_BOOT = 1786956613u,
		[Description("EFI_SOLARIS_ROOT")]
		EFI_SOLARIS_ROOT = 1787154253u,
		[Description("EFI_SOLARIS_SWAP")]
		EFI_SOLARIS_SWAP = 1787282543u,
		[Description("EFI_SOLARIS_BACKUP")]
		EFI_SOLARIS_BACKUP = 1787520043u,
		[Description("EFI_SOLARIS_USR")]
		EFI_SOLARIS_USR = 1787399363u,
		[Description("EFI_SOLARIS_VAR")]
		EFI_SOLARIS_VAR = 1787753193u,
		[Description("EFI_SOLARIS_HOME")]
		EFI_SOLARIS_HOME = 1787869753u,
		[Description("EFI_SOLARIS_ALTERNATE")]
		EFI_SOLARIS_ALTERNATE = 1787986853u,
		[Description("EFI_SOLARIS_RESERVED1")]
		EFI_SOLARIS_RESERVED1 = 1788107323u,
		[Description("EFI_SOLARIS_RESERVED2")]
		EFI_SOLARIS_RESERVED2 = 1788227793u,
		[Description("EFI_SOLARIS_RESERVED3")]
		EFI_SOLARIS_RESERVED3 = 1788348263u,
		[Description("EFI_SOLARIS_RESERVED4")]
		EFI_SOLARIS_RESERVED4 = 1788224383u,
		[Description("EFI_SOLARIS_RESERVED5")]
		EFI_SOLARIS_RESERVED5 = 1787636423u,
		[Description("EFI_NETBSD_SWAP")]
		EFI_NETBSD_SWAP = 1240763698u,
		[Description("EFI_NETBSD_FFS")]
		EFI_NETBSD_FFS = 1240763738u,
		[Description("EFI_NETBSD_LFS")]
		EFI_NETBSD_LFS = 1240763778u,
		[Description("EFI_NETBSD_RAID")]
		EFI_NETBSD_RAID = 1240763818u,
		[Description("EFI_NETBSD_CONCAT")]
		EFI_NETBSD_CONCAT = 766843332u,
		[Description("EFI_NETBSD_ENCRYPT")]
		EFI_NETBSD_ENCRYPT = 766843372u,
		[Description("EFI_CHROMEOS_KERNEL")]
		EFI_CHROMEOS_KERNEL = 4265224797u,
		[Description("EFI_CHROMEOS_ROOTFS")]
		EFI_CHROMEOS_ROOTFS = 1018749442u,
		[Description("EFI_CHROMEOS_FUTURE")]
		EFI_CHROMEOS_FUTURE = 772437309u,
		[Description("EFI_HAIKU")]
		EFI_HAIKU = 1111905073u,
		[Description("EFI_MIDNIGHTBSD_BOOT")]
		EFI_MIDNIGHTBSD_BOOT = 2245387358u,
		[Description("EFI_MIDNIGHTBSD_DATA")]
		EFI_MIDNIGHTBSD_DATA = 2245387354u,
		[Description("EFI_MIDNIGHTBSD_SWAP")]
		EFI_MIDNIGHTBSD_SWAP = 2245387355u,
		[Description("EFI_MIDNIGHTBSD_UFS")]
		EFI_MIDNIGHTBSD_UFS = 60092299u,
		[Description("EFI_MIDNIGHTBSD_VINUM")]
		EFI_MIDNIGHTBSD_VINUM = 2245387356u,
		[Description("EFI_MIDNIGHTBSD_ZFS")]
		EFI_MIDNIGHTBSD_ZFS = 2245387357u,
		[Description("EFI_CEPH_JOURNAL")]
		EFI_CEPH_JOURNAL = 1169200798u,
		[Description("EFI_CEPH_ENCRYPT")]
		EFI_CEPH_ENCRYPT = 1169200798u,
		[Description("EFI_CEPH_OSD")]
		EFI_CEPH_OSD = 1337818665u,
		[Description("EFI_CEPH_ENCRYPT_OSD")]
		EFI_CEPH_ENCRYPT_OSD = 1337818665u,
		[Description("EFI_CEPH_CREATE")]
		EFI_CEPH_CREATE = 2311421848u,
		[Description("EFI_CEPH_ENCRYPT_CREATE")]
		EFI_CEPH_ENCRYPT_CREATE = 2311421848u,
		[Description("EFI_OPENBSD")]
		EFI_OPENBSD = 2186069920u,
		[Description("EFI_QNX")]
		EFI_QNX = 3472206253u,
		[Description("EFI_PLAN9")]
		EFI_PLAN9 = 3373799673u,
		[Description("EFI_VMWARE_VMKCORE")]
		EFI_VMWARE_VMKCORE = 2636600192u,
		[Description("EFI_VMWARE_VMFS")]
		EFI_VMWARE_VMFS = 2855395370u,
		[Description("EFI_VMWARE_RESERVED")]
		EFI_VMWARE_RESERVED = 2442719228u
	}

	public class AEA639BE
	{
		public byte[] C9AD1F10;

		public byte[] C807FB83;

		public ulong A1B451BE;

		public ulong ulong_0;

		public ulong ulong_1;

		public byte[] byte_0;

		public AEA639BE(byte[] byte_1)
		{
			Class607.B630A78B.object_0[571](this);
			GClass65 gClass = new GClass65(byte_1);
			C9AD1F10 = gClass.method_5(16);
			C807FB83 = gClass.method_5(16);
			A1B451BE = gClass.BD305808();
			ulong_0 = gClass.BD305808();
			ulong_1 = gClass.BD305808();
			byte_0 = gClass.D122DA0F(72);
		}
	}

	public class GClass38
	{
		public byte[] byte_0;

		public uint uint_0;

		public uint uint_1;

		public uint uint_2;

		public uint uint_3;

		public ulong ulong_0;

		public ulong ulong_1;

		public ulong BEAC8E1B;

		public ulong B51B27BE;

		public byte[] F7B48DBF;

		public ulong ulong_2;

		public uint uint_4;

		public uint uint_5;

		public GClass38(byte[] E0A70D81)
		{
			Class607.B630A78B.object_0[571](this);
			GClass65 gClass = new GClass65(E0A70D81);
			byte_0 = gClass.method_5(8);
			uint_0 = gClass.method_0();
			uint_1 = gClass.method_0();
			uint_2 = gClass.method_0();
			uint_3 = gClass.method_0();
			ulong_0 = gClass.BD305808();
			ulong_1 = gClass.BD305808();
			BEAC8E1B = gClass.BD305808();
			B51B27BE = gClass.BD305808();
			F7B48DBF = gClass.method_5(16);
			ulong_2 = gClass.BD305808();
			uint_4 = gClass.method_0();
			uint_5 = gClass.method_0();
		}
	}

	public struct B6BA8237
	{
		public int A70F5393;

		public string string_0;

		public ulong B8882BA0;

		public ulong CD1741B1;

		public ulong ulong_0;

		public ulong D78F1C10;

		public ulong ulong_1;

		public string string_1;

		public string string_2;

		public ulong F9937622;

		public ulong ulong_2;
	}

	public List<B6BA8237> list_0 = new List<B6BA8237>();

	public ulong ulong_0;

	public uint uint_0;

	public uint ABAFC78B;

	public uint CCBA4DB6;

	public GClass38 gclass38_0;

	public int BA28FF28;

	public GClass37(uint uint_1 = 0u, uint ABAFC78B = 0u, uint uint_2 = 0u)
	{
		Class607.B630A78B.object_0[571](this);
		uint_0 = uint_1;
		this.ABAFC78B = ABAFC78B;
		CCBA4DB6 = uint_2;
	}

	public GClass38 FDAC7109(byte[] B8ACE710, int F4AB980A = 512)
	{
		return new GClass38(GClass112.smethod_14(B8ACE710, F4AB980A, 92));
	}

	public byte[] method_0(byte[] byte_0, List<B6BA8237> list_1, uint uint_1, ulong ulong_1, int AA27A6B3, uint E80D15BB, uint uint_2)
	{
		ulong num = ((uint_1 == 0) ? (ulong_1 * Class607.B630A78B.object_0[715](AA27A6B3)) : uint_1);
		using (IEnumerator<int> enumerator = Class607.B630A78B.object_0[159](0, Class607.B630A78B.object_0[1262](uint_2)).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				int num2 = Class607.B630A78B.object_0[704](num + Class607.B630A78B.object_0[243](current * E80D15BB));
				int num3 = Class607.B630A78B.object_0[1262](E80D15BB);
				byte[] array = GClass112.smethod_14(byte_0, num2, num3);
				if (!Class607.B630A78B.object_0[297](Class607.B630A78B.object_0[79](Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[1058](GClass112.smethod_14(array, 16, 16)), "-", ""), NumberStyles.HexNumber), 0L))
				{
					B6BA8237 b6BA = list_1[current];
					byte[] array_ = GClass111.smethod_2("<Q", new object[1] { b6BA.D78F1C10 });
					byte[] array_2 = GClass111.smethod_2("<Q", new object[1] { b6BA.ulong_1 + b6BA.D78F1C10 - 1L });
					byte[] array2 = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), b6BA.string_2);
					List<byte> list = new List<byte>();
					byte[] array3 = array2;
					foreach (byte item in array3)
					{
						list.Add(item);
						list.Add(0);
					}
					Class607.B630A78B.object_0[242](array_, 0, array, 32, 8);
					Class607.B630A78B.object_0[242](array_2, 0, array, 40, 8);
					Class607.B630A78B.object_0[242](list.ToArray(), 0, array, 56, list.Count);
					Class607.B630A78B.object_0[242](array, 0, byte_0, num2, num3);
					continue;
				}
				break;
			}
		}
		return byte_0;
	}

	public bool C99040AD(byte[] FE0AA33A, int E72EEA94 = 512)
	{
		gclass38_0 = new GClass38(GClass112.smethod_14(FE0AA33A, E72EEA94, 92));
		BA28FF28 = E72EEA94;
		if (!Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "EFI PART").SequenceEqual(gclass38_0.byte_0))
		{
			return false;
		}
		ulong num = ((CCBA4DB6 == 0) ? (gclass38_0.ulong_2 * Class607.B630A78B.object_0[715](E72EEA94)) : CCBA4DB6);
		uint uint_ = gclass38_0.uint_5;
		list_0.Clear();
		uint uint_2 = gclass38_0.uint_4;
		using (IEnumerator<int> enumerator = Class607.B630A78B.object_0[159](0, Class607.B630A78B.object_0[1262](uint_2)).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				byte[] array = GClass112.smethod_14(FE0AA33A, Class607.B630A78B.object_0[704](num + Class607.B630A78B.object_0[243](current * uint_)), Class607.B630A78B.object_0[1262](uint_));
				if (!Class607.B630A78B.object_0[297](Class607.B630A78B.object_0[79](Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[1058](GClass112.smethod_14(array, 16, 16)), "-", ""), NumberStyles.HexNumber), 0L))
				{
					AEA639BE aEA639BE = new AEA639BE(array);
					B6BA8237 item = default(B6BA8237);
					uint num2 = Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(aEA639BE.C807FB83, 0, 4))[0]);
					ushort ushort_ = Class607.B630A78B.object_0[504](GClass111.C3B9331C("<H", GClass112.smethod_14(aEA639BE.C807FB83, 4, 2))[0]);
					ushort ushort_2 = Class607.B630A78B.object_0[504](GClass111.C3B9331C("<H", GClass112.smethod_14(aEA639BE.C807FB83, 6, 2))[0]);
					ushort ushort_3 = Class607.B630A78B.object_0[504](GClass111.C3B9331C("<H", GClass112.smethod_14(aEA639BE.C807FB83, 8, 2))[0]);
					string text = Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[1058](GClass112.smethod_14(aEA639BE.C807FB83, 10, 6)), "-", "");
					item.string_0 = Class725.smethod_0("{0:X8}-{1:X4}-{2:X4}-{3:X4}-{4}", new object[5]
					{
						Class607.B630A78B.object_0[993](ref num2, "x"),
						Class607.B630A78B.object_0[102](ref ushort_, "x"),
						Class607.B630A78B.object_0[102](ref ushort_2, "x"),
						Class607.B630A78B.object_0[102](ref ushort_3, "x"),
						text
					});
					item.D78F1C10 = aEA639BE.A1B451BE;
					item.ulong_1 = aEA639BE.ulong_0 - aEA639BE.A1B451BE + 1L;
					item.A70F5393 = current;
					item.ulong_0 = aEA639BE.ulong_1;
					uint num3 = Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(aEA639BE.C9AD1F10, 0, 4))[0]);
					try
					{
						item.string_1 = GClass112.smethod_17((GEnum24)num3);
					}
					catch
					{
						item.string_1 = Class607.B630A78B.object_0[993](ref num3, "x");
					}
					item.string_2 = Class607.B630A78B.object_0[647](Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[1020](), aEA639BE.byte_0), new char[1]);
					if (!Class607.B630A78B.object_0[787](item.string_1, "EFI_UNUSED"))
					{
						list_0.Add(item);
					}
					continue;
				}
				break;
			}
		}
		ulong_0 = gclass38_0.B51B27BE + 34L;
		return true;
	}

	public bool C2A35F89(byte[] byte_0, int int_0 = 512)
	{
		BA28FF28 = int_0;
		ulong_0 = 0uL;
		list_0 = new List<B6BA8237>();
		using (IEnumerator<int> enumerator = GClass112.smethod_30(2048, byte_0.Length, 128).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				byte[] array = GClass112.smethod_14(byte_0, current, 128);
				if (GClass112.smethod_14(array, 16, 16).SequenceEqual(new byte[16]))
				{
					break;
				}
				B6BA8237 item = default(B6BA8237);
				using MemoryStream memoryStream = Class607.B630A78B.object_0[10](array);
				Class607.B630A78B.object_0[543](memoryStream, 16L, SeekOrigin.Begin);
				int num = Class607.B630A78B.object_0[137](GClass112.C3BE5516(memoryStream, 4), 0);
				int num2 = Class607.B630A78B.object_0[523](GClass112.C3BE5516(memoryStream, 2), 0);
				int num3 = Class607.B630A78B.object_0[523](GClass112.C3BE5516(memoryStream, 2), 0);
				int num4 = Class607.B630A78B.object_0[523](GClass112.C3BE5516(memoryStream, 2), 0);
				string text = Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[1058](GClass112.C3BE5516(memoryStream, 6)), "-", "");
				item.string_0 = Class725.smethod_0("{0:x8}-{1:x4}-{2:x4}-{3:x4}-{4}", new object[5] { num, num2, num3, num4, text });
				ulong num5 = Class607.B630A78B.object_0[432](GClass112.C3BE5516(memoryStream, 8), 0);
				ulong num6 = Class607.B630A78B.object_0[432](GClass112.C3BE5516(memoryStream, 8), 0);
				item.B8882BA0 = num5;
				item.CD1741B1 = num6;
				item.D78F1C10 = num5;
				item.ulong_1 = num6 - num5 + 1L;
				item.ulong_0 = Class607.B630A78B.object_0[432](GClass112.C3BE5516(memoryStream, 8), 0);
				byte[] f0AED = GClass112.C3BE5516(memoryStream, 72);
				string string_ = Class607.B630A78B.object_0[95](Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[1020](), f0AED), new char[1]);
				item.string_2 = string_;
				item.string_1 = "0";
				if (num6 > ulong_0)
				{
					ulong_0 = num6;
				}
				list_0.Add(item);
			}
		}
		return true;
	}
}
