using System;
using System.Collections.Generic;
using System.Linq;

public class GClass59
{
	public E5964FB0 A13F47A7;

	public A8AAAD35 B92B72B3;

	public GDelegate27 F9949F0F;

	public GClass59(E5964FB0 A13F47A7, A8AAAD35 a8AAAD35_0, GDelegate27 gdelegate27_0)
	{
		Class607.B630A78B.object_0[571](this);
		this.A13F47A7 = A13F47A7;
		B92B72B3 = a8AAAD35_0;
		F9949F0F = gdelegate27_0;
	}

	public Tuple<byte[], GClass37> D60E1983(D905A51F d905A51F_0, string string_0 = "user")
	{
		GClass51.GStruct57 gStruct = B92B72B3(0uL, 2 * A13F47A7.CF001212.uint_0, "", string_0);
		IEnumerable<byte> first = gStruct.byte_0.Take(4);
		byte[] array = new byte[4];
		DEBEDD9C.smethod_0(array, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		if (first.SequenceEqual(array))
		{
			GClass37 gClass = new GClass37(d905A51F_0.E6BF6BB9, d905A51F_0.B53699B8, d905A51F_0.E70E7AAE);
			gStruct = B92B72B3(0uL, 32 * A13F47A7.CF001212.uint_0, "", string_0);
			if (gStruct.byte_0 == null || gStruct.byte_0.Length == 0)
			{
				return null;
			}
			gClass.C2A35F89(gStruct.byte_0, Class607.B630A78B.object_0[1262](A13F47A7.CF001212.uint_0));
			return new Tuple<byte[], GClass37>(gStruct.byte_0, gClass);
		}
		if (gStruct.byte_0.Take(9).SequenceEqual(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[47](), "EMMC_BOOT")) && F9949F0F != null)
		{
			return F9949F0F();
		}
		if (gStruct.byte_0.Take(8).SequenceEqual(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[47](), "UFS_BOOT")) && F9949F0F != null)
		{
			return F9949F0F();
		}
		if (gStruct.byte_0 == null || gStruct.byte_0.Length == 0)
		{
			return null;
		}
		GClass37 gClass2 = new GClass37(d905A51F_0.E6BF6BB9, d905A51F_0.B53699B8, d905A51F_0.E70E7AAE);
		GClass37.GClass38 gClass3 = gClass2.FDAC7109(gStruct.byte_0, Class607.B630A78B.object_0[1262](A13F47A7.CF001212.uint_0));
		if (gClass3.byte_0.SequenceEqual(new byte[8]))
		{
			gClass3 = gClass2.FDAC7109(B92B72B3(A13F47A7.CF001212.ulong_0 - 16384L, 2 * A13F47A7.CF001212.uint_0, "", string_0).byte_0, Class607.B630A78B.object_0[1262](A13F47A7.CF001212.uint_0));
			if (gClass3.byte_0.SequenceEqual(new byte[8]))
			{
				return null;
			}
		}
		ulong bEAC8E1B = gClass3.BEAC8E1B;
		if (bEAC8E1B == 0L)
		{
			return null;
		}
		gStruct = B92B72B3(0uL, bEAC8E1B * A13F47A7.CF001212.uint_0, "", string_0);
		if (gStruct.byte_0 == null || gStruct.byte_0.Length == 0)
		{
			return null;
		}
		gClass2.C99040AD(gStruct.byte_0, Class607.B630A78B.object_0[1262](A13F47A7.CF001212.uint_0));
		return new Tuple<byte[], GClass37>(gStruct.byte_0, gClass2);
	}
}
