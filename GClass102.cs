using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;

public class GClass102 : TextBox
{
	private string string_0 = "<Input>";

	private bool bool_0 = true;

	private Color color_0;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private Color CD082A9D;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private EventHandler<A38B3A22> eventHandler_0;

	private const string string_1 = "<Input>";

	private const int int_0 = 177;

	private bool bool_1;

	private int int_1;

	private bool bool_2;

	[Browsable(false)]
	public bool ********
	{
		get
		{
			return bool_0;
		}
		private set
		{
			if (bool_0 != value)
			{
				Class607.B630A78B.object_0[824](this, ControlStyles.UserMouse, value);
				Class607.B630A78B.object_0[583](this);
				bool_0 = value;
				vmethod_0(value);
			}
		}
	}

	[Description("The placeholder associated with the control.")]
	[DefaultValue("<Input>")]
	[Category("Placeholder")]
	public string String_0
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
			if (********)
			{
				Class607.B630A78B.object_0[1175](this, value);
			}
		}
	}

	[Browsable(false)]
	public override string Text
	{
		get
		{
			if (******** && Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[1216](this), String_0))
			{
				return "";
			}
			return Class607.B630A78B.object_0[1216](this);
		}
		set
		{
			Class607.B630A78B.object_0[1127](this, value);
		}
	}

	[Description("The foreground color of this component, which is used to display the placeholder.")]
	[Category("Appearance")]
	[DefaultValue(typeof(Color), "InactiveCaption")]
	public Color Color_0
	{
		get
		{
			return color_0;
		}
		set
		{
			if (!Class607.B630A78B.object_0[1120](color_0, value))
			{
				color_0 = value;
				if (Class607.B630A78B.object_0[906](this))
				{
					Class607.B630A78B.object_0[583](this);
				}
			}
		}
	}

	[Description("The foreground color of this component, which is used to display text.")]
	[Category("Appearance")]
	[DefaultValue(typeof(Color), "WindowText")]
	public Color Color_1
	{
		[CompilerGenerated]
		get
		{
			return CD082A9D;
		}
		[CompilerGenerated]
		set
		{
			CD082A9D = value;
		}
	}

	[Browsable(false)]
	public override Color ForeColor
	{
		get
		{
			if (********)
			{
				return Color_0;
			}
			return Color_1;
		}
		set
		{
			Color_1 = value;
		}
	}

	public override int TextLength => (!********) ? Class607.B630A78B.object_0[207](this) : 0;

	public GClass106 FFB3EC08 => GClass106.GClass106_0;

	[Description("Occurs when the value of the IsPlaceholderInside property has changed.")]
	public event EventHandler<A38B3A22> Event_0
	{
		[CompilerGenerated]
		add
		{
			EventHandler<A38B3A22> eventHandler = eventHandler_0;
			EventHandler<A38B3A22> eventHandler2;
			do
			{
				eventHandler2 = eventHandler;
				EventHandler<A38B3A22> value2 = (EventHandler<A38B3A22>)Class607.B630A78B.object_0[752](eventHandler2, value);
				eventHandler = Interlocked.CompareExchange(ref eventHandler_0, value2, eventHandler2);
			}
			while ((object)eventHandler != eventHandler2);
		}
		[CompilerGenerated]
		remove
		{
			EventHandler<A38B3A22> eventHandler = eventHandler_0;
			EventHandler<A38B3A22> eventHandler2;
			do
			{
				eventHandler2 = eventHandler;
				EventHandler<A38B3A22> value2 = (EventHandler<A38B3A22>)Class607.B630A78B.object_0[629](eventHandler2, value);
				eventHandler = Interlocked.CompareExchange(ref eventHandler_0, value2, eventHandler2);
			}
			while ((object)eventHandler != eventHandler2);
		}
	}

	public GClass102()
	{
		Class607.B630A78B.object_0[492](this);
		Class607.B630A78B.object_0[1127](this, String_0);
		Color_1 = Class607.B630A78B.object_0[412]();
		Color_0 = Class607.B630A78B.object_0[1051]();
		Class607.B630A78B.object_0[1267](this, FFB3EC08.font_1);
		Class607.B630A78B.object_0[824](this, ControlStyles.UserMouse, A5347B95: true);
	}

	public void CDB84638()
	{
		if (!********)
		{
			******** = true;
			Class607.B630A78B.object_0[1175](this, String_0);
			Class607.B630A78B.object_0[984](this, 0, 0);
		}
	}

	private void DDBC0789(Action action_0)
	{
		bool_1 = true;
		Class607.B630A78B.object_0[1123](action_0);
		bool_1 = false;
	}

	private unsafe void E487E6AC()
	{
		DDBC0789(Class607.B630A78B.object_0[837](this, (nint)__ldftn(GClass102.method_0)));
	}

	protected override void OnCreateControl()
	{
		Class607.B630A78B.object_0[451](this);
		int_1 = Class607.B630A78B.object_0[725](this);
		Class607.B630A78B.object_0[856](this, 32767);
	}

	protected override void OnTextChanged(EventArgs BDB6A605)
	{
		if (!bool_1)
		{
			E487E6AC();
			Class607.B630A78B.object_0[383](this, BDB6A605);
		}
	}

	protected override void WndProc(ref Message B6B3AB12)
	{
		if (!******** || Class607.B630A78B.object_0[615](ref B6B3AB12) != 177)
		{
			Class607.B630A78B.object_0[708](this, ref B6B3AB12);
		}
	}

	protected override void OnKeyDown(KeyEventArgs ke)
	{
		if (********)
		{
			if (Class607.B630A78B.object_0[1142](ke) == Keys.Left || Class607.B630A78B.object_0[1142](ke) == Keys.Right || Class607.B630A78B.object_0[1142](ke) == Keys.Up || Class607.B630A78B.object_0[1142](ke) == Keys.Down || Class607.B630A78B.object_0[1142](ke) == Keys.Delete || Class607.B630A78B.object_0[1142](ke) == Keys.Home || Class607.B630A78B.object_0[1142](ke) == Keys.End || Class607.B630A78B.object_0[1142](ke) == Keys.Back)
			{
				Class607.B630A78B.object_0[1032](ke, bool_0: true);
			}
			if (Class607.B630A78B.object_0[1142](ke) == Keys.A && Class607.B630A78B.object_0[231](Class607.B630A78B.object_0[646](ke), Keys.Control))
			{
				Class607.B630A78B.object_0[1032](ke, bool_0: true);
			}
		}
		Class607.B630A78B.object_0[14](this, ke);
	}

	protected virtual void vmethod_0(bool bool_3)
	{
		eventHandler_0?.Invoke(this, new A38B3A22(bool_3));
	}

	protected override void OnGotFocus(EventArgs e)
	{
		bool flag = false;
		if (!bool_2)
		{
			bool_2 = true;
			if (Class607.B630A78B.object_0[1015](this) == 0 && Class607.B630A78B.object_0[483]() == MouseButtons.None)
			{
				flag = true;
			}
		}
		Class607.B630A78B.object_0[1071](this, e);
		if (flag)
		{
			Class607.B630A78B.object_0[336](this, 0);
			Class607.B630A78B.object_0[367](this);
		}
	}

	[CompilerGenerated]
	private void method_0()
	{
		if (!******** && Class607.B630A78B.object_0[1205](Class607.B630A78B.object_0[814](this)))
		{
			Class607.B630A78B.object_0[856](this, 32767);
			CDB84638();
		}
		else if (******** && Class607.B630A78B.object_0[342](Class607.B630A78B.object_0[814](this)) > 0)
		{
			Class607.B630A78B.object_0[856](this, int_1);
			******** = false;
			if (Class607.B630A78B.object_0[247](Class607.B630A78B.object_0[814](this), String_0))
			{
				Class607.B630A78B.object_0[1175](this, Class607.B630A78B.object_0[31](Class607.B630A78B.object_0[814](this), 0, Class607.B630A78B.object_0[206](this) - Class607.B630A78B.object_0[342](String_0)));
			}
			if (Class607.B630A78B.object_0[342](Class607.B630A78B.object_0[814](this)) > Class607.B630A78B.object_0[725](this))
			{
				Class607.B630A78B.object_0[1175](this, Class607.B630A78B.object_0[31](Class607.B630A78B.object_0[814](this), 0, Class607.B630A78B.object_0[725](this)));
			}
			Class607.B630A78B.object_0[984](this, Class607.B630A78B.object_0[206](this), 0);
		}
	}
}
