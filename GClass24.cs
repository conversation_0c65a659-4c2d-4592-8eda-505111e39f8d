using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;

public class GClass24
{
	private enum Enum2 : uint
	{
		const_0 = uint.MaxValue,
		A69FD80C = 0u,
		const_2 = 1u,
		D3BBA8A2 = 3u,
		const_4 = uint.MaxValue
	}

	private enum EB9D8E0C : uint
	{
		const_0,
		D72EDC20,
		const_2,
		F4AA2113,
		const_4,
		CC1933B1
	}

	private enum BE2DD83B : uint
	{
		const_0 = uint.MaxValue,
		const_1 = 0u,
		DA8BCD2F = 1u,
		E48B65A1 = 2u,
		FF025F36 = 3u,
		EAB1080C = 4u,
		const_6 = 5u,
		D782F489 = 6u,
		const_8 = 7u,
		const_9 = 8u,
		const_10 = 9u,
		B4144605 = uint.MaxValue
	}

	private enum Enum3 : uint
	{
		C8AA1B9A = uint.MaxValue,
		const_1 = 0u,
		const_2 = 1u,
		const_3 = 3u,
		const_4 = uint.MaxValue
	}

	private enum D3A6632D : uint
	{
		const_0 = uint.MaxValue,
		CB1B0811 = 0u,
		AE8BB0AF = 1u,
		const_3 = 2u,
		const_4 = 3u,
		const_5 = 4u,
		ED8E3E22 = 5u,
		const_7 = 6u,
		const_8 = 7u,
		const_9 = 8u,
		DB89F780 = 11u,
		const_11 = 12u,
		CDA94DBA = uint.MaxValue
	}

	private enum Enum4 : uint
	{
		const_0 = uint.MaxValue,
		const_1 = 0u,
		const_2 = 1u,
		D8B2B393 = 2u,
		C0084C17 = 3u,
		E4258D1F = 4u,
		const_6 = 5u,
		BB319784 = 6u,
		const_8 = 7u,
		F12AEE0A = 8u,
		E20D86AA = 9u,
		E19C959E = uint.MaxValue
	}

	private enum Enum5 : uint
	{
		EC13A21C = 0u,
		const_1 = 1u,
		const_2 = 2u,
		const_3 = 10u,
		const_4 = 4u,
		BD166681 = 12u,
		const_6 = 6u,
		const_7 = uint.MaxValue
	}

	private enum Enum6 : uint
	{
		const_0 = uint.MaxValue,
		const_1 = 0u,
		const_2 = 1u,
		BC846696 = uint.MaxValue
	}

	private enum Enum7 : uint
	{
		const_0 = uint.MaxValue,
		const_1 = 0u,
		const_2 = 1u,
		const_3 = 2u,
		const_4 = uint.MaxValue
	}

	private enum Enum8 : uint
	{
		const_0 = 0u,
		const_1 = 1u,
		const_2 = 2u,
		DE30FC9D = 3u,
		const_4 = 4u,
		const_5 = uint.MaxValue
	}

	private enum ******** : uint
	{
		const_0 = uint.MaxValue,
		const_1 = 0u,
		A4384A30 = 1u,
		const_3 = 2u,
		const_4 = 3u
	}

	private enum Enum9
	{
		const_0 = -1,
		CFACD08A = 0,
		const_2 = 1,
		const_3 = 2,
		const_4 = 3,
		B6257F0D = 4,
		const_6 = 5,
		F5353F95 = 6,
		const_8 = 7,
		A1A8FC86 = 8,
		const_10 = 9,
		const_11 = 10,
		const_12 = 11,
		B68F5216 = 12,
		const_14 = 13,
		const_15 = 14,
		const_16 = 15,
		const_17 = 16,
		BE0ED531 = 17,
		A9B6AA17 = 18,
		const_20 = 32,
		AE1F15B9 = 33,
		E501F5A4 = 34,
		const_23 = 35,
		C31A769D = 36,
		const_25 = 37,
		CCAA2B8F = 38,
		FD1EC620 = 39,
		const_28 = 41,
		const_29 = 42,
		const_30 = 43,
		const_31 = 44
	}

	private enum Enum10 : uint
	{
		B086F526 = 0u,
		const_1 = 1u,
		const_2 = 2u,
		const_3 = 3u,
		F5A82007 = 4u,
		C43457A9 = 5u,
		const_6 = 8u,
		D4842A28 = 9u,
		const_8 = 10u,
		F3064397 = 11u
	}

	private enum Enum11 : uint
	{
		A11BF9A7 = 1u,
		C19491B8,
		const_2
	}

	private enum Enum12 : uint
	{
		const_0 = 0u,
		const_1 = 1u,
		const_2 = 2u,
		const_3 = 3u,
		const_4 = 4u,
		BF96FDA7 = 5u,
		const_6 = 6u,
		D9B04FB8 = 7u,
		BC1A588E = 9u,
		const_9 = 10u,
		const_10 = 11u
	}

	private enum Enum13 : uint
	{
		const_0,
		AEAF1BA2,
		F188FDA1
	}

	private enum DCB0D61A : uint
	{
		const_0,
		C694021F,
		E68A0302
	}

	[DefaultMember("Item")]
	private class F4A59784
	{
		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private Dictionary<string, uint> dictionary_0 = new Dictionary<string, uint>();

		public E5964FB0 e5964FB0_0;

		public Dictionary<string, uint> Dictionary_0
		{
			[CompilerGenerated]
			get
			{
				return dictionary_0;
			}
			[CompilerGenerated]
			private set
			{
				dictionary_0 = value;
			}
		}

		internal uint BD847092
		{
			get
			{
				if (EE94CB25.TryGetValue(string_0, out var value))
				{
					return e5964FB0_0.gclass51_0.EE83B4B7(value + GClass112.C78DEB29.A8054FBA.uint_3)[0];
				}
				if (Dictionary_0.TryGetValue(string_0, out var value2))
				{
					return value2;
				}
				return uint.MaxValue;
			}
			set
			{
				if (EE94CB25.TryGetValue(D13A92AF, out var value2))
				{
					e5964FB0_0.gclass51_0.BE2E2DAC(value2 + GClass112.C78DEB29.A8054FBA.uint_3, value);
				}
				else
				{
					Dictionary_0[D13A92AF] = value;
				}
			}
		}

		public F4A59784(E5964FB0 e5964FB0_1)
		{
			Class607.B630A78B.object_0[571](this);
			e5964FB0_0 = e5964FB0_1;
		}
	}

	[Serializable]
	[CompilerGenerated]
	private sealed class B13A4C09
	{
		public static readonly B13A4C09 _003C_003E9 = new B13A4C09();

		public static Func<char, byte> _003C_003E9__103_0;

		public static Func<char, byte> _003C_003E9__104_0;

		public static Func<char, byte> _003C_003E9__104_1;

		public static Func<char, byte> _003C_003E9__105_0;

		public static Func<char, byte> _003C_003E9__105_1;

		public B13A4C09()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal byte BBBDC7AB(char char_0)
		{
			return (byte)char_0;
		}

		internal byte method_0(char char_0)
		{
			return (byte)char_0;
		}

		internal byte method_1(char char_0)
		{
			return (byte)char_0;
		}

		internal byte method_2(char char_0)
		{
			return (byte)char_0;
		}

		internal byte C72C5921(char ADA5E818)
		{
			return (byte)ADA5E818;
		}
	}

	private const ushort F19963B3 = 2560;

	private const ushort ushort_0 = 2568;

	private const ushort D606A713 = 3712;

	private const ushort CC92E89D = 3716;

	private const ushort ushort_1 = 3720;

	private const ushort ADAE20A2 = 3724;

	private const ushort F5944616 = 3728;

	private const ushort ushort_2 = 3732;

	private const ushort ushort_3 = 3740;

	private const ushort E793938F = 2688;

	private const ushort ushort_4 = 2696;

	private const ushort ushort_5 = 2704;

	private const ushort ushort_6 = 2716;

	private const ushort AB3F799A = 2720;

	private const int F712B4A8 = 0;

	private const int int_0 = 0;

	private const int int_1 = 16;

	private const int EBBA4108 = 20;

	private const int int_2 = 28;

	private const int C92E3B8B = 32;

	private const int int_3 = 48;

	private const int E6A5EB1B = 64;

	private const int BB3BD007 = 64;

	private const int int_4 = 64;

	private const int int_5 = 64;

	private const int int_6 = 64;

	private const int CF01C5AF = 128;

	private const int int_7 = 128;

	private const int int_8 = 16;

	private const int F50F4436 = 16;

	private const int int_9 = 7;

	private const int int_10 = 13;

	private const int int_11 = 4;

	private const int int_12 = 16;

	private const int B21A7AA9 = 8;

	private const int A0975010 = 0;

	private const int int_13 = 4;

	private const int int_14 = 4;

	private const int int_15 = 16;

	private const int int_16 = 4;

	private const int E380251F = 16;

	private const int E5070AB2 = 4;

	private const int F10DFE38 = 16;

	private const int int_17 = 8;

	private const int int_18 = 32;

	private const int FD0F84B3 = 8;

	private const int E914663C = 32;

	private const int int_19 = 4;

	private const int C2AB151F = 16;

	private static Dictionary<string, object> dictionary_0 = new Dictionary<string, object>
	{
		{
			"KEY_0_0",
			new List<int> { 1024, 0, 32 }
		},
		{
			"KEY_0_1",
			new List<int> { 1028, 0, 32 }
		},
		{
			"KEY_0_2",
			new List<int> { 1032, 0, 32 }
		},
		{
			"KEY_0_3",
			new List<int> { 1036, 0, 32 }
		},
		{
			"KEY_0_4",
			new List<int> { 1040, 0, 32 }
		},
		{
			"KEY_0_5",
			new List<int> { 1044, 0, 32 }
		},
		{
			"KEY_0_6",
			new List<int> { 1048, 0, 32 }
		},
		{
			"KEY_0_7",
			new List<int> { 1052, 0, 32 }
		},
		{
			"IV_0_0",
			new List<int> { 1088, 0, 32 }
		},
		{
			"IV_0_1",
			new List<int> { 1092, 0, 32 }
		},
		{
			"IV_0_2",
			new List<int> { 1096, 0, 32 }
		},
		{
			"IV_0_3",
			new List<int> { 1100, 0, 32 }
		},
		{
			"CTR_0_0",
			new List<int> { 1120, 0, 32 }
		},
		{
			"CTR_0_1",
			new List<int> { 1124, 0, 32 }
		},
		{
			"CTR_0_2",
			new List<int> { 1128, 0, 32 }
		},
		{
			"CTR_0_3",
			new List<int> { 1132, 0, 32 }
		},
		{
			"BUSY",
			new List<int> { 1136, 0, 1 }
		},
		{
			"SK",
			new List<int> { 1144, 0, 1 }
		},
		{
			"CMAC_INIT",
			new List<int> { 1148, 0, 1 }
		},
		{
			"PREV_IV_0_0",
			new List<int> { 1168, 0, 32 }
		},
		{
			"PREV_IV_0_1",
			new List<int> { 1172, 0, 32 }
		},
		{
			"PREV_IV_0_2",
			new List<int> { 1176, 0, 32 }
		},
		{
			"PREV_IV_0_3",
			new List<int> { 1180, 0, 32 }
		},
		{
			"REMAINING_BYTES",
			new List<int> { 1212, 0, 32 }
		},
		{
			"CONTROL",
			new List<object>
			{
				1216,
				new Dictionary<string, List<int>>
				{
					{
						"DEC_KEY0",
						new List<int> { 0, 1 }
					},
					{
						"MODE0_IS_CBC_CTS",
						new List<int> { 1, 1 }
					},
					{
						"MODE_KEY0",
						new List<int> { 2, 3 }
					},
					{
						"MODE_KEY1",
						new List<int> { 5, 3 }
					},
					{
						"AES_TUNNEL_IS_ON",
						new List<int> { 10, 1 }
					},
					{
						"NK_KEY0",
						new List<int> { 12, 2 }
					},
					{
						"NK_KEY1",
						new List<int> { 14, 2 }
					},
					{
						"AES_TUNNEL1_DECRYPT",
						new List<int> { 22, 1 }
					},
					{
						"AES_TUN_B1_USES_PADDED_DATA_IN",
						new List<int> { 23, 1 }
					},
					{
						"AES_TUNNEL0_ENCRYPT",
						new List<int> { 24, 1 }
					},
					{
						"AES_OUTPUT_MID_TUNNEL_DATA",
						new List<int> { 25, 1 }
					},
					{
						"AES_TUNNEL_B1_PAD_EN",
						new List<int> { 26, 1 }
					},
					{
						"AES_OUT_MID_TUN_TO_HASH",
						new List<int> { 28, 1 }
					},
					{
						"AES_XOR_CRYPTOKEY",
						new List<int> { 29, 1 }
					},
					{
						"DIRECT_ACCESS",
						new List<int> { 31, 1 }
					}
				}
			}
		},
		{
			"HW_FLAGS",
			new List<object>
			{
				1224,
				new Dictionary<string, List<int>>
				{
					{
						"SUPPORT_256_192_KEY",
						new List<int> { 0, 1 }
					},
					{
						"AES_LARGE_RKEK",
						new List<int> { 1, 1 }
					},
					{
						"DPA_CNTRMSR_EXIST",
						new List<int> { 2, 1 }
					},
					{
						"CTR_EXIST",
						new List<int> { 3, 1 }
					},
					{
						"ONLY_ENCRYPT",
						new List<int> { 4, 1 }
					},
					{
						"USE_SBOX_TABLE",
						new List<int> { 5, 1 }
					},
					{
						"USE_5_SBOXES",
						new List<int> { 8, 1 }
					},
					{
						"AES_SUPPORT_PREV_IV",
						new List<int> { 9, 1 }
					},
					{
						"AES_TUNNEL_EXISTS",
						new List<int> { 10, 1 }
					},
					{
						"SECOND_REGS_SET_EXIST",
						new List<int> { 11, 1 }
					},
					{
						"DFA_CNTRMSR_EXIST",
						new List<int> { 12, 1 }
					}
				}
			}
		},
		{
			"XEX_HW_T_CALC_KICK",
			new List<int> { 1228, 0, 1 }
		},
		{
			"XEX_HW_T_CALC_IS_ON",
			new List<int> { 1236, 0, 2 }
		},
		{
			"CTR_NO_INCREMENT",
			new List<int> { 1240, 0, 1 }
		},
		{
			"SW_RESET",
			new List<int> { 1268, 0, 1 }
		},
		{
			"XEX_HW_T_CALC_KEY_0",
			new List<int> { 1280, 0, 32 }
		},
		{
			"XEX_HW_T_CALC_KEY_1",
			new List<int> { 1284, 0, 32 }
		},
		{
			"XEX_HW_T_CALC_KEY_2",
			new List<int> { 1288, 0, 32 }
		},
		{
			"XEX_HW_T_CALC_KEY_3",
			new List<int> { 1292, 0, 32 }
		},
		{
			"XEX_HW_T_CALC_KEY_4",
			new List<int> { 1296, 0, 32 }
		},
		{
			"XEX_HW_T_CALC_KEY_5",
			new List<int> { 1300, 0, 32 }
		},
		{
			"XEX_HW_T_CALC_KEY_6",
			new List<int> { 1304, 0, 32 }
		},
		{
			"XEX_HW_T_CALC_KEY_7",
			new List<int> { 1308, 0, 32 }
		},
		{
			"DATA_UNIT",
			new List<int> { 1312, 0, 32 }
		},
		{
			"AES_CMAC_SIZE0_KICK",
			new List<int> { 1316, 0, 1 }
		}
	};

	private static Dictionary<string, object> D3B8E331 = new Dictionary<string, object>
	{
		{
			"AES_CLK_ENABLE",
			new List<int> { 2064, 0, 1 }
		},
		{
			"DES_CLK_ENABLE",
			new List<int> { 2068, 0, 1 }
		},
		{
			"HASH_CLK_ENABLE",
			new List<int> { 2072, 0, 1 }
		},
		{
			"PKA_CLK_ENABLE",
			new List<int> { 2076, 0, 1 }
		},
		{
			"DMA_CLK_ENABLE",
			new List<int> { 2080, 0, 1 }
		},
		{
			"CLK_STATUS",
			new List<object>
			{
				2084,
				new Dictionary<string, object>
				{
					{
						"AES_CLK_STATUS",
						new List<int> { 0, 1 }
					},
					{
						"DES_CLK_STATUS",
						new List<int> { 1, 1 }
					},
					{
						"HASH_CLK_STATUS",
						new List<int> { 2, 1 }
					},
					{
						"PKA_CLK_STATUS",
						new List<int> { 3, 1 }
					},
					{
						"RC4_CLK_STATUS",
						new List<int> { 4, 1 }
					},
					{
						"C2_CLK_STATUS",
						new List<int> { 7, 1 }
					},
					{
						"DMA_CLK_STATUS",
						new List<int> { 8, 1 }
					}
				}
			}
		},
		{
			"RC4_CLK_ENABLE",
			new List<int> { 2132, 0, 1 }
		},
		{
			"MTI2_CLK_ENABLE",
			new List<int> { 2136, 0, 1 }
		}
	};

	private static Dictionary<string, List<int>> dictionary_1 = new Dictionary<string, List<int>>
	{
		{
			"CRYPTO_CTL",
			new List<int> { 2304, 0, 5 }
		},
		{
			"CRYPTO_BUSY",
			new List<int> { 2320, 0, 1 }
		},
		{
			"HASH_BUSY",
			new List<int> { 2332, 0, 1 }
		},
		{
			"VERSION",
			new List<int> { 2344, 0, 32 }
		},
		{
			"CONTEXT_ID",
			new List<int> { 2352, 0, 8 }
		},
		{
			"HASH_COMPARE_ERR_ID_FIFO0",
			new List<int> { 2368, 0, 26 }
		},
		{
			"HASH_COMPARE_ERR_ID_FIFO1",
			new List<int> { 2372, 0, 26 }
		},
		{
			"HASH_COMPARE_ERR_ID_FIFO2",
			new List<int> { 2376, 0, 26 }
		},
		{
			"HASH_COMPARE_ERR_ID_FIFO3",
			new List<int> { 2380, 0, 26 }
		}
	};

	private static Dictionary<string, object> dictionary_2 = new Dictionary<string, object>
	{
		{
			"DIN_BUFFER",
			new List<int> { 3072, 0, 32 }
		},
		{
			"DIN_MEM_DMA_BUSY",
			new List<int> { 3104, 0, 1 }
		},
		{
			"SRC_LLI_SRAM_ADDR",
			new List<int> { 3108, 0, 15 }
		},
		{
			"SRC_LLI_WORD0",
			new List<int> { 3112, 0, 32 }
		},
		{
			"SRC_LLI_WORD1",
			new List<object>
			{
				3116,
				new Dictionary<string, object>
				{
					{
						"BYTES_NUM",
						new List<int> { 0, 30 }
					},
					{
						"FIRST",
						new List<int> { 30, 1 }
					},
					{
						"LAST",
						new List<int> { 31, 1 }
					}
				}
			}
		},
		{
			"SRAM_SRC_ADDR",
			new List<int> { 3120, 0, 32 }
		},
		{
			"DIN_SRAM_BYTES_LEN",
			new List<int> { 3124, 0, 32 }
		},
		{
			"DIN_SRAM_DMA_BUSY",
			new List<int> { 3128, 0, 1 }
		},
		{
			"DIN_SRAM_ENDIANNESS",
			new List<int> { 3132, 0, 1 }
		},
		{
			"AXI_CPU_DIN_PARAMS",
			new List<object>
			{
				3136,
				new Dictionary<string, object>
				{
					{
						"RDID",
						new List<int> { 0, 4 }
					},
					{
						"PROT",
						new List<int> { 8, 2 }
					}
				}
			}
		},
		{
			"DIN_SW_RESET",
			new List<int> { 3140, 0, 1 }
		},
		{
			"DIN_CPU_DATA_SIZE",
			new List<int> { 3144, 0, 16 }
		},
		{
			"WRITE_ALIGN_LAST",
			new List<int> { 3148, 0, 1 }
		},
		{
			"FIFO_IN_EMPTY",
			new List<int> { 3152, 0, 1 }
		},
		{
			"DISABLE_OUTSTD_REQ",
			new List<int> { 3156, 0, 1 }
		},
		{
			"DIN_FIFO_RST_PNTR",
			new List<int> { 3160, 0, 1 }
		}
	};

	private static Dictionary<string, object> dictionary_3 = new Dictionary<string, object>
	{
		{
			"DOUT_BUFFER",
			new List<int> { 3328, 0, 32 }
		},
		{
			"DOUT_MEM_DMA_BUSY",
			new List<int> { 3360, 0, 1 }
		},
		{
			"DST_LLI_SRAM_ADDR",
			new List<int> { 3364, 0, 15 }
		},
		{
			"DST_LLI_WORD0",
			new List<int> { 3368, 0, 32 }
		},
		{
			"DST_LLI_WORD1",
			new List<object>
			{
				3372,
				new Dictionary<string, object>
				{
					{
						"BYTES_NUM",
						new List<int> { 0, 30 }
					},
					{
						"FIRST",
						new List<int> { 30, 1 }
					},
					{
						"LAST",
						new List<int> { 31, 1 }
					}
				}
			}
		},
		{
			"DOUT_SRAM_BYTES_LEN",
			new List<int> { 3380, 0, 32 }
		},
		{
			"DOUT_SRAM_DMA_BUSY",
			new List<int> { 3384, 0, 1 }
		},
		{
			"DOUT_SRAM_ENDIANNESS",
			new List<int> { 3388, 0, 1 }
		},
		{
			"READ_ALIGN_LAST",
			new List<int> { 3396, 0, 1 }
		},
		{
			"FIFO_MODE",
			new List<int> { 3400, 0, 1 }
		},
		{
			"DOUT_FIFO_EMPTY",
			new List<int> { 3408, 0, 1 }
		},
		{
			"AXI_CPU_DOUT_PARAMS",
			new List<object>
			{
				3412,
				new Dictionary<string, object>
				{
					{
						"CACHE_TYPE",
						new List<int> { 0, 4 }
					},
					{
						"WRID",
						new List<int> { 12, 4 }
					},
					{
						"PROT",
						new List<int> { 16, 2 }
					},
					{
						"FORCE_CPU_PARAMS",
						new List<int> { 18, 1 }
					}
				}
			}
		},
		{
			"DOUT_SW_RESET",
			new List<int> { 3416, 0, 1 }
		}
	};

	private static Dictionary<string, object> dictionary_4 = new Dictionary<string, object>
	{
		{
			"SRAM_DEST_ADDR",
			new List<int> { 3376, 0, 32 }
		},
		{
			"DES_KEY_0",
			new List<int> { 520, 0, 32 }
		},
		{
			"DES_KEY_1",
			new List<int> { 524, 0, 32 }
		},
		{
			"DES_KEY_2",
			new List<int> { 528, 0, 32 }
		},
		{
			"DES_KEY_3",
			new List<int> { 532, 0, 32 }
		},
		{
			"DES_KEY_4",
			new List<int> { 536, 0, 32 }
		},
		{
			"DES_KEY_5",
			new List<int> { 540, 0, 32 }
		},
		{
			"DES_CONTROL_0",
			new List<object>
			{
				544,
				new Dictionary<string, object>
				{
					{
						"ENC",
						new List<int> { 0, 1 }
					},
					{
						"KEY_NUM",
						new List<int> { 1, 2 }
					},
					{
						"MODE",
						new List<int> { 3, 2 }
					}
				}
			}
		},
		{
			"DES_CONTROL_1",
			new List<int> { 548, 0, 32 }
		},
		{
			"DES_IV_0",
			new List<int> { 552, 0, 32 }
		},
		{
			"DES_IV_1",
			new List<int> { 556, 0, 32 }
		},
		{
			"DES_VERSION",
			new List<object>
			{
				560,
				new Dictionary<string, object>
				{
					{
						"FIXES",
						new List<int> { 0, 8 }
					},
					{
						"MINOR",
						new List<int> { 8, 4 }
					},
					{
						"MAJOR",
						new List<int> { 12, 4 }
					}
				}
			}
		},
		{
			"DES_RBG_INIT_VAL",
			new List<int> { 584, 0, 8 }
		},
		{
			"DES_RBG_READY",
			new List<int> { 588, 0, 1 }
		},
		{
			"DES_BUSY",
			new List<int> { 624, 0, 1 }
		},
		{
			"DES_SW_RESET",
			new List<int> { 640, 0, 1 }
		}
	};

	private static Dictionary<string, object> EE0498AA = new Dictionary<string, object>
	{
		{
			"DSCRPTR_COMPLETION_COUNTER0",
			new List<object>
			{
				3584,
				new Dictionary<string, object>
				{
					{
						"COMPLETION_COUNTER",
						new List<int> { 0, 6 }
					},
					{
						"OVERFLOW_COUNTER",
						new List<int> { 6, 1 }
					}
				}
			}
		},
		{
			"DSCRPTR_COMPLETION_COUNTER1",
			new List<object>
			{
				3588,
				new Dictionary<string, object>
				{
					{
						"COMPLETION_COUNTER",
						new List<int> { 0, 6 }
					},
					{
						"OVERFLOW_COUNTER",
						new List<int> { 6, 1 }
					}
				}
			}
		},
		{
			"DSCRPTR_COMPLETION_STATUS",
			new List<int> { 3644, 0, 2 }
		},
		{
			"DSCRPTR_SW_RESET",
			new List<int> { 3648, 0, 1 }
		},
		{
			"DSCRPTR_CNTX_SWITCH_COUNTER_VAL",
			new List<int> { 3652, 0, 32 }
		},
		{
			"DSCRPTR_DISABLE_CNTX_SWITCH",
			new List<int> { 3656, 0, 1 }
		},
		{
			"DSCRPTR_DEBUG_MODE",
			new List<int> { 3660, 0, 1 }
		},
		{
			"DSCRPTR_FILTER_DROPPED_CNT",
			new List<int> { 3664, 0, 32 }
		},
		{
			"DSCRPTR_FILTER_DROPPED_MEM_CNT",
			new List<int> { 3668, 0, 32 }
		},
		{
			"DSCRPTR_FILTER_DEBUG",
			new List<int> { 3672, 0, 8 }
		},
		{
			"DSCRPTR_FILTER_DROPPED_ADDRESS",
			new List<int> { 3676, 0, 32 }
		},
		{
			"DSCRPTR_QUEUE_SRAM_SIZE",
			new List<int> { 3680, 0, 10 }
		},
		{
			"DSCRPTR_SINGLE_ADDR_EN",
			new List<int> { 3684, 0, 1 }
		},
		{
			"DSCRPTR_MEASURE_CNTR",
			new List<int> { 3688, 0, 32 }
		},
		{
			"DSCRPTR_FILTER_DROPPED_ADDRESS_HIGH",
			new List<int> { 3692, 0, 16 }
		},
		{
			"DSCRPTR_QUEUE0_WORD0",
			new List<int> { 3712, 0, 32 }
		},
		{
			"DSCRPTR_QUEUE0_WORD1",
			new List<object>
			{
				3716,
				new Dictionary<string, object>
				{
					{
						"DIN_DMA_MODE",
						new List<int> { 0, 2 }
					},
					{
						"DIN_SIZE",
						new List<int> { 2, 24 }
					},
					{
						"NS_BIT",
						new List<int> { 26, 1 }
					},
					{
						"DIN_CONST_VALUE",
						new List<int> { 27, 1 }
					},
					{
						"NOT_LAST",
						new List<int> { 28, 1 }
					},
					{
						"LOCK_QUEUE",
						new List<int> { 29, 1 }
					},
					{
						"DIN_VIRTUAL_HOST",
						new List<int> { 30, 2 }
					}
				}
			}
		},
		{
			"DSCRPTR_QUEUE0_WORD2",
			new List<int> { 3720, 0, 32 }
		},
		{
			"DSCRPTR_QUEUE0_WORD3",
			new List<object>
			{
				3724,
				new Dictionary<string, object>
				{
					{
						"DOUT_DMA_MODE",
						new List<int> { 0, 2 }
					},
					{
						"DOUT_SIZE",
						new List<int> { 2, 24 }
					},
					{
						"NS_BIT",
						new List<int> { 26, 1 }
					},
					{
						"DOUT_LAST_IND",
						new List<int> { 27, 1 }
					},
					{
						"HASH_XOR_BIT",
						new List<int> { 29, 1 }
					},
					{
						"DOUT_VIRTUAL_HOST",
						new List<int> { 30, 2 }
					}
				}
			}
		},
		{
			"DSCRPTR_QUEUE0_WORD4",
			new List<object>
			{
				3728,
				new Dictionary<string, List<int>>
				{
					{
						"DATA_FLOW_MODE",
						new List<int> { 0, 6 }
					},
					{
						"AES_SEL_N_HASH",
						new List<int> { 6, 1 }
					},
					{
						"AES_XOR_CRYPTO_KEY",
						new List<int> { 7, 1 }
					},
					{
						"ACK_NEEDED",
						new List<int> { 8, 2 }
					},
					{
						"CIPHER_MODE",
						new List<int> { 10, 4 }
					},
					{
						"CMAC_SIZE0",
						new List<int> { 14, 1 }
					},
					{
						"CIPHER_DO",
						new List<int> { 15, 2 }
					},
					{
						"CIPHER_CONF0",
						new List<int> { 17, 2 }
					},
					{
						"CIPHER_CONF1",
						new List<int> { 19, 1 }
					},
					{
						"CIPHER_CONF2",
						new List<int> { 20, 2 }
					},
					{
						"KEY_SIZE",
						new List<int> { 22, 2 }
					},
					{
						"SETUP_OPERATION",
						new List<int> { 24, 4 }
					},
					{
						"DIN_SRAM_ENDIANNESS",
						new List<int> { 28, 1 }
					},
					{
						"DOUT_SRAM_ENDIANNESS",
						new List<int> { 29, 1 }
					},
					{
						"WORD_SWAP",
						new List<int> { 30, 1 }
					},
					{
						"BYTES_SWAP",
						new List<int> { 31, 1 }
					}
				}
			}
		},
		{
			"DSCRPTR_QUEUE0_WORD5",
			new List<object>
			{
				3732,
				new Dictionary<string, object>
				{
					{
						"DIN_ADDR_HIGH",
						new List<int> { 0, 16 }
					},
					{
						"DOUT_ADDR_HIGH",
						new List<int> { 16, 16 }
					}
				}
			}
		},
		{
			"DSCRPTR_QUEUE0_WATERMARK",
			new List<int> { 3736, 0, 10 }
		},
		{
			"DSCRPTR_QUEUE0_CONTENT",
			new List<int> { 3740, 0, 10 }
		},
		{
			"DSCRPTR_QUEUE1_WORD0",
			new List<int> { 3744, 0, 32 }
		},
		{
			"DSCRPTR_QUEUE1_WORD1",
			new List<object>
			{
				3748,
				new Dictionary<string, object>
				{
					{
						"DIN_DMA_MODE",
						new List<int> { 0, 2 }
					},
					{
						"DIN_SIZE",
						new List<int> { 2, 24 }
					},
					{
						"NS_BIT",
						new List<int> { 26, 1 }
					},
					{
						"DIN_CONST",
						new List<int> { 27, 1 }
					},
					{
						"NOT_LAST",
						new List<int> { 28, 1 }
					},
					{
						"LOCK_QUEUE",
						new List<int> { 29, 1 }
					},
					{
						"DIN_VIRTUAL_HOST",
						new List<int> { 30, 2 }
					}
				}
			}
		},
		{
			"DSCRPTR_QUEUE1_WORD2",
			new List<int> { 3752, 0, 32 }
		},
		{
			"DSCRPTR_QUEUE1_WORD3",
			new List<object>
			{
				3756,
				new Dictionary<string, object>
				{
					{
						"DOUT_DMA_MODE",
						new List<int> { 0, 2 }
					},
					{
						"DOUT_SIZE",
						new List<int> { 2, 24 }
					},
					{
						"NS_BIT",
						new List<int> { 26, 1 }
					},
					{
						"DOUT_LAST_IND",
						new List<int> { 27, 1 }
					},
					{
						"HASH_XOR_BIT",
						new List<int> { 29, 1 }
					},
					{
						"DOUT_VIRTUAL_HOST",
						new List<int> { 30, 2 }
					}
				}
			}
		},
		{
			"DSCRPTR_QUEUE1_WORD4",
			new List<object>
			{
				3760,
				new Dictionary<string, object>
				{
					{
						"DATA_FLOW_MODE",
						new List<int> { 0, 6 }
					},
					{
						"AES_SEL_N_HASH",
						new List<int> { 6, 1 }
					},
					{
						"AES_XOR_CRYPTO_KEY",
						new List<int> { 7, 1 }
					},
					{
						"ACK_NEEDED",
						new List<int> { 8, 2 }
					},
					{
						"CIPHER_MODE",
						new List<int> { 10, 4 }
					},
					{
						"CMAC_SIZE0",
						new List<int> { 14, 1 }
					},
					{
						"CIPHER_DO",
						new List<int> { 15, 2 }
					},
					{
						"CIPHER_CONF0",
						new List<int> { 17, 2 }
					},
					{
						"CIPHER_CONF1",
						new List<int> { 19, 1 }
					},
					{
						"CIPHER_CONF2",
						new List<int> { 20, 2 }
					},
					{
						"KEY_SIZE",
						new List<int> { 22, 2 }
					},
					{
						"SETUP_OPERATION",
						new List<int> { 24, 4 }
					},
					{
						"DIN_SRAM_ENDIANNESS",
						new List<int> { 28, 1 }
					},
					{
						"DOUT_SRAM_ENDIANNESS",
						new List<int> { 29, 1 }
					},
					{
						"WORD_SWAP",
						new List<int> { 30, 1 }
					},
					{
						"BYTES_SWAP",
						new List<int> { 31, 1 }
					}
				}
			}
		},
		{
			"DSCRPTR_QUEUE1_WORD5",
			new List<object>
			{
				3764,
				new Dictionary<string, object>
				{
					{
						"DIN_ADDR_HIGH",
						new List<int> { 0, 16 }
					},
					{
						"DOUT_ADDR_HIGH",
						new List<int> { 16, 16 }
					}
				}
			}
		},
		{
			"DSCRPTR_QUEUE1_WATERMARK",
			new List<int> { 3768, 0, 10 }
		},
		{
			"DSCRPTR_QUEUE1_CONTENT",
			new List<int> { 3772, 0, 10 }
		}
	};

	private static Dictionary<string, object> dictionary_5 = new Dictionary<string, object>
	{
		{
			"HASH_H0",
			new List<int> { 1600, 0, 32 }
		},
		{
			"HASH_H1",
			new List<int> { 1604, 0, 32 }
		},
		{
			"HASH_H2",
			new List<int> { 1608, 0, 32 }
		},
		{
			"HASH_H3",
			new List<int> { 1612, 0, 32 }
		},
		{
			"HASH_H4",
			new List<int> { 1616, 0, 32 }
		},
		{
			"HASH_H5",
			new List<int> { 1620, 0, 32 }
		},
		{
			"HASH_H6",
			new List<int> { 1624, 0, 32 }
		},
		{
			"HASH_H7",
			new List<int> { 1628, 0, 32 }
		},
		{
			"HASH_H8",
			new List<int> { 1632, 0, 32 }
		},
		{
			"FLUSH_AES_MAC_BUF",
			new List<int> { 1664, 0, 1 }
		},
		{
			"AUTO_HW_PADDING",
			new List<int> { 1668, 0, 1 }
		},
		{
			"LOAD_INIT_STATE",
			new List<int> { 1684, 0, 1 }
		},
		{
			"TRUNC_OUTPUT",
			new List<int> { 1688, 0, 2 }
		},
		{
			"DUMP_COMPARE_REST",
			new List<int> { 1692, 0, 1 }
		},
		{
			"DUMP_TO_DOUT",
			new List<int> { 1696, 0, 1 }
		},
		{
			"HASH_SEL_AES_MAC",
			new List<int> { 1700, 0, 1 }
		},
		{
			"HASH_H0_SAVED",
			new List<int> { 1856, 0, 32 }
		},
		{
			"HASH_H1_SAVED",
			new List<int> { 1860, 0, 32 }
		},
		{
			"HASH_H2_SAVED",
			new List<int> { 1864, 0, 32 }
		},
		{
			"HASH_H3_SAVED",
			new List<int> { 1868, 0, 32 }
		},
		{
			"HASH_H4_SAVED",
			new List<int> { 1872, 0, 32 }
		},
		{
			"HASH_H5_SAVED",
			new List<int> { 1876, 0, 32 }
		},
		{
			"HASH_H6_SAVED",
			new List<int> { 1880, 0, 32 }
		},
		{
			"HASH_H7_SAVED",
			new List<int> { 1884, 0, 32 }
		},
		{
			"HASH_H8_SAVED",
			new List<int> { 1888, 0, 32 }
		},
		{
			"HASH_VERSION",
			new List<object>
			{
				1968,
				new Dictionary<string, object>
				{
					{
						"FIXES",
						new List<int> { 0, 8 }
					},
					{
						"MINOR_VERSION_NUMBER",
						new List<int> { 8, 4 }
					},
					{
						"MAJOR_VERSION_NUMBER",
						new List<int> { 12, 4 }
					}
				}
			}
		},
		{
			"HASH_CONTROL",
			new List<object>
			{
				1984,
				new Dictionary<string, object>
				{
					{
						"MODE_0_1",
						new List<int> { 0, 2 }
					},
					{
						"MODE_3",
						new List<int> { 3, 1 }
					}
				}
			}
		},
		{
			"HASH_PAD_EN",
			new List<int> { 1988, 0, 1 }
		},
		{
			"HASH_PAD_CFG",
			new List<int> { 1992, 2, 1 }
		},
		{
			"HASH_CUR_LEN_0",
			new List<int> { 1996, 0, 32 }
		},
		{
			"HASH_CUR_LEN_1",
			new List<int> { 2000, 0, 32 }
		},
		{
			"HASH_PARAM",
			new List<object>
			{
				2012,
				new Dictionary<string, object>
				{
					{
						"CW",
						new List<int> { 0, 4 }
					},
					{
						"CH",
						new List<int> { 4, 4 }
					},
					{
						"DW",
						new List<int> { 8, 4 }
					},
					{
						"SHA_512_EXISTS",
						new List<int> { 12, 1 }
					},
					{
						"PAD_EXISTS",
						new List<int> { 13, 1 }
					},
					{
						"MD5_EXISTS",
						new List<int> { 14, 1 }
					},
					{
						"HMAC_EXISTS",
						new List<int> { 15, 1 }
					},
					{
						"SHA_256_EXISTS",
						new List<int> { 16, 1 }
					},
					{
						"HASH_COMPARE_EXISTS",
						new List<int> { 17, 1 }
					},
					{
						"DUMP_HASH_TO_DOUT_EXISTS",
						new List<int> { 18, 1 }
					}
				}
			}
		},
		{
			"HASH_AES_SW_RESET",
			new List<int> { 2020, 0, 1 }
		},
		{
			"HASH_ENDIANESS",
			new List<int> { 2024, 0, 1 }
		},
		{
			"HASH_LOAD_DIGEST",
			new List<int> { 2044, 0, 1 }
		}
	};

	private static Dictionary<string, object> dictionary_6 = new Dictionary<string, object>
	{
		{
			"AXIM_MON_INFLIGHT0",
			new List<int> { 2816, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHT1",
			new List<int> { 2820, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHT2",
			new List<int> { 2824, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHT3",
			new List<int> { 2828, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHT4",
			new List<int> { 2832, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHT5",
			new List<int> { 2836, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHT8",
			new List<int> { 2848, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHT9",
			new List<int> { 2852, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHT10",
			new List<int> { 2856, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHT11",
			new List<int> { 2860, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHTLAST0",
			new List<int> { 2880, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHTLAST1",
			new List<int> { 2884, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHTLAST2",
			new List<int> { 2888, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHTLAST3",
			new List<int> { 2892, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHTLAST4",
			new List<int> { 2896, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHTLAST5",
			new List<int> { 2900, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHTLAST8",
			new List<int> { 2912, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHTLAST9",
			new List<int> { 2916, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHTLAST10",
			new List<int> { 2920, 0, 8 }
		},
		{
			"AXIM_MON_INFLIGHTLAST11",
			new List<int> { 2924, 0, 8 }
		},
		{
			"AXIM_PIDTABLE0",
			new List<object>
			{
				2928,
				new Dictionary<string, object>
				{
					{
						"PID_BROKEN1",
						new List<int> { 0, 1 }
					},
					{
						"PID_BROKEN2",
						new List<int> { 1, 1 }
					},
					{
						"PID_OSCNTR",
						new List<int> { 2, 8 }
					},
					{
						"PID_ID",
						new List<int> { 10, 5 }
					}
				}
			}
		},
		{
			"AXIM_PIDTABLE1",
			new List<object>
			{
				2932,
				new Dictionary<string, object>
				{
					{
						"PID_BROKEN1",
						new List<int> { 0, 1 }
					},
					{
						"PID_BROKEN2",
						new List<int> { 1, 1 }
					},
					{
						"PID_OSCNTR",
						new List<int> { 2, 8 }
					},
					{
						"PID_ID",
						new List<int> { 10, 5 }
					}
				}
			}
		},
		{
			"AXIM_PIDTABLE2",
			new List<object>
			{
				2936,
				new Dictionary<string, object>
				{
					{
						"PID_BROKEN1",
						new List<int> { 0, 1 }
					},
					{
						"PID_BROKEN2",
						new List<int> { 1, 1 }
					},
					{
						"PID_OSCNTR",
						new List<int> { 2, 8 }
					},
					{
						"PID_ID",
						new List<int> { 10, 5 }
					}
				}
			}
		},
		{
			"AXIM_PIDTABLE3",
			new List<object>
			{
				2940,
				new Dictionary<string, object>
				{
					{
						"PID_BROKEN1",
						new List<int> { 0, 1 }
					},
					{
						"PID_BROKEN2",
						new List<int> { 1, 1 }
					},
					{
						"PID_OSCNTR",
						new List<int> { 2, 8 }
					},
					{
						"PID_ID",
						new List<int> { 10, 5 }
					}
				}
			}
		},
		{
			"AXIM_MON_COMP0",
			new List<int> { 2944, 0, 16 }
		},
		{
			"AXIM_MON_COMP1",
			new List<int> { 2948, 0, 16 }
		},
		{
			"AXIM_MON_COMP2",
			new List<int> { 2952, 0, 16 }
		},
		{
			"AXIM_MON_COMP3",
			new List<int> { 2956, 0, 16 }
		},
		{
			"AXIM_MON_COMP4",
			new List<int> { 2960, 0, 16 }
		},
		{
			"AXIM_MON_COMP5",
			new List<int> { 2964, 0, 16 }
		},
		{
			"AXIM_MON_COMP8",
			new List<int> { 2976, 0, 16 }
		},
		{
			"AXIM_MON_COMP9",
			new List<int> { 2980, 0, 16 }
		},
		{
			"AXIM_MON_COMP10",
			new List<int> { 2984, 0, 16 }
		},
		{
			"AXIM_MON_COMP11",
			new List<int> { 2988, 0, 16 }
		},
		{
			"AXIM_MON_RMAX",
			new List<int> { 2996, 0, 32 }
		},
		{
			"AXIM_MON_RMIN",
			new List<int> { 3000, 0, 32 }
		},
		{
			"AXIM_MON_WMAX",
			new List<int> { 3004, 0, 32 }
		},
		{
			"AXIM_MON_WMIN",
			new List<int> { 3008, 0, 32 }
		},
		{
			"AXIM_MON_ERR",
			new List<object>
			{
				3012,
				new Dictionary<string, object>
				{
					{
						"BRESP",
						new List<int> { 0, 2 }
					},
					{
						"BID",
						new List<int> { 2, 4 }
					},
					{
						"RRESP",
						new List<int> { 16, 2 }
					},
					{
						"RID",
						new List<int> { 18, 4 }
					}
				}
			}
		},
		{
			"AXIM_RDSTAT",
			new List<int> { 3016, 0, 4 }
		},
		{
			"AXIM_RLATENCY",
			new List<int> { 3024, 0, 32 }
		},
		{
			"AXIM_RBURST",
			new List<int> { 3028, 0, 32 }
		},
		{
			"AXIM_WLATENCY",
			new List<int> { 3032, 0, 32 }
		},
		{
			"AXIM_WBURST",
			new List<int> { 3036, 0, 32 }
		},
		{
			"AXIM_CACHETYPE_CFG",
			new List<object>
			{
				3040,
				new Dictionary<string, object>
				{
					{
						"ICACHE_ARCACHE",
						new List<int> { 0, 4 }
					},
					{
						"DCACHE_ARCACHE",
						new List<int> { 4, 4 }
					},
					{
						"DD_ARCACHE",
						new List<int> { 8, 4 }
					},
					{
						"NOT_USED0",
						new List<int> { 12, 4 }
					},
					{
						"ICACHE_AWCACHE",
						new List<int> { 16, 4 }
					},
					{
						"DCACHE_AWCACHE",
						new List<int> { 20, 4 }
					},
					{
						"DD_AWCACHE",
						new List<int> { 24, 4 }
					},
					{
						"NOT_USED1",
						new List<int> { 28, 4 }
					}
				}
			}
		},
		{
			"AXIM_PROT_CFG",
			new List<object>
			{
				3044,
				new Dictionary<string, object>
				{
					{
						"ICACHE_ARPROT",
						new List<int> { 0, 2 }
					},
					{
						"DCACHE_ARPROT",
						new List<int> { 2, 2 }
					},
					{
						"DD_ARPROT",
						new List<int> { 4, 1 }
					},
					{
						"NOT_USED0",
						new List<int> { 5, 3 }
					},
					{
						"ICACHE_AWPROT",
						new List<int> { 8, 2 }
					},
					{
						"DCACHE_AWPROT",
						new List<int> { 10, 2 }
					},
					{
						"DD_AWPROT",
						new List<int> { 12, 1 }
					},
					{
						"NOT_USED1",
						new List<int> { 13, 3 }
					}
				}
			}
		},
		{
			"AXIM_CFG1",
			new List<object>
			{
				3048,
				new Dictionary<string, object>
				{
					{
						"RD_AFTER_WR_STALL",
						new List<int> { 0, 4 }
					},
					{
						"BRESPMASK",
						new List<int> { 4, 1 }
					},
					{
						"RRESPMASK",
						new List<int> { 5, 1 }
					},
					{
						"INFLTMASK",
						new List<int> { 6, 1 }
					},
					{
						"COMPMASK",
						new List<int> { 7, 1 }
					},
					{
						"ACCUM_LIMIT",
						new List<int> { 16, 5 }
					}
				}
			}
		},
		{
			"AXIM_ACE_CONST",
			new List<object>
			{
				3052,
				new Dictionary<string, object>
				{
					{
						"ARDOMAIN",
						new List<int> { 0, 2 }
					},
					{
						"AWDOMAIN",
						new List<int> { 2, 2 }
					},
					{
						"ARBAR",
						new List<int> { 4, 2 }
					},
					{
						"AWBAR",
						new List<int> { 6, 2 }
					},
					{
						"ARSNOOP",
						new List<int> { 8, 4 }
					},
					{
						"AWSNOOP_NOT_ALIGNED",
						new List<int> { 12, 3 }
					},
					{
						"AWSNOOP_ALIGNED",
						new List<int> { 15, 3 }
					},
					{
						"AWADDR_NOT_MASKED",
						new List<int> { 18, 7 }
					},
					{
						"AWLEN_VAL",
						new List<int> { 25, 4 }
					}
				}
			}
		},
		{
			"AXIM_CACHE_PARAMS",
			new List<object>
			{
				3056,
				new Dictionary<string, object>
				{
					{
						"AWCACHE_LAST",
						new List<int> { 0, 4 }
					},
					{
						"AWCACHE",
						new List<int> { 4, 4 }
					},
					{
						"ARCACHE",
						new List<int> { 8, 4 }
					}
				}
			}
		}
	};

	private string string_0 = "DACD8B5FDA8A766FB7BCAA43F0B16915CE7B47714F1395FDEBCF12A2D41155B0FB587A51FECCCB4DDA1C8E5EB9EB69B86DAF2C620F6C2735215A5F22C0B6CE377AA0D07EB38ED340B5629FC2890494B078A63D6D07FDEACDBE3E7F27FDE4B143F49DB4971437E6D00D9E18B56F02DABEB0000B6E79516D0C8074B5A42569FD0D9196655D2A4030D42DFE05E9F64883E6D5F79A5BFA3E7014C9A62853DC1F21D5D626F4D0846DB16452187DD776E8886B48C210C9E208059E7CAFC997FD2CA210775C1A5D9AA261252FB975268D970C62733871D57814098A453DF92BC6CA19025CD9D430F02EE46F80DE6C63EA802BEF90673AAC4C6667F2883FB4501FA77455";

	private string EE80FF9A = "C1A9D3E65C7EAEB31932E9DD224C07C070D879FB4FE518C64E92C24B79DC1EE1535D91D38DD34D7E32A22DEED60F0727FF8F8747E2598ACB5DDC73C61D2434A91D568FE3E773BD0D17AA46B0364E0DCF3B41E0034605D572B6CD7DD8A816E7D684181B1646628576D1E22F55071687B9E5B2F9C9536167B7EDCF10F1F85BE57B6EE873BFE952BB33F0001140E0E46AF2D64D39C568D8E372BCE3609BCACA5316E4EBDDE5721B33611E064DF41A4BCF0A3A395791D3203BF220DC71F4267093CEB78E30A844D4631DE8CE6D0514202BB58AD2024B16558C2AD9B30CE05043FF67C4D265A3D5F3275D93AFDC1A39625C2C5BD6FDCDBD75E76E6D9E74E9672B5897";

	private static Dictionary<string, ushort> EE94CB25 = new Dictionary<string, ushort> { { "DXCC_CON", 0 } };

	private readonly F4A59784 E7152EA2;

	public E5964FB0 e5964FB0_0;

	private static uint[] smethod_0()
	{
		return (uint[])new GClass128().E8AA1C3C(null, 11175562);
	}

	private static uint smethod_1(int E808343F)
	{
		if (E808343F < 32)
		{
			return (uint)((1 << E808343F) - 1);
		}
		return uint.MaxValue;
	}

	private static uint D2374691(int int_20, int DCA0A392, int C42FE035)
	{
		return Class607.B630A78B.object_0[836]((int_20 & ((1 << DCA0A392) - 1)) << C42FE035);
	}

	public static uint[] D432151A(uint[] uint_0, int int_20)
	{
		List<int> list = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD4"])[1])["CIPHER_MODE"];
		int c42FE = list[0];
		int dCA0A = list[1];
		uint_0[4] |= D2374691(int_20, dCA0A, c42FE);
		return uint_0;
	}

	public static uint[] smethod_2(uint[] A99AAB38, int C0360D22)
	{
		List<int> list = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD4"])[1])["CIPHER_CONF0"];
		int c42FE = list[0];
		int dCA0A = list[1];
		A99AAB38[4] |= D2374691(C0360D22, dCA0A, c42FE);
		return A99AAB38;
	}

	public static uint[] D7159C96(uint[] uint_0, int int_20)
	{
		List<int> list = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD4"])[1])["CIPHER_CONF1"];
		int c42FE = list[0];
		int dCA0A = list[1];
		uint_0[4] |= D2374691(int_20, dCA0A, c42FE);
		return uint_0;
	}

	public static uint[] CDB52104(uint[] uint_0, int int_20)
	{
		List<int> list = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD4"])[1])["SETUP_OPERATION"];
		int c42FE = list[0];
		int dCA0A = list[1];
		uint_0[4] |= D2374691(int_20, dCA0A, c42FE);
		return uint_0;
	}

	public static uint[] smethod_3(uint[] uint_0, int int_20)
	{
		List<int> list = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD4"])[1])["DATA_FLOW_MODE"];
		int c42FE = list[0];
		int dCA0A = list[1];
		uint_0[4] |= D2374691(int_20, dCA0A, c42FE);
		return uint_0;
	}

	public static uint[] smethod_4(uint[] uint_0, uint uint_1, int int_20)
	{
		List<int> list = (List<int>)EE0498AA["DSCRPTR_QUEUE0_WORD2"];
		int c42FE = list[1];
		int dCA0A = list[2];
		uint_0[2] |= D2374691((int)uint_1 & -1, dCA0A, c42FE);
		List<int> list2 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD3"])[1])["DOUT_DMA_MODE"];
		c42FE = list2[0];
		dCA0A = list2[1];
		uint_0[3] |= D2374691(1, dCA0A, c42FE);
		List<int> list3 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD3"])[1])["DOUT_SIZE"];
		c42FE = list3[0];
		dCA0A = list3[1];
		uint_0[3] |= D2374691(int_20, dCA0A, c42FE);
		return uint_0;
	}

	public static uint[] smethod_5(uint[] uint_0, uint uint_1, int F822AB3F, int B0A407A7, int int_20)
	{
		List<int> list = (List<int>)EE0498AA["DSCRPTR_QUEUE0_WORD2"];
		int c42FE = list[1];
		int dCA0A = list[2];
		uint_0[2] |= D2374691((int)uint_1 & -1, dCA0A, c42FE);
		List<int> list2 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD5"])[1])["DOUT_ADDR_HIGH"];
		c42FE = list2[0];
		dCA0A = list2[1];
		uint_0[5] |= D2374691((int)((uint_1 & 0xFFFFFFFFu) << 16), dCA0A, c42FE);
		List<int> list3 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD3"])[1])["DOUT_DMA_MODE"];
		c42FE = list3[0];
		dCA0A = list3[1];
		uint_0[3] |= D2374691(2, dCA0A, c42FE);
		List<int> list4 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD3"])[1])["DOUT_SIZE"];
		c42FE = list4[0];
		dCA0A = list4[1];
		uint_0[3] |= D2374691(F822AB3F, dCA0A, c42FE);
		List<int> list5 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD3"])[1])["DOUT_LAST_IND"];
		c42FE = list5[0];
		dCA0A = list5[1];
		uint_0[3] |= D2374691(int_20, dCA0A, c42FE);
		List<int> list6 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD3"])[1])["NS_BIT"];
		c42FE = list6[0];
		dCA0A = list6[1];
		uint_0[3] |= D2374691(int_20, dCA0A, c42FE);
		return uint_0;
	}

	public static uint[] B88CF7AD(uint[] uint_0, int DE220CB9)
	{
		List<int> list = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD4"])[1])["KEY_SIZE"];
		int c42FE = list[0];
		int dCA0A = list[1];
		uint_0[4] |= D2374691((DE220CB9 >> 3) - 2, dCA0A, c42FE);
		return uint_0;
	}

	public static uint[] EBB1F7A0(uint[] uint_0, int E8B6FC89, int int_20)
	{
		List<int> list = (List<int>)EE0498AA["DSCRPTR_QUEUE0_WORD0"];
		int c42FE = list[1];
		int dCA0A = list[2];
		uint_0[0] |= D2374691((int)(E8B6FC89 & 0xFFFFFFFFL), dCA0A, c42FE);
		List<int> list2 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD1"])[1])["DIN_DMA_MODE"];
		c42FE = list2[0];
		dCA0A = list2[1];
		uint_0[1] |= D2374691(1, dCA0A, c42FE);
		List<int> list3 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD1"])[1])["DIN_SIZE"];
		c42FE = list3[0];
		dCA0A = list3[1];
		uint_0[1] |= D2374691(int_20, dCA0A, c42FE);
		return uint_0;
	}

	public static uint[] smethod_6(uint[] uint_0, int int_20, int int_21)
	{
		List<int> list = (List<int>)EE0498AA["DSCRPTR_QUEUE0_WORD0"];
		int c42FE = list[1];
		int dCA0A = list[2];
		uint_0[0] |= D2374691((int)(int_20 & 0xFFFFFFFFL), dCA0A, c42FE);
		List<int> list2 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD1"])[1])["DIN_CONST_VALUE"];
		c42FE = list2[0];
		dCA0A = list2[1];
		uint_0[1] |= D2374691(1, dCA0A, c42FE);
		List<int> list3 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD1"])[1])["DIN_DMA_MODE"];
		c42FE = list3[0];
		dCA0A = list3[1];
		uint_0[1] |= D2374691(1, dCA0A, c42FE);
		List<int> list4 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD1"])[1])["DIN_SIZE"];
		c42FE = list4[0];
		dCA0A = list4[1];
		uint_0[1] |= D2374691(int_21, dCA0A, c42FE);
		return uint_0;
	}

	public static uint[] smethod_7(uint[] D8B02C0C, int int_20)
	{
		List<int> list = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD4"])[1])["CIPHER_DO"];
		int c42FE = list[0];
		int dCA0A = list[1];
		D8B02C0C[4] |= D2374691(int_20, dCA0A, c42FE);
		return D8B02C0C;
	}

	public static uint[] DE8F7019(uint[] C7AD4410, int F5161C80, int E7107D0F)
	{
		List<int> list = (List<int>)EE0498AA["DSCRPTR_QUEUE0_WORD0"];
		int c42FE = list[1];
		int dCA0A = list[2];
		C7AD4410[0] |= D2374691((int)(F5161C80 & 0xFFFFFFFFL), dCA0A, c42FE);
		List<int> list2 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD1"])[1])["DIN_DMA_MODE"];
		c42FE = list2[0];
		dCA0A = list2[1];
		C7AD4410[1] |= D2374691(0, dCA0A, c42FE);
		List<int> list3 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD1"])[1])["DIN_SIZE"];
		c42FE = list3[0];
		dCA0A = list3[1];
		C7AD4410[1] |= D2374691(E7107D0F, dCA0A, c42FE);
		return C7AD4410;
	}

	public static uint[] smethod_8(uint[] BB0CFC33, int int_20, int AF0F170C, int int_21, int int_22, int BC8D5A0F)
	{
		List<int> list = (List<int>)EE0498AA["DSCRPTR_QUEUE0_WORD0"];
		int c42FE = list[1];
		int dCA0A = list[2];
		BB0CFC33[0] |= D2374691((int)(AF0F170C & 0xFFFFFFFFL), dCA0A, c42FE);
		List<int> list2 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD5"])[1])["DIN_ADDR_HIGH"];
		c42FE = list2[0];
		dCA0A = list2[1];
		BB0CFC33[5] |= D2374691(AF0F170C & 0xFFFF, dCA0A, c42FE);
		List<int> list3 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD1"])[1])["DIN_DMA_MODE"];
		c42FE = list3[0];
		dCA0A = list3[1];
		BB0CFC33[1] |= D2374691(int_20, dCA0A, c42FE);
		List<int> list4 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD1"])[1])["DIN_SIZE"];
		c42FE = list4[0];
		dCA0A = list4[1];
		BB0CFC33[1] |= D2374691(int_21, dCA0A, c42FE);
		List<int> list5 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD1"])[1])["DIN_VIRTUAL_HOST"];
		c42FE = list5[0];
		dCA0A = list5[1];
		BB0CFC33[1] |= D2374691(int_22, dCA0A, c42FE);
		List<int> list6 = ((Dictionary<string, List<int>>)((List<object>)EE0498AA["DSCRPTR_QUEUE0_WORD1"])[1])["NS_BIT"];
		c42FE = list6[0];
		dCA0A = list6[1];
		BB0CFC33[1] |= D2374691(BC8D5A0F, dCA0A, c42FE);
		return BB0CFC33;
	}

	public GClass24(E5964FB0 FCA2160E)
	{
		Class607.B630A78B.object_0[571](this);
		E7152EA2 = new F4A59784(FCA2160E);
	}

	private void method_0()
	{
		e5964FB0_0.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.uint_4 + 2568, 4u);
	}

	private uint F120C79C()
	{
		uint num;
		do
		{
			num = e5964FB0_0.gclass51_0.EE83B4B7(GClass112.C78DEB29.A8054FBA.uint_4 + 2560)[0];
		}
		while (num == 0);
		return num;
	}

	private void D481C380(int DE2B08A2)
	{
	}

	private int method_1(int B61ACAAA)
	{
		return B61ACAAA;
	}

	private bool F2BCAA26(int D331FA3C)
	{
		if (D331FA3C != 0)
		{
			e5964FB0_0.gclass51_0.BE2E2DAC(268439692u, 402653184u);
		}
		else
		{
			e5964FB0_0.gclass51_0.BE2E2DAC(268439688u, 134217728u);
		}
		return true;
	}

	internal byte[] method_2(int DF892F10 = 32)
	{
		List<byte[]> list = new List<byte[]>();
		long dB9E6AAE = (long)GClass112.C78DEB29.A8054FBA.EDB8851A - 768L;
		F2BCAA26(1);
		byte[] item = "TrustedCorekeymaster".Select((char char_0) => (byte)char_0).ToArray();
		byte[] array = new byte[16];
		DEBEDD9C.smethod_0(array, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		byte[] item2 = array;
		for (int num = 0; num < DF892F10 / 16; num++)
		{
			list.Add(item);
			list.Add(item2);
			byte[] array2 = list.smethod_5((byte)num);
			long long_ = method_8(Enum12.const_1, 0, (IReadOnlyCollection<byte>)(object)array2, ********.const_1, array2.Length, dB9E6AAE);
			List<uint> list2 = e5964FB0_0.gclass51_0.EE83B4B7(Class607.B630A78B.object_0[21](long_), 4);
			foreach (uint item3 in list2)
			{
				list.Add(Class607.B630A78B.object_0[241]((int)item3));
			}
		}
		F2BCAA26(0);
		return list.smethod_5();
	}

	internal byte[] BB3AFD2C(byte ********)
	{
		byte[] array = (from char_0 in Class607.B630A78B.object_0[209]("RPMB KEY")
			select (byte)char_0).ToArray();
		byte[] array2 = (from char_0 in Class607.B630A78B.object_0[209]("SASI")
			select (byte)char_0).ToArray();
		for (int num = 0; num < array.Length; num++)
		{
			array[num] += ********;
		}
		for (int num2 = 0; num2 < array2.Length; num2++)
		{
			array2[num2] += ********;
		}
		int int_ = 32;
		if (******** > 0)
		{
			int_ = 16;
		}
		F2BCAA26(1);
		long long_ = (long)GClass112.C78DEB29.A8054FBA.EDB8851A - 768L;
		byte[] result = method_7(Enum12.const_1, array, array2, int_, long_);
		F2BCAA26(0);
		return result;
	}

	private void A8B57401(out byte[] byte_0, out byte[] byte_1)
	{
		byte_0 = (from char_0 in Class607.B630A78B.object_0[209]("KEY PLAT")
			select (byte)char_0).ToArray();
		byte_1 = (from ADA5E818 in Class607.B630A78B.object_0[209]("PROVISION KEY")
			select (byte)ADA5E818).ToArray();
		F2BCAA26(1);
		long long_ = (long)GClass112.C78DEB29.A8054FBA.EDB8851A - 768L;
		byte[] byte_2 = null;
		using (SHA256 b02A = Class607.B630A78B.object_0[286]())
		{
			byte_2 = Class607.B630A78B.object_0[455](b02A, string_0.D72BFA2C());
		}
		byte_1 = method_7(Enum12.const_2, byte_0, byte_2, 16, long_);
		while ((e5964FB0_0.gclass51_0.EE83B4B7(GClass112.C78DEB29.A8054FBA.uint_4 + 2804)[0] & 1) == 0)
		{
		}
		byte_0 = method_7(Enum12.BF96FDA7, byte_1, byte_2, 16, long_);
		e5964FB0_0.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.uint_4 + 2752, 0u);
		e5964FB0_0.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.uint_4 + 2756, 0u);
		e5964FB0_0.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.uint_4 + 2760, 0u);
		e5964FB0_0.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.uint_4 + 2764, 0u);
		uint[] array = smethod_0();
		array[0] = 0u;
		array[1] = 134217857u;
		array[2] = 134217857u;
		array[3] = 134217857u;
		array[4] = 75504672u;
		array[5] = 134217857u;
		method_9(array);
		method_10();
		F2BCAA26(0);
	}

	internal byte[] C18943AF(byte[] byte_0)
	{
		long num = (long)GClass112.C78DEB29.A8054FBA.EDB8851A - 768L;
		method_3(byte_0, num);
		return e5964FB0_0.gclass51_0.EE83B4B7(Class607.B630A78B.object_0[21](num), 8).Select(BitConverter.GetBytes).ToList()
			.smethod_5();
	}

	private void method_3(byte[] BE3DDE80, long F20C8607)
	{
		long num = F20C8607 + 64L;
		long num2 = F20C8607 + 32L;
		e5964FB0_0.gclass51_0.method_1(268439692u, Class607.B630A78B.object_0[241](402653184));
		byte[] byte_ = "19CDE05BABD9831F8C68059B7F520E513AF54FA572F36E3C85AE67BB67E6096A".D72BFA2C();
		e5964FB0_0.gclass51_0.method_1(Class607.B630A78B.object_0[21](num2), byte_);
		e5964FB0_0.gclass51_0.method_1(Class607.B630A78B.object_0[21](num), BE3DDE80);
		method_6(num2, 0);
		method_5(num, F20C8607, BE3DDE80.Length, bool_0: true, 0, 3);
		method_4(F20C8607);
		e5964FB0_0.gclass51_0.method_1(268439688u, Class607.B630A78B.object_0[241](134217728));
	}

	private uint method_4(long long_0)
	{
		uint[] uint_ = smethod_0();
		uint_ = smethod_5(uint_, (uint)long_0, 16, 0, 0);
		uint_ = smethod_3(uint_, 43);
		uint_ = D432151A(uint_, 2);
		uint_ = smethod_2(uint_, 2);
		uint_ = D7159C96(uint_, 1);
		uint_ = CDB52104(uint_, 8);
		method_9(uint_);
		return method_10();
	}

	private void method_5(long DD85DA81, long long_0, int int_20, bool bool_0, int int_21, int AF9CF117)
	{
		if (AF9CF117 != 2 || method_10() == 0)
		{
			if (bool_0 && (int_21 & 0xFFFFFFFDL) == 0L)
			{
				uint[] uint_ = smethod_0();
				uint_ = smethod_5(uint_, (uint)long_0, 16, 0, 0);
				uint_ = smethod_3(uint_, 43);
				uint_ = D432151A(uint_, 2);
				uint_ = D7159C96(uint_, 1);
				uint_ = CDB52104(uint_, 9);
				method_9(uint_);
			}
			uint[] bB0CFC = smethod_0();
			bB0CFC = smethod_8(bB0CFC, 2, (int)DD85DA81, int_20, 0, 0);
			if (int_21 == 0)
			{
				bB0CFC = smethod_3(bB0CFC, 7);
			}
			method_9(bB0CFC);
			if ((AF9CF117 == 2 && !bool_0) || AF9CF117 == 3)
			{
				method_10();
			}
		}
	}

	private void method_6(long DB99B51E, int int_20)
	{
		uint[] bB0CFC = smethod_0();
		bB0CFC = smethod_8(bB0CFC, 2, (int)DB99B51E, 32, 0, 0);
		bB0CFC = smethod_3(bB0CFC, 37);
		bB0CFC = D432151A(bB0CFC, 2);
		bB0CFC = CDB52104(bB0CFC, 1);
		method_9(bB0CFC);
		uint[] uint_ = smethod_0();
		uint_ = smethod_3(uint_, 37);
		uint_ = D432151A(uint_, 2);
		uint_ = CDB52104(uint_, 4);
		uint_ = smethod_6(uint_, 0, 16);
		method_9(uint_);
	}

	private byte[] method_7(Enum12 FD9EA82C, byte[] byte_0, byte[] byte_1, int int_20, long long_0)
	{
		List<byte[]> list = new List<byte[]>();
		if ((int)(FD9EA82C - 1) <= 4 && ((1 << (int)(FD9EA82C - 1)) & 0x17) != 0)
		{
			if (int_20 <= 255 && ((int_20 << 28) & 0xFFFFFFFFL) == 0L)
			{
				if (byte_0.Length != 0 && byte_0.Length <= 32)
				{
					int num = byte_1.Length + 3 + byte_0.Length;
					int num2 = int_20 + 15 >> 4;
					for (byte b = 0; b < num2; b++)
					{
						List<byte[]> list2 = new List<byte[]>();
						list2.Add(new byte[1] { (byte)(b + 1) });
						list2.Add(byte_0);
						list2.Add(new byte[1]);
						list2.Add(byte_1);
						list2.Add(new byte[1] { (byte)((8 * int_20) & 0xFF) });
						byte[] source = list2.smethod_5();
						long num3 = method_8(FD9EA82C, 0, (IReadOnlyCollection<byte>)(object)source.Take(num).ToArray(), ********.const_1, num, long_0);
						if ((ulong)num3 > 0uL)
						{
							list.AddRange(e5964FB0_0.gclass51_0.EE83B4B7(Class607.B630A78B.object_0[21](num3), 4).Select(BitConverter.GetBytes));
						}
					}
					return list.smethod_5();
				}
				return null;
			}
			return null;
		}
		return null;
	}

	private long method_8(Enum12 enum12_0, int int_20, IReadOnlyCollection<byte> ireadOnlyCollection_0, ******** a4146187_0, int int_21, long DB9E6AAE)
	{
		long num = DB9E6AAE + 16L;
		int num2 = ireadOnlyCollection_0.Count / 32 * 32;
		long num3 = num + num2 + num2;
		e5964FB0_0.gclass51_0.method_1(Class607.B630A78B.object_0[21](num), ireadOnlyCollection_0.Take(int_21).ToArray());
		if (BF2CE825(enum12_0, (int)num3, (int)num, a4146187_0, int_21, DB9E6AAE))
		{
			return DB9E6AAE;
		}
		return 0L;
	}

	private bool BF2CE825(Enum12 D5AD3695, int int_20, int int_21, ******** a4146187_0, int CD90BBAD, long long_0)
	{
		int e8B6FC = 0;
		int dE220CB = 16;
		if (D5AD3695 == Enum12.const_1 && (e5964FB0_0.gclass51_0.EE83B4B7(GClass112.C78DEB29.A8054FBA.uint_4 + 2720)[0] & 2) != 0)
		{
			dE220CB = 32;
		}
		method_0();
		uint[] uint_ = smethod_0();
		uint_ = D432151A(uint_, 7);
		uint_ = smethod_2(uint_, 0);
		uint_ = B88CF7AD(uint_, dE220CB);
		uint_ = EBB1F7A0(uint_, e8B6FC, 16);
		uint_ = smethod_6(uint_, 0, 16);
		uint_ = smethod_3(uint_, 32);
		uint_ = CDB52104(uint_, 1);
		method_9(uint_);
		uint[] array = smethod_0();
		if (D5AD3695 == Enum12.const_0)
		{
			array = EBB1F7A0(array, int_20, 16);
		}
		array = smethod_7(array, (int)D5AD3695);
		array = D432151A(array, 7);
		array = smethod_2(array, 0);
		array = B88CF7AD(array, dE220CB);
		array = smethod_3(array, 32);
		array = CDB52104(array, 4);
		array[4] |= (((uint)D5AD3695 >> 2) & 3) << 20;
		method_9(array);
		uint[] array2 = smethod_0();
		array2 = ((a4146187_0 != ********.A4384A30) ? smethod_8(array2, 2, int_21, CD90BBAD, 0, 0) : EBB1F7A0(array2, int_21, CD90BBAD));
		array2 = smethod_3(array2, 1);
		method_9(array2);
		if (D5AD3695 != Enum12.const_2)
		{
			uint[] uint_2 = smethod_0();
			uint_2 = D432151A(uint_2, 7);
			uint_2 = smethod_2(uint_2, 0);
			uint_2 = CDB52104(uint_2, 8);
			uint_2 = smethod_3(uint_2, 38);
			uint_2 = ((a4146187_0 != ********.A4384A30) ? smethod_5(uint_2, (uint)long_0, 16, 0, 0) : smethod_4(uint_2, (uint)long_0, 16));
			uint_2 = DE8F7019(uint_2, 0, 0);
			method_9(uint_2);
		}
		return method_10() == 0;
	}

	private void method_9(IReadOnlyList<uint> ireadOnlyList_0)
	{
		while (e5964FB0_0.gclass51_0.EE83B4B7(GClass112.C78DEB29.A8054FBA.uint_4 + 3740)[0] << 28 == 0)
		{
		}
		e5964FB0_0.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.uint_4 + 3712, ireadOnlyList_0[0]);
		e5964FB0_0.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.uint_4 + 3716, ireadOnlyList_0[1]);
		e5964FB0_0.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.uint_4 + 3720, ireadOnlyList_0[2]);
		e5964FB0_0.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.uint_4 + 3724, ireadOnlyList_0[3]);
		e5964FB0_0.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.uint_4 + 3728, ireadOnlyList_0[4]);
		e5964FB0_0.gclass51_0.BE2E2DAC(GClass112.C78DEB29.A8054FBA.uint_4 + 3732, ireadOnlyList_0[5]);
	}

	private uint method_10(long long_0 = 0L)
	{
		List<uint> list = new List<uint>();
		method_0();
		int dE2B08A = method_1(0);
		list.Add(0u);
		list.Add(134217745u);
		list.Add((uint)long_0);
		list.Add(134217746u);
		list.Add(256u);
		list.Add(Class607.B630A78B.object_0[21](long_0 >> 32 << 16));
		method_9(list);
		while ((F120C79C() & 4) == 0)
		{
		}
		while (true)
		{
			switch (e5964FB0_0.gclass51_0.EE83B4B7(GClass112.C78DEB29.A8054FBA.uint_4 + 2976)[0])
			{
			case 0u:
				break;
			default:
				return 4127195137u;
			case 1u:
				method_0();
				D481C380(dE2B08A);
				return 0u;
			}
		}
	}

	private byte[] method_11(byte[] byte_0)
	{
		byte[] byte_1 = "B936C14D95A99585073E5607784A51F7444B60D6BFD6110F76D004CCB7E1950E".D72BFA2C();
		using SHA256Managed b02A = Class607.B630A78B.object_0[145]();
		RijndaelManaged rijndaelManaged = Class607.B630A78B.object_0[659]();
		Class631.smethod_0(rijndaelManaged, CipherMode.CBC);
		using RijndaelManaged rijndaelManaged2 = rijndaelManaged;
		byte[] source = Class607.B630A78B.object_0[455](b02A, byte_1);
		Class607.B630A78B.object_0[510](rijndaelManaged2, source.Take(16).ToArray());
		Class607.B630A78B.object_0[868](rijndaelManaged2, source.Skip(16).ToArray());
		using ICryptoTransform object_ = Class607.B630A78B.object_0[762](rijndaelManaged2);
		return Class607.B630A78B.object_0[719](object_, byte_0, 0, byte_0.Length);
	}

	private byte[] method_12(byte[] byte_0)
	{
		byte[] fFB1DB = "5C0E349A27DC46034C7B6744A378BD17".D72BFA2C();
		byte[] byte_1 = "A0B0924686447109F2D51DCDDC93458A".D72BFA2C();
		using GClass108 gClass = new GClass108(fFB1DB, byte_1);
		return gClass.method_9(byte_0);
	}
}
