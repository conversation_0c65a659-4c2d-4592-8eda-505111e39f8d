using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Threading;
using System.Windows.Forms;
using SharpAdbClient;

public class EAB5ABA9 : UserControl
{
	[Serializable]
	[CompilerGenerated]
	private sealed class EE95D9B1
	{
		public static readonly EE95D9B1 _003C_003E9 = new EE95D9B1();

		public static Predicate<GClass120> _003C_003E9__15_0;

		public static Predicate<GStruct90> _003C_003E9__23_0;

		public static Predicate<GStruct90> _003C_003E9__23_1;

		public static Predicate<GStruct90> _003C_003E9__23_2;

		public static Predicate<GStruct90> _003C_003E9__24_0;

		public static Predicate<GStruct90> _003C_003E9__24_1;

		public static Predicate<GStruct90> _003C_003E9__24_2;

		public static Predicate<GStruct90> _003C_003E9__28_0;

		public static Predicate<GStruct90> _003C_003E9__28_1;

		public static Predicate<GStruct90> _003C_003E9__28_2;

		public static Predicate<GStruct96> _003C_003E9__33_0;

		public EE95D9B1()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool method_0(GClass120 gclass120_0)
		{
			return gclass120_0.E903E03A == GEnum39.ADB;
		}

		internal bool method_1(GStruct90 F0AEA09F)
		{
			return Class607.B630A78B.object_0[787](F0AEA09F.string_0, D09F0D3B.smethod_5(1172014u));
		}

		internal bool EB8F6D0F(GStruct90 gstruct90_0)
		{
			return Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1171119u));
		}

		internal bool method_2(GStruct90 gstruct90_0)
		{
			return Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1171952u));
		}

		internal bool B111DF0F(GStruct90 gstruct90_0)
		{
			return Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1172014u));
		}

		internal bool method_3(GStruct90 gstruct90_0)
		{
			return Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1171119u));
		}

		internal bool method_4(GStruct90 gstruct90_0)
		{
			return Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1171952u));
		}

		internal bool method_5(GStruct90 gstruct90_0)
		{
			return Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1172014u));
		}

		internal bool method_6(GStruct90 gstruct90_0)
		{
			return Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1171119u));
		}

		internal bool method_7(GStruct90 gstruct90_0)
		{
			return Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1171952u));
		}

		internal bool method_8(GStruct96 gstruct96_0)
		{
			return Class607.B630A78B.object_0[1240](gstruct96_0.string_1, "SAMSUNG Mobile USB Modem");
		}
	}

	[CompilerGenerated]
	private sealed class AEA6E0B0
	{
		public EAB5ABA9 E78C5120;

		public bool A5A664B4;

		public AEA6E0B0()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void method_0(object sender, EventArgs e)
		{
			E78C5120.FE2EFC30(A5A664B4);
		}
	}

	[CompilerGenerated]
	private sealed class DE2CB104
	{
		public EAB5ABA9 eab5ABA9_0;

		public string string_0;

		public bool bool_0;

		public EF1F389C ef1F389C_0;

		public bool bool_1;

		public DE2CB104()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void AC8F9717(object sender, EventArgs e)
		{
			eab5ABA9_0.method_0(string_0, bool_0, ef1F389C_0, bool_1);
		}
	}

	[CompilerGenerated]
	private sealed class Class27
	{
		public Bitmap bitmap_0;

		public EAB5ABA9 eab5ABA9_0;

		public Class27()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void method_0()
		{
			using A4041E28 a4041E = new A4041E28();
			Class607.B630A78B.object_0[148](a4041E.ED873F29, bitmap_0);
			Class607.B630A78B.object_0[379](a4041E, CF3E7503: true);
			eab5ABA9_0.gform0_0.E2008A2D(a4041E);
		}
	}

	[CompilerGenerated]
	private sealed class A292DF17 : IAsyncStateMachine
	{
		public int int_0;

		public AsyncVoidMethodBuilder asyncVoidMethodBuilder_0;

		public object A1872AAF;

		public EventArgs eventArgs_0;

		public EAB5ABA9 eab5ABA9_0;

		private bool C72DFD0B;

		private A4041E28 a4041E28_0;

		private TaskAwaiter<bool> taskAwaiter_0;

		public A292DF17()
		{
			Class607.B630A78B.object_0[571](this);
		}

		void IAsyncStateMachine.MoveNext()
		{
			int num = int_0;
			try
			{
				TaskAwaiter<bool> awaiter;
				if (num != 0)
				{
					awaiter = eab5ABA9_0.gclass113_0.method_1(eab5ABA9_0.A1853E84).GetAwaiter();
					if (!awaiter.IsCompleted)
					{
						num = 0;
						int_0 = 0;
						taskAwaiter_0 = awaiter;
						A292DF17 stateMachine = this;
						asyncVoidMethodBuilder_0.AwaitUnsafeOnCompleted(ref awaiter, ref stateMachine);
						return;
					}
				}
				else
				{
					awaiter = taskAwaiter_0;
					taskAwaiter_0 = default(TaskAwaiter<bool>);
					num = -1;
					int_0 = -1;
				}
				C72DFD0B = awaiter.GetResult();
				if (C72DFD0B)
				{
					a4041E28_0 = new A4041E28();
					try
					{
						eab5ABA9_0.gform0_0.E2008A2D(a4041E28_0);
					}
					finally
					{
						if (num < 0 && a4041E28_0 != null)
						{
							((IDisposable)a4041E28_0).Dispose();
						}
					}
					a4041E28_0 = null;
				}
			}
			catch (Exception exception_)
			{
				int_0 = -2;
				Class607.B630A78B.object_0[370](ref asyncVoidMethodBuilder_0, exception_);
				return;
			}
			int_0 = -2;
			Class607.B630A78B.object_0[961](ref asyncVoidMethodBuilder_0);
		}

		[DebuggerHidden]
		void IAsyncStateMachine.SetStateMachine(IAsyncStateMachine stateMachine)
		{
		}
	}

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private GClass112.D3A6F5AB E5BADC00;

	public GStruct98 BF2E8E22;

	public DeviceData deviceData_0;

	public List<A808BE0B> list_0 = new List<A808BE0B>();

	public GForm0 gform0_0;

	public GClass116 gclass116_0;

	private GClass113 gclass113_0;

	private IContainer AE01711A = null;

	private GroupBox DEB88E9E;

	private Label label_0;

	private Button button_0;

	private GControl2 gcontrol2_0;

	private GControl1 gcontrol1_0;

	private TabPage BB16FC09;

	private GroupBox groupBox_0;

	private TabPage tabPage_0;

	private GroupBox A8B012BA;

	private ProgressBar DE220E9E;

	private GroupBox groupBox_1;

	private Button button_1;

	private GClass104 F6A3262C;

	private GClass104 D327D1B9;

	private GClass104 B1B9AF3F;

	private GroupBox D4873E04;

	private Button button_2;

	private Button F51A3B03;

	private Button button_3;

	private Button CCA9F8A4;

	private TabPage tabPage_1;

	private GroupBox EA01D4AA;

	private Button button_4;

	private Button button_5;

	private Button D3ACDC32;

	private Button E21AF888;

	private Button C0AEB5A6;

	private Button button_6;

	private GClass104 gclass104_0;

	private GClass104 D5BB13B4;

	private GClass104 F19F4AAB;

	private Button button_7;

	private Button D50C9613;

	private Panel C93DE53A;

	private Panel panel_0;

	private Panel B21C68A4;

	public RichTextBox richTextBox_0;

	private Button button_8;

	private Button B0019D3D;

	private Button button_9;

	private GroupBox EA2B0C8B;

	private Button button_10;

	private GroupBox groupBox_2;

	private Button ********;

	private PictureBox pictureBox_0;

	private Button button_11;

	private Button button_12;

	private Button E815B5B4;

	private TabPage tabPage_2;

	private GroupBox groupBox_3;

	private Button button_13;

	private Button button_14;

	public event GClass112.D3A6F5AB AF368392
	{
		[CompilerGenerated]
		add
		{
			GClass112.D3A6F5AB d3A6F5AB = E5BADC00;
			GClass112.D3A6F5AB d3A6F5AB2;
			do
			{
				d3A6F5AB2 = d3A6F5AB;
				GClass112.D3A6F5AB value2 = (GClass112.D3A6F5AB)Class607.B630A78B.object_0[752](d3A6F5AB2, value);
				d3A6F5AB = Interlocked.CompareExchange(ref E5BADC00, value2, d3A6F5AB2);
			}
			while ((object)d3A6F5AB != d3A6F5AB2);
		}
		[CompilerGenerated]
		remove
		{
			GClass112.D3A6F5AB d3A6F5AB = E5BADC00;
			GClass112.D3A6F5AB d3A6F5AB2;
			do
			{
				d3A6F5AB2 = d3A6F5AB;
				GClass112.D3A6F5AB value2 = (GClass112.D3A6F5AB)Class607.B630A78B.object_0[629](d3A6F5AB2, value);
				d3A6F5AB = Interlocked.CompareExchange(ref E5BADC00, value2, d3A6F5AB2);
			}
			while ((object)d3A6F5AB != d3A6F5AB2);
		}
	}

	public void FB06508C(GForm0 BB2F1D33)
	{
		gform0_0 = BB2F1D33;
	}

	public EAB5ABA9()
	{
		Class607.B630A78B.object_0[194](this);
		method_28();
		gclass113_0 = new GClass113();
		A1853E84(D09F0D3B.smethod_5(1161902u), EF1F389C.Word, bool_0: false, F93C7108: true);
		A1853E84("Version: ");
		A1853E84(D09F0D3B.smethod_5(1161364u), EF1F389C.Success, bool_0: false);
		A1853E84("Selected Tab: ");
		A1853E84("Adb King", EF1F389C.Success, bool_0: false, F93C7108: true);
		gclass116_0 = new GClass116();
	}

	public unsafe void DE38B30B(bool A7821413)
	{
		AEA6E0B0 aEA6E0B = new AEA6E0B0();
		aEA6E0B.E78C5120 = this;
		aEA6E0B.A5A664B4 = A7821413;
		if (Class607.B630A78B.object_0[1012](this))
		{
			Class607.B630A78B.object_0[86](this, Class607.B630A78B.object_0[935](aEA6E0B, (nint)__ldftn(AEA6E0B0.method_0)));
		}
		else
		{
			FE2EFC30(aEA6E0B.A5A664B4);
		}
	}

	public void FE2EFC30(bool B0956787)
	{
		IEnumerator object_ = Class607.B630A78B.object_0[409](Class607.B630A78B.object_0[781](groupBox_0));
		try
		{
			while (Class607.B630A78B.object_0[212](object_))
			{
				object obj = Class607.B630A78B.object_0[107](object_);
				Class607.B630A78B.object_0[722]((Control)obj, !B0956787);
			}
		}
		finally
		{
			IDisposable disposable = object_ as IDisposable;
			if (disposable != null)
			{
				disposable.Dispose();
			}
		}
		IEnumerator object_2 = Class607.B630A78B.object_0[409](Class607.B630A78B.object_0[781](groupBox_1));
		try
		{
			while (Class607.B630A78B.object_0[212](object_2))
			{
				object obj2 = Class607.B630A78B.object_0[107](object_2);
				Class607.B630A78B.object_0[722]((Control)obj2, !B0956787);
			}
		}
		finally
		{
			IDisposable disposable2 = object_2 as IDisposable;
			if (disposable2 != null)
			{
				disposable2.Dispose();
			}
		}
		IEnumerator object_3 = Class607.B630A78B.object_0[409](Class607.B630A78B.object_0[781](EA01D4AA));
		try
		{
			while (Class607.B630A78B.object_0[212](object_3))
			{
				object obj3 = Class607.B630A78B.object_0[107](object_3);
				Class607.B630A78B.object_0[722]((Control)obj3, !B0956787);
			}
		}
		finally
		{
			IDisposable disposable3 = object_3 as IDisposable;
			if (disposable3 != null)
			{
				disposable3.Dispose();
			}
		}
		IEnumerator object_4 = Class607.B630A78B.object_0[409](Class607.B630A78B.object_0[781](D4873E04));
		try
		{
			while (Class607.B630A78B.object_0[212](object_4))
			{
				object obj4 = Class607.B630A78B.object_0[107](object_4);
				Class607.B630A78B.object_0[722]((Control)obj4, !B0956787);
			}
		}
		finally
		{
			IDisposable disposable4 = object_4 as IDisposable;
			if (disposable4 != null)
			{
				disposable4.Dispose();
			}
		}
		IEnumerator object_5 = Class607.B630A78B.object_0[409](Class607.B630A78B.object_0[781](groupBox_3));
		try
		{
			while (Class607.B630A78B.object_0[212](object_5))
			{
				object obj5 = Class607.B630A78B.object_0[107](object_5);
				Class607.B630A78B.object_0[722]((Control)obj5, !B0956787);
			}
		}
		finally
		{
			IDisposable disposable5 = object_5 as IDisposable;
			if (disposable5 != null)
			{
				disposable5.Dispose();
			}
		}
		Class607.B630A78B.object_0[722](button_0, B0956787);
	}

	public unsafe void A1853E84(string AA19539D, EF1F389C B22CBEB3 = EF1F389C.Word, bool bool_0 = true, bool F93C7108 = false)
	{
		DE2CB104 dE2CB = new DE2CB104();
		dE2CB.eab5ABA9_0 = this;
		dE2CB.string_0 = AA19539D;
		dE2CB.bool_0 = bool_0;
		dE2CB.ef1F389C_0 = B22CBEB3;
		dE2CB.bool_1 = F93C7108;
		if (Class607.B630A78B.object_0[1012](this))
		{
			Class607.B630A78B.object_0[1241](this, Class607.B630A78B.object_0[935](dE2CB, (nint)__ldftn(DE2CB104.AC8F9717)));
		}
		else
		{
			method_0(dE2CB.string_0, dE2CB.bool_0, dE2CB.ef1F389C_0, dE2CB.bool_1);
		}
	}

	private void method_0(string string_0, bool bool_0 = true, EF1F389C F9056DBE = EF1F389C.Word, bool A0B1DB9B = false)
	{
		list_0.Add(new A808BE0B
		{
			bold = A0B1DB9B,
			color = F9056DBE,
			newline = bool_0,
			text = string_0
		});
		Class607.B630A78B.object_0[335](richTextBox_0, Class607.B630A78B.object_0[206](richTextBox_0));
		Class607.B630A78B.object_0[1076](richTextBox_0, 0);
		if (bool_0 && F9056DBE != EF1F389C.Success)
		{
			string_0 = Class607.B630A78B.object_0[720]("\r", string_0);
		}
		switch (F9056DBE)
		{
		case EF1F389C.Word:
			Class607.B630A78B.object_0[551](richTextBox_0, Class607.B630A78B.object_0[223]());
			break;
		case EF1F389C.Yellow:
			Class607.B630A78B.object_0[551](richTextBox_0, Class607.B630A78B.object_0[909]());
			Class607.B630A78B.object_0[218](richTextBox_0, Class607.B630A78B.object_0[542](Class607.B630A78B.object_0[129](richTextBox_0), FontStyle.Bold));
			break;
		default:
			Class452.smethod_0(richTextBox_0, (F9056DBE == EF1F389C.Success) ? Class607.B630A78B.object_0[923]() : Class607.B630A78B.object_0[909]());
			Class607.B630A78B.object_0[218](richTextBox_0, Class607.B630A78B.object_0[542](Class607.B630A78B.object_0[129](richTextBox_0), FontStyle.Bold));
			break;
		}
		if (A0B1DB9B)
		{
			Class607.B630A78B.object_0[218](richTextBox_0, Class607.B630A78B.object_0[542](Class607.B630A78B.object_0[129](richTextBox_0), FontStyle.Bold));
		}
		Class607.B630A78B.object_0[327](richTextBox_0, string_0);
		Class607.B630A78B.object_0[551](richTextBox_0, Class607.B630A78B.object_0[546](richTextBox_0));
		Class607.B630A78B.object_0[183](richTextBox_0);
	}

	public bool method_1()
	{
		A1853E84("waiting for adb device <timeout 3 minute>: ");
		if (!D0145D9F.smethod_4(200000))
		{
			A1853E84("no connected adb device", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			return false;
		}
		A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
		A1853E84("Finding adb device: ");
		GClass120 gClass = GClass115.list_0.Find((GClass120 gclass120_0) => gclass120_0.E903E03A == GEnum39.ADB);
		if (gClass == null || gClass.GStruct98_0.Device == null)
		{
			A1853E84("no connected adb device", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			return false;
		}
		BF2E8E22 = gClass.GStruct98_0;
		deviceData_0 = gClass.GStruct98_0.Device;
		A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
		D0145D9F.smethod_1(BF2E8E22, A1853E84);
		return true;
	}

	private bool method_2(string D8942431)
	{
		return Class607.B630A78B.object_0[787](D8942431, "9") || Class607.B630A78B.object_0[787](D8942431, "10") || Class607.B630A78B.object_0[787](D8942431, "11") || Class607.B630A78B.object_0[787](D8942431, "12") || Class607.B630A78B.object_0[787](D8942431, "13") || Class607.B630A78B.object_0[787](D8942431, "14") || Class607.B630A78B.object_0[787](D8942431, "15");
	}

	public void method_3()
	{
		new GClass128().DFB12B0F(new object[1] { this }, 419236);
	}

	public void method_4()
	{
		string string_ = "Adb Bypass Tecno Infinix MDM [BLACKSCREEN]";
		try
		{
			E5BADC00?.Invoke(bool_0: true, string_);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				A1853E84("Bypassing Tecno Infinix MDM [BLACKSCREEN]: ");
				string[] array = new string[6]
				{
					D09F0D3B.smethod_5(1170413u),
					D09F0D3B.smethod_5(1170521u),
					D09F0D3B.smethod_5(1170637u),
					D09F0D3B.smethod_5(1170731u),
					D09F0D3B.smethod_5(1170835u),
					D09F0D3B.smethod_5(1170935u)
				};
				string[] array2 = array;
				foreach (string d20D52B in array2)
				{
					deviceData_0.D532AD84(d20D52B);
					GClass112.smethod_28(3000);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("rebooting device: ");
				if (!deviceData_0.smethod_5())
				{
					A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
				}
				else
				{
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				}
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, string_);
		}
	}

	private void method_5(object sender, EventArgs e)
	{
		new GClass128().C5017C25(new object[3] { this, sender, e }, 22421119);
	}

	public void method_6()
	{
		string string_ = "ARKA Block APP MDM All Android";
		try
		{
			E5BADC00?.Invoke(bool_0: true, string_);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				if (!ACB7982A.gstruct94_0.E417AB0A)
				{
					ACB7982A.gstruct94_0 = ACB7982A.B60553A1(new List<GStruct90>
					{
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1171119u),
							E30848BA = D828C7A0.const_1
						},
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1171181u),
							E30848BA = D828C7A0.const_1
						}
					}, gform0_0);
				}
				if (!ACB7982A.gstruct94_0.E417AB0A)
				{
					return;
				}
				A1853E84("Executing King Auth: ");
				foreach (GStruct90 item in ACB7982A.gstruct94_0.CA96D92F)
				{
					if (!deviceData_0.smethod_9(item.byte_0))
					{
						A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
						return;
					}
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("Executing King Auth: ");
				string[] array = new string[5]
				{
					D09F0D3B.smethod_5(1171288u),
					"sh /storage/emulated/0/Android/data/moe.shizuku.privileged.api/start.sh",
					D09F0D3B.smethod_5(1171577u),
					D09F0D3B.smethod_5(1171660u),
					D09F0D3B.smethod_5(1171794u)
				};
				string[] array2 = array;
				foreach (string d20D52B in array2)
				{
					deviceData_0.D532AD84(d20D52B);
					GClass112.smethod_28(3000);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				Class607.B630A78B.object_0[318](D09F0D3B.smethod_5(1171894u));
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, string_);
		}
	}

	private void method_7(object sender, EventArgs e)
	{
		new GClass128().AD84C1A2(new object[3] { this, sender, e }, 560898);
	}

	private void method_8(object sender, EventArgs e)
	{
		new GClass128().B4154402(new object[3] { this, sender, e }, 22056473);
	}

	public void method_9(string string_0 = "Adb King Bypass Macro Pay")
	{
		new GClass128().method_68(new object[2] { this, string_0 }, 516676);
	}

	public void C802158B(string string_0)
	{
		try
		{
			E5BADC00?.Invoke(bool_0: true, string_0);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				if (!ACB7982A.C8111EA7.E417AB0A)
				{
					ACB7982A.C8111EA7 = ACB7982A.B60553A1(new List<GStruct90>
					{
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1171119u),
							E30848BA = D828C7A0.const_1
						},
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1171952u),
							E30848BA = D828C7A0.const_1
						},
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1172014u),
							E30848BA = D828C7A0.const_1
						}
					}, gform0_0);
				}
				if (!ACB7982A.C8111EA7.E417AB0A)
				{
					return;
				}
				A1853E84("Executing King Auth: ");
				GStruct90 gStruct = ACB7982A.C8111EA7.CA96D92F.Find((GStruct90 gstruct90_0) => Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1172014u)));
				if (!deviceData_0.smethod_9(gStruct.byte_0))
				{
					A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
					return;
				}
				string[] array = new string[41]
				{
					D09F0D3B.smethod_5(1178232u),
					D09F0D3B.smethod_5(1178352u),
					D09F0D3B.smethod_5(1178446u),
					D09F0D3B.smethod_5(1178534u),
					D09F0D3B.smethod_5(1178620u),
					D09F0D3B.smethod_5(1178680u),
					D09F0D3B.smethod_5(1178742u),
					D09F0D3B.smethod_5(1178810u),
					D09F0D3B.smethod_5(1178352u),
					D09F0D3B.smethod_5(1178446u),
					D09F0D3B.smethod_5(1178910u),
					D09F0D3B.smethod_5(1178810u),
					D09F0D3B.smethod_5(1178992u),
					D09F0D3B.smethod_5(1179059u),
					D09F0D3B.smethod_5(1179207u),
					D09F0D3B.smethod_5(1178992u),
					D09F0D3B.smethod_5(1179207u),
					D09F0D3B.smethod_5(1178534u),
					D09F0D3B.smethod_5(1178620u),
					D09F0D3B.smethod_5(1178680u),
					D09F0D3B.smethod_5(1178742u),
					D09F0D3B.smethod_5(1179331u),
					D09F0D3B.smethod_5(1179451u),
					D09F0D3B.smethod_5(1179579u),
					D09F0D3B.smethod_5(1179683u),
					D09F0D3B.smethod_5(1179804u),
					D09F0D3B.smethod_5(1179942u),
					D09F0D3B.smethod_5(1180068u),
					D09F0D3B.smethod_5(1180189u),
					D09F0D3B.smethod_5(1180320u),
					D09F0D3B.smethod_5(1180452u),
					D09F0D3B.smethod_5(1180580u),
					D09F0D3B.smethod_5(1180707u),
					D09F0D3B.smethod_5(1180837u),
					D09F0D3B.smethod_5(1180948u),
					D09F0D3B.smethod_5(1181088u),
					D09F0D3B.smethod_5(1181216u),
					D09F0D3B.smethod_5(1181339u),
					D09F0D3B.smethod_5(1181473u),
					D09F0D3B.smethod_5(1181592u),
					D09F0D3B.smethod_5(1175246u)
				};
				string[] array2 = array;
				foreach (string d20D52B in array2)
				{
					deviceData_0.D532AD84(d20D52B);
				}
				array = new string[15]
				{
					D09F0D3B.smethod_5(1175332u),
					D09F0D3B.smethod_5(1175408u),
					D09F0D3B.smethod_5(1175496u),
					D09F0D3B.smethod_5(1175606u),
					D09F0D3B.smethod_5(1175704u),
					D09F0D3B.smethod_5(1175800u),
					D09F0D3B.smethod_5(1175904u),
					D09F0D3B.smethod_5(1176006u),
					D09F0D3B.smethod_5(1176098u),
					D09F0D3B.smethod_5(1176192u),
					D09F0D3B.smethod_5(1176284u),
					D09F0D3B.smethod_5(1176386u),
					D09F0D3B.smethod_5(1176460u),
					D09F0D3B.smethod_5(1176556u),
					D09F0D3B.smethod_5(1176655u)
				};
				string[] array3 = array;
				foreach (string d20D52B2 in array3)
				{
					GClass112.smethod_28(4000);
					deviceData_0.D532AD84(d20D52B2);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("rebooting device: ");
				if (!deviceData_0.smethod_5())
				{
					A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
					return;
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("waiting for device: ");
				if (deviceData_0.F23C4699(100000))
				{
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84("waiting for boot complete: ");
					string b809E90D;
					do
					{
						GClass112.smethod_28(1000);
						b809E90D = deviceData_0.D532AD84("getprop sys.boot_completed");
					}
					while (Class607.B630A78B.object_0[121](Class607.B630A78B.object_0[1233](b809E90D), "1"));
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84(D09F0D3B.smethod_5(1176969u));
					array = new string[2]
					{
						D09F0D3B.smethod_5(1178446u),
						D09F0D3B.smethod_5(1181766u)
					};
					string[] array4 = array;
					foreach (string d20D52B3 in array4)
					{
						GClass112.smethod_28(4000);
						deviceData_0.D532AD84(d20D52B3);
					}
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84("installing Scripts: ");
					gStruct = ACB7982A.F883AF0D.CA96D92F.Find((GStruct90 gstruct90_0) => Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1171119u)));
					if (!deviceData_0.smethod_9(gStruct.byte_0))
					{
						A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
						return;
					}
					gStruct = ACB7982A.F883AF0D.CA96D92F.Find((GStruct90 gstruct90_0) => Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1171952u)));
					if (!deviceData_0.smethod_9(gStruct.byte_0))
					{
						A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
						return;
					}
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84(D09F0D3B.smethod_5(1176969u));
					array = new string[5]
					{
						D09F0D3B.smethod_5(1171288u),
						"sh /storage/emulated/0/Android/data/moe.shizuku.privileged.api/start.sh",
						D09F0D3B.smethod_5(1171577u),
						D09F0D3B.smethod_5(1177143u),
						D09F0D3B.smethod_5(1181937u)
					};
					string[] array5 = array;
					foreach (string d20D52B4 in array5)
					{
						GClass112.smethod_28(4000);
						deviceData_0.D532AD84(d20D52B4);
					}
					array = new string[14]
					{
						D09F0D3B.smethod_5(1178050u),
						D09F0D3B.smethod_5(1178134u),
						D09F0D3B.smethod_5(1175496u),
						D09F0D3B.smethod_5(1175408u),
						D09F0D3B.smethod_5(1175606u),
						D09F0D3B.smethod_5(1175800u),
						D09F0D3B.smethod_5(1175704u),
						D09F0D3B.smethod_5(1175904u),
						D09F0D3B.smethod_5(1176006u),
						D09F0D3B.smethod_5(1176098u),
						D09F0D3B.smethod_5(1176192u),
						D09F0D3B.smethod_5(1176284u),
						D09F0D3B.smethod_5(1176386u),
						D09F0D3B.smethod_5(1176460u)
					};
					string[] array6 = array;
					foreach (string d20D52B5 in array6)
					{
						deviceData_0.D532AD84(d20D52B5);
					}
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84("rebooting device: ");
					if (!deviceData_0.smethod_5())
					{
						A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
					}
					else
					{
						A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					}
				}
				else
				{
					A1853E84("TimeOut", EF1F389C.Error, bool_0: false, F93C7108: true);
				}
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, string_0);
		}
	}

	private void ********(object sender, EventArgs e)
	{
		new GClass128().DFB12B0F(new object[3] { this, sender, e }, 11035091);
	}

	public void method_10(string D3908A0B = "Bypass Samsung ADB Knox Gaurd")
	{
		try
		{
			E5BADC00?.Invoke(bool_0: true, D3908A0B);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				if (!ACB7982A.gstruct94_1.E417AB0A)
				{
					ACB7982A.gstruct94_1 = ACB7982A.B60553A1(new List<GStruct90>
					{
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1182171u),
							E30848BA = D828C7A0.const_1
						}
					}, gform0_0);
				}
				if (!ACB7982A.gstruct94_1.E417AB0A)
				{
					return;
				}
				A1853E84("Executing King Auth: ");
				GStruct90 gStruct = ACB7982A.gstruct94_1.CA96D92F.FirstOrDefault();
				string text = "/data/local/tmp/s";
				if (!deviceData_0.BD302E0F(text, gStruct.byte_0))
				{
					A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
					return;
				}
				deviceData_0.BC013711(777, text);
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("Executing King Auth: ");
				string[] array = new string[34]
				{
					D09F0D3B.smethod_5(1175332u),
					D09F0D3B.smethod_5(1175408u),
					D09F0D3B.smethod_5(1182269u),
					D09F0D3B.smethod_5(1175606u),
					D09F0D3B.smethod_5(1182387u),
					D09F0D3B.smethod_5(1182461u),
					D09F0D3B.smethod_5(1182549u),
					D09F0D3B.smethod_5(1175246u),
					D09F0D3B.smethod_5(1182671u),
					D09F0D3B.smethod_5(1182771u),
					D09F0D3B.smethod_5(1182845u),
					D09F0D3B.smethod_5(1182921u),
					D09F0D3B.smethod_5(1183003u),
					D09F0D3B.smethod_5(1183117u),
					D09F0D3B.smethod_5(1183225u),
					D09F0D3B.smethod_5(1183003u),
					D09F0D3B.smethod_5(1183321u),
					D09F0D3B.smethod_5(1183401u),
					D09F0D3B.smethod_5(1183504u),
					D09F0D3B.smethod_5(1183667u),
					D09F0D3B.smethod_5(1183321u),
					D09F0D3B.smethod_5(1182671u),
					D09F0D3B.smethod_5(1182771u),
					D09F0D3B.smethod_5(1182845u),
					D09F0D3B.smethod_5(1182921u),
					D09F0D3B.smethod_5(1183806u),
					D09F0D3B.smethod_5(1183941u),
					D09F0D3B.smethod_5(1184083u),
					D09F0D3B.smethod_5(1184202u),
					D09F0D3B.smethod_5(1184337u),
					D09F0D3B.smethod_5(1184490u),
					D09F0D3B.smethod_5(1184631u),
					D09F0D3B.smethod_5(1184766u),
					"chmod +x /data/local/tmp/s"
				};
				string[] array2 = array;
				foreach (string d20D52B in array2)
				{
					deviceData_0.D532AD84(d20D52B);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("Executing King Auth: ");
				Class607.B630A78B.object_0[697](D0145D9F.D1B4DB27());
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("Executing King Auth: ");
				deviceData_0.smethod_18("/data/local/tmp/ag.apk");
				deviceData_0.smethod_18(text);
				deviceData_0.D532AD84(D09F0D3B.smethod_5(1176655u));
				deviceData_0.D532AD84(D09F0D3B.smethod_5(1185054u));
				deviceData_0.D532AD84(D09F0D3B.smethod_5(1185138u));
				deviceData_0.D532AD84("settings put global device_provisioned 1");
				deviceData_0.D532AD84("settings put secure user_setup_complete 1");
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("rebooting device: ");
				if (!deviceData_0.smethod_5())
				{
					A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
				}
				else
				{
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				}
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, D3908A0B);
		}
	}

	public void method_11(string string_0 = "Security Plugin MTK")
	{
		try
		{
			E5BADC00?.Invoke(bool_0: true, string_0);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				if (!ACB7982A.AB202F97.E417AB0A)
				{
					ACB7982A.AB202F97 = ACB7982A.B60553A1(new List<GStruct90>
					{
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1171119u),
							E30848BA = D828C7A0.const_1
						},
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1171952u),
							E30848BA = D828C7A0.const_1
						}
					}, gform0_0);
				}
				if (!ACB7982A.AB202F97.E417AB0A)
				{
					return;
				}
				A1853E84("Executing King Auth: ");
				string[] array = new string[7]
				{
					D09F0D3B.smethod_5(1185386u),
					D09F0D3B.smethod_5(1185450u),
					D09F0D3B.smethod_5(1185386u),
					D09F0D3B.smethod_5(1185526u),
					D09F0D3B.smethod_5(1185604u),
					D09F0D3B.smethod_5(1185702u),
					D09F0D3B.smethod_5(1185764u)
				};
				string[] array2 = array;
				foreach (string d20D52B in array2)
				{
					deviceData_0.D532AD84(d20D52B);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("instaling scripts: ");
				foreach (GStruct90 item in ACB7982A.AB202F97.CA96D92F)
				{
					if (!deviceData_0.smethod_9(item.byte_0))
					{
						A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
						return;
					}
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				array = new string[5]
				{
					D09F0D3B.smethod_5(1171288u),
					"sh /storage/emulated/0/Android/data/moe.shizuku.privileged.api/start.sh",
					D09F0D3B.smethod_5(1171577u),
					D09F0D3B.smethod_5(1177143u),
					D09F0D3B.smethod_5(1185871u)
				};
				A1853E84("Executing King Auth: ");
				string[] array3 = array;
				foreach (string d20D52B2 in array3)
				{
					deviceData_0.D532AD84(d20D52B2);
					GClass112.smethod_28(4000);
				}
				array = new string[14]
				{
					D09F0D3B.smethod_5(1178050u),
					D09F0D3B.smethod_5(1178134u),
					D09F0D3B.smethod_5(1175496u),
					D09F0D3B.smethod_5(1175408u),
					D09F0D3B.smethod_5(1175606u),
					D09F0D3B.smethod_5(1175800u),
					D09F0D3B.smethod_5(1175704u),
					D09F0D3B.smethod_5(1175904u),
					D09F0D3B.smethod_5(1176006u),
					D09F0D3B.smethod_5(1176098u),
					D09F0D3B.smethod_5(1176192u),
					D09F0D3B.smethod_5(1176284u),
					D09F0D3B.smethod_5(1176386u),
					D09F0D3B.smethod_5(1176460u)
				};
				string[] array4 = array;
				foreach (string d20D52B3 in array4)
				{
					deviceData_0.D532AD84(d20D52B3);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("rebooting device: ");
				if (!deviceData_0.smethod_5())
				{
					A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
				}
				else
				{
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				}
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, string_0);
		}
	}

	public void F80AA9B5(string string_0)
	{
		try
		{
			E5BADC00?.Invoke(bool_0: true, string_0);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				if (!ACB7982A.F883AF0D.E417AB0A)
				{
					ACB7982A.F883AF0D = ACB7982A.B60553A1(new List<GStruct90>
					{
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1171119u),
							E30848BA = D828C7A0.const_1
						},
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1171952u),
							E30848BA = D828C7A0.const_1
						},
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1172014u),
							E30848BA = D828C7A0.const_1
						}
					}, gform0_0);
				}
				if (!ACB7982A.F883AF0D.E417AB0A)
				{
					return;
				}
				A1853E84("Executing King Auth: ");
				GStruct90 gStruct = ACB7982A.F883AF0D.CA96D92F.Find((GStruct90 gstruct90_0) => Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1172014u)));
				if (!deviceData_0.smethod_9(gStruct.byte_0))
				{
					A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
					return;
				}
				string[] array = new string[41]
				{
					D09F0D3B.smethod_5(1178232u),
					D09F0D3B.smethod_5(1186109u),
					D09F0D3B.smethod_5(1186207u),
					D09F0D3B.smethod_5(1186299u),
					D09F0D3B.smethod_5(1186389u),
					D09F0D3B.smethod_5(1186453u),
					D09F0D3B.smethod_5(1186519u),
					D09F0D3B.smethod_5(1186591u),
					D09F0D3B.smethod_5(1186109u),
					D09F0D3B.smethod_5(1186207u),
					D09F0D3B.smethod_5(1186695u),
					D09F0D3B.smethod_5(1186591u),
					D09F0D3B.smethod_5(1186781u),
					D09F0D3B.smethod_5(1186852u),
					D09F0D3B.smethod_5(1187004u),
					D09F0D3B.smethod_5(1186781u),
					D09F0D3B.smethod_5(1187004u),
					D09F0D3B.smethod_5(1186299u),
					D09F0D3B.smethod_5(1186389u),
					D09F0D3B.smethod_5(1186453u),
					D09F0D3B.smethod_5(1186519u),
					D09F0D3B.smethod_5(1187132u),
					D09F0D3B.smethod_5(1187257u),
					D09F0D3B.smethod_5(1187389u),
					D09F0D3B.smethod_5(1187497u),
					D09F0D3B.smethod_5(1187622u),
					D09F0D3B.smethod_5(1187765u),
					D09F0D3B.smethod_5(1187895u),
					D09F0D3B.smethod_5(1188020u),
					D09F0D3B.smethod_5(1188155u),
					D09F0D3B.smethod_5(1188292u),
					D09F0D3B.smethod_5(1188425u),
					D09F0D3B.smethod_5(1188556u),
					D09F0D3B.smethod_5(1188690u),
					D09F0D3B.smethod_5(1188805u),
					D09F0D3B.smethod_5(1188950u),
					D09F0D3B.smethod_5(1189082u),
					D09F0D3B.smethod_5(1189209u),
					D09F0D3B.smethod_5(1189347u),
					D09F0D3B.smethod_5(1189470u),
					D09F0D3B.smethod_5(1175246u)
				};
				string[] array2 = array;
				foreach (string d20D52B in array2)
				{
					deviceData_0.D532AD84(d20D52B);
				}
				array = new string[15]
				{
					D09F0D3B.smethod_5(1175332u),
					D09F0D3B.smethod_5(1175408u),
					D09F0D3B.smethod_5(1175496u),
					D09F0D3B.smethod_5(1175606u),
					D09F0D3B.smethod_5(1175704u),
					D09F0D3B.smethod_5(1175800u),
					D09F0D3B.smethod_5(1175904u),
					D09F0D3B.smethod_5(1176006u),
					D09F0D3B.smethod_5(1176098u),
					D09F0D3B.smethod_5(1176192u),
					D09F0D3B.smethod_5(1176284u),
					D09F0D3B.smethod_5(1176386u),
					D09F0D3B.smethod_5(1176460u),
					D09F0D3B.smethod_5(1176556u),
					D09F0D3B.smethod_5(1176655u)
				};
				string[] array3 = array;
				foreach (string d20D52B2 in array3)
				{
					GClass112.smethod_28(4000);
					deviceData_0.D532AD84(d20D52B2);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("rebooting device: ");
				if (!deviceData_0.smethod_5())
				{
					A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
					return;
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("waiting for device: ");
				if (deviceData_0.F23C4699(100000))
				{
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84("waiting for boot complete: ");
					string b809E90D;
					do
					{
						GClass112.smethod_28(1000);
						b809E90D = deviceData_0.D532AD84("getprop sys.boot_completed");
					}
					while (Class607.B630A78B.object_0[121](Class607.B630A78B.object_0[1233](b809E90D), "1"));
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84(D09F0D3B.smethod_5(1176969u));
					deviceData_0.D532AD84(D09F0D3B.smethod_5(1186207u));
					GClass112.smethod_28(4000);
					deviceData_0.D532AD84(D09F0D3B.smethod_5(1189649u));
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84("installing Scripts: ");
					gStruct = ACB7982A.F883AF0D.CA96D92F.Find((GStruct90 gstruct90_0) => Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1171119u)));
					if (!deviceData_0.smethod_9(gStruct.byte_0))
					{
						A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
						return;
					}
					gStruct = ACB7982A.F883AF0D.CA96D92F.Find((GStruct90 gstruct90_0) => Class607.B630A78B.object_0[787](gstruct90_0.string_0, D09F0D3B.smethod_5(1171952u)));
					if (!deviceData_0.smethod_9(gStruct.byte_0))
					{
						A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
						return;
					}
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84(D09F0D3B.smethod_5(1176969u));
					array = new string[5]
					{
						D09F0D3B.smethod_5(1171288u),
						"sh /storage/emulated/0/Android/data/moe.shizuku.privileged.api/start.sh",
						D09F0D3B.smethod_5(1171577u),
						D09F0D3B.smethod_5(1177143u),
						D09F0D3B.smethod_5(1185871u)
					};
					string[] array4 = array;
					foreach (string d20D52B3 in array4)
					{
						GClass112.smethod_28(4000);
						deviceData_0.D532AD84(d20D52B3);
					}
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					array = new string[14]
					{
						D09F0D3B.smethod_5(1178050u),
						D09F0D3B.smethod_5(1178134u),
						D09F0D3B.smethod_5(1175496u),
						D09F0D3B.smethod_5(1175408u),
						D09F0D3B.smethod_5(1175606u),
						D09F0D3B.smethod_5(1175800u),
						D09F0D3B.smethod_5(1175704u),
						D09F0D3B.smethod_5(1175904u),
						D09F0D3B.smethod_5(1176006u),
						D09F0D3B.smethod_5(1176098u),
						D09F0D3B.smethod_5(1176192u),
						D09F0D3B.smethod_5(1176284u),
						D09F0D3B.smethod_5(1176386u),
						D09F0D3B.smethod_5(1176460u)
					};
					string[] array5 = array;
					foreach (string d20D52B4 in array5)
					{
						deviceData_0.D532AD84(d20D52B4);
					}
					A1853E84("rebooting device: ");
					if (!deviceData_0.smethod_5())
					{
						A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
					}
					else
					{
						A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					}
				}
				else
				{
					A1853E84("TimeOut", EF1F389C.Error, bool_0: false, F93C7108: true);
				}
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, string_0);
		}
	}

	public void A88B459C(string A5B99039 = "Disable Factory Reset")
	{
		try
		{
			E5BADC00?.Invoke(bool_0: true, A5B99039);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				if (!ACB7982A.FB92F18B.E417AB0A)
				{
					ACB7982A.FB92F18B = ACB7982A.B60553A1(new List<GStruct90>
					{
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1189781u),
							E30848BA = D828C7A0.const_1
						}
					}, gform0_0);
				}
				if (!ACB7982A.FB92F18B.E417AB0A)
				{
					return;
				}
				A1853E84("Executing King Auth: ");
				GStruct90 gStruct = ACB7982A.FB92F18B.CA96D92F.FirstOrDefault();
				if (!deviceData_0.smethod_9(gStruct.byte_0))
				{
					A1853E84("Failed", EF1F389C.Error, bool_0: false, F93C7108: true);
					return;
				}
				string[] array = new string[2]
				{
					D09F0D3B.smethod_5(1189844u),
					D09F0D3B.smethod_5(1189974u)
				};
				string[] array2 = array;
				foreach (string d20D52B in array2)
				{
					deviceData_0.D532AD84(d20D52B);
					GClass112.smethod_28(2000);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, A5B99039);
		}
	}

	public void AD0FB40F(string string_0 = "Bypass IT Admin New Sec [Metod 1] ")
	{
		try
		{
			E5BADC00?.Invoke(bool_0: true, string_0);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				A1853E84("Executing King Auth: ");
				string[] array = new string[14]
				{
					D09F0D3B.smethod_5(1190076u),
					D09F0D3B.smethod_5(1190166u),
					D09F0D3B.smethod_5(1190242u),
					D09F0D3B.smethod_5(1190338u),
					D09F0D3B.smethod_5(1190420u),
					D09F0D3B.smethod_5(1190518u),
					D09F0D3B.smethod_5(1190602u),
					D09F0D3B.smethod_5(1190698u),
					D09F0D3B.smethod_5(1190781u),
					D09F0D3B.smethod_5(1190936u),
					D09F0D3B.smethod_5(1191076u),
					D09F0D3B.smethod_5(1191196u),
					D09F0D3B.smethod_5(1191302u),
					D09F0D3B.smethod_5(1191416u)
				};
				string[] array2 = array;
				foreach (string d20D52B in array2)
				{
					deviceData_0.D532AD84(d20D52B);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("rebooting device: ");
				deviceData_0.smethod_5();
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("waiting for device: ");
				if (deviceData_0.F23C4699(100000))
				{
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84("waiting for boot complete: ");
					string b809E90D;
					do
					{
						GClass112.smethod_28(1000);
						b809E90D = deviceData_0.D532AD84("getprop sys.boot_completed");
					}
					while (Class607.B630A78B.object_0[121](Class607.B630A78B.object_0[1233](b809E90D), "1"));
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				}
				else
				{
					A1853E84("TimeOut", EF1F389C.Error, bool_0: false, F93C7108: true);
				}
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, string_0);
		}
	}

	public void method_12(string DD34D520 = "Bypass IT Admin New Sec [Metod 2] ")
	{
		try
		{
			E5BADC00?.Invoke(bool_0: true, DD34D520);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				A1853E84("Executing King Auth: ");
				string[] array = new string[14]
				{
					D09F0D3B.smethod_5(1191516u),
					D09F0D3B.smethod_5(1190166u),
					D09F0D3B.smethod_5(1191600u),
					D09F0D3B.smethod_5(1190338u),
					D09F0D3B.smethod_5(1191690u),
					D09F0D3B.smethod_5(1190518u),
					D09F0D3B.smethod_5(1191782u),
					D09F0D3B.smethod_5(1190698u),
					D09F0D3B.smethod_5(1191873u),
					D09F0D3B.smethod_5(1190936u),
					D09F0D3B.smethod_5(1192021u),
					D09F0D3B.smethod_5(1191196u),
					D09F0D3B.smethod_5(1192135u),
					D09F0D3B.smethod_5(1191416u)
				};
				string[] array2 = array;
				foreach (string d20D52B in array2)
				{
					deviceData_0.D532AD84(d20D52B);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("rebooting device: ");
				deviceData_0.smethod_5();
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("waiting for device: ");
				if (deviceData_0.F23C4699(100000))
				{
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84("waiting for boot complete: ");
					string b809E90D;
					do
					{
						GClass112.smethod_28(1000);
						b809E90D = deviceData_0.D532AD84("getprop sys.boot_completed");
					}
					while (Class607.B630A78B.object_0[121](Class607.B630A78B.object_0[1233](b809E90D), "1"));
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				}
				else
				{
					A1853E84("TimeOut", EF1F389C.Error, bool_0: false, F93C7108: true);
				}
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, DD34D520);
		}
	}

	public void method_13(string DA158087 = "IT Admin [Method 1]")
	{
		try
		{
			E5BADC00?.Invoke(bool_0: true, DA158087);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				if (!ACB7982A.C90E990C.E417AB0A)
				{
					ACB7982A.C90E990C = ACB7982A.B60553A1(new List<GStruct90>
					{
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1192243u),
							E30848BA = D828C7A0.const_1
						},
						new GStruct90
						{
							string_0 = D09F0D3B.smethod_5(1192305u),
							E30848BA = D828C7A0.const_1
						}
					}, gform0_0);
				}
				if (!ACB7982A.C90E990C.E417AB0A)
				{
					return;
				}
				A1853E84("Executing King Auth: ");
				deviceData_0.D532AD84(D09F0D3B.smethod_5(1190602u));
				foreach (GStruct90 item in ACB7982A.C90E990C.CA96D92F)
				{
					if (!deviceData_0.smethod_9(item.byte_0))
					{
						return;
					}
				}
				deviceData_0.D532AD84(D09F0D3B.smethod_5(1185386u));
				deviceData_0.D532AD84(D09F0D3B.smethod_5(1192368u));
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, DA158087);
		}
	}

	public void method_14()
	{
		new GClass128().C5017C25(new object[1] { this }, 255779);
	}

	public void method_15(string string_0 = "Fixing KG Services")
	{
		try
		{
			if (!method_1())
			{
				return;
			}
			A1853E84("Fixing KG Services: ");
			string[] array = new string[11]
			{
				D09F0D3B.smethod_5(1175332u),
				D09F0D3B.smethod_5(1175408u),
				D09F0D3B.smethod_5(1182269u),
				D09F0D3B.smethod_5(1175606u),
				D09F0D3B.smethod_5(1182387u),
				D09F0D3B.smethod_5(1182461u),
				D09F0D3B.smethod_5(1182549u),
				"svc wifi disable",
				"settings put global device_provisioned 1",
				"settings put secure user_setup_complete 1",
				D09F0D3B.smethod_5(1194008u)
			};
			string[] array2 = array;
			foreach (string d20D52B in array2)
			{
				deviceData_0.D532AD84(d20D52B);
			}
			A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
			A1853E84("rebooting device: ");
			deviceData_0.smethod_5();
			A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
			A1853E84("waiting for device: ");
			if (deviceData_0.F23C4699(180000))
			{
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("waiting for boot complete: ");
				string b809E90D;
				do
				{
					GClass112.smethod_28(1000);
					b809E90D = deviceData_0.D532AD84("getprop sys.boot_completed");
				}
				while (Class607.B630A78B.object_0[121](Class607.B630A78B.object_0[1233](b809E90D), "1"));
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
			}
			else
			{
				A1853E84("TimeOut", EF1F389C.Error, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
	}

	public void method_16(string A4907913 = "Bypass Knox Cloud ")
	{
		try
		{
			E5BADC00?.Invoke(bool_0: true, A4907913);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				A1853E84("Perfom Bypass: ");
				string[] array = new string[17]
				{
					"pkill -9 -f com.samsung.android.enrollment.service",
					"pkill -9 -f com.sec.enterprise.knox.cloudmdm.smdms",
					D09F0D3B.smethod_5(1194350u),
					D09F0D3B.smethod_5(1194456u),
					D09F0D3B.smethod_5(1194562u),
					D09F0D3B.smethod_5(1194690u),
					D09F0D3B.smethod_5(1194818u),
					D09F0D3B.smethod_5(1182549u),
					D09F0D3B.smethod_5(1194940u),
					D09F0D3B.smethod_5(1195018u),
					D09F0D3B.smethod_5(1175332u),
					D09F0D3B.smethod_5(1175408u),
					D09F0D3B.smethod_5(1182269u),
					D09F0D3B.smethod_5(1175606u),
					D09F0D3B.smethod_5(1182461u),
					D09F0D3B.smethod_5(1182549u),
					D09F0D3B.smethod_5(1194008u)
				};
				string[] array2 = array;
				foreach (string d20D52B in array2)
				{
					deviceData_0.D532AD84(d20D52B);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("rebooting device: ");
				deviceData_0.smethod_5();
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("waiting for device: ");
				if (deviceData_0.F23C4699(100000))
				{
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84("waiting for boot complete... ");
					string b809E90D;
					do
					{
						GClass112.smethod_28(1000);
						b809E90D = deviceData_0.D532AD84("getprop sys.boot_completed");
					}
					while (Class607.B630A78B.object_0[121](Class607.B630A78B.object_0[1233](b809E90D), "1"));
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				}
				else
				{
					A1853E84("TimeOut", EF1F389C.Error, bool_0: false, F93C7108: true);
				}
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, A4907913);
		}
	}

	public void method_17(string string_0 = "Bypass Kiosco MDM [AT&T] ")
	{
		try
		{
			E5BADC00?.Invoke(bool_0: true, string_0);
			if (!gclass113_0.C48D0DB3(A1853E84) || !method_1())
			{
				return;
			}
			if (method_2(BF2E8E22.OsVersion))
			{
				A1853E84("Perfom Bypass: ");
				string[] array = new string[9]
				{
					D09F0D3B.smethod_5(1195170u),
					D09F0D3B.smethod_5(1195291u),
					D09F0D3B.smethod_5(1195421u),
					D09F0D3B.smethod_5(1195503u),
					D09F0D3B.smethod_5(1175332u),
					D09F0D3B.smethod_5(1195580u),
					D09F0D3B.smethod_5(1195720u),
					D09F0D3B.smethod_5(1195808u),
					D09F0D3B.smethod_5(1195920u)
				};
				string[] array2 = array;
				foreach (string d20D52B in array2)
				{
					deviceData_0.D532AD84(d20D52B);
				}
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("rebooting device: ");
				deviceData_0.smethod_5();
				A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				A1853E84("waiting for device: ");
				if (deviceData_0.F23C4699(100000))
				{
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
					A1853E84("waiting for boot complete... ");
					string b809E90D;
					do
					{
						GClass112.smethod_28(1000);
						b809E90D = deviceData_0.D532AD84("getprop sys.boot_completed");
					}
					while (Class607.B630A78B.object_0[121](Class607.B630A78B.object_0[1233](b809E90D), "1"));
					A1853E84("Ok", EF1F389C.Success, bool_0: false, F93C7108: true);
				}
				else
				{
					A1853E84("TimeOut", EF1F389C.Error, bool_0: false, F93C7108: true);
				}
			}
			else
			{
				A1853E84("Error: ");
				A1853E84("Android Version no Supported", EF1F389C.Yellow, bool_0: false, F93C7108: true);
			}
		}
		catch (Exception object_)
		{
			A1853E84("Error: ");
			A1853E84(Class607.B630A78B.object_0[1160](object_), EF1F389C.Error, bool_0: false, F93C7108: true);
		}
		finally
		{
			E5BADC00?.Invoke(bool_0: false, string_0);
		}
	}

	public void A72FCF20(string string_0 = "IT Admin New Sec 2025 Relocks [OP]")
	{
		new GClass128().EF8D5E3B(new object[2] { this, string_0 }, 22981378);
	}

	public void FE216929(string string_0 = "IT Admin New Sec 2025 Relocks [AS]")
	{
		new GClass128().F3BD1601(new object[2] { this, string_0 }, 22041392);
	}

	public void method_18()
	{
		new GClass128().E6A33692(new object[1] { this }, 22973646);
	}

	public void method_19(string A3BD3507 = "Unlock sim [Only Pixel] ")
	{
		new GClass128().E6A33692(new object[2] { this, A3BD3507 }, 22581536);
	}

	private string F7A77A39(string string_0)
	{
		using SHA256 b02A = Class607.B630A78B.object_0[286]();
		byte[] byte_ = Class607.B630A78B.object_0[455](b02A, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1124](), string_0));
		string object_ = Class607.B630A78B.object_0[496](byte_);
		return B6335191.smethod_0(B6335191.smethod_0(D2AEF685.smethod_0(object_, new char[1] { '=' }), '+', '-'), '/', '_');
	}

	[AsyncStateMachine(typeof(A292DF17))]
	[DebuggerStepThrough]
	private void method_20(object sender, EventArgs e)
	{
		new GClass128().E6A33692(new object[3] { this, sender, e }, 558567);
	}

	private void method_21(object sender, EventArgs e)
	{
		new GClass128().method_68(new object[3] { this, sender, e }, 291663);
	}

	private void F1A4B7AA(object sender, EventArgs e)
	{
		new GClass128().E8AA1C3C(new object[3] { this, sender, e }, 343043);
	}

	private void E83FEFAC(object sender, EventArgs e)
	{
		new GClass128().method_68(new object[3] { this, sender, e }, 22982828);
	}

	private void method_22(object sender, EventArgs e)
	{
		new GClass128().B4154402(new object[3] { this, sender, e }, 22086456);
	}

	private void F12E36AC(object sender, EventArgs e)
	{
		new GClass128().E8AA1C3C(new object[3] { this, sender, e }, 41812);
	}

	private void method_23(object sender, EventArgs e)
	{
		new GClass128().F3BD1601(new object[3] { this, sender, e }, 403732);
	}

	private void CE84D489(object sender, EventArgs e)
	{
		new GClass128().method_323(new object[3] { this, sender, e }, 22511938);
	}

	private void method_24(object sender, EventArgs e)
	{
		new GClass128().E8AA1C3C(new object[3] { this, sender, e }, 22522357);
	}

	private void F813838F(object sender, EventArgs e)
	{
		new GClass128().method_323(new object[3] { this, sender, e }, 22987759);
	}

	private void method_25(object sender, EventArgs e)
	{
		new GClass128().E6A33692(new object[3] { this, sender, e }, 22496132);
	}

	private unsafe void E8A97E31(object sender, EventArgs e)
	{
		Thread object_ = Class607.B630A78B.object_0[1033](Class607.B630A78B.object_0[439](this, (nint)__ldftn(EAB5ABA9.CB89F005)));
		Class607.B630A78B.object_0[403](object_);
	}

	private unsafe void B737BDB9(object sender, EventArgs e)
	{
		Thread object_ = Class607.B630A78B.object_0[1033](Class607.B630A78B.object_0[439](this, (nint)__ldftn(EAB5ABA9.F20C443B)));
		Class607.B630A78B.object_0[403](object_);
	}

	private unsafe void EE07369A(object sender, EventArgs e)
	{
		Thread object_ = Class607.B630A78B.object_0[1033](Class607.B630A78B.object_0[439](this, (nint)__ldftn(EAB5ABA9.A0AFFB05)));
		Class607.B630A78B.object_0[403](object_);
	}

	private unsafe void A8A7BD13(object sender, EventArgs e)
	{
		Thread object_ = Class607.B630A78B.object_0[1033](Class607.B630A78B.object_0[439](this, (nint)__ldftn(EAB5ABA9.method_37)));
		Class607.B630A78B.object_0[403](object_);
	}

	private unsafe void method_26(object sender, EventArgs e)
	{
		Thread object_ = Class607.B630A78B.object_0[1033](Class607.B630A78B.object_0[439](this, (nint)__ldftn(EAB5ABA9.BD81A8A7)));
		Class607.B630A78B.object_0[403](object_);
	}

	private unsafe void method_27(object sender, EventArgs e)
	{
		Thread object_ = Class607.B630A78B.object_0[1033](Class607.B630A78B.object_0[439](this, (nint)__ldftn(EAB5ABA9.method_38)));
		Class607.B630A78B.object_0[403](object_);
	}

	private unsafe void E88D0909(object sender, EventArgs e)
	{
		Thread object_ = Class607.B630A78B.object_0[1033](Class607.B630A78B.object_0[439](this, (nint)__ldftn(EAB5ABA9.method_39)));
		Class607.B630A78B.object_0[403](object_);
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && AE01711A != null)
		{
			AE01711A.Dispose();
		}
		Class607.B630A78B.object_0[878](this, disposing);
	}

	private unsafe void method_28()
	{
		DEB88E9E = Class607.B630A78B.object_0[1206]();
		DE220E9E = Class607.B630A78B.object_0[926]();
		label_0 = Class607.B630A78B.object_0[113]();
		A8B012BA = Class607.B630A78B.object_0[1206]();
		richTextBox_0 = Class607.B630A78B.object_0[344]();
		C93DE53A = Class607.B630A78B.object_0[853]();
		panel_0 = Class607.B630A78B.object_0[853]();
		gcontrol1_0 = new GControl1();
		tabPage_1 = Class607.B630A78B.object_0[139]();
		groupBox_2 = Class607.B630A78B.object_0[1206]();
		EA2B0C8B = Class607.B630A78B.object_0[1206]();
		EA01D4AA = Class607.B630A78B.object_0[1206]();
		BB16FC09 = Class607.B630A78B.object_0[139]();
		groupBox_0 = Class607.B630A78B.object_0[1206]();
		tabPage_0 = Class607.B630A78B.object_0[139]();
		D4873E04 = Class607.B630A78B.object_0[1206]();
		groupBox_1 = Class607.B630A78B.object_0[1206]();
		gclass104_0 = new GClass104();
		D5BB13B4 = new GClass104();
		F19F4AAB = new GClass104();
		B1B9AF3F = new GClass104();
		D327D1B9 = new GClass104();
		F6A3262C = new GClass104();
		B21C68A4 = Class607.B630A78B.object_0[853]();
		gcontrol2_0 = new GControl2();
		pictureBox_0 = Class607.B630A78B.object_0[1114]();
		******** = Class607.B630A78B.object_0[1112]();
		button_10 = Class607.B630A78B.object_0[1112]();
		button_4 = Class607.B630A78B.object_0[1112]();
		button_5 = Class607.B630A78B.object_0[1112]();
		button_9 = Class607.B630A78B.object_0[1112]();
		button_6 = Class607.B630A78B.object_0[1112]();
		F51A3B03 = Class607.B630A78B.object_0[1112]();
		C0AEB5A6 = Class607.B630A78B.object_0[1112]();
		E21AF888 = Class607.B630A78B.object_0[1112]();
		D3ACDC32 = Class607.B630A78B.object_0[1112]();
		button_11 = Class607.B630A78B.object_0[1112]();
		D50C9613 = Class607.B630A78B.object_0[1112]();
		button_3 = Class607.B630A78B.object_0[1112]();
		CCA9F8A4 = Class607.B630A78B.object_0[1112]();
		button_2 = Class607.B630A78B.object_0[1112]();
		E815B5B4 = Class607.B630A78B.object_0[1112]();
		button_12 = Class607.B630A78B.object_0[1112]();
		B0019D3D = Class607.B630A78B.object_0[1112]();
		button_8 = Class607.B630A78B.object_0[1112]();
		button_7 = Class607.B630A78B.object_0[1112]();
		button_1 = Class607.B630A78B.object_0[1112]();
		button_0 = Class607.B630A78B.object_0[1112]();
		tabPage_2 = Class607.B630A78B.object_0[139]();
		groupBox_3 = Class607.B630A78B.object_0[1206]();
		button_13 = Class607.B630A78B.object_0[1112]();
		button_14 = Class607.B630A78B.object_0[1112]();
		Class607.B630A78B.object_0[549](DEB88E9E);
		Class607.B630A78B.object_0[549](A8B012BA);
		Class607.B630A78B.object_0[549](C93DE53A);
		Class607.B630A78B.object_0[549](panel_0);
		Class607.B630A78B.object_0[549](gcontrol1_0);
		Class607.B630A78B.object_0[549](tabPage_1);
		Class607.B630A78B.object_0[549](groupBox_2);
		Class607.B630A78B.object_0[549](EA2B0C8B);
		Class607.B630A78B.object_0[549](EA01D4AA);
		Class607.B630A78B.object_0[549](BB16FC09);
		Class607.B630A78B.object_0[549](groupBox_0);
		Class607.B630A78B.object_0[549](tabPage_0);
		Class607.B630A78B.object_0[549](D4873E04);
		Class607.B630A78B.object_0[549](groupBox_1);
		Class607.B630A78B.object_0[549](B21C68A4);
		Class607.B630A78B.object_0[361](pictureBox_0);
		Class607.B630A78B.object_0[549](tabPage_2);
		Class607.B630A78B.object_0[549](groupBox_3);
		Class607.B630A78B.object_0[550](this);
		Class607.B630A78B.object_0[781](DEB88E9E).Add(DE220E9E);
		Class607.B630A78B.object_0[781](DEB88E9E).Add(label_0);
		Class607.B630A78B.object_0[781](DEB88E9E).Add(button_0);
		Class607.B630A78B.object_0[1278](DEB88E9E, DockStyle.Fill);
		Class607.B630A78B.object_0[388](DEB88E9E, Class607.B630A78B.object_0[760](5, 5));
		Class607.B630A78B.object_0[689](DEB88E9E, "GrpBxResult");
		Class607.B630A78B.object_0[1276](DEB88E9E, Class607.B630A78B.object_0[174](1138, 39));
		Class607.B630A78B.object_0[334](DEB88E9E, 35);
		Class607.B630A78B.object_0[1183](DEB88E9E, bool_0: false);
		Class607.B630A78B.object_0[1175](DEB88E9E, "Result");
		Class607.B630A78B.object_0[1242](DE220E9E, AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[388](DE220E9E, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](DE220E9E, "ProgBar");
		Class607.B630A78B.object_0[1276](DE220E9E, Class607.B630A78B.object_0[174](920, 5));
		Class607.B630A78B.object_0[334](DE220E9E, 29);
		Class607.B630A78B.object_0[1242](label_0, AnchorStyles.Bottom | AnchorStyles.Right);
		Class607.B630A78B.object_0[11](label_0, E704E9A1: true);
		Class607.B630A78B.object_0[388](label_0, Class607.B630A78B.object_0[760](932, 18));
		Class607.B630A78B.object_0[689](label_0, "lblstate");
		Class607.B630A78B.object_0[1276](label_0, Class607.B630A78B.object_0[174](15, 13));
		Class607.B630A78B.object_0[334](label_0, 28);
		Class607.B630A78B.object_0[1175](label_0, "%");
		Class607.B630A78B.object_0[781](A8B012BA).Add(pictureBox_0);
		Class607.B630A78B.object_0[781](A8B012BA).Add(richTextBox_0);
		Class607.B630A78B.object_0[1278](A8B012BA, DockStyle.Fill);
		Class607.B630A78B.object_0[388](A8B012BA, Class607.B630A78B.object_0[760](5, 5));
		Class607.B630A78B.object_0[689](A8B012BA, "GrpBxLog");
		Class607.B630A78B.object_0[1276](A8B012BA, Class607.B630A78B.object_0[174](629, 423));
		Class607.B630A78B.object_0[334](A8B012BA, 36);
		Class607.B630A78B.object_0[1183](A8B012BA, bool_0: false);
		Class607.B630A78B.object_0[1175](A8B012BA, "Result");
		Class607.B630A78B.object_0[823](richTextBox_0, Class607.B630A78B.object_0[235](33, 33, 33));
		Class607.B630A78B.object_0[1278](richTextBox_0, DockStyle.Fill);
		Class607.B630A78B.object_0[1267](richTextBox_0, Class607.B630A78B.object_0[1152]("Courier New", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[388](richTextBox_0, Class607.B630A78B.object_0[760](3, 16));
		Class607.B630A78B.object_0[689](richTextBox_0, "RichLog");
		Class607.B630A78B.object_0[664](richTextBox_0, B9A668BD: true);
		Class607.B630A78B.object_0[1276](richTextBox_0, Class607.B630A78B.object_0[174](623, 404));
		Class607.B630A78B.object_0[334](richTextBox_0, 5);
		Class607.B630A78B.object_0[1175](richTextBox_0, "");
		Class607.B630A78B.object_0[823](C93DE53A, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](C93DE53A).Add(DEB88E9E);
		Class607.B630A78B.object_0[1278](C93DE53A, DockStyle.Bottom);
		Class607.B630A78B.object_0[388](C93DE53A, Class607.B630A78B.object_0[760](0, 460));
		Class607.B630A78B.object_0[689](C93DE53A, "panel1");
		Class607.B630A78B.object_0[356](C93DE53A, Class607.B630A78B.object_0[1237](5));
		Class607.B630A78B.object_0[1276](C93DE53A, Class607.B630A78B.object_0[174](1148, 49));
		Class607.B630A78B.object_0[334](C93DE53A, 39);
		Class607.B630A78B.object_0[823](panel_0, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](panel_0).Add(gcontrol1_0);
		Class607.B630A78B.object_0[1278](panel_0, DockStyle.Right);
		Class607.B630A78B.object_0[388](panel_0, Class607.B630A78B.object_0[760](639, 27));
		Class607.B630A78B.object_0[689](panel_0, "panel2");
		Class607.B630A78B.object_0[356](panel_0, Class607.B630A78B.object_0[1237](5));
		Class607.B630A78B.object_0[1276](panel_0, Class607.B630A78B.object_0[174](509, 433));
		Class607.B630A78B.object_0[334](panel_0, 40);
		Class607.B630A78B.object_0[781](gcontrol1_0).Add(tabPage_1);
		Class607.B630A78B.object_0[781](gcontrol1_0).Add(BB16FC09);
		Class607.B630A78B.object_0[781](gcontrol1_0).Add(tabPage_0);
		Class607.B630A78B.object_0[781](gcontrol1_0).Add(tabPage_2);
		gcontrol1_0.Int32_0 = 0;
		Class607.B630A78B.object_0[1278](gcontrol1_0, DockStyle.Fill);
		Class607.B630A78B.object_0[388](gcontrol1_0, Class607.B630A78B.object_0[760](5, 5));
		gcontrol1_0.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](gcontrol1_0, "TPFeatures");
		Class607.B630A78B.object_0[734](gcontrol1_0, 0);
		Class607.B630A78B.object_0[1276](gcontrol1_0, Class607.B630A78B.object_0[174](499, 423));
		Class607.B630A78B.object_0[334](gcontrol1_0, 37);
		Class607.B630A78B.object_0[823](tabPage_1, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](tabPage_1).Add(groupBox_2);
		Class607.B630A78B.object_0[781](tabPage_1).Add(EA2B0C8B);
		Class607.B630A78B.object_0[781](tabPage_1).Add(EA01D4AA);
		Class607.B630A78B.object_0[1250](tabPage_1, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](tabPage_1, "tpsamsung");
		Class607.B630A78B.object_0[356](tabPage_1, Class607.B630A78B.object_0[1237](3));
		Class607.B630A78B.object_0[1276](tabPage_1, Class607.B630A78B.object_0[174](491, 397));
		Class607.B630A78B.object_0[295](tabPage_1, 3);
		Class607.B630A78B.object_0[1175](tabPage_1, "samsung");
		Class607.B630A78B.object_0[823](groupBox_2, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](groupBox_2).Add(********);
		Class607.B630A78B.object_0[1278](groupBox_2, DockStyle.Top);
		Class607.B630A78B.object_0[388](groupBox_2, Class607.B630A78B.object_0[760](3, 172));
		Class607.B630A78B.object_0[689](groupBox_2, "groupBox3");
		Class607.B630A78B.object_0[1276](groupBox_2, Class607.B630A78B.object_0[174](485, 65));
		Class607.B630A78B.object_0[334](groupBox_2, 6);
		Class607.B630A78B.object_0[1183](groupBox_2, bool_0: false);
		Class607.B630A78B.object_0[1175](groupBox_2, "Bypass Knox CLoud");
		Class607.B630A78B.object_0[823](EA2B0C8B, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](EA2B0C8B).Add(button_10);
		Class607.B630A78B.object_0[1278](EA2B0C8B, DockStyle.Top);
		Class607.B630A78B.object_0[388](EA2B0C8B, Class607.B630A78B.object_0[760](3, 107));
		Class607.B630A78B.object_0[689](EA2B0C8B, "groupBox2");
		Class607.B630A78B.object_0[1276](EA2B0C8B, Class607.B630A78B.object_0[174](485, 65));
		Class607.B630A78B.object_0[334](EA2B0C8B, 5);
		Class607.B630A78B.object_0[1183](EA2B0C8B, bool_0: false);
		Class607.B630A78B.object_0[1175](EA2B0C8B, "Bypass KG Broken Error Prenormal Checking or Active");
		Class607.B630A78B.object_0[823](EA01D4AA, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](EA01D4AA).Add(button_4);
		Class607.B630A78B.object_0[781](EA01D4AA).Add(button_5);
		Class607.B630A78B.object_0[1278](EA01D4AA, DockStyle.Top);
		Class607.B630A78B.object_0[388](EA01D4AA, Class607.B630A78B.object_0[760](3, 3));
		Class607.B630A78B.object_0[689](EA01D4AA, "groupBox1");
		Class607.B630A78B.object_0[1276](EA01D4AA, Class607.B630A78B.object_0[174](485, 104));
		Class607.B630A78B.object_0[334](EA01D4AA, 4);
		Class607.B630A78B.object_0[1183](EA01D4AA, bool_0: false);
		Class607.B630A78B.object_0[1175](EA01D4AA, "Bypass KG");
		Class607.B630A78B.object_0[823](BB16FC09, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](BB16FC09).Add(groupBox_0);
		Class607.B630A78B.object_0[1250](BB16FC09, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](BB16FC09, "tpfeature");
		Class607.B630A78B.object_0[356](BB16FC09, Class607.B630A78B.object_0[1237](3));
		Class607.B630A78B.object_0[1276](BB16FC09, Class607.B630A78B.object_0[174](491, 397));
		Class607.B630A78B.object_0[295](BB16FC09, 1);
		Class607.B630A78B.object_0[1175](BB16FC09, "Tecno/Infinix");
		Class607.B630A78B.object_0[823](groupBox_0, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](groupBox_0).Add(button_9);
		Class607.B630A78B.object_0[781](groupBox_0).Add(button_6);
		Class607.B630A78B.object_0[781](groupBox_0).Add(F51A3B03);
		Class607.B630A78B.object_0[781](groupBox_0).Add(C0AEB5A6);
		Class607.B630A78B.object_0[781](groupBox_0).Add(E21AF888);
		Class607.B630A78B.object_0[781](groupBox_0).Add(D3ACDC32);
		Class607.B630A78B.object_0[1278](groupBox_0, DockStyle.Top);
		Class607.B630A78B.object_0[388](groupBox_0, Class607.B630A78B.object_0[760](3, 3));
		Class607.B630A78B.object_0[689](groupBox_0, "GrpBxUnlock");
		Class607.B630A78B.object_0[1276](groupBox_0, Class607.B630A78B.object_0[174](485, 259));
		Class607.B630A78B.object_0[334](groupBox_0, 2);
		Class607.B630A78B.object_0[1183](groupBox_0, bool_0: false);
		Class607.B630A78B.object_0[1175](groupBox_0, "ADB Service");
		Class607.B630A78B.object_0[823](tabPage_0, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](tabPage_0).Add(D4873E04);
		Class607.B630A78B.object_0[781](tabPage_0).Add(groupBox_1);
		Class607.B630A78B.object_0[1250](tabPage_0, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](tabPage_0, "tpnetwork");
		Class607.B630A78B.object_0[356](tabPage_0, Class607.B630A78B.object_0[1237](3));
		Class607.B630A78B.object_0[1276](tabPage_0, Class607.B630A78B.object_0[174](491, 397));
		Class607.B630A78B.object_0[295](tabPage_0, 2);
		Class607.B630A78B.object_0[1175](tabPage_0, "Multi Brand/IT Admin");
		Class607.B630A78B.object_0[823](D4873E04, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](D4873E04).Add(button_11);
		Class607.B630A78B.object_0[781](D4873E04).Add(D50C9613);
		Class607.B630A78B.object_0[781](D4873E04).Add(button_3);
		Class607.B630A78B.object_0[781](D4873E04).Add(CCA9F8A4);
		Class607.B630A78B.object_0[781](D4873E04).Add(button_2);
		Class607.B630A78B.object_0[1278](D4873E04, DockStyle.Top);
		Class607.B630A78B.object_0[388](D4873E04, Class607.B630A78B.object_0[760](3, 234));
		Class607.B630A78B.object_0[689](D4873E04, "GrpBxADBFeatures");
		Class607.B630A78B.object_0[1276](D4873E04, Class607.B630A78B.object_0[174](485, 182));
		Class607.B630A78B.object_0[334](D4873E04, 4);
		Class607.B630A78B.object_0[1183](D4873E04, bool_0: false);
		Class607.B630A78B.object_0[1175](D4873E04, "Generic Bypass All Brands");
		Class607.B630A78B.object_0[823](groupBox_1, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](groupBox_1).Add(E815B5B4);
		Class607.B630A78B.object_0[781](groupBox_1).Add(button_12);
		Class607.B630A78B.object_0[781](groupBox_1).Add(B0019D3D);
		Class607.B630A78B.object_0[781](groupBox_1).Add(button_8);
		Class607.B630A78B.object_0[781](groupBox_1).Add(gclass104_0);
		Class607.B630A78B.object_0[781](groupBox_1).Add(D5BB13B4);
		Class607.B630A78B.object_0[781](groupBox_1).Add(F19F4AAB);
		Class607.B630A78B.object_0[781](groupBox_1).Add(B1B9AF3F);
		Class607.B630A78B.object_0[781](groupBox_1).Add(D327D1B9);
		Class607.B630A78B.object_0[781](groupBox_1).Add(F6A3262C);
		Class607.B630A78B.object_0[781](groupBox_1).Add(button_7);
		Class607.B630A78B.object_0[781](groupBox_1).Add(button_1);
		Class607.B630A78B.object_0[1278](groupBox_1, DockStyle.Top);
		Class607.B630A78B.object_0[388](groupBox_1, Class607.B630A78B.object_0[760](3, 3));
		Class607.B630A78B.object_0[689](groupBox_1, "GrpBxMDM");
		Class607.B630A78B.object_0[1276](groupBox_1, Class607.B630A78B.object_0[174](485, 231));
		Class607.B630A78B.object_0[334](groupBox_1, 3);
		Class607.B630A78B.object_0[1183](groupBox_1, bool_0: false);
		Class607.B630A78B.object_0[1175](groupBox_1, "IT ADMIN");
		Class607.B630A78B.object_0[11](gclass104_0, E704E9A1: true);
		Class607.B630A78B.object_0[823](gclass104_0, Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[706](gclass104_0, bool_0: true);
		gclass104_0.Int32_0 = 0;
		Class607.B630A78B.object_0[1267](gclass104_0, Class607.B630A78B.object_0[1269]("Microsoft Sans Serif", 10f));
		Class607.B630A78B.object_0[388](gclass104_0, Class607.B630A78B.object_0[760](7, 12));
		Class607.B630A78B.object_0[349](gclass104_0, Class607.B630A78B.object_0[1237](0));
		gclass104_0.Point_0 = Class607.B630A78B.object_0[760](-1, -1);
		gclass104_0.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](gclass104_0, "checkauto");
		gclass104_0.Boolean_0 = true;
		Class607.B630A78B.object_0[1276](gclass104_0, Class607.B630A78B.object_0[174](99, 30));
		Class607.B630A78B.object_0[334](gclass104_0, 4);
		Class607.B630A78B.object_0[521](gclass104_0, bool_0: true);
		Class607.B630A78B.object_0[1175](gclass104_0, "auto detect");
		Class607.B630A78B.object_0[270](gclass104_0, CA2A151B: false);
		Class607.B630A78B.object_0[11](D5BB13B4, E704E9A1: true);
		Class607.B630A78B.object_0[823](D5BB13B4, Class607.B630A78B.object_0[477]());
		D5BB13B4.Int32_0 = 0;
		Class607.B630A78B.object_0[1267](D5BB13B4, Class607.B630A78B.object_0[1269]("Microsoft Sans Serif", 10f));
		Class607.B630A78B.object_0[388](D5BB13B4, Class607.B630A78B.object_0[760](96, 42));
		Class607.B630A78B.object_0[349](D5BB13B4, Class607.B630A78B.object_0[1237](0));
		D5BB13B4.Point_0 = Class607.B630A78B.object_0[760](-1, -1);
		D5BB13B4.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](D5BB13B4, "checkhuawei");
		D5BB13B4.Boolean_0 = true;
		Class607.B630A78B.object_0[1276](D5BB13B4, Class607.B630A78B.object_0[174](118, 30));
		Class607.B630A78B.object_0[334](D5BB13B4, 4);
		Class607.B630A78B.object_0[1175](D5BB13B4, "Huawei/Honor");
		Class607.B630A78B.object_0[270](D5BB13B4, CA2A151B: false);
		Class607.B630A78B.object_0[11](F19F4AAB, E704E9A1: true);
		Class607.B630A78B.object_0[823](F19F4AAB, Class607.B630A78B.object_0[477]());
		F19F4AAB.Int32_0 = 0;
		Class607.B630A78B.object_0[1267](F19F4AAB, Class607.B630A78B.object_0[1269]("Microsoft Sans Serif", 10f));
		Class607.B630A78B.object_0[388](F19F4AAB, Class607.B630A78B.object_0[760](264, 12));
		Class607.B630A78B.object_0[349](F19F4AAB, Class607.B630A78B.object_0[1237](0));
		F19F4AAB.Point_0 = Class607.B630A78B.object_0[760](-1, -1);
		F19F4AAB.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](F19F4AAB, "checkmoto");
		F19F4AAB.Boolean_0 = true;
		Class607.B630A78B.object_0[1276](F19F4AAB, Class607.B630A78B.object_0[174](85, 30));
		Class607.B630A78B.object_0[334](F19F4AAB, 4);
		Class607.B630A78B.object_0[1175](F19F4AAB, "Motorola");
		Class607.B630A78B.object_0[270](F19F4AAB, CA2A151B: false);
		Class607.B630A78B.object_0[11](B1B9AF3F, E704E9A1: true);
		Class607.B630A78B.object_0[823](B1B9AF3F, Class607.B630A78B.object_0[477]());
		B1B9AF3F.Int32_0 = 0;
		Class607.B630A78B.object_0[1267](B1B9AF3F, Class607.B630A78B.object_0[1269]("Microsoft Sans Serif", 10f));
		Class607.B630A78B.object_0[388](B1B9AF3F, Class607.B630A78B.object_0[760](185, 12));
		Class607.B630A78B.object_0[349](B1B9AF3F, Class607.B630A78B.object_0[1237](0));
		B1B9AF3F.Point_0 = Class607.B630A78B.object_0[760](-1, -1);
		B1B9AF3F.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](B1B9AF3F, "checkinfinix");
		B1B9AF3F.Boolean_0 = true;
		Class607.B630A78B.object_0[1276](B1B9AF3F, Class607.B630A78B.object_0[174](67, 30));
		Class607.B630A78B.object_0[334](B1B9AF3F, 4);
		Class607.B630A78B.object_0[1175](B1B9AF3F, "Infinix");
		Class607.B630A78B.object_0[270](B1B9AF3F, CA2A151B: false);
		Class607.B630A78B.object_0[11](D327D1B9, E704E9A1: true);
		Class607.B630A78B.object_0[823](D327D1B9, Class607.B630A78B.object_0[477]());
		D327D1B9.Int32_0 = 0;
		Class607.B630A78B.object_0[1267](D327D1B9, Class607.B630A78B.object_0[1269]("Microsoft Sans Serif", 10f));
		Class607.B630A78B.object_0[388](D327D1B9, Class607.B630A78B.object_0[760](114, 12));
		Class607.B630A78B.object_0[349](D327D1B9, Class607.B630A78B.object_0[1237](0));
		D327D1B9.Point_0 = Class607.B630A78B.object_0[760](-1, -1);
		D327D1B9.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](D327D1B9, "checktecno");
		D327D1B9.Boolean_0 = true;
		Class607.B630A78B.object_0[1276](D327D1B9, Class607.B630A78B.object_0[174](67, 30));
		Class607.B630A78B.object_0[334](D327D1B9, 4);
		Class607.B630A78B.object_0[1175](D327D1B9, "Tecno");
		Class607.B630A78B.object_0[270](D327D1B9, CA2A151B: false);
		Class607.B630A78B.object_0[11](F6A3262C, E704E9A1: true);
		Class607.B630A78B.object_0[823](F6A3262C, Class607.B630A78B.object_0[477]());
		F6A3262C.Int32_0 = 0;
		Class607.B630A78B.object_0[1267](F6A3262C, Class607.B630A78B.object_0[1269]("Microsoft Sans Serif", 10f));
		Class607.B630A78B.object_0[388](F6A3262C, Class607.B630A78B.object_0[760](7, 42));
		Class607.B630A78B.object_0[349](F6A3262C, Class607.B630A78B.object_0[1237](0));
		F6A3262C.Point_0 = Class607.B630A78B.object_0[760](-1, -1);
		F6A3262C.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](F6A3262C, "checkxiaomi");
		F6A3262C.Boolean_0 = true;
		Class607.B630A78B.object_0[1276](F6A3262C, Class607.B630A78B.object_0[174](72, 30));
		Class607.B630A78B.object_0[334](F6A3262C, 4);
		Class607.B630A78B.object_0[1175](F6A3262C, "Xiaomi");
		Class607.B630A78B.object_0[270](F6A3262C, CA2A151B: false);
		Class607.B630A78B.object_0[823](B21C68A4, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](B21C68A4).Add(A8B012BA);
		Class607.B630A78B.object_0[1278](B21C68A4, DockStyle.Fill);
		Class607.B630A78B.object_0[388](B21C68A4, Class607.B630A78B.object_0[760](0, 27));
		Class607.B630A78B.object_0[689](B21C68A4, "panel3");
		Class607.B630A78B.object_0[356](B21C68A4, Class607.B630A78B.object_0[1237](5));
		Class607.B630A78B.object_0[1276](B21C68A4, Class607.B630A78B.object_0[174](639, 433));
		Class607.B630A78B.object_0[334](B21C68A4, 41);
		gcontrol2_0.GControl1_0 = gcontrol1_0;
		gcontrol2_0.Int32_0 = 0;
		Class607.B630A78B.object_0[1278](gcontrol2_0, DockStyle.Top);
		Class607.B630A78B.object_0[388](gcontrol2_0, Class607.B630A78B.object_0[760](0, 0));
		gcontrol2_0.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](gcontrol2_0, "TPNav");
		Class607.B630A78B.object_0[1276](gcontrol2_0, Class607.B630A78B.object_0[174](1148, 27));
		gcontrol2_0.E4A0A31D = GEnum37.B7AD2FB4;
		Class607.B630A78B.object_0[334](gcontrol2_0, 38);
		Class607.B630A78B.object_0[823](pictureBox_0, Class607.B630A78B.object_0[235](33, 33, 33));
		Class607.B630A78B.object_0[148](pictureBox_0, A282DB38.Loading______);
		Class607.B630A78B.object_0[388](pictureBox_0, Class607.B630A78B.object_0[760](499, 317));
		Class607.B630A78B.object_0[689](pictureBox_0, "pictureBox1");
		Class607.B630A78B.object_0[1276](pictureBox_0, Class607.B630A78B.object_0[174](100, 100));
		Class607.B630A78B.object_0[606](pictureBox_0, PictureBoxSizeMode.StretchImage);
		Class607.B630A78B.object_0[831](pictureBox_0, 6);
		Class607.B630A78B.object_0[573](pictureBox_0, FF38159D: false);
		Class607.B630A78B.object_0[1242](********, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](********, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](********, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](********, A282DB38.shield);
		Class607.B630A78B.object_0[997](********, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](********, Class607.B630A78B.object_0[760](6, 19));
		Class607.B630A78B.object_0[689](********, "BtnKnoxcloud");
		Class607.B630A78B.object_0[1276](********, Class607.B630A78B.object_0[174](472, 33));
		Class607.B630A78B.object_0[334](********, 4);
		Class607.B630A78B.object_0[1175](********, "Bypass Knox Cloud");
		Class607.B630A78B.object_0[494](********, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](********, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](********, CA2A151B: true);
		Class607.B630A78B.object_0[1239](********, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.B737BDB9)));
		Class607.B630A78B.object_0[1242](button_10, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_10, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_10, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_10, A282DB38.shield);
		Class607.B630A78B.object_0[997](button_10, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_10, Class607.B630A78B.object_0[760](6, 19));
		Class607.B630A78B.object_0[689](button_10, "BtnKnoxGaurdSamsungnew");
		Class607.B630A78B.object_0[1276](button_10, Class607.B630A78B.object_0[174](472, 33));
		Class607.B630A78B.object_0[334](button_10, 4);
		Class607.B630A78B.object_0[1175](button_10, " KG (Knox Guard) bypass 2025 All Security Patch");
		Class607.B630A78B.object_0[494](button_10, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_10, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_10, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_10, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_25)));
		Class607.B630A78B.object_0[1242](button_4, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_4, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_4, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_4, A282DB38.shield);
		Class607.B630A78B.object_0[997](button_4, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_4, Class607.B630A78B.object_0[760](6, 58));
		Class607.B630A78B.object_0[689](button_4, "BtnKnoxGaurdSamsung");
		Class607.B630A78B.object_0[1276](button_4, Class607.B630A78B.object_0[174](472, 33));
		Class607.B630A78B.object_0[334](button_4, 3);
		Class607.B630A78B.object_0[1175](button_4, " KG (Knox Guard) bypass until October 2024 Security Patch");
		Class607.B630A78B.object_0[494](button_4, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_4, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_4, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_4, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_21)));
		Class607.B630A78B.object_0[1242](button_5, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_5, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_5, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_5, A282DB38.qr_code);
		Class607.B630A78B.object_0[997](button_5, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_5, Class607.B630A78B.object_0[760](6, 19));
		Class607.B630A78B.object_0[689](button_5, "BtnEnableADB");
		Class607.B630A78B.object_0[1276](button_5, Class607.B630A78B.object_0[174](472, 33));
		Class607.B630A78B.object_0[334](button_5, 3);
		Class607.B630A78B.object_0[1175](button_5, "Enable ADB QR");
		Class607.B630A78B.object_0[494](button_5, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_5, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_5, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_5, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_20)));
		Class607.B630A78B.object_0[1242](button_9, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_9, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_9, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_9, A282DB38.tool);
		Class607.B630A78B.object_0[997](button_9, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_9, Class607.B630A78B.object_0[760](7, 213));
		Class607.B630A78B.object_0[689](button_9, "btmb");
		Class607.B630A78B.object_0[1276](button_9, Class607.B630A78B.object_0[174](474, 33));
		Class607.B630A78B.object_0[334](button_9, 5);
		Class607.B630A78B.object_0[1175](button_9, "Bypass Tecno MDM [BLACKSCREEN]");
		Class607.B630A78B.object_0[494](button_9, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_9, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_9, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_9, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.E8A97E31)));
		Class607.B630A78B.object_0[1242](button_6, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_6, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_6, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_6, A282DB38.tool);
		Class607.B630A78B.object_0[997](button_6, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_6, Class607.B630A78B.object_0[760](7, 174));
		Class607.B630A78B.object_0[689](button_6, "BtnDisableFactoryReset");
		Class607.B630A78B.object_0[1276](button_6, Class607.B630A78B.object_0[174](474, 33));
		Class607.B630A78B.object_0[334](button_6, 3);
		Class607.B630A78B.object_0[1175](button_6, "Disable Factory Reset");
		Class607.B630A78B.object_0[494](button_6, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_6, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_6, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_6, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.F12E36AC)));
		Class607.B630A78B.object_0[1242](F51A3B03, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](F51A3B03, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](F51A3B03, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](F51A3B03, A282DB38.tool);
		Class607.B630A78B.object_0[997](F51A3B03, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](F51A3B03, Class607.B630A78B.object_0[760](7, 135));
		Class607.B630A78B.object_0[689](F51A3B03, "BtnARKAProtocol");
		Class607.B630A78B.object_0[1276](F51A3B03, Class607.B630A78B.object_0[174](474, 33));
		Class607.B630A78B.object_0[334](F51A3B03, 3);
		Class607.B630A78B.object_0[1175](F51A3B03, "ARKA Protocol Extreme ALL Android");
		Class607.B630A78B.object_0[494](F51A3B03, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](F51A3B03, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](F51A3B03, CA2A151B: true);
		Class607.B630A78B.object_0[1239](F51A3B03, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_7)));
		Class607.B630A78B.object_0[1242](C0AEB5A6, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](C0AEB5A6, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](C0AEB5A6, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](C0AEB5A6, A282DB38.electric_fire);
		Class607.B630A78B.object_0[997](C0AEB5A6, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](C0AEB5A6, Class607.B630A78B.object_0[760](7, 96));
		Class607.B630A78B.object_0[689](C0AEB5A6, "BtnSecurityPluginM2");
		Class607.B630A78B.object_0[1276](C0AEB5A6, Class607.B630A78B.object_0[174](474, 33));
		Class607.B630A78B.object_0[334](C0AEB5A6, 4);
		Class607.B630A78B.object_0[1175](C0AEB5A6, "Security Plugin MTK SPD  [Method2] ");
		Class607.B630A78B.object_0[494](C0AEB5A6, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](C0AEB5A6, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](C0AEB5A6, CA2A151B: true);
		Class607.B630A78B.object_0[1239](C0AEB5A6, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_22)));
		Class607.B630A78B.object_0[1242](E21AF888, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](E21AF888, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](E21AF888, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](E21AF888, A282DB38.electric_fire);
		Class607.B630A78B.object_0[997](E21AF888, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](E21AF888, Class607.B630A78B.object_0[760](7, 57));
		Class607.B630A78B.object_0[689](E21AF888, "BtnSecurityPluginSPD");
		Class607.B630A78B.object_0[1276](E21AF888, Class607.B630A78B.object_0[174](474, 33));
		Class607.B630A78B.object_0[334](E21AF888, 4);
		Class607.B630A78B.object_0[1175](E21AF888, "Security Plugin SPD [Method1]");
		Class607.B630A78B.object_0[494](E21AF888, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](E21AF888, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](E21AF888, CA2A151B: true);
		Class607.B630A78B.object_0[1239](E21AF888, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.E83FEFAC)));
		Class607.B630A78B.object_0[1242](D3ACDC32, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](D3ACDC32, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](D3ACDC32, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](D3ACDC32, A282DB38.electric_fire);
		Class607.B630A78B.object_0[997](D3ACDC32, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](D3ACDC32, Class607.B630A78B.object_0[760](7, 18));
		Class607.B630A78B.object_0[689](D3ACDC32, "SecurityPlugin");
		Class607.B630A78B.object_0[1276](D3ACDC32, Class607.B630A78B.object_0[174](474, 33));
		Class607.B630A78B.object_0[334](D3ACDC32, 4);
		Class607.B630A78B.object_0[1175](D3ACDC32, "Security Plugin MTK KING  [Method1]");
		Class607.B630A78B.object_0[494](D3ACDC32, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](D3ACDC32, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](D3ACDC32, CA2A151B: true);
		Class607.B630A78B.object_0[1239](D3ACDC32, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.F1A4B7AA)));
		Class607.B630A78B.object_0[1242](button_11, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_11, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_11, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_11, A282DB38.tool);
		Class607.B630A78B.object_0[997](button_11, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_11, Class607.B630A78B.object_0[760](7, 97));
		Class607.B630A78B.object_0[689](button_11, "btnbkiozco");
		Class607.B630A78B.object_0[1276](button_11, Class607.B630A78B.object_0[174](471, 33));
		Class607.B630A78B.object_0[334](button_11, 4);
		Class607.B630A78B.object_0[1175](button_11, "Bypass Kiosco MDM [ATT] ");
		Class607.B630A78B.object_0[494](button_11, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_11, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_11, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_11, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.EE07369A)));
		Class607.B630A78B.object_0[1242](D50C9613, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](D50C9613, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](D50C9613, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](D50C9613, A282DB38.tool);
		Class607.B630A78B.object_0[997](D50C9613, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](D50C9613, Class607.B630A78B.object_0[760](237, 19));
		Class607.B630A78B.object_0[689](D50C9613, "button1");
		Class607.B630A78B.object_0[1276](D50C9613, Class607.B630A78B.object_0[174](241, 33));
		Class607.B630A78B.object_0[334](D50C9613, 3);
		Class607.B630A78B.object_0[1175](D50C9613, "Block apps MDM With ARKA Protocol APP");
		Class607.B630A78B.object_0[494](D50C9613, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](D50C9613, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](D50C9613, CA2A151B: true);
		Class607.B630A78B.object_0[1239](D50C9613, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_7)));
		Class607.B630A78B.object_0[1242](button_3, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_3, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_3, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_3, A282DB38.pay);
		Class607.B630A78B.object_0[997](button_3, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_3, Class607.B630A78B.object_0[760](237, 58));
		Class607.B630A78B.object_0[689](button_3, "BtnMacroPay");
		Class607.B630A78B.object_0[1276](button_3, Class607.B630A78B.object_0[174](241, 33));
		Class607.B630A78B.object_0[334](button_3, 3);
		Class607.B630A78B.object_0[1175](button_3, "Bypass Payjoy King 2");
		Class607.B630A78B.object_0[494](button_3, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_3, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_3, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_3, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.********)));
		Class607.B630A78B.object_0[1242](CCA9F8A4, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](CCA9F8A4, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](CCA9F8A4, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](CCA9F8A4, A282DB38.pay);
		Class607.B630A78B.object_0[997](CCA9F8A4, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](CCA9F8A4, Class607.B630A78B.object_0[760](7, 58));
		Class607.B630A78B.object_0[689](CCA9F8A4, "BtnBypassWhiteScreen");
		Class607.B630A78B.object_0[1276](CCA9F8A4, Class607.B630A78B.object_0[174](207, 33));
		Class607.B630A78B.object_0[334](CCA9F8A4, 3);
		Class607.B630A78B.object_0[1175](CCA9F8A4, "Bypass Payjoy King 1 ");
		Class607.B630A78B.object_0[494](CCA9F8A4, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](CCA9F8A4, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](CCA9F8A4, CA2A151B: true);
		Class607.B630A78B.object_0[1239](CCA9F8A4, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_8)));
		Class607.B630A78B.object_0[1242](button_2, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_2, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_2, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_2, A282DB38.pincl);
		Class607.B630A78B.object_0[997](button_2, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_2, Class607.B630A78B.object_0[760](7, 19));
		Class607.B630A78B.object_0[689](button_2, "BtnBypassFRP");
		Class607.B630A78B.object_0[1276](button_2, Class607.B630A78B.object_0[174](207, 33));
		Class607.B630A78B.object_0[334](button_2, 3);
		Class607.B630A78B.object_0[1175](button_2, "Bypass Pin Claro y Telcel King 1");
		Class607.B630A78B.object_0[494](button_2, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_2, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_2, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_2, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_5)));
		Class607.B630A78B.object_0[1242](E815B5B4, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](E815B5B4, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](E815B5B4, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](E815B5B4, A282DB38.aurora);
		Class607.B630A78B.object_0[997](E815B5B4, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](E815B5B4, Class607.B630A78B.object_0[760](237, 153));
		Class607.B630A78B.object_0[689](E815B5B4, "btnas");
		Class607.B630A78B.object_0[1276](E815B5B4, Class607.B630A78B.object_0[174](241, 33));
		Class607.B630A78B.object_0[334](E815B5B4, 8);
		Class607.B630A78B.object_0[1175](E815B5B4, "IT Admin New Sec Relocks[AS]");
		Class607.B630A78B.object_0[494](E815B5B4, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](E815B5B4, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](E815B5B4, CA2A151B: true);
		Class607.B630A78B.object_0[1239](E815B5B4, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_26)));
		Class607.B630A78B.object_0[1242](button_12, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_12, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_12, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_12, A282DB38.playstore);
		Class607.B630A78B.object_0[997](button_12, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_12, Class607.B630A78B.object_0[760](7, 153));
		Class607.B630A78B.object_0[689](button_12, "btitoriginal");
		Class607.B630A78B.object_0[1276](button_12, Class607.B630A78B.object_0[174](207, 33));
		Class607.B630A78B.object_0[334](button_12, 7);
		Class607.B630A78B.object_0[1175](button_12, "IT Admin New Sec Relocks[OP]");
		Class607.B630A78B.object_0[494](button_12, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_12, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_12, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_12, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.A8A7BD13)));
		Class607.B630A78B.object_0[1242](B0019D3D, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](B0019D3D, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](B0019D3D, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](B0019D3D, A282DB38.itmin);
		Class607.B630A78B.object_0[997](B0019D3D, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](B0019D3D, Class607.B630A78B.object_0[760](237, 114));
		Class607.B630A78B.object_0[689](B0019D3D, "BtnITAdMinM4");
		Class607.B630A78B.object_0[1276](B0019D3D, Class607.B630A78B.object_0[174](241, 33));
		Class607.B630A78B.object_0[334](B0019D3D, 6);
		Class607.B630A78B.object_0[1175](B0019D3D, "IT ADMIN NEW SEC [Metod 2]");
		Class607.B630A78B.object_0[494](B0019D3D, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](B0019D3D, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](B0019D3D, CA2A151B: true);
		Class607.B630A78B.object_0[1239](B0019D3D, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.F813838F)));
		Class607.B630A78B.object_0[1242](button_8, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_8, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_8, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_8, A282DB38.itmin);
		Class607.B630A78B.object_0[997](button_8, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_8, Class607.B630A78B.object_0[760](7, 114));
		Class607.B630A78B.object_0[689](button_8, "BtnITAdMinM3");
		Class607.B630A78B.object_0[1276](button_8, Class607.B630A78B.object_0[174](207, 33));
		Class607.B630A78B.object_0[334](button_8, 5);
		Class607.B630A78B.object_0[1175](button_8, "IT ADMIN NEW SEC [Metod 1]");
		Class607.B630A78B.object_0[494](button_8, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_8, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_8, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_8, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_24)));
		Class607.B630A78B.object_0[1242](button_7, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_7, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_7, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_7, A282DB38.itmin);
		Class607.B630A78B.object_0[997](button_7, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_7, Class607.B630A78B.object_0[760](237, 75));
		Class607.B630A78B.object_0[689](button_7, "BtnITAdMinM2");
		Class607.B630A78B.object_0[1276](button_7, Class607.B630A78B.object_0[174](241, 33));
		Class607.B630A78B.object_0[334](button_7, 3);
		Class607.B630A78B.object_0[1175](button_7, "IT ADMIN [Method2]");
		Class607.B630A78B.object_0[494](button_7, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_7, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_7, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_7, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.CE84D489)));
		Class607.B630A78B.object_0[1242](button_1, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_1, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_1, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_1, A282DB38.itmin);
		Class607.B630A78B.object_0[997](button_1, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_1, Class607.B630A78B.object_0[760](7, 75));
		Class607.B630A78B.object_0[689](button_1, "BtnITAdMin");
		Class607.B630A78B.object_0[1276](button_1, Class607.B630A78B.object_0[174](207, 33));
		Class607.B630A78B.object_0[334](button_1, 3);
		Class607.B630A78B.object_0[1175](button_1, "IT ADMIN [Method 1]");
		Class607.B630A78B.object_0[494](button_1, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_1, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_1, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_1, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_23)));
		Class607.B630A78B.object_0[1242](button_0, AnchorStyles.Bottom | AnchorStyles.Right);
		Class607.B630A78B.object_0[823](button_0, Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[308](button_0, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[722](button_0, E91CBAA4: false);
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](button_0), Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[540](button_0, FlatStyle.Flat);
		Class607.B630A78B.object_0[326](button_0, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_0, A282DB38.banned);
		Class607.B630A78B.object_0[997](button_0, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_0, Class607.B630A78B.object_0[760](1066, 10));
		Class607.B630A78B.object_0[349](button_0, Class607.B630A78B.object_0[1237](2));
		Class607.B630A78B.object_0[689](button_0, "BtnStop");
		Class607.B630A78B.object_0[1276](button_0, Class607.B630A78B.object_0[174](65, 22));
		Class607.B630A78B.object_0[334](button_0, 27);
		Class607.B630A78B.object_0[1175](button_0, "STOP");
		Class607.B630A78B.object_0[494](button_0, ContentAlignment.MiddleRight);
		Class607.B630A78B.object_0[270](button_0, CA2A151B: false);
		Class607.B630A78B.object_0[781](tabPage_2).Add(groupBox_3);
		Class607.B630A78B.object_0[1250](tabPage_2, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](tabPage_2, "tpunlock");
		Class607.B630A78B.object_0[356](tabPage_2, Class607.B630A78B.object_0[1237](3));
		Class607.B630A78B.object_0[1276](tabPage_2, Class607.B630A78B.object_0[174](491, 397));
		Class607.B630A78B.object_0[295](tabPage_2, 4);
		Class607.B630A78B.object_0[1175](tabPage_2, "Unlock Sim");
		Class607.B630A78B.object_0[1031](tabPage_2, bool_0: true);
		Class607.B630A78B.object_0[823](groupBox_3, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](groupBox_3).Add(button_14);
		Class607.B630A78B.object_0[781](groupBox_3).Add(button_13);
		Class607.B630A78B.object_0[1278](groupBox_3, DockStyle.Top);
		Class607.B630A78B.object_0[388](groupBox_3, Class607.B630A78B.object_0[760](3, 3));
		Class607.B630A78B.object_0[689](groupBox_3, "GrpBxsim");
		Class607.B630A78B.object_0[1276](groupBox_3, Class607.B630A78B.object_0[174](485, 231));
		Class607.B630A78B.object_0[334](groupBox_3, 4);
		Class607.B630A78B.object_0[1183](groupBox_3, bool_0: false);
		Class607.B630A78B.object_0[1175](groupBox_3, "Unlock Network");
		Class607.B630A78B.object_0[1242](button_13, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_13, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_13, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_13, A282DB38.unlocked);
		Class607.B630A78B.object_0[997](button_13, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_13, Class607.B630A78B.object_0[760](6, 19));
		Class607.B630A78B.object_0[689](button_13, "btmtmb");
		Class607.B630A78B.object_0[1276](button_13, Class607.B630A78B.object_0[174](473, 33));
		Class607.B630A78B.object_0[334](button_13, 3);
		Class607.B630A78B.object_0[1175](button_13, "Unlock Device Unlock APP [Tmobile/MetroPCS]");
		Class607.B630A78B.object_0[494](button_13, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_13, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_13, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_13, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.method_27)));
		Class607.B630A78B.object_0[1242](button_14, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_14, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[326](button_14, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_14, A282DB38.unlocked);
		Class607.B630A78B.object_0[997](button_14, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_14, Class607.B630A78B.object_0[760](6, 58));
		Class607.B630A78B.object_0[689](button_14, "btnpixel");
		Class607.B630A78B.object_0[1276](button_14, Class607.B630A78B.object_0[174](473, 33));
		Class607.B630A78B.object_0[334](button_14, 4);
		Class607.B630A78B.object_0[1175](button_14, "Unlock Sim  [Only Pixel]");
		Class607.B630A78B.object_0[494](button_14, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_14, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_14, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_14, Class607.B630A78B.object_0[935](this, (nint)__ldftn(EAB5ABA9.E88D0909)));
		Class607.B630A78B.object_0[511](this, Class607.B630A78B.object_0[1223](6f, 13f));
		Class607.B630A78B.object_0[1043](this, AutoScaleMode.Font);
		Class607.B630A78B.object_0[782](this).Add(B21C68A4);
		Class607.B630A78B.object_0[782](this).Add(panel_0);
		Class607.B630A78B.object_0[782](this).Add(C93DE53A);
		Class607.B630A78B.object_0[782](this).Add(gcontrol2_0);
		Class607.B630A78B.object_0[690](this, "Service");
		Class607.B630A78B.object_0[1277](this, Class607.B630A78B.object_0[174](1148, 509));
		Class607.B630A78B.object_0[1149](DEB88E9E, EB25F899: false);
		Class607.B630A78B.object_0[63](DEB88E9E);
		Class607.B630A78B.object_0[1149](A8B012BA, EB25F899: false);
		Class607.B630A78B.object_0[1149](C93DE53A, EB25F899: false);
		Class607.B630A78B.object_0[1149](panel_0, EB25F899: false);
		Class607.B630A78B.object_0[1149](gcontrol1_0, EB25F899: false);
		Class607.B630A78B.object_0[1149](tabPage_1, EB25F899: false);
		Class607.B630A78B.object_0[1149](groupBox_2, EB25F899: false);
		Class607.B630A78B.object_0[1149](EA2B0C8B, EB25F899: false);
		Class607.B630A78B.object_0[1149](EA01D4AA, EB25F899: false);
		Class607.B630A78B.object_0[1149](BB16FC09, EB25F899: false);
		Class607.B630A78B.object_0[1149](groupBox_0, EB25F899: false);
		Class607.B630A78B.object_0[1149](tabPage_0, EB25F899: false);
		Class607.B630A78B.object_0[1149](D4873E04, EB25F899: false);
		Class607.B630A78B.object_0[1149](groupBox_1, EB25F899: false);
		Class607.B630A78B.object_0[63](groupBox_1);
		Class607.B630A78B.object_0[1149](B21C68A4, EB25F899: false);
		Class607.B630A78B.object_0[1040](pictureBox_0);
		Class607.B630A78B.object_0[1149](tabPage_2, EB25F899: false);
		Class607.B630A78B.object_0[1149](groupBox_3, EB25F899: false);
		Class607.B630A78B.object_0[1150](this, bool_0: false);
	}

	[CompilerGenerated]
	private void C415702E()
	{
		method_3();
	}

	[CompilerGenerated]
	private void method_29()
	{
		method_6();
	}

	[CompilerGenerated]
	private void method_30()
	{
		method_3();
	}

	[CompilerGenerated]
	private void DBA0E487()
	{
		C802158B("Adb Bypass Payjoy King 2");
	}

	[CompilerGenerated]
	private void D52D1EB0()
	{
		method_10();
	}

	[CompilerGenerated]
	private void D5A54B1A()
	{
		method_11();
	}

	[CompilerGenerated]
	private void method_31()
	{
		method_11("Security Plugin SPD");
	}

	[CompilerGenerated]
	private void E728409C()
	{
		F80AA9B5("Security Plugin SPD and MTK [Method2]");
	}

	[CompilerGenerated]
	private void method_32()
	{
		A88B459C();
	}

	[CompilerGenerated]
	private void method_33()
	{
		method_13();
	}

	[CompilerGenerated]
	private void method_34()
	{
		method_13("IT Admin [Method 2]");
	}

	[CompilerGenerated]
	private void E0A5B380()
	{
		AD0FB40F();
	}

	[CompilerGenerated]
	private void method_35()
	{
		method_12();
	}

	[CompilerGenerated]
	private void method_36()
	{
		method_14();
	}

	[CompilerGenerated]
	private void CB89F005()
	{
		method_4();
	}

	[CompilerGenerated]
	private void F20C443B()
	{
		method_16();
	}

	[CompilerGenerated]
	private void A0AFFB05()
	{
		method_17();
	}

	[CompilerGenerated]
	private void method_37()
	{
		A72FCF20();
	}

	[CompilerGenerated]
	private void BD81A8A7()
	{
		FE216929();
	}

	[CompilerGenerated]
	private void method_38()
	{
		method_18();
	}

	[CompilerGenerated]
	private void method_39()
	{
		method_19();
	}
}
