using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Text;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

public class GControl2 : Control, GInterface2
{
	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private int int_0;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private GEnum36 genum36_0;

	private GControl1 gcontrol1_0;

	private int int_1;

	private Point point_0;

	private readonly Class56 DF9DE2B3;

	private List<Rectangle> list_0;

	private const int int_2 = 24;

	private const int int_3 = 2;

	private Color color_0 = 2171169.smethod_0();

	private GEnum37 genum37_0;

	[Browsable(false)]
	public int Int32_0
	{
		[CompilerGenerated]
		get
		{
			return int_0;
		}
		[CompilerGenerated]
		set
		{
			int_0 = value;
		}
	}

	[Browsable(false)]
	public GClass106 DD8EF439 => GClass106.GClass106_0;

	[Browsable(false)]
	public GEnum36 C8B39594
	{
		[CompilerGenerated]
		get
		{
			return genum36_0;
		}
		[CompilerGenerated]
		set
		{
			genum36_0 = value;
		}
	}

	public unsafe GControl1 GControl1_0
	{
		get
		{
			return gcontrol1_0;
		}
		set
		{
			gcontrol1_0 = value;
			if (gcontrol1_0 != null)
			{
				int_1 = Class607.B630A78B.object_0[635](gcontrol1_0);
				Class607.B630A78B.object_0[401](gcontrol1_0, Class607.B630A78B.object_0[375](this, (nint)__ldftn(GControl2.D429000F)));
				Class607.B630A78B.object_0[1246](gcontrol1_0, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GControl2.method_1)));
				Class607.B630A78B.object_0[918](gcontrol1_0, Class607.B630A78B.object_0[407](this, (nint)__ldftn(GControl2.method_2)));
				Class607.B630A78B.object_0[392](gcontrol1_0, Class607.B630A78B.object_0[407](this, (nint)__ldftn(GControl2.DB326AAA)));
			}
		}
	}

	public GEnum37 E4A0A31D
	{
		get
		{
			return genum37_0;
		}
		set
		{
			if (value == GEnum37.EAB24C33)
			{
				color_0 = 2171169.smethod_0();
			}
			else
			{
				color_0 = 2171169.smethod_0();
			}
			Class607.B630A78B.object_0[583](this);
			genum37_0 = value;
		}
	}

	public GControl2()
	{
		Class607.B630A78B.object_0[453](this);
		Class607.B630A78B.object_0[824](this, ControlStyles.DoubleBuffer | ControlStyles.OptimizedDoubleBuffer, A5347B95: true);
		Class607.B630A78B.object_0[975](this, 48);
		DF9DE2B3 = new Class56
		{
			F8AB7C94_0 = F8AB7C94.EFA5971F,
			D31D243E = 0.04
		};
		DF9DE2B3.AE044886 += delegate
		{
			Class607.B630A78B.object_0[583](this);
		};
	}

	protected override void OnPaint(PaintEventArgs pe)
	{
		Graphics graphics = Class607.B630A78B.object_0[340](pe);
		Class607.B630A78B.object_0[423](graphics, TextRenderingHint.AntiAlias);
		Class607.B630A78B.object_0[1141](graphics, color_0);
		if (gcontrol1_0 == null)
		{
			return;
		}
		if (!DF9DE2B3.method_1() || list_0 == null || list_0.Count != Class607.B630A78B.object_0[1109](gcontrol1_0))
		{
			EA856DA5();
		}
		double num = DF9DE2B3.D6187CAA();
		if (DF9DE2B3.method_1())
		{
			SolidBrush solidBrush = Class607.B630A78B.object_0[1044](Class607.B630A78B.object_0[117]((int)(51.0 - num * 50.0), Class607.B630A78B.object_0[639]()));
			Rectangle AC0D2B9C = list_0[Class607.B630A78B.object_0[635](gcontrol1_0)];
			int num2 = (int)(num * (double)Class607.B630A78B.object_0[56](ref AC0D2B9C) * 1.75);
			Class607.B630A78B.object_0[180](graphics, list_0[Class607.B630A78B.object_0[635](gcontrol1_0)]);
			Class607.B630A78B.object_0[333](graphics, solidBrush, Class607.B630A78B.object_0[1023](Class607.B630A78B.object_0[372](ref point_0) - num2 / 2, Class607.B630A78B.object_0[1229](ref point_0) - num2 / 2, num2, num2));
			Class607.B630A78B.object_0[1057](graphics);
			Class607.B630A78B.object_0[529](solidBrush);
		}
		IEnumerator object_ = Class607.B630A78B.object_0[1227](gcontrol1_0).GetEnumerator();
		try
		{
			while (Class607.B630A78B.object_0[212](object_))
			{
				TabPage tabPage = (TabPage)Class607.B630A78B.object_0[107](object_);
				int num3 = Class607.B630A78B.object_0[1227](gcontrol1_0).IndexOf(tabPage);
				Brush brush = Class607.B630A78B.object_0[1044](Class607.B630A78B.object_0[117](method_0(num3, num), DD8EF439.CEBAB4AB.color_4));
				string string_ = Class607.B630A78B.object_0[1019](Class607.B630A78B.object_0[814](tabPage));
				Font b39C48BD = DD8EF439.B39C48BD;
				RectangleF rectangleF_ = Class607.B630A78B.object_0[1188](list_0[num3]);
				StringFormat stringFormat = Class607.B630A78B.object_0[960]();
				Class245.C01A2A1E(stringFormat, StringAlignment.Center);
				Class859.smethod_0(stringFormat, StringAlignment.Center);
				D9985907.DE8FE29E(graphics, string_, b39C48BD, brush, rectangleF_, stringFormat);
				Class607.B630A78B.object_0[529](brush);
			}
		}
		finally
		{
			IDisposable disposable = object_ as IDisposable;
			if (disposable != null)
			{
				disposable.Dispose();
			}
		}
		int index = ((int_1 == -1) ? Class607.B630A78B.object_0[635](gcontrol1_0) : int_1);
		Rectangle rectangle_ = list_0[index];
		Rectangle CE8388A = list_0[Class607.B630A78B.object_0[635](gcontrol1_0)];
		int num4 = Class607.B630A78B.object_0[267](ref CE8388A) - 2;
		int num5 = Class607.B630A78B.object_0[801](ref rectangle_) + (int)((double)(Class607.B630A78B.object_0[801](ref CE8388A) - Class607.B630A78B.object_0[801](ref rectangle_)) * num);
		int num6 = Class607.B630A78B.object_0[56](ref rectangle_) + (int)((double)(Class607.B630A78B.object_0[56](ref CE8388A) - Class607.B630A78B.object_0[56](ref rectangle_)) * num);
		Class607.B630A78B.object_0[592](graphics, DD8EF439.CEBAB4AB.brush_2, num5, num4, num6, 2);
	}

	private int method_0(int int_4, double AF099D19)
	{
		Color color_ = DD8EF439.color_16;
		int num = Class607.B630A78B.object_0[430](ref color_);
		color_ = DD8EF439.color_17;
		int num2 = Class607.B630A78B.object_0[430](ref color_);
		if (int_4 == Class607.B630A78B.object_0[635](gcontrol1_0) && !DF9DE2B3.method_1())
		{
			return num;
		}
		if (int_4 != int_1 && int_4 != Class607.B630A78B.object_0[635](gcontrol1_0))
		{
			return num2;
		}
		if (int_4 == int_1)
		{
			return num - (int)((double)(num - num2) * AF099D19);
		}
		return num2 + (int)((double)(num - num2) * AF099D19);
	}

	protected override void OnMouseUp(MouseEventArgs mevent)
	{
		Class607.B630A78B.object_0[872](this, mevent);
		if (list_0 == null)
		{
			EA856DA5();
		}
		for (int i = 0; i < list_0.Count; i++)
		{
			Rectangle C8BB14A = list_0[i];
			if (Class607.B630A78B.object_0[9](ref C8BB14A, Class607.B630A78B.object_0[613](mevent)))
			{
				Class607.B630A78B.object_0[734](gcontrol1_0, i);
			}
		}
		point_0 = Class607.B630A78B.object_0[613](mevent);
	}

	private void EA856DA5()
	{
		list_0 = new List<Rectangle>();
		if (gcontrol1_0 == null || Class607.B630A78B.object_0[1109](gcontrol1_0) == 0)
		{
			return;
		}
		using Bitmap f = Class607.B630A78B.object_0[74](1, 1);
		using Graphics object_ = Class607.B630A78B.object_0[1082](f);
		List<Rectangle> list = list_0;
		object obj = Class607.B630A78B.object_0[1023];
		int num = DD8EF439.int_0;
		SizeF sizeF_ = Class607.B630A78B.object_0[1203](object_, Class607.B630A78B.object_0[814](Class607.B630A78B.object_0[1227](gcontrol1_0)[0]), DD8EF439.B39C48BD);
		list.Add(obj(num, 0, 48 + (int)Class607.B630A78B.object_0[331](ref sizeF_), Class607.B630A78B.object_0[500](this)));
		for (int i = 1; i < Class607.B630A78B.object_0[1227](gcontrol1_0).Count; i++)
		{
			List<Rectangle> list2 = list_0;
			Rectangle rectangle_ = list_0[i - 1];
			object obj2 = Class607.B630A78B.object_0[1023];
			int num2 = Class607.B630A78B.object_0[865](ref rectangle_);
			sizeF_ = Class607.B630A78B.object_0[1203](object_, Class607.B630A78B.object_0[814](Class607.B630A78B.object_0[1227](gcontrol1_0)[i]), DD8EF439.B39C48BD);
			list2.Add(obj2(num2, 0, 48 + (int)Class607.B630A78B.object_0[331](ref sizeF_), Class607.B630A78B.object_0[500](this)));
		}
	}

	[CompilerGenerated]
	private void D429000F(object sender, TabControlEventArgs e)
	{
		int_1 = Class607.B630A78B.object_0[635](gcontrol1_0);
	}

	[CompilerGenerated]
	private void method_1(object sender, EventArgs e)
	{
		DF9DE2B3.method_11(0.0);
		DF9DE2B3.method_2(Enum14.const_0);
	}

	[CompilerGenerated]
	private void method_2(object sender, ControlEventArgs e)
	{
		Class607.B630A78B.object_0[583](this);
	}

	[CompilerGenerated]
	private void DB326AAA(object sender, ControlEventArgs e)
	{
		Class607.B630A78B.object_0[583](this);
	}

	[CompilerGenerated]
	private void method_3(object object_0)
	{
		Class607.B630A78B.object_0[583](this);
	}
}
