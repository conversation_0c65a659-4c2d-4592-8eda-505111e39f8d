using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Threading;

public class GClass41
{
	public struct GStruct48
	{
		public string string_0;

		public byte[] byte_0;

		public string string_1;
	}

	[CompilerGenerated]
	private sealed class Class11
	{
		public string string_0;

		public Class11()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool method_0(GClass37.B6BA8237 b6BA8237_0)
		{
			return Class607.B630A78B.object_0[787](b6BA8237_0.string_2, string_0);
		}
	}

	[CompilerGenerated]
	private sealed class Class12
	{
		public string string_0;

		public Class12()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool E5B1381E(GClass37.B6BA8237 D031C12A)
		{
			return Class607.B630A78B.object_0[787](D031C12A.string_2, string_0);
		}
	}

	[CompilerGenerated]
	private sealed class Class13
	{
		public string C3B69282;

		public Class13()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool ECA69104(GClass37.B6BA8237 b6BA8237_0)
		{
			return Class607.B630A78B.object_0[787](b6BA8237_0.string_2, C3B69282);
		}
	}

	public F7020D24 f7020D24_0;

	public E5964FB0 e5964FB0_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private GClass112.E39CAE0B BD205B85;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private GDelegate25 A59D3707;

	private string[] string_0;

	public event GClass112.E39CAE0B B581541E
	{
		[CompilerGenerated]
		add
		{
			GClass112.E39CAE0B e39CAE0B = BD205B85;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[752](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref BD205B85, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
		[CompilerGenerated]
		remove
		{
			GClass112.E39CAE0B e39CAE0B = BD205B85;
			GClass112.E39CAE0B e39CAE0B2;
			do
			{
				e39CAE0B2 = e39CAE0B;
				GClass112.E39CAE0B value2 = (GClass112.E39CAE0B)Class607.B630A78B.object_0[629](e39CAE0B2, value);
				e39CAE0B = Interlocked.CompareExchange(ref BD205B85, value2, e39CAE0B2);
			}
			while ((object)e39CAE0B != e39CAE0B2);
		}
	}

	public event GDelegate25 Event_0
	{
		[CompilerGenerated]
		add
		{
			GDelegate25 gDelegate = A59D3707;
			GDelegate25 gDelegate2;
			do
			{
				gDelegate2 = gDelegate;
				GDelegate25 value2 = (GDelegate25)Class607.B630A78B.object_0[752](gDelegate2, value);
				gDelegate = Interlocked.CompareExchange(ref A59D3707, value2, gDelegate2);
			}
			while ((object)gDelegate != gDelegate2);
		}
		[CompilerGenerated]
		remove
		{
			GDelegate25 gDelegate = A59D3707;
			GDelegate25 gDelegate2;
			do
			{
				gDelegate2 = gDelegate;
				GDelegate25 value2 = (GDelegate25)Class607.B630A78B.object_0[629](gDelegate2, value);
				gDelegate = Interlocked.CompareExchange(ref A59D3707, value2, gDelegate2);
			}
			while ((object)gDelegate != gDelegate2);
		}
	}

	public GClass41()
	{
		Class607.B630A78B.object_0[571](this);
		f7020D24_0 = new F7020D24();
		e5964FB0_0 = new E5964FB0(f7020D24_0, this);
		e5964FB0_0.C723BD08 += A023A195;
		e5964FB0_0.Event_0 += method_0;
		string_0 = new string[1] { "MT6853" };
	}

	private void method_0(ulong ulong_0, ulong ulong_1)
	{
		A59D3707?.Invoke(ulong_0, ulong_1);
	}

	private void A023A195(string string_1, EF1F389C ef1F389C_0 = EF1F389C.Word, bool bool_0 = true, bool bool_1 = false)
	{
		BD205B85?.Invoke(string_1, ef1F389C_0);
	}

	public C0031B8C EEB85E0D()
	{
		return (C0031B8C)new GClass128().method_68(new object[1] { this }, 352717);
	}

	public bool method_1(string string_1, string string_2, string E787DA39 = "")
	{
		Class11 CS_0024_003C_003E8__locals2 = new Class11();
		CS_0024_003C_003E8__locals2.string_0 = string_1;
		if (EEB85E0D().E8A07FA2)
		{
			Tuple<byte[], List<GClass37.B6BA8237>> tuple = null;
			if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.const_1)
			{
				Tuple<byte[], GClass37> tuple2 = e5964FB0_0.gclass51_0.gclass59_0.D60E1983(new D905A51F(0u, 0u, 0u));
				tuple = Tuple.Create(tuple2.Item1, tuple2.Item2.list_0);
			}
			else if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.const_0)
			{
				Tuple<byte[], GClass37> tuple3 = e5964FB0_0.A335BB39.gclass59_0.D60E1983(new D905A51F(0u, 0u, 0u));
				tuple = Tuple.Create(tuple3.Item1, tuple3.Item2.list_0);
			}
			else if (e5964FB0_0.A18A06B0 == GClass34.AB881C92.D03BC921)
			{
				Tuple<byte[], GClass37> tuple4 = e5964FB0_0.gclass8_0.gclass59_0.D60E1983(new D905A51F(0u, 0u, 0u));
				tuple = Tuple.Create(tuple4.Item1, tuple4.Item2.list_0);
			}
			if (tuple != null && tuple.Item2.Count > 0)
			{
				_ = tuple.Item1;
				List<GClass37.B6BA8237> item = tuple.Item2;
				GClass37.B6BA8237 b6BA8237_ = item.Find((GClass37.B6BA8237 b6BA) => Class607.B630A78B.object_0[787](b6BA.string_2, CS_0024_003C_003E8__locals2.string_0));
				if (!Class607.B630A78B.object_0[1205](b6BA8237_.string_2))
				{
					return e5964FB0_0.method_44(string_2, b6BA8237_, E787DA39);
				}
			}
			return false;
		}
		return false;
	}

	public bool method_2(bool D198C504)
	{
		try
		{
			if (EEB85E0D().E8A07FA2)
			{
				return e5964FB0_0.method_50(D198C504);
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public byte[] method_3()
	{
		try
		{
			E5964FB0.GStruct45 gStruct = e5964FB0_0.method_30(bool_1: true);
			if (gStruct.C220E421)
			{
				if (!gStruct.bool_0)
				{
					e5964FB0_0.method_54();
				}
				BD205B85?.Invoke("Reading Preloader from device :");
				E5964FB0.BB0CB201 bB0CB = e5964FB0_0.method_17();
				if (bB0CB.byte_0 != null && bB0CB.byte_0.Length != 0)
				{
					BD205B85?.Invoke("Ok", EF1F389C.Success);
					return bB0CB.byte_0;
				}
				BD205B85?.Invoke("Failed", EF1F389C.Error);
			}
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
		return null;
	}

	public bool C32A4DBB(string FE0F96A7)
	{
		try
		{
			if (EEB85E0D().E8A07FA2)
			{
				bool result;
				if (result = e5964FB0_0.method_38(FE0F96A7))
				{
					BD205B85?.Invoke("Ok", EF1F389C.Success);
					return result;
				}
				BD205B85?.Invoke("Failed", EF1F389C.Error);
			}
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
		return false;
	}

	public bool method_4()
	{
		return e5964FB0_0.method_39();
	}

	public bool method_5(ulong ulong_0, ulong A235A703)
	{
		try
		{
			if (EEB85E0D().E8A07FA2)
			{
				if (e5964FB0_0.method_48(ulong_0, A235A703))
				{
					BD205B85?.Invoke("Ok", EF1F389C.Success);
					return true;
				}
				BD205B85?.Invoke("Failed", EF1F389C.Error);
				return false;
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public bool A71B3290()
	{
		try
		{
			if (EEB85E0D().E8A07FA2)
			{
				Tuple<byte[], GClass37> tuple = e5964FB0_0.method_37();
				if (tuple != null)
				{
					BD205B85?.Invoke("Erasing All Partition : ");
					if (e5964FB0_0.method_48(0uL, tuple.Item2.ABAFC78B))
					{
						BD205B85?.Invoke("Ok", EF1F389C.Success);
						return true;
					}
					BD205B85?.Invoke("Failed", EF1F389C.Error);
					return false;
				}
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public bool BA909C9D(string[] string_1, string EE868FB9 = "")
	{
		try
		{
			if (EEB85E0D().E8A07FA2)
			{
				Tuple<byte[], GClass37> tuple = e5964FB0_0.method_37();
				if (tuple != null)
				{
					if (!Class607.B630A78B.object_0[1205](EE868FB9))
					{
						BD205B85?.Invoke("Erasing ");
						BD205B85?.Invoke(EE868FB9, EF1F389C.Success);
						BD205B85?.Invoke(" sector : ", EF1F389C.Success);
					}
					int num = 0;
					while (true)
					{
						if (num < string_1.Length)
						{
							Class12 CS_0024_003C_003E8__locals2 = new Class12();
							CS_0024_003C_003E8__locals2.string_0 = string_1[num];
							GClass37.B6BA8237 b6BA = tuple.Item2.list_0.Find((GClass37.B6BA8237 D031C12A) => Class607.B630A78B.object_0[787](D031C12A.string_2, CS_0024_003C_003E8__locals2.string_0));
							if (!Class607.B630A78B.object_0[1205](b6BA.string_2))
							{
								if (!e5964FB0_0.method_48(b6BA.D78F1C10 * e5964FB0_0.gclass51_0.AA329715.uint_0, b6BA.ulong_1 * e5964FB0_0.gclass51_0.AA329715.uint_0, "", Class607.B630A78B.object_0[1205](EE868FB9) ? true : false))
								{
									break;
								}
								BD205B85?.Invoke("Ok", EF1F389C.Success);
							}
							num++;
							continue;
						}
						return true;
					}
					BD205B85?.Invoke("Failed", EF1F389C.Error);
					return false;
				}
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public bool method_6(List<GStruct48> list_0, string DC99602C)
	{
		return (bool)new GClass128().method_323(new object[3] { this, list_0, DC99602C }, 383584);
	}

	public Tuple<bool, string> method_7()
	{
		try
		{
			if (EEB85E0D().E8A07FA2)
			{
				return e5964FB0_0.DB83CF95();
			}
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
		return Tuple.Create(item1: false, "");
	}

	public bool CE100829(string string_1)
	{
		try
		{
			if (EEB85E0D().E8A07FA2 && e5964FB0_0.A18DD08F(string_1))
			{
				return true;
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public bool A521708C()
	{
		try
		{
			if (e5964FB0_0.method_31())
			{
				if (!e5964FB0_0.D3A2FF25.bool_1 && !e5964FB0_0.D3A2FF25.DCB55E39)
				{
					BD205B85?.Invoke("Your device sla\\daa isnot enabled , so no need crash preloader , skipped.");
					return true;
				}
				if (e5964FB0_0.genum23_0 == E5964FB0.GEnum23.const_0)
				{
					BD205B85?.Invoke("Your device detected in brom service, no need to crash preloader, skipped");
					return true;
				}
				if (e5964FB0_0.method_33())
				{
					return true;
				}
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public bool CA11CA16()
	{
		try
		{
			if (EEB85E0D().E8A07FA2 && e5964FB0_0.method_40())
			{
				return true;
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public bool method_8(int D5352CAA, int int_0)
	{
		try
		{
			if (EEB85E0D().E8A07FA2 && e5964FB0_0.method_41(D5352CAA, int_0))
			{
				return true;
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public List<GClass37.B6BA8237> method_9(string[] E50CA9A3, string A3314638, string string_1 = "")
	{
		try
		{
			GClass112.E209C304(A3314638);
			if (EEB85E0D().E8A07FA2)
			{
				return e5964FB0_0.method_35(E50CA9A3, A3314638, string_1);
			}
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
		return null;
	}

	public bool method_10(string string_1)
	{
		try
		{
			if (EEB85E0D().E8A07FA2 && e5964FB0_0.F503EE35(string_1))
			{
				return true;
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public bool DE101236(byte[] byte_0)
	{
		try
		{
			if (EEB85E0D().E8A07FA2 && e5964FB0_0.method_42(byte_0))
			{
				return true;
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public bool method_11(string string_1)
	{
		try
		{
			if (EEB85E0D().E8A07FA2 && e5964FB0_0.method_45(string_1, ""))
			{
				return true;
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public bool method_12(string string_1)
	{
		try
		{
			if (EEB85E0D().E8A07FA2 && e5964FB0_0.method_49(string_1, ""))
			{
				return true;
			}
			return false;
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
	}

	public bool method_13(byte[] byte_0)
	{
		try
		{
			if (EEB85E0D().E8A07FA2)
			{
				return e5964FB0_0.AFA62933(byte_0);
			}
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
		return false;
	}

	public Tuple<byte[], GClass37> CF0C6B1F()
	{
		try
		{
			if (EEB85E0D().E8A07FA2)
			{
				return e5964FB0_0.method_37();
			}
		}
		finally
		{
			e5964FB0_0.f7020D24_0.DC1596BA();
		}
		return null;
	}
}
