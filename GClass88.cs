using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Threading;

public class GClass88 : INotifyPropertyChanged
{
	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private ulong E8398CA7;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private int int_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private ulong ulong_0;

	public string SECTOR_SIZE_IN_BYTES;

	private bool _isenable;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private PropertyChangedEventHandler propertyChangedEventHandler_0;

	public string name
	{
		[CompilerGenerated]
		get
		{
			return string_0;
		}
		[CompilerGenerated]
		set
		{
			string_0 = value;
		}
	}

	public ulong Sector
	{
		[CompilerGenerated]
		get
		{
			return E8398CA7;
		}
		[CompilerGenerated]
		set
		{
			E8398CA7 = value;
		}
	}

	public int lun
	{
		[CompilerGenerated]
		get
		{
			return int_0;
		}
		[CompilerGenerated]
		set
		{
			int_0 = value;
		}
	}

	public ulong Sectors
	{
		[CompilerGenerated]
		get
		{
			return ulong_0;
		}
		[CompilerGenerated]
		set
		{
			ulong_0 = value;
		}
	}

	public bool Enable
	{
		get
		{
			return _isenable;
		}
		set
		{
			_isenable = value;
			NotifyPropertyChanged("isChecked");
		}
	}

	public event PropertyChangedEventHandler PropertyChanged
	{
		[CompilerGenerated]
		add
		{
			PropertyChangedEventHandler propertyChangedEventHandler = propertyChangedEventHandler_0;
			PropertyChangedEventHandler propertyChangedEventHandler2;
			do
			{
				propertyChangedEventHandler2 = propertyChangedEventHandler;
				PropertyChangedEventHandler value2 = (PropertyChangedEventHandler)Class607.B630A78B.object_0[752](propertyChangedEventHandler2, value);
				propertyChangedEventHandler = Interlocked.CompareExchange(ref propertyChangedEventHandler_0, value2, propertyChangedEventHandler2);
			}
			while ((object)propertyChangedEventHandler != propertyChangedEventHandler2);
		}
		[CompilerGenerated]
		remove
		{
			PropertyChangedEventHandler propertyChangedEventHandler = propertyChangedEventHandler_0;
			PropertyChangedEventHandler propertyChangedEventHandler2;
			do
			{
				propertyChangedEventHandler2 = propertyChangedEventHandler;
				PropertyChangedEventHandler value2 = (PropertyChangedEventHandler)Class607.B630A78B.object_0[629](propertyChangedEventHandler2, value);
				propertyChangedEventHandler = Interlocked.CompareExchange(ref propertyChangedEventHandler_0, value2, propertyChangedEventHandler2);
			}
			while ((object)propertyChangedEventHandler != propertyChangedEventHandler2);
		}
	}

	private void NotifyPropertyChanged(string info)
	{
		PropertyChangedEventHandler propertyChangedEventHandler = propertyChangedEventHandler_0;
		if (propertyChangedEventHandler != null)
		{
			Class492.smethod_0(propertyChangedEventHandler, this, Class607.B630A78B.object_0[1027](info));
		}
	}

	public GClass88()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
