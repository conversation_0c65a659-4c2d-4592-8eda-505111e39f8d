using System;
using System.Runtime.ExceptionServices;
using System.Runtime.InteropServices;
using System.Security;

public class GClass30
{
	[StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
	public struct D535F108
	{
		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
		public string string_0;

		public uint DCB84537;

		public IntPtr FB2611A8;

		public uint A89A3F10;

		public uint uint_0;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 16)]
		public string B809EB8E;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
		public string FA9D5331;

		private B832F2A9 b832F2A9_0;
	}

	public enum B832F2A9
	{
		B79A383E,
		D89B56BB
	}

	public static string C0B02E2F = Class607.B630A78B.object_0[720](GClass112.********, "\\bin\\DA\\universal\\MTK_AllInOne_DA_5.2228.bin");

	public static IntPtr DC0BCB0A;

	public static string[] String_0
	{
		get
		{
			string string_ = Class607.B630A78B.object_0[720](GClass112.********, "\\bin\\DA\\universal");
			return Class607.B630A78B.object_0[536](string_);
		}
	}

	public GClass30()
	{
		Class607.B630A78B.object_0[571](this);
		smethod_0();
	}

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	private static extern int DA_IsReady(IntPtr intptr_0, ref D535F108 d535F108_0, bool A027749B);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	private static extern int DA_Create(ref IntPtr intptr_0);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	private static extern int DA_Unload(IntPtr intptr_0);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	private static extern int DA_Destroy(ref IntPtr BDAF3815);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, EntryPoint = "DA_IsReady", SetLastError = true)]
	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	private static extern int DA_IsReady_1(IntPtr C3B16390, IntPtr intptr_0, bool bool_0);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	private static extern int DA_Load(IntPtr intptr_0, byte[] byte_0, bool bool_0, bool F73F3291);

	[DllImport("FlashToolLib.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, SetLastError = true)]
	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	private static extern int DA_GetInfo(IntPtr intptr_0, ref D535F108 D89F691F);

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static void smethod_0()
	{
		int num = -1;
		num = DA_Create(ref DC0BCB0A);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
	}

	[HandleProcessCorruptedStateExceptions]
	[SecurityCritical]
	public static void AA803AAE()
	{
		int num = -1;
		if (!Class607.B630A78B.object_0[1182](DC0BCB0A, Class607.B630A78B.object_0[945](0)))
		{
			num = DA_Unload(DC0BCB0A);
			if (num != 0)
			{
				string f097F68F = GClass29.smethod_0(num);
				f097F68F = GClass29.smethod_1(f097F68F, num);
			}
			num = DA_Destroy(ref DC0BCB0A);
			if (num != 0)
			{
				string f097F68F2 = GClass29.smethod_0(num);
				f097F68F2 = GClass29.smethod_1(f097F68F2, num);
			}
		}
	}

	[SecurityCritical]
	[HandleProcessCorruptedStateExceptions]
	public static bool DD38AEA2()
	{
		D535F108 d535F108_ = default(D535F108);
		int num = DA_IsReady(DC0BCB0A, ref d535F108_, A027749B: true);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
			return false;
		}
		return true;
	}

	[SecurityCritical]
	[HandleProcessCorruptedStateExceptions]
	public static bool smethod_1(string FB0594B4)
	{
		int num = -1;
		num = DA_Unload(DC0BCB0A);
		if (num != 0)
		{
			string f097F68F = GClass29.smethod_0(num);
			f097F68F = GClass29.smethod_1(f097F68F, num);
		}
		num = DA_Load(DC0BCB0A, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), FB0594B4), bool_0: false, F73F3291: false);
		if (num != 0)
		{
			string f097F68F2 = GClass29.smethod_0(num);
			f097F68F2 = GClass29.smethod_1(f097F68F2, num);
			return true;
		}
		return false;
	}

	[SecurityCritical]
	[HandleProcessCorruptedStateExceptions]
	public static D535F108 smethod_2()
	{
		D535F108 D89F691F = default(D535F108);
		DA_GetInfo(DC0BCB0A, ref D89F691F);
		return D89F691F;
	}
}
