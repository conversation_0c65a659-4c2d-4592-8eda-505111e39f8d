using System.Diagnostics;
using System.Runtime.CompilerServices;

public class GClass83
{
	public class CA374214
	{
		public static bool bool_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static bool bool_1;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static bool bool_2;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static bool bool_3;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static bool D80C3F82;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static bool bool_4;

		public static bool Boolean_0
		{
			[CompilerGenerated]
			get
			{
				return bool_1;
			}
			[CompilerGenerated]
			set
			{
				bool_1 = value;
			}
		}

		public static bool CEBAB6BE
		{
			[CompilerGenerated]
			get
			{
				return bool_2;
			}
			[CompilerGenerated]
			set
			{
				bool_2 = value;
			}
		}

		public static bool BF369816
		{
			[CompilerGenerated]
			get
			{
				return bool_3;
			}
			[CompilerGenerated]
			set
			{
				bool_3 = value;
			}
		}

		public static bool Boolean_1
		{
			[CompilerGenerated]
			get
			{
				return D80C3F82;
			}
			[CompilerGenerated]
			set
			{
				D80C3F82 = value;
			}
		}

		public static bool Boolean_2
		{
			[CompilerGenerated]
			get
			{
				return bool_4;
			}
			[CompilerGenerated]
			set
			{
				bool_4 = value;
			}
		}

		public static void smethod_0(bool bool_5)
		{
			Boolean_0 = bool_5;
			CEBAB6BE = bool_5;
			BF369816 = bool_5;
			Boolean_1 = bool_5;
			Boolean_2 = bool_5;
		}

		public CA374214()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class EC848CA4
	{
		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string string_0;

		public static string String_0
		{
			[CompilerGenerated]
			get
			{
				return string_0;
			}
			[CompilerGenerated]
			set
			{
				string_0 = value;
			}
		}

		public EC848CA4()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass84
	{
		public static string CB811536;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string string_0;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private static string EB33F982;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private static string string_1;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string BF0F4006;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private static string string_2;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string AB01FE20;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string F03F0B33;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private static string D5878AAE;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string string_3;

		public static string String_0
		{
			[CompilerGenerated]
			get
			{
				return string_0;
			}
			[CompilerGenerated]
			set
			{
				string_0 = value;
			}
		}

		public static string String_1
		{
			[CompilerGenerated]
			get
			{
				return EB33F982;
			}
			[CompilerGenerated]
			set
			{
				EB33F982 = value;
			}
		}

		public static string FA05C11D
		{
			[CompilerGenerated]
			get
			{
				return string_1;
			}
			[CompilerGenerated]
			set
			{
				string_1 = value;
			}
		}

		public static string String_2
		{
			[CompilerGenerated]
			get
			{
				return BF0F4006;
			}
			[CompilerGenerated]
			set
			{
				BF0F4006 = value;
			}
		}

		public static string F51EF492
		{
			[CompilerGenerated]
			get
			{
				return string_2;
			}
			[CompilerGenerated]
			set
			{
				string_2 = value;
			}
		}

		public static string String_3
		{
			[CompilerGenerated]
			get
			{
				return AB01FE20;
			}
			[CompilerGenerated]
			set
			{
				AB01FE20 = value;
			}
		}

		public static string FD0AE59A
		{
			[CompilerGenerated]
			get
			{
				return F03F0B33;
			}
			[CompilerGenerated]
			set
			{
				F03F0B33 = value;
			}
		}

		public static string String_4
		{
			[CompilerGenerated]
			get
			{
				return D5878AAE;
			}
			[CompilerGenerated]
			set
			{
				D5878AAE = value;
			}
		}

		public static string F62FBF30
		{
			[CompilerGenerated]
			get
			{
				return string_3;
			}
			[CompilerGenerated]
			set
			{
				string_3 = value;
			}
		}

		public GClass84()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass85
	{
		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string F915342D;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private static string string_0;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private static string D7321C99;

		public static string EB02AD87
		{
			[CompilerGenerated]
			get
			{
				return F915342D;
			}
			[CompilerGenerated]
			set
			{
				F915342D = value;
			}
		}

		public static string String_0
		{
			[CompilerGenerated]
			get
			{
				return string_0;
			}
			[CompilerGenerated]
			set
			{
				string_0 = value;
			}
		}

		public static string String_1
		{
			[CompilerGenerated]
			get
			{
				return D7321C99;
			}
			[CompilerGenerated]
			set
			{
				D7321C99 = value;
			}
		}

		public GClass85()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass86
	{
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private static string string_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string string_1;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string string_2;

		public static string String_0
		{
			[CompilerGenerated]
			get
			{
				return string_0;
			}
			[CompilerGenerated]
			set
			{
				string_0 = value;
			}
		}

		public static string FEB36A36
		{
			[CompilerGenerated]
			get
			{
				return string_1;
			}
			[CompilerGenerated]
			set
			{
				string_1 = value;
			}
		}

		public static string String_1
		{
			[CompilerGenerated]
			get
			{
				return string_2;
			}
			[CompilerGenerated]
			set
			{
				string_2 = value;
			}
		}

		public GClass86()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class D2004EA8
	{
		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string F6B5501F;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string string_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string string_1;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string string_2;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string string_3;

		public static string CDBBD9B9
		{
			[CompilerGenerated]
			get
			{
				return F6B5501F;
			}
			[CompilerGenerated]
			set
			{
				F6B5501F = value;
			}
		}

		public static string BF3CA408
		{
			[CompilerGenerated]
			get
			{
				return string_0;
			}
			[CompilerGenerated]
			set
			{
				string_0 = value;
			}
		}

		public static string E2A66CA7
		{
			[CompilerGenerated]
			get
			{
				return string_1;
			}
			[CompilerGenerated]
			set
			{
				string_1 = value;
			}
		}

		public static string String_0
		{
			[CompilerGenerated]
			get
			{
				return string_2;
			}
			[CompilerGenerated]
			set
			{
				string_2 = value;
			}
		}

		public static string String_1
		{
			[CompilerGenerated]
			get
			{
				return string_3;
			}
			[CompilerGenerated]
			set
			{
				string_3 = value;
			}
		}

		public D2004EA8()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass87
	{
		public static string string_0 = Class607.B630A78B.object_0[351](Class607.B630A78B.object_0[1192](), "config.xml");

		public static string BF10503C = "nvdata.img,nvram.img";

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private static string string_1;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private static string EC057C92;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private static int DF06B103;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static bool bool_0;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private static string string_2;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string string_3;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private static string string_4;

		public static string String_0
		{
			[CompilerGenerated]
			get
			{
				return string_1;
			}
			[CompilerGenerated]
			set
			{
				string_1 = value;
			}
		}

		public static string B93769BE
		{
			[CompilerGenerated]
			get
			{
				return EC057C92;
			}
			[CompilerGenerated]
			set
			{
				EC057C92 = value;
			}
		}

		public static int Int32_0
		{
			[CompilerGenerated]
			get
			{
				return DF06B103;
			}
			[CompilerGenerated]
			set
			{
				DF06B103 = value;
			}
		}

		public static bool Boolean_0
		{
			[CompilerGenerated]
			get
			{
				return bool_0;
			}
			[CompilerGenerated]
			set
			{
				bool_0 = value;
			}
		}

		public static string String_1
		{
			[CompilerGenerated]
			get
			{
				return string_2;
			}
			[CompilerGenerated]
			set
			{
				string_2 = value;
			}
		}

		public static string ADBD6835
		{
			[CompilerGenerated]
			get
			{
				return string_3;
			}
			[CompilerGenerated]
			set
			{
				string_3 = value;
			}
		}

		public static string String_2
		{
			[CompilerGenerated]
			get
			{
				return string_4;
			}
			[CompilerGenerated]
			set
			{
				string_4 = value;
			}
		}

		public GClass87()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public static string smethod_0(string string_0)
	{
		string result = "Qualcomm";
		if (Class607.B630A78B.object_0[1240](string_0, "0073"))
		{
			result = "Vivo";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0000"))
		{
			result = "Qualcomm";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0072"))
		{
			result = "Xiaomi";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0051"))
		{
			result = "Oppo/OnePlus";
		}
		return result;
	}

	public static string smethod_1(string string_0)
	{
		string result = "Unknown";
		if (Class607.B630A78B.object_0[1240](string_0, "70"))
		{
			result = "OnePlus";
		}
		return result;
	}

	public static string smethod_2(string string_0)
	{
		string result = "Qualcomm Snapdragon";
		if (Class607.B630A78B.object_0[1240](string_0, "007050E1"))
		{
			result = "Snapdragon 410";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000560E1"))
		{
			result = "Snapdragon 425";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0004F0E1"))
		{
			result = "Snapdragon 430";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0006B0E1"))
		{
			result = "Snapdragon 435";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "008050E1"))
		{
			result = "Snapdragon 400";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "008110E1"))
		{
			result = "Snapdragon 200";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "00310000"))
		{
			result = "Snapdragon 808";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0090B0E1"))
		{
			result = "Snapdragon 610";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "00ED0002"))
		{
			result = "Snapdragon 425";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000460E1"))
		{
			result = "Snapdragon 625";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "00390029"))
		{
			result = "Snapdragon 210";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "001360E1"))
		{
			result = "Snapdragon 460";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000BF0E1"))
		{
			result = "Snapdragon 439";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0014D0E1"))
		{
			result = "Snapdragon 662";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000950E1"))
		{
			result = "Snapdragon 675";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000E60E1"))
		{
			result = "Snapdragon 730";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "001080E1"))
		{
			result = "Snapdragon 712";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0010A0E1"))
		{
			result = "Snapdragon 665";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "001B80E1"))
		{
			result = "Snapdragon 680";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000A50E1"))
		{
			result = "Snapdragon 855";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000CC0E1"))
		{
			result = "Snapdragon 636";
		}
		return result;
	}

	public static string smethod_3(string string_0)
	{
		string result = "QCXXX";
		if (Class607.B630A78B.object_0[1240](string_0, "007050E1"))
		{
			result = "MSM8916";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000560E1"))
		{
			result = "MSM8917";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0004F0E1"))
		{
			result = "MSM8937";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0006B0E1"))
		{
			result = "MSM8940";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "008050E1"))
		{
			result = "MSM8x26/28";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "008110E1"))
		{
			result = "MSM8X10_12";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "00310000"))
		{
			result = "MSM8992";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0090B0E1"))
		{
			result = "MSM8936";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "00ED0002"))
		{
			result = "MSM8917";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000460E1"))
		{
			result = "MSM8953";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "00390029"))
		{
			result = "MSM8x09";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "001360E1"))
		{
			result = "SM4250-AA";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000BF0E1"))
		{
			result = "SDM439";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0014D0E1"))
		{
			result = "SDM662";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000950E1"))
		{
			result = "SM6150";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000E60E1"))
		{
			result = "SM7150-AA";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "001B80E1"))
		{
			result = "SM6250";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "001080E1"))
		{
			result = "SDM712";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "0010A0E1"))
		{
			result = "SDM665";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000A50E1"))
		{
			result = "SDM855";
		}
		if (Class607.B630A78B.object_0[1240](string_0, "000CC0E1"))
		{
			result = "SDM636";
		}
		return result;
	}

	public GClass83()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
