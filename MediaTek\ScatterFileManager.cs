using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace MotoKingPro.MediaTek
{
    /// <summary>
    /// MediaTek scatter file parser and manager
    /// </summary>
    public class ScatterFileManager
    {
        #region Properties
        public string ScatterFilePath { get; private set; }
        public ScatterFileInfo ScatterInfo { get; private set; }
        public List<RomEntry> RomEntries { get; private set; }
        public bool IsLoaded { get; private set; }
        #endregion

        #region Events
        public event EventHandler<string> LogMessage;
        #endregion

        #region Constructor
        public ScatterFileManager()
        {
            RomEntries = new List<RomEntry>();
            IsLoaded = false;
        }
        #endregion

        #region Public Methods
        public bool LoadScatterFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    LogMessage?.Invoke(this, $"Scatter file not found: {filePath}");
                    return false;
                }

                ScatterFilePath = filePath;
                string content = File.ReadAllText(filePath);
                
                // Parse scatter file
                ParseScatterFile(content);
                
                IsLoaded = true;
                LogMessage?.Invoke(this, $"Scatter file loaded successfully: {Path.GetFileName(filePath)}");
                LogMessage?.Invoke(this, $"Platform: {ScatterInfo.Platform}");
                LogMessage?.Invoke(this, $"Config: {ScatterInfo.Config}");
                LogMessage?.Invoke(this, $"Found {RomEntries.Count} ROM entries");
                
                return true;
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"Failed to load scatter file: {ex.Message}");
                return false;
            }
        }

        public List<RomEntry> GetEnabledRomEntries()
        {
            return RomEntries.Where(entry => entry.IsEnabled).ToList();
        }

        public void SetRomEntryEnabled(string partitionName, bool enabled)
        {
            var entry = RomEntries.FirstOrDefault(e => e.PartitionName.Equals(partitionName, StringComparison.OrdinalIgnoreCase));
            if (entry != null)
            {
                entry.IsEnabled = enabled;
                LogMessage?.Invoke(this, $"ROM entry '{partitionName}' {(enabled ? "enabled" : "disabled")}");
            }
        }

        public RomEntry GetRomEntry(string partitionName)
        {
            return RomEntries.FirstOrDefault(e => e.PartitionName.Equals(partitionName, StringComparison.OrdinalIgnoreCase));
        }

        public bool ValidateRomFiles()
        {
            bool allValid = true;
            string scatterDir = Path.GetDirectoryName(ScatterFilePath);

            foreach (var entry in RomEntries.Where(e => e.IsEnabled))
            {
                string fullPath = Path.IsPathRooted(entry.FilePath) ? entry.FilePath : Path.Combine(scatterDir, entry.FilePath);
                
                if (!File.Exists(fullPath))
                {
                    LogMessage?.Invoke(this, $"ROM file not found: {entry.FilePath}");
                    allValid = false;
                }
                else
                {
                    entry.FullFilePath = fullPath;
                    entry.FileSize = new FileInfo(fullPath).Length;
                }
            }

            return allValid;
        }
        #endregion

        #region Private Methods
        private void ParseScatterFile(string content)
        {
            RomEntries.Clear();
            ScatterInfo = new ScatterFileInfo();

            string[] lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            
            // Parse header information
            ParseHeader(lines);
            
            // Parse ROM entries
            ParseRomEntries(lines);
        }

        private void ParseHeader(string[] lines)
        {
            foreach (string line in lines)
            {
                string trimmedLine = line.Trim();
                
                if (trimmedLine.StartsWith("platform", StringComparison.OrdinalIgnoreCase))
                {
                    ScatterInfo.Platform = ExtractValue(trimmedLine);
                }
                else if (trimmedLine.StartsWith("config", StringComparison.OrdinalIgnoreCase))
                {
                    ScatterInfo.Config = ExtractValue(trimmedLine);
                }
                else if (trimmedLine.StartsWith("project", StringComparison.OrdinalIgnoreCase))
                {
                    ScatterInfo.Project = ExtractValue(trimmedLine);
                }
                else if (trimmedLine.StartsWith("storage", StringComparison.OrdinalIgnoreCase))
                {
                    ScatterInfo.Storage = ExtractValue(trimmedLine);
                }
                else if (trimmedLine.StartsWith("boot_channel", StringComparison.OrdinalIgnoreCase))
                {
                    ScatterInfo.BootChannel = ExtractValue(trimmedLine);
                }
                else if (trimmedLine.StartsWith("block_size", StringComparison.OrdinalIgnoreCase))
                {
                    if (long.TryParse(ExtractValue(trimmedLine), out long blockSize))
                    {
                        ScatterInfo.BlockSize = blockSize;
                    }
                }
            }
        }

        private void ParseRomEntries(string[] lines)
        {
            RomEntry currentEntry = null;
            
            foreach (string line in lines)
            {
                string trimmedLine = line.Trim();
                
                // Check for partition start
                if (trimmedLine.StartsWith("partition_index", StringComparison.OrdinalIgnoreCase))
                {
                    // Save previous entry if exists
                    if (currentEntry != null && !string.IsNullOrEmpty(currentEntry.PartitionName))
                    {
                        RomEntries.Add(currentEntry);
                    }
                    
                    // Start new entry
                    currentEntry = new RomEntry();
                    if (int.TryParse(ExtractValue(trimmedLine), out int index))
                    {
                        currentEntry.PartitionIndex = index;
                    }
                }
                else if (currentEntry != null)
                {
                    ParseRomEntryProperty(currentEntry, trimmedLine);
                }
            }
            
            // Add last entry
            if (currentEntry != null && !string.IsNullOrEmpty(currentEntry.PartitionName))
            {
                RomEntries.Add(currentEntry);
            }
        }

        private void ParseRomEntryProperty(RomEntry entry, string line)
        {
            if (line.StartsWith("partition_name", StringComparison.OrdinalIgnoreCase))
            {
                entry.PartitionName = ExtractValue(line);
            }
            else if (line.StartsWith("file_name", StringComparison.OrdinalIgnoreCase))
            {
                entry.FilePath = ExtractValue(line);
            }
            else if (line.StartsWith("is_download", StringComparison.OrdinalIgnoreCase))
            {
                string value = ExtractValue(line).ToLower();
                entry.IsDownload = value == "true" || value == "1";
            }
            else if (line.StartsWith("type", StringComparison.OrdinalIgnoreCase))
            {
                if (int.TryParse(ExtractValue(line), out int type))
                {
                    entry.Type = type;
                }
            }
            else if (line.StartsWith("linear_start_addr", StringComparison.OrdinalIgnoreCase))
            {
                string value = ExtractValue(line);
                if (value.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                {
                    if (long.TryParse(value.Substring(2), System.Globalization.NumberStyles.HexNumber, null, out long addr))
                    {
                        entry.LinearStartAddr = addr;
                    }
                }
                else if (long.TryParse(value, out long addr2))
                {
                    entry.LinearStartAddr = addr2;
                }
            }
            else if (line.StartsWith("physical_start_addr", StringComparison.OrdinalIgnoreCase))
            {
                string value = ExtractValue(line);
                if (value.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                {
                    if (long.TryParse(value.Substring(2), System.Globalization.NumberStyles.HexNumber, null, out long addr))
                    {
                        entry.PhysicalStartAddr = addr;
                    }
                }
                else if (long.TryParse(value, out long addr2))
                {
                    entry.PhysicalStartAddr = addr2;
                }
            }
            else if (line.StartsWith("partition_size", StringComparison.OrdinalIgnoreCase))
            {
                string value = ExtractValue(line);
                if (value.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                {
                    if (long.TryParse(value.Substring(2), System.Globalization.NumberStyles.HexNumber, null, out long size))
                    {
                        entry.PartitionSize = size;
                    }
                }
                else if (long.TryParse(value, out long size2))
                {
                    entry.PartitionSize = size2;
                }
            }
            else if (line.StartsWith("region", StringComparison.OrdinalIgnoreCase))
            {
                if (int.TryParse(ExtractValue(line), out int region))
                {
                    entry.Region = region;
                }
            }
            else if (line.StartsWith("storage", StringComparison.OrdinalIgnoreCase))
            {
                if (int.TryParse(ExtractValue(line), out int storage))
                {
                    entry.Storage = storage;
                }
            }
            else if (line.StartsWith("boundary_check", StringComparison.OrdinalIgnoreCase))
            {
                string value = ExtractValue(line).ToLower();
                entry.BoundaryCheck = value == "true" || value == "1";
            }
            else if (line.StartsWith("is_reserved", StringComparison.OrdinalIgnoreCase))
            {
                string value = ExtractValue(line).ToLower();
                entry.IsReserved = value == "true" || value == "1";
            }
            else if (line.StartsWith("operation_type", StringComparison.OrdinalIgnoreCase))
            {
                if (int.TryParse(ExtractValue(line), out int opType))
                {
                    entry.OperationType = opType;
                }
            }
            else if (line.StartsWith("d_type", StringComparison.OrdinalIgnoreCase))
            {
                if (int.TryParse(ExtractValue(line), out int dType))
                {
                    entry.DType = dType;
                }
            }
        }

        private string ExtractValue(string line)
        {
            // Extract value after colon or equals sign
            int colonIndex = line.IndexOf(':');
            int equalsIndex = line.IndexOf('=');
            
            int separatorIndex = -1;
            if (colonIndex >= 0 && equalsIndex >= 0)
            {
                separatorIndex = Math.Min(colonIndex, equalsIndex);
            }
            else if (colonIndex >= 0)
            {
                separatorIndex = colonIndex;
            }
            else if (equalsIndex >= 0)
            {
                separatorIndex = equalsIndex;
            }
            
            if (separatorIndex >= 0 && separatorIndex < line.Length - 1)
            {
                return line.Substring(separatorIndex + 1).Trim().Trim('"', '\'');
            }
            
            return string.Empty;
        }
        #endregion
    }

    /// <summary>
    /// Scatter file header information
    /// </summary>
    public class ScatterFileInfo
    {
        public string Platform { get; set; } = string.Empty;
        public string Config { get; set; } = string.Empty;
        public string Project { get; set; } = string.Empty;
        public string Storage { get; set; } = string.Empty;
        public string BootChannel { get; set; } = string.Empty;
        public long BlockSize { get; set; } = 0;
    }

    /// <summary>
    /// ROM entry in scatter file
    /// </summary>
    public class RomEntry
    {
        public int PartitionIndex { get; set; }
        public string PartitionName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string FullFilePath { get; set; } = string.Empty;
        public bool IsDownload { get; set; } = true;
        public bool IsEnabled { get; set; } = true;
        public int Type { get; set; }
        public long LinearStartAddr { get; set; }
        public long PhysicalStartAddr { get; set; }
        public long PartitionSize { get; set; }
        public long FileSize { get; set; }
        public int Region { get; set; }
        public int Storage { get; set; }
        public bool BoundaryCheck { get; set; } = true;
        public bool IsReserved { get; set; } = false;
        public int OperationType { get; set; }
        public int DType { get; set; }

        public string DisplayName => !string.IsNullOrEmpty(PartitionName) ? PartitionName : Path.GetFileName(FilePath);
        
        public string SizeString
        {
            get
            {
                if (FileSize > 0)
                {
                    return FormatBytes(FileSize);
                }
                else if (PartitionSize > 0)
                {
                    return FormatBytes(PartitionSize);
                }
                return "Unknown";
            }
        }

        public bool IsCriticalPartition
        {
            get
            {
                string name = PartitionName.ToLower();
                return name.Contains("preloader") || name.Contains("boot") || name.Contains("recovery") || 
                       name.Contains("system") || name.Contains("lk") || name.Contains("logo");
            }
        }

        private string FormatBytes(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        public override string ToString()
        {
            return $"{DisplayName} ({SizeString})";
        }
    }
}
