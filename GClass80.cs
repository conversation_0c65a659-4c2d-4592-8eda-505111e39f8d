using System.Diagnostics;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;

public class GClass80
{
	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_0;

	private string _model;

	private string _product;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string B315BBB8;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_1;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_2;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_3;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string DF9DF337;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_4;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_5;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_6;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_7;

	public string ShowDev
	{
		[CompilerGenerated]
		get
		{
			return string_0;
		}
		[CompilerGenerated]
		set
		{
			string_0 = value;
		}
	}

	[JsonProperty(PropertyName = "Brand")]
	public string String_0
	{
		[CompilerGenerated]
		get
		{
			return B315BBB8;
		}
		[CompilerGenerated]
		set
		{
			B315BBB8 = value;
		}
	}

	[JsonProperty(PropertyName = "id")]
	public string BE0F5130
	{
		[CompilerGenerated]
		get
		{
			return string_1;
		}
		[CompilerGenerated]
		set
		{
			string_1 = value;
		}
	}

	public string Model
	{
		get
		{
			return _model;
		}
		set
		{
			_model = value;
			ShowDev = Class607.B630A78B.object_0[1140](ShowDev, value, " [");
		}
	}

	public string URL
	{
		[CompilerGenerated]
		get
		{
			return string_2;
		}
		[CompilerGenerated]
		set
		{
			string_2 = value;
		}
	}

	public string FileID
	{
		[CompilerGenerated]
		get
		{
			return string_3;
		}
		[CompilerGenerated]
		set
		{
			string_3 = value;
		}
	}

	public string FileSize
	{
		[CompilerGenerated]
		get
		{
			return DF9DF337;
		}
		[CompilerGenerated]
		set
		{
			DF9DF337 = value;
		}
	}

	public string Product
	{
		get
		{
			return _product;
		}
		set
		{
			_product = value;
			ShowDev = Class607.B630A78B.object_0[1140](ShowDev, value, "]");
		}
	}

	public string UnlockSupported
	{
		[CompilerGenerated]
		get
		{
			return string_4;
		}
		[CompilerGenerated]
		set
		{
			string_4 = value;
		}
	}

	public string FBUnlock
	{
		[CompilerGenerated]
		get
		{
			return string_5;
		}
		[CompilerGenerated]
		set
		{
			string_5 = value;
		}
	}

	public string FlashStorage
	{
		[CompilerGenerated]
		get
		{
			return string_6;
		}
		[CompilerGenerated]
		set
		{
			string_6 = value;
		}
	}

	public string FastbootCMD
	{
		[CompilerGenerated]
		get
		{
			return string_7;
		}
		[CompilerGenerated]
		set
		{
			string_7 = value;
		}
	}

	public GClass80()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
