using System;

public static class GClass3
{
	public static void smethod_0(ushort A3B42C3F, byte[] byte_0, int int_0)
	{
		byte_0[int_0] = (byte)(A3B42C3F & 0xFF);
		byte_0[int_0 + 1] = (byte)((A3B42C3F >> 8) & 0xFF);
	}

	public static void smethod_1(uint uint_0, byte[] byte_0, int D929EE23)
	{
		byte_0[D929EE23] = (byte)(uint_0 & 0xFF);
		byte_0[D929EE23 + 1] = (byte)((uint_0 >> 8) & 0xFF);
		byte_0[D929EE23 + 2] = (byte)((uint_0 >> 16) & 0xFF);
		byte_0[D929EE23 + 3] = (byte)((uint_0 >> 24) & 0xFF);
	}

	public static void smethod_2(ulong ulong_0, byte[] byte_0, int int_0)
	{
		byte_0[int_0] = (byte)(ulong_0 & 0xFFL);
		byte_0[int_0 + 1] = (byte)((ulong_0 >> 8) & 0xFFL);
		byte_0[int_0 + 2] = (byte)((ulong_0 >> 16) & 0xFFL);
		byte_0[int_0 + 3] = (byte)((ulong_0 >> 24) & 0xFFL);
		byte_0[int_0 + 4] = (byte)((ulong_0 >> 32) & 0xFFL);
		byte_0[int_0 + 5] = (byte)((ulong_0 >> 40) & 0xFFL);
		byte_0[int_0 + 6] = (byte)((ulong_0 >> 48) & 0xFFL);
		byte_0[int_0 + 7] = (byte)((ulong_0 >> 56) & 0xFFL);
	}

	public static void smethod_3(short short_0, byte[] byte_0, int int_0)
	{
		smethod_0((ushort)short_0, byte_0, int_0);
	}

	public static void AF0DF532(int A988A19B, byte[] byte_0, int int_0)
	{
		smethod_1((uint)A988A19B, byte_0, int_0);
	}

	public static void smethod_4(long long_0, byte[] DD2C1624, int D39D843F)
	{
		smethod_2((ulong)long_0, DD2C1624, D39D843F);
	}

	public static void smethod_5(Guid F03470A2, byte[] byte_0, int int_0)
	{
		Class607.B630A78B.object_0[242](Class607.B630A78B.object_0[157](ref F03470A2), 0, byte_0, int_0, 16);
	}

	public static void smethod_6(ushort ushort_0, byte[] byte_0, int int_0)
	{
		byte_0[int_0] = (byte)(ushort_0 >> 8);
		byte_0[int_0 + 1] = (byte)(ushort_0 & 0xFF);
	}

	public static void BB3A7629(uint uint_0, byte[] AC076B34, int int_0)
	{
		AC076B34[int_0] = (byte)((uint_0 >> 24) & 0xFF);
		AC076B34[int_0 + 1] = (byte)((uint_0 >> 16) & 0xFF);
		AC076B34[int_0 + 2] = (byte)((uint_0 >> 8) & 0xFF);
		AC076B34[int_0 + 3] = (byte)(uint_0 & 0xFF);
	}

	public static void smethod_7(ulong F692E202, byte[] byte_0, int C33389B4)
	{
		byte_0[C33389B4] = (byte)((F692E202 >> 56) & 0xFFL);
		byte_0[C33389B4 + 1] = (byte)((F692E202 >> 48) & 0xFFL);
		byte_0[C33389B4 + 2] = (byte)((F692E202 >> 40) & 0xFFL);
		byte_0[C33389B4 + 3] = (byte)((F692E202 >> 32) & 0xFFL);
		byte_0[C33389B4 + 4] = (byte)((F692E202 >> 24) & 0xFFL);
		byte_0[C33389B4 + 5] = (byte)((F692E202 >> 16) & 0xFFL);
		byte_0[C33389B4 + 6] = (byte)((F692E202 >> 8) & 0xFFL);
		byte_0[C33389B4 + 7] = (byte)(F692E202 & 0xFFL);
	}

	public static void smethod_8(short short_0, byte[] BC87F60C, int int_0)
	{
		smethod_6((ushort)short_0, BC87F60C, int_0);
	}

	public static void smethod_9(int int_0, byte[] CCAFD093, int C6BEFA3A)
	{
		BB3A7629((uint)int_0, CCAFD093, C6BEFA3A);
	}

	public static void smethod_10(long BFA73C9A, byte[] byte_0, int FC25579E)
	{
		smethod_7((ulong)BFA73C9A, byte_0, FC25579E);
	}

	public static void A6226490(Guid FDAF5F33, byte[] A89BA31F, int int_0)
	{
		byte[] array = Class607.B630A78B.object_0[157](ref FDAF5F33);
		BB3A7629(E038002D(array, 0), A89BA31F, int_0);
		smethod_6(smethod_11(array, 4), A89BA31F, int_0 + 4);
		smethod_6(smethod_11(array, 6), A89BA31F, int_0 + 6);
		Class607.B630A78B.object_0[242](array, 8, A89BA31F, int_0 + 8, 8);
	}

	public static ushort smethod_11(byte[] byte_0, int int_0)
	{
		return (ushort)(((byte_0[int_0 + 1] << 8) & 0xFF00) | (byte_0[int_0] & 0xFF));
	}

	public static uint E038002D(byte[] A5A16431, int int_0)
	{
		return (uint)(((A5A16431[int_0 + 3] << 24) & 0xFF000000L) | ((A5A16431[int_0 + 2] << 16) & 0xFF0000L) | ((A5A16431[int_0 + 1] << 8) & 0xFF00L) | ((int)A5A16431[int_0] & 0xFFL));
	}

	public static ulong smethod_12(byte[] byte_0, int F8A5A228)
	{
		return ((ulong)E038002D(byte_0, F8A5A228 + 4) << 32) | E038002D(byte_0, F8A5A228);
	}

	public static short BC0ED40F(byte[] BD0BE71D, int int_0)
	{
		return (short)smethod_11(BD0BE71D, int_0);
	}

	public static int B18CCEA6(byte[] byte_0, int B09FAE0A)
	{
		return (int)E038002D(byte_0, B09FAE0A);
	}

	public static long D614502D(byte[] byte_0, int C02821B4)
	{
		return (long)smethod_12(byte_0, C02821B4);
	}

	public static ushort E91D790F(byte[] CE2C9E35, int FE09F997)
	{
		return (ushort)(((CE2C9E35[FE09F997] << 8) & 0xFF00) | (CE2C9E35[FE09F997 + 1] & 0xFF));
	}

	public static uint FD32A601(byte[] byte_0, int int_0)
	{
		return (uint)(((byte_0[int_0] << 24) & 0xFF000000L) | ((byte_0[int_0 + 1] << 16) & 0xFF0000L) | ((byte_0[int_0 + 2] << 8) & 0xFF00L) | ((int)byte_0[int_0 + 3] & 0xFFL));
	}

	public static ulong smethod_13(byte[] byte_0, int int_0)
	{
		return ((ulong)FD32A601(byte_0, int_0) << 32) | FD32A601(byte_0, int_0 + 4);
	}

	public static short smethod_14(byte[] byte_0, int int_0)
	{
		return (short)E91D790F(byte_0, int_0);
	}

	public static int smethod_15(byte[] AD861E2F, int FF0BBFBC)
	{
		return (int)FD32A601(AD861E2F, FF0BBFBC);
	}

	public static long smethod_16(byte[] AA89B58F, int int_0)
	{
		return (long)smethod_13(AA89B58F, int_0);
	}

	public static Guid smethod_17(byte[] byte_0, int int_0)
	{
		byte[] array = new byte[16];
		Class607.B630A78B.object_0[242](byte_0, int_0, array, 0, 16);
		return Class607.B630A78B.object_0[284](array);
	}

	public static Guid smethod_18(byte[] byte_0, int int_0)
	{
		return Class607.B630A78B.object_0[191](FD32A601(byte_0, int_0), E91D790F(byte_0, int_0 + 4), E91D790F(byte_0, int_0 + 6), byte_0[int_0 + 8], byte_0[int_0 + 9], byte_0[int_0 + 10], byte_0[int_0 + 11], byte_0[int_0 + 12], byte_0[int_0 + 13], byte_0[int_0 + 14], byte_0[int_0 + 15]);
	}

	public static byte[] smethod_19(byte[] E1A5842F, int int_0, int int_1)
	{
		byte[] array = new byte[int_1];
		Class607.B630A78B.object_0[242](E1A5842F, int_0, array, 0, int_1);
		return array;
	}

	public static T smethod_20<T>(byte[] byte_0, int F92AEE9C) where T : GInterface0, new()
	{
		T result = new T();
		result.B0B6FA3F(byte_0, F92AEE9C);
		return result;
	}

	public static void BC179A96(string string_0, byte[] byte_0, int int_0, int int_1)
	{
		char[] array = Class607.B630A78B.object_0[1234](string_0, 0, Class607.B630A78B.object_0[78](Class607.B630A78B.object_0[342](string_0), int_1));
		int i;
		for (i = 0; i < array.Length && i < int_1; i++)
		{
			byte_0[i + int_0] = (byte)array[i];
		}
		for (; i < int_1; i++)
		{
			byte_0[i + int_0] = 0;
		}
	}

	public static string smethod_21(byte[] D425A316, int int_0, int A436589F)
	{
		char[] array = new char[A436589F];
		for (int i = 0; i < A436589F; i++)
		{
			array[i] = (char)D425A316[i + int_0];
		}
		return Class607.B630A78B.object_0[1260](array);
	}

	public static string CDA41A19(byte[] AE0A1826, int int_0, int int_1)
	{
		char[] array = new char[int_1];
		int num = 0;
		while (true)
		{
			if (num < int_1)
			{
				byte b = AE0A1826[num + int_0];
				if (b == 0)
				{
					break;
				}
				array[num] = (char)b;
				num++;
				continue;
			}
			return Class607.B630A78B.object_0[1260](array);
		}
		return Class607.B630A78B.object_0[225](array, 0, num);
	}
}
