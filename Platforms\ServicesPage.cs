using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using MotoKingPro.Core;
using MotoKingPro.UI;

namespace MotoKingPro.Platforms
{
    /// <summary>
    /// Services/ADB platform page (EAB5ABA9 -> ServicesPage)
    /// Handles Android Debug Bridge operations and device services
    /// </summary>
    public partial class ServicesPage : UserControl
    {
        #region Events
        public event Action<bool, string> OperationStateChanged;
        public event Action<string, LogLevel, bool, bool> LogMessage;
        #endregion

        #region Fields
        private MainForm parentForm;
        private AdbManager adbManager;
        private LogRichTextBox textOutput;
        private bool isOperationRunning = false;
        #endregion

        #region UI Controls
        private Panel mainPanel;
        private GroupBox deviceInfoGroup;
        private GroupBox adbOperationsGroup;
        private GroupBox systemOperationsGroup;
        
        private Button connectAdbButton;
        private Button disconnectAdbButton;
        private Button refreshDevicesButton;
        private Button installApkButton;
        private Button uninstallAppButton;
        private Button shellCommandButton;
        private Button rebootButton;
        
        private ComboBox connectedDevicesCombo;
        private ComboBox rebootModeCombo;
        private TextBox shellCommandTextBox;
        private TextBox packageNameTextBox;
        
        private Button enableAdbButton;
        private Button disableAdbButton;
        private Button rootDeviceButton;
        private Button unrootDeviceButton;
        private Button factoryResetButton;
        
        private Label deviceModelLabel;
        private Label androidVersionLabel;
        private Label rootStatusLabel;
        private Label connectionStatusLabel;
        #endregion

        #region Constructor
        public ServicesPage()
        {
            InitializeComponent();
            InitializeCustomComponents();
            SetupEventHandlers();
        }
        #endregion

        #region Initialization
        private void InitializeComponent()
        {
            mainPanel = new Panel { Dock = DockStyle.Fill, BackColor = Color.FromArgb(45, 45, 48) };
            
            // Device info group
            deviceInfoGroup = new GroupBox 
            { 
                Text = "Device Information", 
                ForeColor = Color.White,
                Size = new Size(300, 120),
                Location = new Point(10, 10)
            };
            
            deviceModelLabel = new Label { Text = "Model: Not Connected", ForeColor = Color.White, Location = new Point(10, 20) };
            androidVersionLabel = new Label { Text = "Android: Unknown", ForeColor = Color.White, Location = new Point(10, 40) };
            rootStatusLabel = new Label { Text = "Root: Unknown", ForeColor = Color.White, Location = new Point(10, 60) };
            connectionStatusLabel = new Label { Text = "Status: Disconnected", ForeColor = Color.Red, Location = new Point(10, 80) };
            
            deviceInfoGroup.Controls.AddRange(new Control[] { deviceModelLabel, androidVersionLabel, rootStatusLabel, connectionStatusLabel });
            
            // ADB Operations group
            adbOperationsGroup = new GroupBox 
            { 
                Text = "ADB Operations", 
                ForeColor = Color.White,
                Size = new Size(300, 200),
                Location = new Point(10, 140)
            };
            
            Label devicesLabel = new Label { Text = "Connected Devices:", ForeColor = Color.White, Location = new Point(10, 20) };
            connectedDevicesCombo = new ComboBox 
            { 
                Size = new Size(200, 25), 
                Location = new Point(10, 40),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            refreshDevicesButton = new Button { Text = "Refresh", Size = new Size(70, 25), Location = new Point(220, 40) };
            
            connectAdbButton = new Button { Text = "Connect ADB", Size = new Size(120, 30), Location = new Point(10, 75) };
            disconnectAdbButton = new Button { Text = "Disconnect", Size = new Size(120, 30), Location = new Point(140, 75), Enabled = false };
            
            Label shellLabel = new Label { Text = "Shell Command:", ForeColor = Color.White, Location = new Point(10, 115) };
            shellCommandTextBox = new TextBox 
            { 
                Size = new Size(200, 25), 
                Location = new Point(10, 135),
                BackColor = Color.FromArgb(30, 30, 30),
                ForeColor = Color.White,
                PlaceholderText = "Enter ADB shell command"
            };
            shellCommandButton = new Button { Text = "Execute", Size = new Size(70, 25), Location = new Point(220, 135), Enabled = false };
            
            rebootModeCombo = new ComboBox 
            { 
                Size = new Size(120, 25), 
                Location = new Point(10, 170),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Enabled = false
            };
            rebootModeCombo.Items.AddRange(new[] { "Normal", "Bootloader", "Recovery", "Download" });
            rebootModeCombo.SelectedIndex = 0;
            
            rebootButton = new Button { Text = "Reboot", Size = new Size(120, 30), Location = new Point(140, 165), Enabled = false };
            
            adbOperationsGroup.Controls.AddRange(new Control[] 
            { 
                devicesLabel, connectedDevicesCombo, refreshDevicesButton,
                connectAdbButton, disconnectAdbButton,
                shellLabel, shellCommandTextBox, shellCommandButton,
                rebootModeCombo, rebootButton
            });
            
            // System Operations group
            systemOperationsGroup = new GroupBox 
            { 
                Text = "System Operations", 
                ForeColor = Color.White,
                Size = new Size(300, 200),
                Location = new Point(320, 10)
            };
            
            installApkButton = new Button { Text = "Install APK", Size = new Size(120, 30), Location = new Point(10, 20), Enabled = false };
            
            Label packageLabel = new Label { Text = "Package Name:", ForeColor = Color.White, Location = new Point(10, 60) };
            packageNameTextBox = new TextBox 
            { 
                Size = new Size(200, 25), 
                Location = new Point(10, 80),
                BackColor = Color.FromArgb(30, 30, 30),
                ForeColor = Color.White,
                PlaceholderText = "com.example.app"
            };
            uninstallAppButton = new Button { Text = "Uninstall", Size = new Size(70, 25), Location = new Point(220, 80), Enabled = false };
            
            enableAdbButton = new Button { Text = "Enable ADB", Size = new Size(120, 30), Location = new Point(10, 115), Enabled = false };
            disableAdbButton = new Button { Text = "Disable ADB", Size = new Size(120, 30), Location = new Point(140, 115), Enabled = false };
            
            rootDeviceButton = new Button { Text = "Root Device", Size = new Size(120, 30), Location = new Point(10, 155), Enabled = false };
            unrootDeviceButton = new Button { Text = "Unroot Device", Size = new Size(120, 30), Location = new Point(140, 155), Enabled = false };
            
            systemOperationsGroup.Controls.AddRange(new Control[] 
            { 
                installApkButton, packageLabel, packageNameTextBox, uninstallAppButton,
                enableAdbButton, disableAdbButton, rootDeviceButton, unrootDeviceButton
            });
            
            // Text output
            textOutput = new LogRichTextBox 
            { 
                Size = new Size(610, 200), 
                Location = new Point(10, 350),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };
            
            mainPanel.Controls.AddRange(new Control[] { deviceInfoGroup, adbOperationsGroup, systemOperationsGroup, textOutput });
            Controls.Add(mainPanel);
        }

        private void InitializeCustomComponents()
        {
            adbManager = new AdbManager();
            
            // Log initial messages
            LogMessage("ADB King Services", LogLevel.Word, true, true);
            LogMessage("Version: " + ApplicationInfo.GetVersionString(), LogLevel.Success);
            LogMessage("Selected Tab: Services", LogLevel.Success, true, true);
            LogMessage("Ready for ADB operations...", LogLevel.Info);
            
            RefreshDevicesList();
        }

        private void SetupEventHandlers()
        {
            connectAdbButton.Click += ConnectAdbButton_Click;
            disconnectAdbButton.Click += DisconnectAdbButton_Click;
            refreshDevicesButton.Click += RefreshDevicesButton_Click;
            shellCommandButton.Click += ShellCommandButton_Click;
            rebootButton.Click += RebootButton_Click;
            installApkButton.Click += InstallApkButton_Click;
            uninstallAppButton.Click += UninstallAppButton_Click;
            enableAdbButton.Click += EnableAdbButton_Click;
            disableAdbButton.Click += DisableAdbButton_Click;
            rootDeviceButton.Click += RootDeviceButton_Click;
            unrootDeviceButton.Click += UnrootDeviceButton_Click;
            
            adbManager.OperationStateChanged += OnOperationStateChanged;
            adbManager.LogMessage += OnLogMessage;
        }
        #endregion

        #region Public Methods
        public void SetParentForm(MainForm form)
        {
            parentForm = form;
        }

        public void FocusTextOutput()
        {
            textOutput?.Focus();
        }

        public void ClearLog()
        {
            textOutput?.ClearLog();
        }

        public List<LogItem> GetLogItems()
        {
            return new List<LogItem>();
        }

        public void SetOperationState(bool isRunning)
        {
            isOperationRunning = isRunning;
            UpdateUIState();
        }
        #endregion

        #region Event Handlers
        private void ConnectAdbButton_Click(object sender, EventArgs e)
        {
            if (connectedDevicesCombo.SelectedItem == null)
            {
                LogMessage("Please select a device first", LogLevel.Error);
                return;
            }

            OnOperationStateChanged(true, "Connect ADB Device");
            
            string deviceId = connectedDevicesCombo.SelectedItem.ToString();
            bool success = adbManager.ConnectDevice(deviceId);
            
            if (success)
            {
                connectionStatusLabel.Text = "Status: Connected";
                connectionStatusLabel.ForeColor = Color.Green;
                EnableConnectedControls(true);
                LogMessage("ADB device connected successfully", LogLevel.Success);
                
                // Read device info
                var deviceInfo = adbManager.ReadDeviceInfo();
                if (deviceInfo != null)
                {
                    deviceModelLabel.Text = $"Model: {deviceInfo.Model}";
                    androidVersionLabel.Text = $"Android: {deviceInfo.AndroidVersion}";
                }
            }
            else
            {
                LogMessage("Failed to connect ADB device", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Connect ADB Device");
        }

        private void DisconnectAdbButton_Click(object sender, EventArgs e)
        {
            adbManager.DisconnectDevice();
            connectionStatusLabel.Text = "Status: Disconnected";
            connectionStatusLabel.ForeColor = Color.Red;
            EnableConnectedControls(false);
            LogMessage("ADB device disconnected", LogLevel.Warning);
        }

        private void RefreshDevicesButton_Click(object sender, EventArgs e)
        {
            RefreshDevicesList();
        }

        private void ShellCommandButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(shellCommandTextBox.Text))
            {
                LogMessage("Please enter a shell command", LogLevel.Error);
                return;
            }

            OnOperationStateChanged(true, "Execute Shell Command");
            
            string command = shellCommandTextBox.Text;
            string result = adbManager.ExecuteShellCommand(command);
            
            LogMessage($"Command: {command}", LogLevel.Info);
            LogMessage($"Result: {result}", LogLevel.Success);
            
            OnOperationStateChanged(false, "Execute Shell Command");
        }

        private void RebootButton_Click(object sender, EventArgs e)
        {
            string mode = rebootModeCombo.SelectedItem.ToString();
            OnOperationStateChanged(true, $"Reboot to {mode}");
            
            bool success = adbManager.RebootDevice(mode);
            LogMessage(success ? $"Device rebooted to {mode} mode" : $"Failed to reboot to {mode} mode", 
                success ? LogLevel.Success : LogLevel.Error);
            
            OnOperationStateChanged(false, $"Reboot to {mode}");
        }

        private void InstallApkButton_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog ofd = new OpenFileDialog())
            {
                ofd.Filter = "APK Files (*.apk)|*.apk|All Files (*.*)|*.*";
                ofd.Title = "Select APK File";
                
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    OnOperationStateChanged(true, "Install APK");
                    
                    bool success = adbManager.InstallApk(ofd.FileName);
                    LogMessage(success ? "APK installed successfully" : "APK installation failed", 
                        success ? LogLevel.Success : LogLevel.Error);
                    
                    OnOperationStateChanged(false, "Install APK");
                }
            }
        }

        private void UninstallAppButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(packageNameTextBox.Text))
            {
                LogMessage("Please enter package name", LogLevel.Error);
                return;
            }

            OnOperationStateChanged(true, "Uninstall App");
            
            bool success = adbManager.UninstallApp(packageNameTextBox.Text);
            LogMessage(success ? "App uninstalled successfully" : "App uninstall failed", 
                success ? LogLevel.Success : LogLevel.Error);
            
            OnOperationStateChanged(false, "Uninstall App");
        }

        private void EnableAdbButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Enable ADB");
            
            bool success = adbManager.EnableAdb();
            LogMessage(success ? "ADB enabled successfully" : "Failed to enable ADB", 
                success ? LogLevel.Success : LogLevel.Error);
            
            OnOperationStateChanged(false, "Enable ADB");
        }

        private void DisableAdbButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Disable ADB");
            
            bool success = adbManager.DisableAdb();
            LogMessage(success ? "ADB disabled successfully" : "Failed to disable ADB", 
                success ? LogLevel.Success : LogLevel.Error);
            
            OnOperationStateChanged(false, "Disable ADB");
        }

        private void RootDeviceButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Root Device");
            
            bool success = adbManager.RootDevice();
            if (success)
            {
                rootStatusLabel.Text = "Root: Rooted";
                LogMessage("Device rooted successfully", LogLevel.Success);
            }
            else
            {
                LogMessage("Failed to root device", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Root Device");
        }

        private void UnrootDeviceButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Unroot Device");
            
            bool success = adbManager.UnrootDevice();
            if (success)
            {
                rootStatusLabel.Text = "Root: Not Rooted";
                LogMessage("Device unrooted successfully", LogLevel.Success);
            }
            else
            {
                LogMessage("Failed to unroot device", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Unroot Device");
        }

        private void OnOperationStateChanged(bool isRunning, string operationName)
        {
            OperationStateChanged?.Invoke(isRunning, operationName);
        }

        private void OnLogMessage(string message, LogLevel level, bool newLine, bool bold)
        {
            LogMessage?.Invoke(message, level, newLine, bold);
            textOutput.LogMessage(message, level, newLine, bold);
        }
        #endregion

        #region Helper Methods
        private void RefreshDevicesList()
        {
            connectedDevicesCombo.Items.Clear();
            
            var devices = adbManager.GetConnectedDevices();
            foreach (string device in devices)
            {
                connectedDevicesCombo.Items.Add(device);
            }
            
            if (connectedDevicesCombo.Items.Count > 0)
            {
                connectedDevicesCombo.SelectedIndex = 0;
                LogMessage($"Found {connectedDevicesCombo.Items.Count} ADB device(s)", LogLevel.Info);
            }
            else
            {
                LogMessage("No ADB devices found", LogLevel.Warning);
            }
        }

        private void EnableConnectedControls(bool enabled)
        {
            disconnectAdbButton.Enabled = enabled;
            shellCommandButton.Enabled = enabled;
            rebootModeCombo.Enabled = enabled;
            rebootButton.Enabled = enabled;
            installApkButton.Enabled = enabled;
            uninstallAppButton.Enabled = enabled;
            enableAdbButton.Enabled = enabled;
            disableAdbButton.Enabled = enabled;
            rootDeviceButton.Enabled = enabled;
            unrootDeviceButton.Enabled = enabled;
            
            connectAdbButton.Enabled = !enabled;
        }

        private void UpdateUIState()
        {
            // Update UI based on operation state
        }

        private void LogMessage(string message, LogLevel level = LogLevel.Info, bool newLine = true, bool bold = false)
        {
            textOutput?.LogMessage(message, level, newLine, bold);
        }
        #endregion
    }

    /// <summary>
    /// ADB manager for Android Debug Bridge operations
    /// </summary>
    public class AdbManager : BasePlatformManager
    {
        private DeviceConnectionState connectionState = DeviceConnectionState.Disconnected;
        private DeviceInfo currentDevice;
        private string currentDeviceId;

        public override bool ConnectDevice()
        {
            return ConnectDevice("default");
        }

        public bool ConnectDevice(string deviceId)
        {
            try
            {
                OnLogMessage($"Connecting to ADB device: {deviceId}", LogLevel.Info);
                System.Threading.Thread.Sleep(800);

                connectionState = DeviceConnectionState.ADB;
                currentDeviceId = deviceId;
                currentDevice = new DeviceInfo
                {
                    Model = "Android Device",
                    Manufacturer = "Generic",
                    AndroidVersion = "11",
                    Platform = PlatformType.Generic,
                    SerialNumber = deviceId,
                    BuildNumber = "RQ3A.210905.001"
                };

                OnLogMessage("ADB device connected successfully", LogLevel.Success);
                return true;
            }
            catch (Exception ex)
            {
                OnLogMessage($"ADB connection failed: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        public override bool DisconnectDevice()
        {
            connectionState = DeviceConnectionState.Disconnected;
            currentDevice = null;
            currentDeviceId = null;
            OnLogMessage("ADB device disconnected", LogLevel.Warning);
            return true;
        }

        public override DeviceConnectionState GetConnectionState()
        {
            return connectionState;
        }

        public DeviceInfo ReadDeviceInfo()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return null;

            OnLogMessage("Reading device information via ADB...", LogLevel.Info);
            System.Threading.Thread.Sleep(500);

            return currentDevice;
        }

        public List<string> GetConnectedDevices()
        {
            OnLogMessage("Scanning for ADB devices...", LogLevel.Info);

            // Simulate device discovery
            var devices = new List<string>
            {
                "emulator-5554",
                "192.168.1.100:5555",
                "ABC123DEF456"
            };

            return devices;
        }

        public string ExecuteShellCommand(string command)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return "Error: No device connected";

            OnLogMessage($"Executing: adb shell {command}", LogLevel.Info);
            System.Threading.Thread.Sleep(1000);

            // Simulate command execution
            string result = command.ToLower() switch
            {
                "getprop ro.build.version.release" => "11",
                "getprop ro.product.model" => "Android Device",
                "whoami" => "shell",
                "id" => "uid=2000(shell) gid=2000(shell)",
                _ => $"Command '{command}' executed successfully"
            };

            return result;
        }

        public bool RebootDevice(string mode)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            string adbCommand = mode.ToLower() switch
            {
                "bootloader" => "adb reboot bootloader",
                "recovery" => "adb reboot recovery",
                "download" => "adb reboot download",
                _ => "adb reboot"
            };

            OnLogMessage($"Executing: {adbCommand}", LogLevel.Info);
            System.Threading.Thread.Sleep(1000);
            OnLogMessage($"Device reboot to {mode} initiated", LogLevel.Success);
            return true;
        }

        public bool InstallApk(string apkPath)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage($"Installing APK: {System.IO.Path.GetFileName(apkPath)}", LogLevel.Info);
            OnLogMessage($"Executing: adb install \"{apkPath}\"", LogLevel.Info);
            System.Threading.Thread.Sleep(3000);
            OnLogMessage("APK installation completed successfully", LogLevel.Success);
            return true;
        }

        public bool UninstallApp(string packageName)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage($"Uninstalling package: {packageName}", LogLevel.Info);
            OnLogMessage($"Executing: adb uninstall {packageName}", LogLevel.Info);
            System.Threading.Thread.Sleep(2000);
            OnLogMessage("App uninstalled successfully", LogLevel.Success);
            return true;
        }

        public bool EnableAdb()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage("Enabling ADB debugging...", LogLevel.Info);
            OnLogMessage("Executing: adb shell settings put global adb_enabled 1", LogLevel.Info);
            System.Threading.Thread.Sleep(1500);
            OnLogMessage("ADB debugging enabled", LogLevel.Success);
            return true;
        }

        public bool DisableAdb()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage("Disabling ADB debugging...", LogLevel.Warning);
            OnLogMessage("Executing: adb shell settings put global adb_enabled 0", LogLevel.Info);
            System.Threading.Thread.Sleep(1500);
            OnLogMessage("ADB debugging disabled", LogLevel.Success);
            return true;
        }

        public bool RootDevice()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage("Attempting to root device...", LogLevel.Warning);
            OnLogMessage("WARNING: This may void your warranty!", LogLevel.Error);
            OnLogMessage("Executing root exploit...", LogLevel.Info);
            System.Threading.Thread.Sleep(4000);
            OnLogMessage("Device rooted successfully", LogLevel.Success);
            OnLogMessage("Root access granted", LogLevel.Success);
            return true;
        }

        public bool UnrootDevice()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage("Removing root access...", LogLevel.Info);
            OnLogMessage("Restoring original system files...", LogLevel.Info);
            System.Threading.Thread.Sleep(3000);
            OnLogMessage("Root access removed successfully", LogLevel.Success);
            return true;
        }
    }
}
