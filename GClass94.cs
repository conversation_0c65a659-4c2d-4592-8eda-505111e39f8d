using System;
using System.Collections.Generic;
using System.IO;

public class GClass94
{
	public readonly Version A927D526;

	public readonly uint C102E5B1;

	public readonly ulong ulong_0;

	public readonly ulong ulong_1;

	public readonly ulong D8901D3F;

	public readonly ulong ulong_2;

	public readonly Guid guid_0;

	public readonly ulong F7885D87;

	public readonly uint DF80B293;

	public readonly uint B296561E;

	public readonly GClass96[] gclass96_0;

	public GClass94(string B0B3A68F)
	{
		Class607.B630A78B.object_0[571](this);
		try
		{
			FileStream e6240C = Class607.B630A78B.object_0[792](B0B3A68F, FileMode.Open, FileAccess.Read);
			GClass95 gClass = new GClass95(smethod_1(e6240C, 512uL, 512uL));
			A927D526 = gClass.version_0;
			C102E5B1 = gClass.C597679E;
			ulong_0 = gClass.ulong_0;
			ulong_1 = gClass.E6827325;
			D8901D3F = gClass.ulong_1;
			ulong_2 = gClass.ulong_2;
			guid_0 = gClass.B09DE43C;
			F7885D87 = gClass.ulong_3;
			DF80B293 = gClass.uint_1;
			B296561E = gClass.uint_2;
			List<GClass96> list = new List<GClass96>();
			bool flag = true;
			for (ulong num = gClass.ulong_3; num < gClass.ulong_3 + gClass.uint_1 / (512uL / (ulong)gClass.uint_2); num++)
			{
				byte[] byte_ = smethod_1(e6240C, 512L * num, 512uL);
				for (uint num2 = 0u; num2 < 512; num2 += gClass.uint_2)
				{
					GClass96 gClass2 = new GClass96(smethod_0(byte_, num2, gClass.uint_2));
					if (!Class607.B630A78B.object_0[930](gClass2.guid_1, Class607.B630A78B.object_0[188]("00000000-0000-0000-0000-000000000000")))
					{
						list.Add(gClass2);
						continue;
					}
					flag = false;
					break;
				}
				if (!flag)
				{
					break;
				}
			}
			gclass96_0 = list.ToArray();
		}
		catch
		{
			throw Class607.B630A78B.object_0[778]("No GPT found. Please use Get-MBR cmdlet");
		}
	}

	internal static byte[] smethod_0(byte[] byte_0, uint E20BC92A, uint uint_0)
	{
		byte[] array = new byte[uint_0];
		Class607.B630A78B.object_0[912](byte_0, E20BC92A, array, 0L, array.Length);
		return array;
	}

	internal static byte[] smethod_1(FileStream E6240C31, ulong ulong_3, ulong ulong_4)
	{
		if (ulong_4 % 512L > 0L)
		{
			throw Class607.B630A78B.object_0[1005]("Size parameter must be divisible by 512");
		}
		if (ulong_3 % 512L > 0L)
		{
			throw Class607.B630A78B.object_0[1005]("Offset parameter must be divisible by 512");
		}
		Class607.B630A78B.object_0[636](E6240C31, (long)ulong_3);
		byte[] array = new byte[ulong_4];
		try
		{
			int num = Class607.B630A78B.object_0[53](E6240C31, array, 0, array.Length);
			if (num != array.Length && num > array.Length)
			{
				throw Class607.B630A78B.object_0[778]("The readDrive method read more bytes from disk than expected.");
			}
		}
		catch (ArgumentNullException)
		{
			throw Class607.B630A78B.object_0[875]("The readDrive method experienced an ArgumentNullException.");
		}
		catch (ArgumentOutOfRangeException)
		{
			throw Class607.B630A78B.object_0[144]("The readDrive method experienced an ArgumentOutOfRangeException.");
		}
		catch (EndOfStreamException)
		{
			throw Class607.B630A78B.object_0[802]("The readDrive method experienced an EndOfStreamException.");
		}
		catch (IOException)
		{
			throw Class607.B630A78B.object_0[1178]("The readDrive method experienced an IOException.");
		}
		catch (ArgumentException)
		{
			throw Class607.B630A78B.object_0[1005]("The readDrive method experienced an ArgumentException");
		}
		catch (ObjectDisposedException)
		{
			throw Class607.B630A78B.object_0[200]("The readDrive method experienced an ObjectDisposedException");
		}
		return array;
	}

	internal GClass94(byte[] F6A13D95)
	{
		Class607.B630A78B.object_0[571](this);
		GClass95 gClass = new GClass95(smethod_0(F6A13D95, 0u, 512u));
		A927D526 = gClass.version_0;
		C102E5B1 = gClass.C597679E;
		ulong_0 = gClass.ulong_0;
		ulong_1 = gClass.E6827325;
		D8901D3F = gClass.ulong_1;
		ulong_2 = gClass.ulong_2;
		guid_0 = gClass.B09DE43C;
		F7885D87 = gClass.ulong_3;
		DF80B293 = gClass.uint_1;
		B296561E = gClass.uint_2;
		List<GClass96> list = new List<GClass96>();
		for (uint num = 0u; num < F6A13D95.Length - 512; num += gClass.uint_2)
		{
			GClass96 gClass2 = new GClass96(smethod_0(F6A13D95, num + 512, gClass.uint_2));
			if (Class607.B630A78B.object_0[930](gClass2.guid_0, Class607.B630A78B.object_0[188]("00000000-0000-0000-0000-000000000000")))
			{
				break;
			}
			list.Add(gClass2);
		}
		gclass96_0 = list.ToArray();
	}

	public static byte[] FD1D8D18(string string_0)
	{
		return smethod_1(Class607.B630A78B.object_0[792](string_0, FileMode.Open, FileAccess.Read), 512uL, 512uL);
	}

	public static GClass94 EF89DB39(string string_0)
	{
		return new GClass94(FD1D8D18(string_0));
	}

	public GClass96[] EDBCFC07()
	{
		return gclass96_0;
	}
}
