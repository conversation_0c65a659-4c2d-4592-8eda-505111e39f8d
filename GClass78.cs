using System.Collections.Generic;

public class GClass78
{
	public uint uint_0 = 0u;

	public uint uint_1 = 4u;

	public uint D783180E = 1024u;

	public uint uint_2 = 1028u;

	public uint uint_3 = 1032u;

	public uint AB1F8009 = 1040u;

	public uint uint_4 = 1044u;

	public uint uint_5 = 1048u;

	public uint uint_6 = 1052u;

	public uint uint_7 = 1056u;

	public uint uint_8 = 1088u;

	public uint B5B9AB11 = 1144u;

	public uint uint_9 = 2048u;

	public uint B0960CB0 = 2052u;

	public uint EA3E08AE = 2056u;

	public uint uint_10 = 3072u;

	public uint uint_11 = 3076u;

	public uint EF93BF97 = 3080u;

	public uint uint_12 = 3084u;

	public uint uint_13 = 3088u;

	public uint BB23D1A2 = 3092u;

	public uint A1A64D09 = 3096u;

	public uint uint_14 = 3100u;

	public uint uint_15 = 3104u;

	public uint DC370034 = 3108u;

	public uint C3282089 = 3112u;

	public uint uint_16 = 3116u;

	public uint uint_17 = 3120u;

	public uint uint_18 = 3124u;

	public uint uint_19 = 3128u;

	public uint uint_20 = 3132u;

	public uint uint_21 = 3136u;

	public Dictionary<string, uint> F4237397;

	public uint uint_22;

	public A48DCE0E C91C271D;

	public GDelegate28 CD135688;

	public bool method_0(string string_0, uint uint_23)
	{
		if (F4237397.ContainsKey(string_0))
		{
			uint num = F4237397[string_0] + uint_22;
			return C91C271D(num, uint_23);
		}
		return false;
	}

	public uint method_1(string string_0)
	{
		if (F4237397.ContainsKey(string_0))
		{
			uint num = F4237397[string_0] + uint_22;
			List<uint> list = CD135688(num);
			if (list.Count > 0)
			{
				return list[0];
			}
		}
		return 0u;
	}

	public GClass78(GClass77 B234EE36)
	{
		Class607.B630A78B.object_0[571](this);
		F4237397 = new Dictionary<string, uint>
		{
			{ "GCPU_REG_CTL", 0u },
			{ "GCPU_REG_MSC", 4u },
			{ "GCPU_REG_PC_CTL", 1024u },
			{ "GCPU_REG_MEM_ADDR", 1028u },
			{ "GCPU_REG_MEM_DATA", 1032u },
			{ "GCPU_REG_READ_REG", 1040u },
			{ "GCPU_REG_MONCTL", 1044u },
			{ "GCPU_REG_DRAM_MON", 1048u },
			{ "GCPU_REG_CYC", 1052u },
			{ "GCPU_REG_DRAM_INST_BASE", 1056u },
			{ "GCPU_REG_TRAP_START", 1088u },
			{ "GCPU_REG_TRAP_END", 1144u },
			{ "GCPU_REG_INT_SET", 2048u },
			{ "GCPU_REG_INT_CLR", 2052u },
			{ "GCPU_REG_INT_EN", 2056u },
			{ "GCPU_REG_MEM_CMD", 3072u },
			{ "GCPU_REG_MEM_P0", 3076u },
			{ "GCPU_REG_MEM_P1", 3080u },
			{ "GCPU_REG_MEM_P2", 3084u },
			{ "GCPU_REG_MEM_P3", 3088u },
			{ "GCPU_REG_MEM_P4", 3092u },
			{ "GCPU_REG_MEM_P5", 3096u },
			{ "GCPU_REG_MEM_P6", 3100u },
			{ "GCPU_REG_MEM_P7", 3104u },
			{ "GCPU_REG_MEM_P8", 3108u },
			{ "GCPU_REG_MEM_P9", 3112u },
			{ "GCPU_REG_MEM_P10", 3116u },
			{ "GCPU_REG_MEM_P11", 3120u },
			{ "GCPU_REG_MEM_P12", 3124u },
			{ "GCPU_REG_MEM_P13", 3128u },
			{ "GCPU_REG_MEM_P14", 3132u },
			{ "GCPU_REG_MEM_Slot", 3136u }
		};
		uint_22 = B234EE36.F814F009;
		C91C271D = B234EE36.a48DCE0E_0;
		CD135688 = B234EE36.gdelegate28_0;
	}
}
