using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;

/// <summary>
/// Main application form for MotoKingPro - Mobile device repair and flashing tool
/// Supports Qualcomm, MediaTek, Motorola devices and ADB operations
/// </summary>
public class MainForm : Form
{
	/// <summary>
	/// Helper class for Qualcomm operations UI state management
	/// </summary>
	[CompilerGenerated]
	private sealed class QualcommOperationHelper
	{
		public MainForm parentForm;
		public bool isOperationRunning;

		public QualcommOperationHelper()
		{
			// Constructor call through obfuscated method
		}

		internal void UpdateUIState()
		{
			// Enable/disable UI controls based on operation state
			parentForm.tabSelector.Enabled = !isOperationRunning;
			parentForm.historyButton.Enabled = !isOperationRunning;
			parentForm.accountButton.Enabled = !isOperationRunning;
		}

		internal void FocusQualcommTab()
		{
			// Focus on Qualcomm tab's text output
			parentForm.qualcommPage.textOutput.Focus();
		}
	}

	/// <summary>
	/// Helper class for ADB/Services operations UI state management
	/// </summary>
	[CompilerGenerated]
	private sealed class ServicesOperationHelper
	{
		public MainForm parentForm;
		public bool isOperationRunning;

		public ServicesOperationHelper()
		{
			// Constructor call through obfuscated method
		}

		internal void UpdateUIState()
		{
			// Enable/disable UI controls based on operation state
			parentForm.tabSelector.Enabled = !isOperationRunning;
			parentForm.historyButton.Enabled = !isOperationRunning;
			parentForm.accountButton.Enabled = !isOperationRunning;
		}

		internal void FocusServicesTab()
		{
			// Focus on Services tab's text output
			parentForm.servicesPage.textOutput.Focus();
		}
	}

	/// <summary>
	/// Helper class for Motorola operations UI state management
	/// </summary>
	[CompilerGenerated]
	private sealed class MotorolaOperationHelper
	{
		public MainForm parentForm;
		public bool isOperationRunning;

		public MotorolaOperationHelper()
		{
			// Constructor call through obfuscated method
		}

		internal void UpdateUIState()
		{
			// Enable/disable UI controls based on operation state
			parentForm.tabSelector.Enabled = !isOperationRunning;
			parentForm.historyButton.Enabled = !isOperationRunning;
			parentForm.accountButton.Enabled = !isOperationRunning;
		}

		internal void FocusMotorolaTab()
		{
			// Focus on Motorola tab's text output
			parentForm.motorolaPage.textOutput.Focus();
		}
	}

	/// <summary>
	/// Helper class for MediaTek operations UI state management
	/// </summary>
	[CompilerGenerated]
	private sealed class MediaTekOperationHelper
	{
		public MainForm parentForm;
		public bool isOperationRunning;

		public MediaTekOperationHelper()
		{
			// Constructor call through obfuscated method
		}

		internal void UpdateUIState()
		{
			// Enable/disable UI controls based on operation state
			parentForm.tabSelector.Enabled = !isOperationRunning;
			parentForm.historyButton.Enabled = !isOperationRunning;
			parentForm.accountButton.Enabled = !isOperationRunning;
		}

		internal void FocusMediaTekTab()
		{
			// Focus on MediaTek tab's text output
			parentForm.mediaTekPage.textOutput.Focus();
		}
	}

	// Mouse drag support for borderless window
	private Point lastMousePosition = new Point();

	// Custom control for device status display
	private GControl0 deviceStatusControl = new GControl0();

	// Operation tracking objects for each platform
	private FD3D7D02 servicesOperation;      // ADB/Services operations
	private FD3D7D02 qualcommOperation;      // Qualcomm operations
	private FD3D7D02 mediaTekOperation;      // MediaTek operations
	private FD3D7D02 motorolaOperation;      // Motorola operations

	private bool isClosingForced = false;
	private IContainer components = null;

	// UI Controls
	private Panel navigationPanel;
	private Button maximizeButton;
	private Button minimizeButton;
	private Button closeButton;
	private A09F0208 titleLabel;
	private GControl2 tabSelector;
	private GControl1 mainTabControl;

	// Tab Pages
	private TabPage motorolaTabPage;
	private TabPage qualcommTabPage;
	private TabPage servicesTabPage;
	private TabPage mediaTekTabPage;

	// Platform-specific pages
	private C220CEB6 mediaTekPage;
	private EE97B6A8 qualcommPage;
	private AA16C934 motorolaPage;
	private Button historyButton;
	private EAB5ABA9 servicesPage;
	private Panel mainContentPanel;
	private Button accountButton;
	private Label versionLabel;
	private Button signOutButton;

	public GForm0()
	{
		Class607.B630A78B.object_0[1016](this);
		A42B948A();
		new GClass101().method_1(this);
		ee97B6A8_0.FA00212E(this);
		aa16C934_0.CC1D9E9C(this);
		eab5ABA9_0.FB06508C(this);
		ee97B6A8_0.D3027184 += method_1;
		c220CEB6_0.Event_0 += D4B0D232;
		aa16C934_0.Event_0 += BB354495;
		eab5ABA9_0.AF368392 += method_0;
		Class607.B630A78B.object_0[781](DB3962B0).Add(CC20ED1A);
		Class607.B630A78B.object_0[1278](CC20ED1A, DockStyle.Fill);
		Class607.B630A78B.object_0[128](CC20ED1A, B7128B0A: false);
		Class607.B630A78B.object_0[1175](label_0, D09F0D3B.smethod_5(1161364u));
	}

	private unsafe void method_0(bool bool_0, string string_0)
	{
		D99AC113 d99AC = new D99AC113();
		d99AC.D28D5738 = this;
		d99AC.bool_0 = bool_0;
		eab5ABA9_0.DE38B30B(d99AC.bool_0);
		Class607.B630A78B.object_0[86](this, Class607.B630A78B.object_0[1210](d99AC, (nint)__ldftn(D99AC113.method_0)));
		if (d99AC.bool_0)
		{
			DateTime startedAt = Class607.B630A78B.object_0[402]();
			fd3D7D02_0 = new FD3D7D02
			{
				StartedAt = startedAt,
				OperationName = string_0,
				Tab = "Services"
			};
			eab5ABA9_0.list_0.Clear();
			Class607.B630A78B.object_0[86](this, Class607.B630A78B.object_0[1210](d99AC, (nint)__ldftn(D99AC113.method_1)));
		}
		else
		{
			DateTime dateTime = Class607.B630A78B.object_0[402]();
			eab5ABA9_0.A1853E84("Operation: ");
			eab5ABA9_0.A1853E84(string_0, EF1F389C.Yellow, bool_0: false, F93C7108: true);
			eab5ABA9_0.A1853E84("Elapsed time: ");
			TimeSpan A02E7C = Class607.B630A78B.object_0[739](dateTime, fd3D7D02_0.StartedAt);
			eab5ABA9_0.A1853E84(Class607.B630A78B.object_0[554](ref A02E7C, "hh\\:mm\\:ss"), EF1F389C.Yellow, bool_0: false, F93C7108: true);
			fd3D7D02_0.EndedAt = dateTime;
			fd3D7D02_0.Items = eab5ABA9_0.list_0;
			C8087599.E41A7B83.Operations.Add(fd3D7D02_0);
			GClass112.D2A17E03();
		}
	}

	private unsafe void BB354495(bool B79E4A32, string string_0)
	{
		Class20 @class = new Class20();
		@class.BE317C9A = this;
		@class.bool_0 = B79E4A32;
		aa16C934_0.F28F709E(@class.bool_0);
		Class607.B630A78B.object_0[86](this, Class607.B630A78B.object_0[1210](@class, (nint)__ldftn(Class20.C01AD19C)));
		if (@class.bool_0)
		{
			DateTime startedAt = Class607.B630A78B.object_0[402]();
			B28DA83F = new FD3D7D02
			{
				StartedAt = startedAt,
				OperationName = string_0,
				Tab = "Motorola"
			};
			aa16C934_0.list_1.Clear();
			Class607.B630A78B.object_0[86](this, Class607.B630A78B.object_0[1210](@class, (nint)__ldftn(Class20.method_0)));
		}
		else
		{
			DateTime dateTime = Class607.B630A78B.object_0[402]();
			aa16C934_0.method_2("Operation: ");
			aa16C934_0.method_2(string_0, EF1F389C.Yellow, B1B0C820: false, bool_0: true);
			aa16C934_0.method_2("Elapsed time: ");
			TimeSpan A02E7C = Class607.B630A78B.object_0[739](dateTime, B28DA83F.StartedAt);
			aa16C934_0.method_2(Class607.B630A78B.object_0[554](ref A02E7C, "hh\\:mm\\:ss"), EF1F389C.Yellow, B1B0C820: false, bool_0: true);
			B28DA83F.EndedAt = dateTime;
			B28DA83F.Items = aa16C934_0.list_1;
			C8087599.E41A7B83.Operations.Add(B28DA83F);
			GClass112.D2A17E03();
		}
	}

	private unsafe void D4B0D232(bool A3A94AA5, string CAB7798C)
	{
		B6361807 b = new B6361807();
		b.CCA89494 = this;
		b.bool_0 = A3A94AA5;
		c220CEB6_0.method_3(b.bool_0);
		Class607.B630A78B.object_0[86](this, Class607.B630A78B.object_0[1210](b, (nint)__ldftn(B6361807.DF0A3321)));
		if (b.bool_0)
		{
			DateTime startedAt = Class607.B630A78B.object_0[402]();
			fd3D7D02_2 = new FD3D7D02
			{
				StartedAt = startedAt,
				OperationName = CAB7798C,
				Tab = "Mediatek"
			};
			c220CEB6_0.FD014DB0.Clear();
			Class607.B630A78B.object_0[86](this, Class607.B630A78B.object_0[1210](b, (nint)__ldftn(B6361807.method_0)));
		}
		else
		{
			DateTime dateTime = Class607.B630A78B.object_0[402]();
			c220CEB6_0.AC2B0F9A("Operation: ");
			c220CEB6_0.AC2B0F9A(CAB7798C, EF1F389C.Yellow, bool_0: false, bool_1: true);
			c220CEB6_0.AC2B0F9A("Elapsed time: ");
			TimeSpan A02E7C = Class607.B630A78B.object_0[739](dateTime, fd3D7D02_2.StartedAt);
			c220CEB6_0.AC2B0F9A(Class607.B630A78B.object_0[554](ref A02E7C, "hh\\:mm\\:ss"), EF1F389C.Yellow, bool_0: false, bool_1: true);
			fd3D7D02_2.EndedAt = dateTime;
			fd3D7D02_2.Items = c220CEB6_0.FD014DB0;
			C8087599.E41A7B83.Operations.Add(fd3D7D02_2);
			GClass112.D2A17E03();
		}
	}

	private unsafe void method_1(bool bool_0, string FAABEB2F)
	{
		Class19 @class = new Class19();
		@class.C21E1A92 = this;
		@class.bool_0 = bool_0;
		ee97B6A8_0.F2079996(@class.bool_0);
		Class607.B630A78B.object_0[86](this, Class607.B630A78B.object_0[1210](@class, (nint)__ldftn(Class19.method_0)));
		if (@class.bool_0)
		{
			DateTime startedAt = Class607.B630A78B.object_0[402]();
			fd3D7D02_1 = new FD3D7D02
			{
				StartedAt = startedAt,
				OperationName = FAABEB2F,
				Tab = "Qualcomm"
			};
			ee97B6A8_0.list_2.Clear();
			Class607.B630A78B.object_0[86](this, Class607.B630A78B.object_0[1210](@class, (nint)__ldftn(Class19.A106B51B)));
		}
		else
		{
			DateTime dateTime = Class607.B630A78B.object_0[402]();
			ee97B6A8_0.method_3("Operation: ");
			ee97B6A8_0.method_3(FAABEB2F, EF1F389C.Yellow, bool_0: false, bool_1: true);
			ee97B6A8_0.method_3("Elapsed time: ");
			TimeSpan A02E7C = Class607.B630A78B.object_0[739](dateTime, fd3D7D02_1.StartedAt);
			ee97B6A8_0.method_3(Class607.B630A78B.object_0[554](ref A02E7C, "hh\\:mm\\:ss"), EF1F389C.Yellow, bool_0: false, bool_1: true);
			fd3D7D02_1.EndedAt = dateTime;
			fd3D7D02_1.Items = ee97B6A8_0.list_2;
			C8087599.E41A7B83.Operations.Add(fd3D7D02_1);
			GClass112.D2A17E03();
		}
	}

	private void method_2(object sender, EventArgs e)
	{
		new GClass128().E8AA1C3C(new object[3] { this, sender, e }, 22419629);
	}

	private void ED2FAC34(object sender, EventArgs e)
	{
		new GClass128().AD84C1A2(new object[3] { this, sender, e }, 183807);
	}

	private void AE0D42BF(object sender, EventArgs e)
	{
		new GClass128().B4154402(new object[3] { this, sender, e }, 266231);
	}

	private void BD3B01A2(object sender, MouseEventArgs e)
	{
		if (Class607.B630A78B.object_0[944](e) == MouseButtons.Left)
		{
			C4B51586 = Class607.B630A78B.object_0[760](Class607.B630A78B.object_0[841](e), Class607.B630A78B.object_0[913](e));
		}
	}

	private void method_3(object sender, MouseEventArgs e)
	{
		if (Class607.B630A78B.object_0[577](C4B51586, Class607.B630A78B.object_0[1245]()))
		{
			Class607.B630A78B.object_0[579](this, Class607.B630A78B.object_0[760](Class607.B630A78B.object_0[877](this) + Class607.B630A78B.object_0[841](e) - Class607.B630A78B.object_0[372](ref C4B51586), Class607.B630A78B.object_0[1135](this) + Class607.B630A78B.object_0[913](e) - Class607.B630A78B.object_0[1229](ref C4B51586)));
		}
	}

	private void method_4(object sender, MouseEventArgs e)
	{
		if (Class607.B630A78B.object_0[944](e) == MouseButtons.Left)
		{
			C4B51586 = Class607.B630A78B.object_0[1245]();
		}
	}

	private void EEBF9695()
	{
		D0145D9F.BEA4093A();
		Class69.DC3F988F();
		GClass118.smethod_0();
		D0145D9F.smethod_24();
		DCAC9A27 dCAC9A = new DCAC9A27();
		Class607.B630A78B.object_0[964](dCAC9A, bool_0: false);
		Class607.B630A78B.object_0[590](dCAC9A, FormBorderStyle.None);
		Class607.B630A78B.object_0[128](dCAC9A, B7128B0A: false);
		Class607.B630A78B.object_0[880](dCAC9A, FormWindowState.Minimized);
		Class607.B630A78B.object_0[604](dCAC9A);
		Class607.B630A78B.object_0[812](dCAC9A);
		while (!GClass112.FD237E8B)
		{
			Class607.B630A78B.object_0[697](Class607.B630A78B.object_0[316](50));
			Class607.B630A78B.object_0[167]();
		}
	}

	private unsafe void method_5(object sender, EventArgs e)
	{
		Thread object_ = Class607.B630A78B.object_0[1033](Class607.B630A78B.object_0[439](this, (nint)__ldftn(GForm0.EEBF9695)));
		Class607.B630A78B.object_0[138](object_, ApartmentState.MTA);
		Class607.B630A78B.object_0[403](object_);
		FD886F9B();
		c220CEB6_0.method_35();
	}

	public void FD886F9B()
	{
		string b41CC = Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\loader\\auto");
		string[] array = Class607.B630A78B.object_0[127](b41CC, "*", SearchOption.AllDirectories);
		string[] array2 = array;
		foreach (string text in array2)
		{
			string key = Class607.B630A78B.object_0[386](text);
			GClass112.dictionary_0[key] = text;
		}
	}

	private void B4BA712D(object sender, FormClosingEventArgs e)
	{
		if (!E2BF872E)
		{
			DialogResult dialogResult = GClass110.C2AB1F9F(D09F0D3B.smethod_5(1161536u), "Question", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1, MessageBoxOptions.DefaultDesktopOnly);
			if (dialogResult == DialogResult.Yes)
			{
				GClass112.FD237E8B = true;
				Class607.B630A78B.object_0[958](0);
				Class607.B630A78B.object_0[502]();
				Class607.B630A78B.object_0[982](Class607.B630A78B.object_0[955]());
			}
			Class607.B630A78B.object_0[839](e, bool_0: true);
		}
	}

	private void C7BA288B(object sender, EventArgs e)
	{
		new GClass128().AD84C1A2(new object[3] { this, sender, e }, 341457);
	}

	private void method_6(object sender, EventArgs e)
	{
	}

	private void E08FDE38(object sender, EventArgs e)
	{
		new GClass128().DFB12B0F(new object[3] { this, sender, e }, 234159);
	}

	private void CD2D780D(object sender, EventArgs e)
	{
		new GClass128().E6A33692(new object[3] { this, sender, e }, 82286);
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && icontainer_0 != null)
		{
			icontainer_0.Dispose();
		}
		Class607.B630A78B.object_0[1072](this, disposing);
	}

	private unsafe void A42B948A()
	{
		ComponentResourceManager object_ = Class607.B630A78B.object_0[566](Class607.B630A78B.object_0[6](typeof(GForm0).TypeHandle));
		panel_0 = Class607.B630A78B.object_0[853]();
		label_0 = Class607.B630A78B.object_0[113]();
		button_3 = Class607.B630A78B.object_0[1112]();
		DD09338B = Class607.B630A78B.object_0[1112]();
		A6128E19 = Class607.B630A78B.object_0[1112]();
		a09F0208_0 = new A09F0208();
		button_0 = Class607.B630A78B.object_0[1112]();
		button_1 = Class607.B630A78B.object_0[1112]();
		button_2 = Class607.B630A78B.object_0[1112]();
		DB3962B0 = Class607.B630A78B.object_0[853]();
		gcontrol1_0 = new GControl1();
		tabPage_1 = Class607.B630A78B.object_0[139]();
		eab5ABA9_0 = new EAB5ABA9();
		tabPage_0 = Class607.B630A78B.object_0[139]();
		aa16C934_0 = new AA16C934();
		tabPage_2 = Class607.B630A78B.object_0[139]();
		c220CEB6_0 = new C220CEB6();
		F9ABDD17 = Class607.B630A78B.object_0[139]();
		ee97B6A8_0 = new EE97B6A8();
		DE9CD49C = new GControl2();
		Class607.B630A78B.object_0[549](panel_0);
		Class607.B630A78B.object_0[549](DB3962B0);
		Class607.B630A78B.object_0[549](gcontrol1_0);
		Class607.B630A78B.object_0[549](tabPage_1);
		Class607.B630A78B.object_0[549](tabPage_0);
		Class607.B630A78B.object_0[549](tabPage_2);
		Class607.B630A78B.object_0[549](F9ABDD17);
		Class607.B630A78B.object_0[550](this);
		Class607.B630A78B.object_0[823](panel_0, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](panel_0).Add(label_0);
		Class607.B630A78B.object_0[781](panel_0).Add(button_3);
		Class607.B630A78B.object_0[781](panel_0).Add(DD09338B);
		Class607.B630A78B.object_0[781](panel_0).Add(A6128E19);
		Class607.B630A78B.object_0[781](panel_0).Add(a09F0208_0);
		Class607.B630A78B.object_0[781](panel_0).Add(button_0);
		Class607.B630A78B.object_0[781](panel_0).Add(button_1);
		Class607.B630A78B.object_0[781](panel_0).Add(button_2);
		Class607.B630A78B.object_0[1278](panel_0, DockStyle.Top);
		Class607.B630A78B.object_0[388](panel_0, Class607.B630A78B.object_0[760](0, 0));
		Class607.B630A78B.object_0[689](panel_0, "NavBar");
		Class607.B630A78B.object_0[1276](panel_0, Class607.B630A78B.object_0[174](1156, 38));
		Class607.B630A78B.object_0[334](panel_0, 1);
		Class607.B630A78B.object_0[1017](panel_0, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GForm0.BD3B01A2)));
		Class607.B630A78B.object_0[427](panel_0, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GForm0.method_3)));
		Class607.B630A78B.object_0[609](panel_0, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GForm0.method_4)));
		Class607.B630A78B.object_0[1242](label_0, AnchorStyles.Top);
		Class607.B630A78B.object_0[11](label_0, E704E9A1: true);
		Class607.B630A78B.object_0[823](label_0, Class607.B630A78B.object_0[585]());
		Class607.B630A78B.object_0[326](label_0, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[388](label_0, Class607.B630A78B.object_0[760](530, 6));
		Class607.B630A78B.object_0[689](label_0, "LblVersion");
		Class607.B630A78B.object_0[356](label_0, Class607.B630A78B.object_0[1237](1));
		Class607.B630A78B.object_0[1276](label_0, Class607.B630A78B.object_0[174](63, 15));
		Class607.B630A78B.object_0[334](label_0, 4);
		Class607.B630A78B.object_0[1175](label_0, D09F0D3B.smethod_5(1161740u));
		Class607.B630A78B.object_0[1242](button_3, AnchorStyles.Top | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_3, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[953](button_3, A282DB38.arrow);
		Class607.B630A78B.object_0[388](button_3, Class607.B630A78B.object_0[760](1021, 7));
		Class607.B630A78B.object_0[689](button_3, "BtnSignout");
		Class607.B630A78B.object_0[1276](button_3, Class607.B630A78B.object_0[174](27, 25));
		Class607.B630A78B.object_0[334](button_3, 3);
		Class607.B630A78B.object_0[270](button_3, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_3, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GForm0.C7BA288B)));
		Class607.B630A78B.object_0[308](DD09338B, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[953](DD09338B, A282DB38.stamp);
		Class607.B630A78B.object_0[997](DD09338B, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](DD09338B, Class607.B630A78B.object_0[760](78, 7));
		Class607.B630A78B.object_0[689](DD09338B, "BtnLicense");
		Class607.B630A78B.object_0[1276](DD09338B, Class607.B630A78B.object_0[174](74, 25));
		Class607.B630A78B.object_0[334](DD09338B, 3);
		Class607.B630A78B.object_0[1175](DD09338B, "Account");
		Class607.B630A78B.object_0[494](DD09338B, ContentAlignment.MiddleRight);
		Class607.B630A78B.object_0[270](DD09338B, CA2A151B: true);
		Class607.B630A78B.object_0[1239](DD09338B, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GForm0.CD2D780D)));
		Class607.B630A78B.object_0[308](A6128E19, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[953](A6128E19, A282DB38.history);
		Class607.B630A78B.object_0[997](A6128E19, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](A6128E19, Class607.B630A78B.object_0[760](10, 7));
		Class607.B630A78B.object_0[689](A6128E19, "BtnHistory");
		Class607.B630A78B.object_0[1276](A6128E19, Class607.B630A78B.object_0[174](62, 25));
		Class607.B630A78B.object_0[334](A6128E19, 3);
		Class607.B630A78B.object_0[1175](A6128E19, "History");
		Class607.B630A78B.object_0[494](A6128E19, ContentAlignment.MiddleRight);
		Class607.B630A78B.object_0[270](A6128E19, CA2A151B: true);
		Class607.B630A78B.object_0[1239](A6128E19, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GForm0.E08FDE38)));
		Class607.B630A78B.object_0[1242](a09F0208_0, AnchorStyles.Top);
		Class607.B630A78B.object_0[11](a09F0208_0, E704E9A1: true);
		a09F0208_0.Int32_0 = 0;
		Class607.B630A78B.object_0[1267](a09F0208_0, Class607.B630A78B.object_0[1269]("Roboto Medium", 11f));
		Class607.B630A78B.object_0[326](a09F0208_0, Class607.B630A78B.object_0[1194](222, 0, 0, 0));
		Class607.B630A78B.object_0[388](a09F0208_0, Class607.B630A78B.object_0[760](425, 10));
		a09F0208_0.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](a09F0208_0, "lblnav");
		Class607.B630A78B.object_0[1276](a09F0208_0, Class607.B630A78B.object_0[174](98, 18));
		Class607.B630A78B.object_0[334](a09F0208_0, 2);
		Class607.B630A78B.object_0[1175](a09F0208_0, D09F0D3B.smethod_5(1161902u));
		Class607.B630A78B.object_0[1017](a09F0208_0, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GForm0.BD3B01A2)));
		Class607.B630A78B.object_0[427](a09F0208_0, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GForm0.method_3)));
		Class607.B630A78B.object_0[609](a09F0208_0, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GForm0.method_4)));
		Class607.B630A78B.object_0[1242](button_0, AnchorStyles.Top | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_0, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](button_0), Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[807](Class607.B630A78B.object_0[227](button_0), Class607.B630A78B.object_0[300]());
		Class607.B630A78B.object_0[1214](Class607.B630A78B.object_0[227](button_0), Class607.B630A78B.object_0[300]());
		Class607.B630A78B.object_0[540](button_0, FlatStyle.Flat);
		Class607.B630A78B.object_0[953](button_0, A282DB38.maximize_window);
		Class607.B630A78B.object_0[388](button_0, Class607.B630A78B.object_0[760](1088, 7));
		Class607.B630A78B.object_0[689](button_0, "BtnMaximize");
		Class607.B630A78B.object_0[1276](button_0, Class607.B630A78B.object_0[174](27, 25));
		Class607.B630A78B.object_0[334](button_0, 1);
		Class607.B630A78B.object_0[270](button_0, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_0, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GForm0.ED2FAC34)));
		Class607.B630A78B.object_0[1242](button_1, AnchorStyles.Top | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_1, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](button_1), Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[807](Class607.B630A78B.object_0[227](button_1), Class607.B630A78B.object_0[300]());
		Class607.B630A78B.object_0[1214](Class607.B630A78B.object_0[227](button_1), Class607.B630A78B.object_0[300]());
		Class607.B630A78B.object_0[540](button_1, FlatStyle.Flat);
		Class607.B630A78B.object_0[953](button_1, A282DB38.minimize_window);
		Class607.B630A78B.object_0[388](button_1, Class607.B630A78B.object_0[760](1054, 7));
		Class607.B630A78B.object_0[689](button_1, "BtnMinimize");
		Class607.B630A78B.object_0[1276](button_1, Class607.B630A78B.object_0[174](27, 25));
		Class607.B630A78B.object_0[334](button_1, 1);
		Class607.B630A78B.object_0[270](button_1, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_1, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GForm0.AE0D42BF)));
		Class607.B630A78B.object_0[1242](button_2, AnchorStyles.Top | AnchorStyles.Right);
		Class607.B630A78B.object_0[308](button_2, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](button_2), Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[807](Class607.B630A78B.object_0[227](button_2), Class607.B630A78B.object_0[300]());
		Class607.B630A78B.object_0[1214](Class607.B630A78B.object_0[227](button_2), Class607.B630A78B.object_0[300]());
		Class607.B630A78B.object_0[540](button_2, FlatStyle.Flat);
		Class607.B630A78B.object_0[953](button_2, A282DB38.close_window);
		Class607.B630A78B.object_0[388](button_2, Class607.B630A78B.object_0[760](1120, 7));
		Class607.B630A78B.object_0[689](button_2, "BtnClose");
		Class607.B630A78B.object_0[1276](button_2, Class607.B630A78B.object_0[174](27, 25));
		Class607.B630A78B.object_0[334](button_2, 1);
		Class607.B630A78B.object_0[270](button_2, CA2A151B: true);
		Class607.B630A78B.object_0[1239](button_2, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GForm0.method_2)));
		Class607.B630A78B.object_0[781](DB3962B0).Add(gcontrol1_0);
		Class607.B630A78B.object_0[1278](DB3962B0, DockStyle.Fill);
		Class607.B630A78B.object_0[388](DB3962B0, Class607.B630A78B.object_0[760](0, 80));
		Class607.B630A78B.object_0[689](DB3962B0, "PnlMain");
		Class607.B630A78B.object_0[1276](DB3962B0, Class607.B630A78B.object_0[174](1156, 545));
		Class607.B630A78B.object_0[334](DB3962B0, 4);
		Class607.B630A78B.object_0[781](gcontrol1_0).Add(tabPage_1);
		Class607.B630A78B.object_0[781](gcontrol1_0).Add(tabPage_0);
		Class607.B630A78B.object_0[781](gcontrol1_0).Add(tabPage_2);
		Class607.B630A78B.object_0[781](gcontrol1_0).Add(F9ABDD17);
		gcontrol1_0.Int32_0 = 0;
		Class607.B630A78B.object_0[1278](gcontrol1_0, DockStyle.Fill);
		Class607.B630A78B.object_0[388](gcontrol1_0, Class607.B630A78B.object_0[760](0, 0));
		gcontrol1_0.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](gcontrol1_0, "TPCMain");
		Class607.B630A78B.object_0[734](gcontrol1_0, 0);
		Class607.B630A78B.object_0[1276](gcontrol1_0, Class607.B630A78B.object_0[174](1156, 545));
		Class607.B630A78B.object_0[334](gcontrol1_0, 3);
		Class607.B630A78B.object_0[781](tabPage_1).Add(eab5ABA9_0);
		Class607.B630A78B.object_0[1250](tabPage_1, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](tabPage_1, "TPAdvanced");
		Class607.B630A78B.object_0[356](tabPage_1, Class607.B630A78B.object_0[570](0, 7, 0, 3));
		Class607.B630A78B.object_0[1276](tabPage_1, Class607.B630A78B.object_0[174](1148, 519));
		Class607.B630A78B.object_0[295](tabPage_1, 2);
		Class607.B630A78B.object_0[1175](tabPage_1, "Adb King");
		Class607.B630A78B.object_0[1031](tabPage_1, bool_0: true);
		Class607.B630A78B.object_0[1278](eab5ABA9_0, DockStyle.Fill);
		Class607.B630A78B.object_0[388](eab5ABA9_0, Class607.B630A78B.object_0[760](0, 7));
		Class607.B630A78B.object_0[689](eab5ABA9_0, "ServicePage");
		Class607.B630A78B.object_0[1276](eab5ABA9_0, Class607.B630A78B.object_0[174](1148, 509));
		Class607.B630A78B.object_0[334](eab5ABA9_0, 0);
		Class607.B630A78B.object_0[781](tabPage_0).Add(aa16C934_0);
		Class607.B630A78B.object_0[1250](tabPage_0, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](tabPage_0, "TPMotorola");
		Class607.B630A78B.object_0[356](tabPage_0, Class607.B630A78B.object_0[570](0, 7, 0, 3));
		Class607.B630A78B.object_0[1276](tabPage_0, Class607.B630A78B.object_0[174](1148, 519));
		Class607.B630A78B.object_0[295](tabPage_0, 0);
		Class607.B630A78B.object_0[1175](tabPage_0, "Motorola");
		Class607.B630A78B.object_0[1031](tabPage_0, bool_0: true);
		Class607.B630A78B.object_0[1278](aa16C934_0, DockStyle.Fill);
		Class607.B630A78B.object_0[388](aa16C934_0, Class607.B630A78B.object_0[760](0, 7));
		Class607.B630A78B.object_0[689](aa16C934_0, "MotorolaPage");
		Class607.B630A78B.object_0[1276](aa16C934_0, Class607.B630A78B.object_0[174](1148, 509));
		Class607.B630A78B.object_0[334](aa16C934_0, 0);
		Class607.B630A78B.object_0[781](tabPage_2).Add(c220CEB6_0);
		Class607.B630A78B.object_0[1250](tabPage_2, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](tabPage_2, "TPMediatek");
		Class607.B630A78B.object_0[356](tabPage_2, Class607.B630A78B.object_0[570](0, 7, 0, 3));
		Class607.B630A78B.object_0[1276](tabPage_2, Class607.B630A78B.object_0[174](1148, 519));
		Class607.B630A78B.object_0[295](tabPage_2, 3);
		Class607.B630A78B.object_0[1175](tabPage_2, "Mediatek");
		Class607.B630A78B.object_0[1031](tabPage_2, bool_0: true);
		Class607.B630A78B.object_0[1278](c220CEB6_0, DockStyle.Fill);
		Class607.B630A78B.object_0[388](c220CEB6_0, Class607.B630A78B.object_0[760](0, 7));
		Class607.B630A78B.object_0[689](c220CEB6_0, "MediatekPage");
		Class607.B630A78B.object_0[1276](c220CEB6_0, Class607.B630A78B.object_0[174](1148, 509));
		Class607.B630A78B.object_0[334](c220CEB6_0, 0);
		Class607.B630A78B.object_0[781](F9ABDD17).Add(ee97B6A8_0);
		Class607.B630A78B.object_0[1250](F9ABDD17, Class607.B630A78B.object_0[760](4, 22));
		Class607.B630A78B.object_0[689](F9ABDD17, "TPQualcomm");
		Class607.B630A78B.object_0[356](F9ABDD17, Class607.B630A78B.object_0[570](0, 7, 0, 3));
		Class607.B630A78B.object_0[1276](F9ABDD17, Class607.B630A78B.object_0[174](1148, 519));
		Class607.B630A78B.object_0[295](F9ABDD17, 1);
		Class607.B630A78B.object_0[1175](F9ABDD17, "Qualcomm");
		Class607.B630A78B.object_0[1031](F9ABDD17, bool_0: true);
		Class607.B630A78B.object_0[1278](ee97B6A8_0, DockStyle.Fill);
		Class607.B630A78B.object_0[388](ee97B6A8_0, Class607.B630A78B.object_0[760](0, 7));
		Class607.B630A78B.object_0[689](ee97B6A8_0, "QcomPage");
		Class607.B630A78B.object_0[1276](ee97B6A8_0, Class607.B630A78B.object_0[174](1148, 509));
		Class607.B630A78B.object_0[334](ee97B6A8_0, 0);
		DE9CD49C.GControl1_0 = gcontrol1_0;
		DE9CD49C.Int32_0 = 0;
		Class607.B630A78B.object_0[1278](DE9CD49C, DockStyle.Top);
		Class607.B630A78B.object_0[388](DE9CD49C, Class607.B630A78B.object_0[760](0, 38));
		DE9CD49C.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](DE9CD49C, "TBNav");
		Class607.B630A78B.object_0[1276](DE9CD49C, Class607.B630A78B.object_0[174](1156, 42));
		DE9CD49C.E4A0A31D = GEnum37.EAB24C33;
		Class607.B630A78B.object_0[334](DE9CD49C, 2);
		Class607.B630A78B.object_0[1175](DE9CD49C, "materialTabSelector1");
		Class607.B630A78B.object_0[511](this, Class607.B630A78B.object_0[1223](6f, 13f));
		Class607.B630A78B.object_0[1043](this, AutoScaleMode.Font);
		Class607.B630A78B.object_0[485](this, Class607.B630A78B.object_0[174](1156, 625));
		Class607.B630A78B.object_0[782](this).Add(DB3962B0);
		Class607.B630A78B.object_0[782](this).Add(DE9CD49C);
		Class607.B630A78B.object_0[782](this).Add(panel_0);
		Class607.B630A78B.object_0[591](this, FormBorderStyle.None);
		Class607.B630A78B.object_0[376](this, (Icon)Class607.B630A78B.object_0[656](object_, "$this.Icon"));
		Class607.B630A78B.object_0[690](this, "Main");
		Class607.B630A78B.object_0[236](this, Class607.B630A78B.object_0[800](this, (nint)__ldftn(GForm0.B4BA712D)));
		Class607.B630A78B.object_0[161](this, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GForm0.method_5)));
		Class607.B630A78B.object_0[1149](panel_0, EB25F899: false);
		Class607.B630A78B.object_0[63](panel_0);
		Class607.B630A78B.object_0[1149](DB3962B0, EB25F899: false);
		Class607.B630A78B.object_0[1149](gcontrol1_0, EB25F899: false);
		Class607.B630A78B.object_0[1149](tabPage_1, EB25F899: false);
		Class607.B630A78B.object_0[1149](tabPage_0, EB25F899: false);
		Class607.B630A78B.object_0[1149](tabPage_2, EB25F899: false);
		Class607.B630A78B.object_0[1149](F9ABDD17, EB25F899: false);
		Class607.B630A78B.object_0[1150](this, bool_0: false);
	}
}
