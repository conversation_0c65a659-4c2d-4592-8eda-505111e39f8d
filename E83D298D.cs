internal sealed class E83D298D
{
	internal static void smethod_0(object object_0, string string_0)
	{
		sbyte b = -113;
		do
		{
			Class607.B630A78B.object_0[((-668587630 & (706025750 - b)) << ((b << (int)b) % (1403963531 / b) >> 5)) ^ 0x8040A55](object_0, string_0);
		}
		while (~((int)(1862647562u / (uint)b) * (int)b) * ((3243284431u > (uint)(~b)) ? 1 : 0) == (int)(0 - ~(562068664u / (uint)b)) >> (int)((1518602537u % (uint)(b + b)) ^ (uint)b));
	}
}
