public class GException5 : GException2
{
	protected object BBAFB7B3;

	protected object object_0;

	public GException5(byte[] BBAFB7B3, byte[] byte_0, E3348281 e3348281_0, string string_0)
		: base(Class607.B630A78B.object_0[398]("not in range, max ", GException2.smethod_0(BBAFB7B3), ", but got ", GException2.smethod_0(byte_0)), e3348281_0, string_0)
	{
		this.BBAFB7B3 = BBAFB7B3;
		object_0 = byte_0;
	}

	public GException5(object object_1, object object_2, E3348281 E823B7A5, string string_0)
		: base(Class334.smethod_0("not in range, max ", object_1?.ToString(), ", but got ", object_2?.ToString()), E823B7A5, string_0)
	{
		BBAFB7B3 = object_1;
		object_0 = object_2;
	}
}
