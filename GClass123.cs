using System;
using System.Diagnostics;
using System.Net.Security;
using System.Runtime.CompilerServices;
using System.Security.Cryptography.X509Certificates;
using System.Text;

[DebuggerDisplay("{2+B630A78B.A2A5AFA5()}")]
public class GClass123
{
	public class GClass125 : GClass124
	{
		private string AB254895;
	}

	public class GClass126 : GClass124
	{
	}

	[DebuggerDisplay("{2+B630A78B.A2A5AFA5()}")]
	public class GClass124
	{
		[Serializable]
		[DebuggerDisplay("{2+B630A78B.A2A5AFA5()}")]
		private sealed class Class77
		{
			public static RemoteCertificateValidationCallback _003C_003E9__4_0;

			public static readonly Class77 _003C_003E9 = new Class77();

			internal bool DF301993(object F399AC35, X509Certificate x509Certificate_0, X509Chain x509Chain_0, SslPolicyErrors F68E95A5)
			{
				return true;
			}
		}

		private string string_0;

		private string string_1;

		private void method_0(string string_2, bool bool_0)
		{
			if (bool_0)
			{
				StringBuilder stringBuilder = new StringBuilder(F431D28A());
				foreach (char c in string_2)
				{
					switch (c)
					{
					default:
						stringBuilder.Append(c);
						break;
					case '=':
						stringBuilder.Append("%3D");
						break;
					case '/':
						stringBuilder.Append("%2F");
						break;
					case '+':
						stringBuilder.Append("%2B");
						break;
					}
				}
				E4371F31(stringBuilder.ToString());
			}
			else
			{
				E4371F31(F431D28A() + string_2);
			}
		}

		[SpecialName]
		private void E4371F31(string FD1C5590)
		{
			string_1 = FD1C5590;
		}

		[SpecialName]
		private void method_1(string B385A338)
		{
			string_0 = B385A338;
		}

		protected void method_2()
		{
			E4371F31(Convert.ToBase64String(Encoding.UTF8.GetBytes(F431D28A())));
		}

		[SpecialName]
		public string B83AAEAC()
		{
			return string_0;
		}

		[SpecialName]
		public string F431D28A()
		{
			return string_1;
		}

		protected void AD1D248F(string string_2, string string_3)
		{
			method_0(string_2, bool_0: false);
			method_0(string_3, bool_0: true);
		}

		protected bool method_3(byte[] F72B601A)
		{
			//IL_0008: Expected I4, but got O
			//IL_0009: Expected I4, but got O
			//IL_0016: Expected I4, but got O
			//IL_0017: Expected I4, but got O
			int num = (int)GClass5.smethod_0((int)F72B601A, 32);
			if (num == 0)
			{
				return false;
			}
			int index = (int)GClass5.smethod_0((int)F72B601A, 28);
			E4371F31(Encoding.UTF8.GetString(F72B601A, index, num));
			if (F431D28A()[F431D28A().Length - 1] != '/')
			{
				E4371F31(F431D28A() + "/");
			}
			return true;
		}
	}

	private readonly object object_0;

	private byte[] byte_0;

	private ulong E4AC4F8C;

	private A538CD23 a538CD23_0;

	private readonly int int_0;

	private int F8999938;

	private uint uint_0;

	private readonly byte[] byte_1;

	public GClass123()
	{
		new GClass128().EF8D5E3B(new object[1] { this }, 11170603);
	}
}
