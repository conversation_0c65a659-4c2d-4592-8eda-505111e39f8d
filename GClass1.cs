using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using System.Windows.Forms.VisualStyles;

public class GClass1 : DataGridViewColumnHeaderCell
{
	private Point F2275D17;

	private Size size_0;

	private bool F0ADD48F = false;

	private Point F7326189 = default(Point);

	private CheckBoxState checkBoxState_0 = CheckBoxState.UncheckedNormal;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private GDelegate0 FE2A678A;

	public event GDelegate0 Event_0
	{
		[CompilerGenerated]
		add
		{
			GDelegate0 gDelegate = FE2A678A;
			GDelegate0 gDelegate2;
			do
			{
				gDelegate2 = gDelegate;
				GDelegate0 value2 = (GDelegate0)Class607.B630A78B.object_0[752](gDelegate2, value);
				gDelegate = Interlocked.CompareExchange(ref FE2A678A, value2, gDelegate2);
			}
			while ((object)gDelegate != gDelegate2);
		}
		[CompilerGenerated]
		remove
		{
			GDelegate0 gDelegate = FE2A678A;
			GDelegate0 gDelegate2;
			do
			{
				gDelegate2 = gDelegate;
				GDelegate0 value2 = (GDelegate0)Class607.B630A78B.object_0[629](gDelegate2, value);
				gDelegate = Interlocked.CompareExchange(ref FE2A678A, value2, gDelegate2);
			}
			while ((object)gDelegate != gDelegate2);
		}
	}

	public GClass1()
	{
		Class607.B630A78B.object_0[201](this);
	}

	protected override void Paint(Graphics graphics, Rectangle clipBounds, Rectangle cellBounds, int rowIndex, DataGridViewElementStates dataGridViewElementState, object A08A6690, object formattedValue, string errorText, DataGridViewCellStyle DC365B92, DataGridViewAdvancedBorderStyle advancedBorderStyle, DataGridViewPaintParts A08E55A0)
	{
		Class607.B630A78B.object_0[435](this, graphics, clipBounds, cellBounds, rowIndex, dataGridViewElementState, A08A6690, formattedValue, errorText, DC365B92, advancedBorderStyle, A08E55A0);
		Point D93326B = default(Point);
		Size DA = Class607.B630A78B.object_0[1170](graphics, CheckBoxState.UncheckedNormal);
		object obj = Class607.B630A78B.object_0[871];
		Point point_ = Class607.B630A78B.object_0[667](ref cellBounds);
		obj(ref D93326B, (int)((double)Class607.B630A78B.object_0[372](ref point_) + (double)Class607.B630A78B.object_0[56](ref cellBounds) / 2.0 - (double)(Class607.B630A78B.object_0[676](ref DA) / 2)));
		object obj2 = Class607.B630A78B.object_0[819];
		point_ = Class607.B630A78B.object_0[667](ref cellBounds);
		obj2(ref D93326B, (int)((double)Class607.B630A78B.object_0[1229](ref point_) + (double)Class607.B630A78B.object_0[970](ref cellBounds) / 2.0 - (double)(Class607.B630A78B.object_0[1008](ref DA) / 2)));
		F7326189 = Class607.B630A78B.object_0[667](ref cellBounds);
		F2275D17 = D93326B;
		size_0 = DA;
		if (F0ADD48F)
		{
			checkBoxState_0 = CheckBoxState.CheckedNormal;
		}
		else
		{
			checkBoxState_0 = CheckBoxState.UncheckedNormal;
		}
		Class607.B630A78B.object_0[700](graphics, F2275D17, checkBoxState_0);
	}

	protected override void OnMouseClick(DataGridViewCellMouseEventArgs e)
	{
		Point point_ = default(Point);
		Class607.B630A78B.object_0[759](ref point_, Class607.B630A78B.object_0[841](e) + Class607.B630A78B.object_0[372](ref F7326189), Class607.B630A78B.object_0[913](e) + Class607.B630A78B.object_0[1229](ref F7326189));
		if (Class607.B630A78B.object_0[372](ref point_) >= Class607.B630A78B.object_0[372](ref F2275D17) && Class607.B630A78B.object_0[372](ref point_) <= Class607.B630A78B.object_0[372](ref F2275D17) + Class607.B630A78B.object_0[676](ref size_0) && Class607.B630A78B.object_0[1229](ref point_) >= Class607.B630A78B.object_0[1229](ref F2275D17) && Class607.B630A78B.object_0[1229](ref point_) <= Class607.B630A78B.object_0[1229](ref F2275D17) + Class607.B630A78B.object_0[1008](ref size_0))
		{
			F0ADD48F = !F0ADD48F;
			FE2A678A?.Invoke(Class607.B630A78B.object_0[307](e), F0ADD48F);
			Class607.B630A78B.object_0[1062](Class607.B630A78B.object_0[854](this), this);
		}
		Class607.B630A78B.object_0[740](this, e);
	}
}
