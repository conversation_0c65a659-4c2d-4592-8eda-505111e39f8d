using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace MotoKingPro.Core
{
    /// <summary>
    /// Base operation tracker for logging and timing operations
    /// </summary>
    public class OperationTracker
    {
        public DateTime StartedAt { get; set; }
        public DateTime EndedAt { get; set; }
        public string OperationName { get; set; }
        public string Tab { get; set; }
        public List<LogItem> Items { get; set; } = new List<LogItem>();
    }

    /// <summary>
    /// Log item for operation tracking
    /// </summary>
    public class LogItem
    {
        public DateTime Timestamp { get; set; }
        public string Message { get; set; }
        public LogLevel Level { get; set; }
        public bool IsNewLine { get; set; }
        public bool IsBold { get; set; }
    }

    /// <summary>
    /// Log levels for colored output
    /// </summary>
    public enum LogLevel
    {
        Info,
        Success,
        Warning,
        Error,
        Word
    }

    /// <summary>
    /// Application settings and configuration
    /// </summary>
    public static class ApplicationSettings
    {
        public static string ApplicationPath { get; set; } = Application.StartupPath;
        public static Dictionary<string, string> AutoLoaderFiles { get; set; } = new Dictionary<string, string>();
        public static bool IsExiting { get; set; } = false;
    }

    /// <summary>
    /// Operation history manager
    /// </summary>
    public class OperationHistory
    {
        private static OperationHistory _instance;
        public static OperationHistory Instance => _instance ??= new OperationHistory();

        public List<OperationTracker> Operations { get; set; } = new List<OperationTracker>();

        public static void SaveToFile()
        {
            // Implementation for saving operation history
        }
    }

    /// <summary>
    /// Application information
    /// </summary>
    public static class ApplicationInfo
    {
        public static string GetVersionString()
        {
            return "MotoKingPro v1.1.0.0";
        }
    }

    /// <summary>
    /// Theme manager for UI styling
    /// </summary>
    public class ThemeManager
    {
        public void ApplyTheme(Form form)
        {
            // Apply dark theme or custom styling
            form.BackColor = Color.FromArgb(45, 45, 48);
            form.ForeColor = Color.White;
        }
    }

    /// <summary>
    /// Extension methods for UI thread safety
    /// </summary>
    public static class ControlExtensions
    {
        public static void InvokeIfRequired(this Control control, Action action)
        {
            if (control.InvokeRequired)
            {
                control.Invoke(action);
            }
            else
            {
                action();
            }
        }
    }

    /// <summary>
    /// Device connection states
    /// </summary>
    public enum DeviceConnectionState
    {
        Disconnected,
        ADB,
        Fastboot,
        EDL,
        DownloadMode,
        Recovery
    }

    /// <summary>
    /// Platform types supported
    /// </summary>
    public enum PlatformType
    {
        Qualcomm,
        MediaTek,
        Motorola,
        Generic
    }

    /// <summary>
    /// Base class for platform-specific operations
    /// </summary>
    public abstract class BasePlatformManager
    {
        public event Action<bool, string> OperationStateChanged;
        public event Action<string, LogLevel, bool, bool> LogMessage;

        protected void OnOperationStateChanged(bool isRunning, string operationName)
        {
            OperationStateChanged?.Invoke(isRunning, operationName);
        }

        protected void OnLogMessage(string message, LogLevel level = LogLevel.Info, bool newLine = true, bool bold = false)
        {
            LogMessage?.Invoke(message, level, newLine, bold);
        }

        public abstract bool ConnectDevice();
        public abstract bool DisconnectDevice();
        public abstract DeviceConnectionState GetConnectionState();
    }

    /// <summary>
    /// Device information structure
    /// </summary>
    public class DeviceInfo
    {
        public string Model { get; set; }
        public string Manufacturer { get; set; }
        public string AndroidVersion { get; set; }
        public string BuildNumber { get; set; }
        public string SerialNumber { get; set; }
        public string Chipset { get; set; }
        public bool IsQualcomm { get; set; }
        public bool IsMediaTek { get; set; }
        public string Language { get; set; }
        public string SimCount { get; set; }
        public string SecurityPath { get; set; }
        public PlatformType Platform { get; set; }
    }

    /// <summary>
    /// Flash operation result
    /// </summary>
    public class FlashResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Exception Error { get; set; }
    }

    /// <summary>
    /// Partition information
    /// </summary>
    public class PartitionInfo
    {
        public string Name { get; set; }
        public ulong StartAddress { get; set; }
        public ulong Size { get; set; }
        public string Type { get; set; }
    }
}
