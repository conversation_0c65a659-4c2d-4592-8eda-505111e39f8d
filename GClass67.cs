using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;

public static class GClass67
{
	[CompilerGenerated]
	private sealed class Class14
	{
		public (string, string, string) valueTuple_0;

		public Class14()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal byte method_0(int D601B139)
		{
			return Class607.B630A78B.object_0[276](Class607.B630A78B.object_0[31](valueTuple_0.Item1, D601B139 * 2, 2), 16);
		}

		internal byte EDADC004(int CF162004)
		{
			return Class607.B630A78B.object_0[276](Class607.B630A78B.object_0[31](valueTuple_0.Item2, CF162004 * 2, 2), 16);
		}
	}

	[CompilerGenerated]
	private sealed class Class15
	{
		public (string Pattern, string Replacement, string Description) valueTuple_0;

		public Class15()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal byte method_0(int int_0)
		{
			return Class607.B630A78B.object_0[276](Class607.B630A78B.object_0[31](valueTuple_0.Pattern, int_0 * 2, 2), 16);
		}

		internal byte CB294C1D(int CE98F0AC)
		{
			return Class607.B630A78B.object_0[276](Class607.B630A78B.object_0[31](valueTuple_0.Replacement, CE98F0AC * 2, 2), 16);
		}
	}

	public static string String_0 => Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\da_x.bin");

	public static string String_1 => Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\da_xml.bin");

	public static uint? smethod_0(uint uint_0, uint uint_1, int int_0)
	{
		int num = (int)((uint_0 & 0xF000) >> 12);
		int num2 = (int)((uint_1 & 0xF000) >> 12);
		int num3 = (int)((uint_0 & 0xF00000) >> 20);
		int num4 = (int)((uint_1 & 0xF00000) >> 20);
		uint num5 = (((uint_1 & 0xF0000) >> 4) | (uint_1 & 0xFFF)) << num4 * 4;
		uint num6 = (((uint_0 & 0xF0000) >> 4) | (uint_0 & 0xFFF)) << num3 * 4;
		if (num == num2 && num == int_0)
		{
			return num5 | num6;
		}
		return null;
	}

	public static byte[] smethod_1(GClass70 gclass70_0)
	{
		byte[] byte_ = gclass70_0.byte_0;
		uint uint_ = gclass70_0.********.list_0[2].uint_1;
		if (Class607.B630A78B.object_0[695](String_1))
		{
			byte[] array = Class607.B630A78B.object_0[649](String_1);
			byte[] array2 = new byte[4];
			DEBEDD9C.smethod_0(array2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num = GClass112.smethod_24(array, array2, 0);
			byte[] array3 = new byte[4];
			DEBEDD9C.smethod_0(array3, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int eCBD6DAF = GClass112.smethod_24(array, array3, 0);
			byte[] array4 = new byte[4];
			DEBEDD9C.smethod_0(array4, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int eCBD6DAF2 = GClass112.smethod_24(array, array4, 0);
			byte[] array5 = new byte[4];
			DEBEDD9C.smethod_0(array5, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int eCBD6DAF3 = GClass112.smethod_24(array, array5, 0);
			byte[] array6 = new byte[4];
			DEBEDD9C.smethod_0(array6, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int eCBD6DAF4 = GClass112.smethod_24(array, array6, 0);
			byte[] array7 = new byte[4];
			DEBEDD9C.smethod_0(array7, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int eCBD6DAF5 = GClass112.smethod_24(array, array7, 0);
			byte[] array8 = new byte[4];
			DEBEDD9C.smethod_0(array8, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int eCBD6DAF6 = GClass112.smethod_24(array, array8, 0);
			byte[] array9 = new byte[16];
			DEBEDD9C.smethod_0(array9, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num2 = GClass112.smethod_24(byte_, array9, 0);
			byte[] array10 = new byte[16];
			DEBEDD9C.smethod_0(array10, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num3 = GClass112.smethod_24(byte_, array10, 0);
			uint num4 = 0u;
			uint num5 = 0u;
			uint num6 = 0u;
			if (num3 != -1)
			{
				uint uint_2 = Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(byte_, num3 - 8, 4))[0]);
				uint uint_3 = Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(byte_, num3 - 4, 4))[0]);
				num4 = smethod_0(uint_2, uint_3, 4).Value;
				byte[] array11 = new byte[16];
				DEBEDD9C.smethod_0(array11, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num5 = B9B10F3A.smethod_0(GClass112.smethod_24(byte_, array11, 0)) + uint_;
				byte[] array12 = new byte[16];
				DEBEDD9C.smethod_0(array12, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num6 = B9B10F3A.smethod_0(GClass112.smethod_24(byte_, array12, 0)) + uint_;
			}
			byte[] array13 = new byte[8];
			DEBEDD9C.smethod_0(array13, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num7 = GClass112.smethod_24(byte_, array13, 0) - 12;
			byte[] array14 = new byte[12];
			DEBEDD9C.smethod_0(array14, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num8 = GClass112.smethod_24(byte_, array14, 0);
			byte[] array15 = new byte[12];
			DEBEDD9C.smethod_0(array15, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num9 = GClass112.smethod_24(byte_, array15, 0);
			if (num != -1)
			{
				num2 = ((num2 != -1) ? (num2 + (int)uint_) : 0);
				A03A75BE.CE843BA7(GClass111.smethod_2("<I", new object[1] { num2 }), array, num);
				A03A75BE.CE843BA7(GClass111.smethod_2("<I", new object[1] { num7 }), array, eCBD6DAF);
				A03A75BE.CE843BA7(GClass111.smethod_2("<I", new object[1] { num8 }), array, eCBD6DAF2);
				A03A75BE.CE843BA7(GClass111.smethod_2("<I", new object[1] { num9 }), array, eCBD6DAF3);
				A03A75BE.CE843BA7(GClass111.smethod_2("<I", new object[1] { num6 }), array, eCBD6DAF5);
				A03A75BE.CE843BA7(GClass111.smethod_2("<I", new object[1] { num5 }), array, eCBD6DAF4);
				A03A75BE.CE843BA7(GClass111.smethod_2("<I", new object[1] { num4 }), array, eCBD6DAF6);
				return array;
			}
		}
		return null;
	}

	public static int smethod_2(byte[] byte_0, byte[] byte_1, int ******** = 0)
	{
		byte[][] array = GClass112.smethod_10(byte_1, new byte[1] { 46 });
		int num = 0;
		List<int> list = new List<int>();
		while (true)
		{
			if (num != -1)
			{
				num = GClass112.smethod_24(GClass112.smethod_14(byte_0, ********, byte_0.Length - ********), array[0], num);
				if (num == -1)
				{
					if (list.Count <= 0)
					{
						break;
					}
					foreach (int item in list)
					{
						int num2 = 0;
						int num3 = item + array[0].Length;
						for (int i = 1; i < array.Length; i++)
						{
							if (array[i].Length == 0)
							{
								num3++;
								continue;
							}
							num3++;
							if (GClass112.smethod_24(GClass112.smethod_14(byte_0, num3, byte_0.Length - num3), array[i], 0) == 0)
							{
								num3 += array[i].Length;
								continue;
							}
							num2 = 1;
							break;
						}
						if (num2 == 0)
						{
							return item + ********;
						}
					}
				}
				else
				{
					list.Add(num);
					num++;
				}
				continue;
			}
			return -1;
		}
		return -1;
	}

	private static byte[][] AD0105B5(byte[] byte_0, byte B18265A8)
	{
		List<byte[]> list = new List<byte[]>();
		List<byte> list2 = new List<byte>();
		foreach (byte b in byte_0)
		{
			if (b == B18265A8)
			{
				list.Add(list2.ToArray());
				list2.Clear();
			}
			else
			{
				list2.Add(b);
			}
		}
		list.Add(list2.ToArray());
		return list.ToArray();
	}

	private static int E32B0C8A(byte[] E48C5098, byte[] byte_0, int int_0)
	{
		int num = int_0;
		while (true)
		{
			if (num <= E48C5098.Length - byte_0.Length)
			{
				bool flag = true;
				for (int i = 0; i < byte_0.Length; i++)
				{
					if (E48C5098[num + i] != byte_0[i])
					{
						flag = false;
						break;
					}
				}
				if (flag)
				{
					break;
				}
				num++;
				continue;
			}
			return -1;
		}
		return num - int_0;
	}

	public static byte[] smethod_3(GClass70 gclass70_0)
	{
		byte[] byte_ = gclass70_0.byte_0;
		uint uint_ = gclass70_0.********.list_0[2].uint_1;
		byte[] array = Class607.B630A78B.object_0[649](String_0);
		int num = smethod_2(byte_, GClass112.FF06B0AD("38b505460c20"));
		int num2 = smethod_2(byte_, GClass112.FF06B0AD("4b4ff43c72"));
		if (num2 != -1)
		{
			num2--;
		}
		else
		{
			num2 = smethod_2(byte_, GClass112.FF06B0AD("a3eb0013181a02eb0010"));
			if (num2 != -1)
			{
				num2 -= 10;
			}
		}
		int f = 0;
		int num3 = 0;
		do
		{
			num3 = smethod_2(byte_, GClass112.FF06B0AD("c3690a4610b5"), f);
			if (num3 == -1)
			{
				break;
			}
			f = num3 + 1;
		}
		while (!GClass112.smethod_14(byte_, num3 + 20, 2).SequenceEqual(new byte[2] { 179, 33 }));
		if (num3 == -1)
		{
			num3 = smethod_2(byte_, GClass112.FF06B0AD("c36913f00103"));
		}
		int num4 = smethod_2(byte_, GClass112.FF06B0AD("f8b506469df81850"));
		int num5 = 0;
		if (num4 == -1)
		{
			num4 = smethod_2(byte_, GClass112.FF06B0AD("2de9f0414ff6fd74"));
		}
		byte[] array2 = new byte[10];
		DEBEDD9C.smethod_0(array2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int num6 = smethod_2(byte_, array2);
		if (num6 != -1)
		{
			num5 = Class607.B630A78B.object_0[84](GClass111.C3B9331C("<I", GClass112.smethod_14(byte_, num6 + 10, 4))[0]);
		}
		else
		{
			byte[] array3 = new byte[8];
			DEBEDD9C.smethod_0(array3, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			num6 = smethod_2(byte_, array3);
			if (num6 != -1)
			{
				num5 = Class607.B630A78B.object_0[84](GClass111.C3B9331C("<I", GClass112.smethod_14(byte_, num6 + 8, 4))[0]);
			}
			else
			{
				byte[] array4 = new byte[10];
				DEBEDD9C.smethod_0(array4, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num6 = smethod_2(byte_, array4);
				if (num6 != -1)
				{
					num5 = Class607.B630A78B.object_0[84](GClass111.C3B9331C("<I", GClass112.smethod_14(byte_, num6 + 10 + 8, 4))[0]);
				}
			}
		}
		int num7;
		int num8;
		if (num6 != -1)
		{
			byte[] array5 = new byte[5];
			DEBEDD9C.smethod_0(array5, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			num7 = smethod_2(byte_, array5);
			byte[] array6 = new byte[6];
			DEBEDD9C.smethod_0(array6, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			num8 = smethod_2(byte_, array6);
		}
		else
		{
			num5 = 0;
			num7 = 0;
			num8 = 0;
		}
		int num9 = GClass112.smethod_24(array, GClass112.FF06B0AD("11111111"), 0);
		int num10 = GClass112.smethod_24(array, GClass112.FF06B0AD("22222222"), 0);
		int c21EAC = GClass112.smethod_24(array, GClass112.FF06B0AD("33333333"), 0);
		int c21EAC2 = GClass112.smethod_24(array, GClass112.FF06B0AD("44444444"), 0);
		int c21EAC3 = GClass112.smethod_24(array, GClass112.FF06B0AD("55555555"), 0);
		int c21EAC4 = GClass112.smethod_24(array, GClass112.FF06B0AD("66666666"), 0);
		int c21EAC5 = GClass112.smethod_24(array, GClass112.FF06B0AD("77777777"), 0);
		if (num9 != -1 && num10 != -1)
		{
			num = (Class607.B630A78B.object_0[136](num) ? ((num + Class607.B630A78B.object_0[1262](uint_)) | 1) : 0);
			num2 = ((num2 > 0) ? ((num2 + Class607.B630A78B.object_0[1262](uint_)) | 1) : 0);
			num3 = ((num3 > 0) ? ((num3 + Class607.B630A78B.object_0[1262](uint_)) | 1) : 0);
			num4 = ((num4 > 0) ? ((num4 + Class607.B630A78B.object_0[1262](uint_)) | 1) : 0);
			num7 = ((num7 > 0) ? ((num7 + Class607.B630A78B.object_0[1262](uint_ - 1)) | 1) : 0);
			num8 = ((num8 > 0) ? ((num8 + Class607.B630A78B.object_0[1262](uint_)) | 1) : 0);
			if (num5 <= 0)
			{
				num5 = 0;
			}
			byte[] array_ = GClass111.smethod_2("<I", new object[1] { num });
			byte[] array_2 = GClass111.smethod_2("<I", new object[1] { num2 });
			byte[] array_3 = GClass111.smethod_2("<I", new object[1] { num3 });
			byte[] array_4 = GClass111.smethod_2("<I", new object[1] { num4 });
			byte[] array_5 = GClass111.smethod_2("<I", new object[1] { num7 });
			byte[] array_6 = GClass111.smethod_2("<I", new object[1] { num8 });
			byte[] array_7 = GClass111.smethod_2("<I", new object[1] { num5 });
			Class607.B630A78B.object_0[242](array_, 0, array, num9, 4);
			Class607.B630A78B.object_0[242](array_2, 0, array, num10, 4);
			Class607.B630A78B.object_0[242](array_3, 0, array, c21EAC, 4);
			Class607.B630A78B.object_0[242](array_4, 0, array, c21EAC2, 4);
			Class607.B630A78B.object_0[242](array_5, 0, array, c21EAC4, 4);
			Class607.B630A78B.object_0[242](array_6, 0, array, c21EAC3, 4);
			Class607.B630A78B.object_0[242](array_7, 0, array, c21EAC5, 4);
			return array;
		}
		return null;
	}

	public static byte[] ********(GClass70 gclass70_0)
	{
		byte[] byte_ = gclass70_0.byte_0;
		uint uint_ = gclass70_0.********.list_0[2].uint_1;
		if (Class607.B630A78B.object_0[695](String_0))
		{
			byte[] array = Class607.B630A78B.object_0[649](String_0);
			byte[] array2 = new byte[6];
			DEBEDD9C.smethod_0(array2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num = smethod_2(byte_, array2);
			byte[] array3 = new byte[5];
			DEBEDD9C.smethod_0(array3, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num2 = smethod_2(byte_, array3);
			if (num2 != -1)
			{
				num2--;
			}
			else
			{
				byte[] array4 = new byte[10];
				DEBEDD9C.smethod_0(array4, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num2 = smethod_2(byte_, array4);
				if (num2 != -1)
				{
					num2 -= 10;
				}
			}
			int f = 0;
			int num3;
			do
			{
				byte[] array5 = new byte[6];
				DEBEDD9C.smethod_0(array5, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num3 = smethod_2(byte_, array5, f);
				if (num3 == -1)
				{
					break;
				}
				f = num3 + 1;
			}
			while (!byte_.Skip(num3 + 20).Take(2).SequenceEqual(new byte[2] { 179, 33 }));
			if (num3 == -1)
			{
				byte[] array6 = new byte[6];
				DEBEDD9C.smethod_0(array6, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num3 = smethod_2(byte_, array6);
			}
			byte[] array7 = new byte[8];
			DEBEDD9C.smethod_0(array7, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num4 = smethod_2(byte_, array7);
			if (num4 == -1)
			{
				byte[] array8 = new byte[8];
				DEBEDD9C.smethod_0(array8, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num4 = smethod_2(byte_, array8);
			}
			int num5 = -1;
			byte[] array9 = new byte[10];
			DEBEDD9C.smethod_0(array9, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num6 = smethod_2(byte_, array9);
			if (num6 != -1)
			{
				num5 = Class607.B630A78B.object_0[137](byte_, num6 + 10);
			}
			else
			{
				byte[] array10 = new byte[8];
				DEBEDD9C.smethod_0(array10, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num6 = smethod_2(byte_, array10);
				if (num6 != -1)
				{
					num5 = Class607.B630A78B.object_0[137](byte_, num6 + 8);
				}
				else
				{
					byte[] array11 = new byte[10];
					DEBEDD9C.smethod_0(array11, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
					num6 = smethod_2(byte_, array11);
					if (num6 != -1)
					{
						num5 = Class607.B630A78B.object_0[137](byte_, num6 + 18);
					}
				}
			}
			int num7 = -1;
			int num8 = -1;
			if (num6 != -1)
			{
				byte[] array12 = new byte[5];
				DEBEDD9C.smethod_0(array12, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num7 = smethod_2(byte_, array12);
				byte[] array13 = new byte[6];
				DEBEDD9C.smethod_0(array13, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num8 = smethod_2(byte_, array13);
			}
			byte[] array14 = new byte[4];
			DEBEDD9C.smethod_0(array14, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num9 = smethod_2(array, array14);
			byte[] array15 = new byte[4];
			DEBEDD9C.smethod_0(array15, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num10 = smethod_2(array, array15);
			byte[] array16 = new byte[4];
			DEBEDD9C.smethod_0(array16, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int int_ = smethod_2(array, array16);
			byte[] array17 = new byte[4];
			DEBEDD9C.smethod_0(array17, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int int_2 = smethod_2(array, array17);
			byte[] array18 = new byte[4];
			DEBEDD9C.smethod_0(array18, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int int_3 = smethod_2(array, array18);
			byte[] array19 = new byte[4];
			DEBEDD9C.smethod_0(array19, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int int_4 = smethod_2(array, array19);
			byte[] array20 = new byte[4];
			DEBEDD9C.smethod_0(array20, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int int_5 = smethod_2(array, array20);
			byte[] array21 = new byte[4];
			DEBEDD9C.smethod_0(array21, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num11 = smethod_2(array, array21);
			if (num5 == -1)
			{
				num5 = 0;
			}
			if (num9 != -1 && num10 != -1)
			{
				uint uint_2 = (uint)((num != -1) ? ((ulong)((num + uint_) | 1L)) : 0uL);
				uint uint_3 = (uint)((num2 != -1) ? ((ulong)((num2 + uint_) | 1L)) : 0uL);
				uint uint_4 = (uint)((num3 != -1) ? ((ulong)((num3 + uint_) | 1L)) : 0uL);
				uint uint_5 = (uint)((num4 != -1) ? ((ulong)((num4 + uint_) | 1L)) : 0uL);
				uint uint_6 = (uint)((num7 != -1) ? ((ulong)((num7 + uint_ - 1L) | 1L)) : 0uL);
				uint uint_7 = (uint)((num8 != -1) ? ((ulong)((num8 + uint_) | 1L)) : 0uL);
				int int_6 = GClass112.C78DEB29.A8054FBA.int_3;
				smethod_4(array, num9, uint_2);
				smethod_4(array, num10, uint_3);
				smethod_4(array, int_, uint_4);
				smethod_4(array, int_2, uint_5);
				smethod_4(array, int_4, uint_6);
				smethod_4(array, int_3, uint_7);
				smethod_4(array, int_5, (uint)num5);
				if (num11 != -1)
				{
					smethod_4(array, num11, (uint)int_6);
				}
				return array;
			}
		}
		return null;
	}

	private static void smethod_4(byte[] E61DA198, int int_0, uint uint_0)
	{
		byte[] array = Class607.B630A78B.object_0[190](uint_0);
		Class607.B630A78B.object_0[242](array, 0, E61DA198, int_0, array.Length);
	}

	public static byte[] A826F023(byte[] C8949D86, byte[] FC10621B, int EA05CFB0, int int_0, int A984CA8B)
	{
		byte[] array = new byte[C8949D86.Length];
		Class607.B630A78B.object_0[1010](C8949D86, array, C8949D86.Length);
		byte[] array2 = null;
		switch (int_0)
		{
		case 1:
		{
			using (SHA1 b02A2 = Class607.B630A78B.object_0[287]())
			{
				array2 = Class607.B630A78B.object_0[455](b02A2, FC10621B.Take(A984CA8B).ToArray());
			}
			break;
		}
		case 2:
		{
			using (SHA256 b02A = Class607.B630A78B.object_0[286]())
			{
				array2 = Class607.B630A78B.object_0[455](b02A, FC10621B.Take(A984CA8B).ToArray());
			}
			break;
		}
		}
		if (array2 != null)
		{
			Class607.B630A78B.object_0[242](array2, 0, array, EA05CFB0, array2.Length);
		}
		return array;
	}

	public static byte[] FB2B3DAF(byte[] FAA6C6B9)
	{
		byte[] array = smethod_6(FAA6C6B9);
		byte[] array2 = new byte[10];
		DEBEDD9C.smethod_0(array2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int num = smethod_2(FAA6C6B9, array2);
		if (num != -1)
		{
			byte[] array_ = GClass112.FF06B0AD("00207047");
			Class607.B630A78B.object_0[242](array_, 0, array, num, 4);
		}
		return array;
	}

	public static byte[] smethod_5(byte[] byte_0)
	{
		byte[] array = null;
		if (byte_0 != null)
		{
			array = new byte[byte_0.Length];
			Class607.B630A78B.object_0[1010](byte_0, array, byte_0.Length);
			array = smethod_6(array);
			byte[] array2 = new byte[10];
			DEBEDD9C.smethod_0(array2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num = smethod_2(byte_0, array2);
			if (num != -1)
			{
				byte[] array_ = new byte[4];
				DEBEDD9C.smethod_0(array_, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				E13FE3AF.smethod_0(array_, 0, array, num, 4);
			}
		}
		return array;
	}

	public static byte[] F82A9D04(byte[] ********)
	{
		bool flag = false;
		byte[] array = ********.ToArray();
		List<Tuple<string, string, string>> list = new List<Tuple<string, string, string>>
		{
			Tuple.Create("A3687BB12846", "0123A3602846", "6F70706F207365637572697479"),
			Tuple.Create("B3F5807F01D1", "B3F5807F01D14FF000004FF000007047", "6D743637333920633330"),
			Tuple.Create("B3F5807F04BF4FF4807305F011B84FF0FF307047", "B3F5807F04BF4FF480734FF000004FF000007047", "726567756C6172"),
			Tuple.Create("10B50C680268", "10B5012010BD", "72616D20626C61636B6C697374"),
			Tuple.Create("08B5104B7B441B681B68", "00207047000000000000", "7365636C69625F7365635F757362646C5F656E61626C6564"),
			Tuple.Create("5072656C6F61646572205374617274", "50617463686564204C205374617274", "50617463686564204C6F61646572206D7367"),
			Tuple.Create("F0B58BB002AE20250C460746", "002070470000000000205374617274", "7365635F696D675F61757468"),
			Tuple.Create("FFC0F3400008BD", "FF4FF0000008BD", "6765745F7666795F706F6C696379"),
			Tuple.Create("040007C0", "00000000", "686173685F636865636B"),
			Tuple.Create("CCF20709", "4FF00009", "686173685F636865636B32"),
			Tuple.Create("142CF62EFEE7", "000000000000", "686173685F636865636B33")
		};
		int num = 0;
		foreach (Tuple<string, string, string> item in list)
		{
			byte[] byte_ = GClass112.FF06B0AD(item.Item1);
			int num2 = smethod_2(********, byte_);
			if (num2 != -1)
			{
				byte[] array2 = GClass112.FF06B0AD(item.Item2);
				Class607.B630A78B.object_0[242](array2, 0, array, num2, array2.Length);
				flag = true;
			}
			num++;
		}
		if (flag)
		{
		}
		return array;
	}

	public static byte[] smethod_6(byte[] byte_0)
	{
		bool flag = false;
		byte[] array = new byte[byte_0.Length];
		Class607.B630A78B.object_0[1010](byte_0, array, byte_0.Length);
		List<(string, string, string)> list = new List<(string, string, string)>
		{
			("A3687BB12846", "0123A3602846", "oppo security"),
			("B3F5807F01D1", "B3F5807F01D14FF000004FF000007047", "mt6739 c30"),
			("B3F5807F04BF4FF4807305F011B84FF0FF307047", "B3F5807F04BF4FF480734FF000004FF000007047", "regular"),
			("10B50C680268", "10B5012010BD", "ram blacklist"),
			("08B5104B7B441B681B68", "00207047000000000000", "seclib_sec_usbdl_enabled"),
			("5072656C6F61646572205374617274", "50617463686564204C205374617274", "Patched loader msg"),
			("F0B58BB002AE20250C460746", "002070470000000000205374617274", "sec_img_auth"),
			("FFC0F3400008BD", "FF4FF0000008BD", "get_vfy_policy"),
			("040007C0", "00000000", "hash_check"),
			("CCF20709", "4FF00009", "hash_check2")
		};
		List<(byte[], byte[], string)> list2 = new List<(byte[], byte[], string)>();
		byte[] array2 = new byte[6];
		DEBEDD9C.smethod_0(array2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		list2.Add((array2, new byte[6], "hash_check3"));
		List<(byte[], byte[], string)> list3 = list2;
		using (List<(string, string, string)>.Enumerator enumerator = list.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				Class14 CS_0024_003C_003E8__locals5 = new Class14();
				CS_0024_003C_003E8__locals5.valueTuple_0 = enumerator.Current;
				byte[] bE14E = (from D601B139 in Class607.B630A78B.object_0[159](0, Class607.B630A78B.object_0[342](CS_0024_003C_003E8__locals5.valueTuple_0.Item1) / 2)
					select Class607.B630A78B.object_0[276](Class607.B630A78B.object_0[31](CS_0024_003C_003E8__locals5.valueTuple_0.Item1, D601B139 * 2, 2), 16)).ToArray();
				int num = Class607.B630A78B.object_0[134](array, bE14E);
				if (num != -1)
				{
					byte[] array3 = (from CF162004 in Class607.B630A78B.object_0[159](0, Class607.B630A78B.object_0[342](CS_0024_003C_003E8__locals5.valueTuple_0.Item2) / 2)
						select Class607.B630A78B.object_0[276](Class607.B630A78B.object_0[31](CS_0024_003C_003E8__locals5.valueTuple_0.Item2, CF162004 * 2, 2), 16)).ToArray();
					Class607.B630A78B.object_0[242](array3, 0, array, num, array3.Length);
					flag = true;
				}
			}
		}
		foreach (var item in list3)
		{
			int num2 = smethod_2(array, item.Item1);
			if (num2 != -1)
			{
				Class607.B630A78B.object_0[242](item.Item2, 0, array, num2, item.Item2.Length);
				flag = true;
			}
		}
		if (flag)
		{
		}
		return array;
	}

	public static byte[] smethod_7(byte[] byte_0)
	{
		bool flag = false;
		byte[] array = byte_0.ToArray();
		List<Tuple<string, string, string>> list = new List<Tuple<string, string, string>>
		{
			Tuple.Create("A3687BB12846", "0123A3602846", "6F70706F207365637572697479"),
			Tuple.Create("B3F5807F01D1", "B3F5807F01D14FF000004FF000007047", "6D743637333920633330"),
			Tuple.Create("B3F5807F04BF4FF4807305F011B84FF0FF307047", "B3F5807F04BF4FF480734FF000004FF000007047", "726567756C6172"),
			Tuple.Create("10B50C680268", "10B5012010BD", "72616D20626C61636B6C697374"),
			Tuple.Create("08B5104B7B441B681B68", "00207047000000000000", "7365636C69625F7365635F757362646C5F656E61626C6564"),
			Tuple.Create("5072656C6F61646572205374617274", "50617463686564204C205374617274", "50617463686564204C6F61646572206D7367"),
			Tuple.Create("F0B58BB002AE20250C460746", "002070470000000000205374617274", "7365635F696D675F61757468"),
			Tuple.Create("FFC0F3400008BD", "FF4FF0000008BD", "6765745F7666795F706F6C696379")
		};
		int num = 0;
		foreach (Tuple<string, string, string> item in list)
		{
			byte[] byte_1 = GClass112.FF06B0AD(item.Item1);
			int num2 = smethod_2(byte_0, byte_1);
			if (num2 != -1)
			{
				byte[] array2 = GClass112.FF06B0AD(item.Item2);
				Class607.B630A78B.object_0[242](array2, 0, array, num2, array2.Length);
				flag = true;
			}
			num++;
		}
		if (flag)
		{
		}
		return array;
	}

	public static byte[] smethod_8(byte[] AD02C52B)
	{
		bool flag = false;
		byte[] array = new byte[AD02C52B.Length];
		Class607.B630A78B.object_0[1010](AD02C52B, array, AD02C52B.Length);
		List<(string, string, string)> list = new List<(string, string, string)>
		{
			("A3687BB12846", "0123A3602846", "oppo security"),
			("B3F5807F01D1", "B3F5807F01D14FF000004FF000007047", "mt6739 c30"),
			("B3F5807F04BF4FF4807305F011B84FF0FF307047", "B3F5807F04BF4FF480734FF000004FF000007047", "regular"),
			("10B50C680268", "10B5012010BD", "ram blacklist"),
			("08B5104B7B441B681B68", "00207047000000000000", "seclib_sec_usbdl_enabled"),
			("5072656C6F61646572205374617274", "50617463686564204C205374617274", "Patched loader msg"),
			("F0B58BB002AE20250C460746", "002070470000000000205374617274", "sec_img_auth"),
			("FFC0F3400008BD", "FF4FF0000008BD", "get_vfy_policy")
		};
		using (List<(string, string, string)>.Enumerator enumerator = list.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				Class15 CS_0024_003C_003E8__locals5 = new Class15();
				CS_0024_003C_003E8__locals5.valueTuple_0 = enumerator.Current;
				byte[] byte_ = (from int_0 in Class607.B630A78B.object_0[159](0, Class607.B630A78B.object_0[342](CS_0024_003C_003E8__locals5.valueTuple_0.Pattern) / 2)
					select Class607.B630A78B.object_0[276](Class607.B630A78B.object_0[31](CS_0024_003C_003E8__locals5.valueTuple_0.Pattern, int_0 * 2, 2), 16)).ToArray();
				byte[] array2 = (from CE98F0AC in Class607.B630A78B.object_0[159](0, Class607.B630A78B.object_0[342](CS_0024_003C_003E8__locals5.valueTuple_0.Replacement) / 2)
					select Class607.B630A78B.object_0[276](Class607.B630A78B.object_0[31](CS_0024_003C_003E8__locals5.valueTuple_0.Replacement, CE98F0AC * 2, 2), 16)).ToArray();
				int num = GClass112.smethod_24(array, byte_, 0);
				if (num != -1)
				{
					Class607.B630A78B.object_0[242](array2, 0, array, num, array2.Length);
					flag = true;
				}
			}
		}
		if (flag)
		{
		}
		return array;
	}

	public static byte[] smethod_9(byte[] byte_0, C10B6585 ********)
	{
		byte_0 = smethod_8(byte_0);
		byte[] array = (byte[])Class607.B630A78B.object_0[302](byte_0);
		byte[] byte_1 = byte_0;
		byte[] array2 = new byte[6];
		DEBEDD9C.smethod_0(array2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int num = smethod_2(byte_1, array2);
		if (num != -1)
		{
			Class607.B630A78B.object_0[242](new byte[4], 0, array, num, 4);
		}
		byte[] byte_2 = byte_0;
		byte[] array3 = new byte[8];
		DEBEDD9C.smethod_0(array3, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int num2 = smethod_2(byte_2, array3);
		if (num2 != -1)
		{
			uint num3 = Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(array, num2 - 4, 4))[0]);
			int num4 = Class607.B630A78B.object_0[1262](num3 - ********.list_0[2].uint_1);
			if (Class607.B630A78B.object_0[40](GClass111.C3B9331C("<I", GClass112.smethod_14(array, num4 + 4, 4))[0]) == 3)
			{
				array[num4] = 1;
			}
		}
		else
		{
			byte[] byte_3 = byte_0;
			byte[] array4 = new byte[17];
			DEBEDD9C.smethod_0(array4, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			num2 = smethod_2(byte_3, array4);
			if (num2 != -1)
			{
				array[num2 + 16] = 1;
			}
			else
			{
				byte[] byte_4 = byte_0;
				byte[] array5 = new byte[17];
				DEBEDD9C.smethod_0(array5, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num2 = smethod_2(byte_4, array5);
				if (num2 != -1)
				{
					array[num2 + 16] = 1;
				}
			}
		}
		int f = 0;
		while (true)
		{
			byte[] byte_5 = byte_0;
			byte[] array6 = new byte[6];
			DEBEDD9C.smethod_0(array6, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			if ((num2 = smethod_2(byte_5, array6, f)) == -1)
			{
				break;
			}
			byte[] array_ = new byte[4];
			DEBEDD9C.smethod_0(array_, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			E13FE3AF.smethod_0(array_, 0, array, num2, 4);
			f = num2 + 1;
		}
		byte[] byte_6 = byte_0;
		byte[] array7 = new byte[10];
		DEBEDD9C.smethod_0(array7, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int num5 = smethod_2(byte_6, array7);
		if (num5 != -1)
		{
			array[num5] = 0;
		}
		int num6 = smethod_2(byte_0, GClass111.smethod_2("<I", new object[1] { 3221684228u }));
		if (num6 != -1)
		{
			Class607.B630A78B.object_0[979](array, num6, 4);
		}
		else
		{
			byte[] byte_7 = byte_0;
			byte[] array8 = new byte[8];
			DEBEDD9C.smethod_0(array8, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			num6 = smethod_2(byte_7, array8);
			if (num6 != -1)
			{
				byte[] array_2 = new byte[8];
				DEBEDD9C.smethod_0(array_2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				E13FE3AF.smethod_0(array_2, 0, array, num6, 8);
			}
			else
			{
				byte[] byte_8 = byte_0;
				byte[] array9 = new byte[14];
				DEBEDD9C.smethod_0(array9, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num6 = smethod_2(byte_8, array9);
				if (num6 != -1)
				{
					byte[] array_3 = new byte[14];
					DEBEDD9C.smethod_0(array_3, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
					E13FE3AF.smethod_0(array_3, 0, array, num6, 14);
				}
			}
		}
		byte[] byte_9 = byte_0;
		byte[] array10 = new byte[10];
		DEBEDD9C.smethod_0(array10, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int num7 = smethod_2(byte_9, array10);
		if (num7 != -1)
		{
			E13FE3AF.smethod_0(new byte[2] { 0, 35 }, 0, array, num7, 2);
		}
		int num8 = smethod_2(byte_0, GClass111.smethod_2("<I", new object[1] { 3221356627u }));
		if (num8 != -1)
		{
			Class607.B630A78B.object_0[979](array, num8, 4);
		}
		byte[] byte_10 = byte_0;
		byte[] array11 = new byte[10];
		DEBEDD9C.smethod_0(array11, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int num9 = smethod_2(byte_10, array11);
		if (num9 != -1)
		{
			E13FE3AF.smethod_0(new byte[4] { 79, 240, 0, 0 }, 0, array, num9 + 4, 4);
		}
		int num10 = smethod_2(byte_0, GClass111.smethod_2("<I", new object[1] { 3221487629u }));
		if (num10 != -1)
		{
			Class607.B630A78B.object_0[979](array, num10, 4);
		}
		bool flag = false;
		int num11 = 0;
		while (num11 != -1)
		{
			byte[] byte_11 = byte_0;
			byte[] array12 = new byte[10];
			DEBEDD9C.smethod_0(array12, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			num11 = GClass112.smethod_24(byte_11, array12, 0);
			if (num11 != -1)
			{
				byte[] array_4 = new byte[8];
				DEBEDD9C.smethod_0(array_4, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				E13FE3AF.smethod_0(array_4, 0, array, num11, 8);
				flag = true;
				continue;
			}
			byte[] byte_12 = byte_0;
			byte[] array13 = new byte[6];
			DEBEDD9C.smethod_0(array13, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			num11 = GClass112.smethod_24(byte_12, array13, 0);
			if (num11 != -1)
			{
				byte[] array_5 = new byte[6];
				DEBEDD9C.smethod_0(array_5, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				E13FE3AF.smethod_0(array_5, 0, array, num11, 6);
				byte[] byte_13 = byte_0;
				byte[] array14 = new byte[6];
				DEBEDD9C.smethod_0(array14, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				int num12 = GClass112.smethod_24(byte_13, array14, 0);
				if (num12 != -1)
				{
					byte[] array_6 = new byte[6];
					DEBEDD9C.smethod_0(array_6, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
					E13FE3AF.smethod_0(array_6, 0, array, num12, 6);
				}
				flag = true;
			}
		}
		if (flag)
		{
		}
		return array;
	}

	public static byte[] BB93B517(byte[] byte_0, C10B6585 c10B6585_0)
	{
		byte_0 = smethod_8(byte_0);
		byte[] array = new byte[byte_0.Length];
		Class607.B630A78B.object_0[1010](byte_0, array, byte_0.Length);
		int num = 0;
		byte[] byte_1 = byte_0;
		byte[] array2 = new byte[6];
		DEBEDD9C.smethod_0(array2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int num2 = smethod_2(byte_1, array2);
		if (num2 != -1)
		{
			Class607.B630A78B.object_0[242](new byte[4], 0, array, num2, 4);
		}
		if (smethod_2(byte_0, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[47](), "[oplus]")) != -1 || smethod_2(byte_0, Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[47](), "[OPPO]")) != -1)
		{
			byte[] byte_2 = byte_0;
			byte[] array3 = new byte[8];
			DEBEDD9C.smethod_0(array3, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			int num3 = smethod_2(byte_2, array3);
			if (num3 != -1)
			{
				int num4 = Class607.B630A78B.object_0[137](array, num3 - 4);
				int num5 = num4 - Class607.B630A78B.object_0[1262](c10B6585_0.list_0[2].uint_1);
				if (Class607.B630A78B.object_0[137](array, num5) == 3)
				{
					array[num5] = 1;
				}
			}
			else
			{
				byte[] byte_3 = byte_0;
				byte[] array4 = new byte[17];
				DEBEDD9C.smethod_0(array4, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num3 = smethod_2(byte_3, array4);
				if (num3 != -1)
				{
					array[num3 + 16] = 1;
				}
				else
				{
					byte[] byte_4 = byte_0;
					byte[] array5 = new byte[17];
					DEBEDD9C.smethod_0(array5, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
					num3 = smethod_2(byte_4, array5);
					if (num3 != -1)
					{
						array[num3 + 16] = 1;
					}
				}
			}
			num = 0;
			while (true)
			{
				byte[] byte_5 = byte_0;
				byte[] array6 = new byte[6];
				DEBEDD9C.smethod_0(array6, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				if ((num3 = smethod_2(byte_5, array6, num)) == -1)
				{
					break;
				}
				byte[] array_ = new byte[4];
				DEBEDD9C.smethod_0(array_, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				E13FE3AF.smethod_0(array_, 0, array, num3, 4);
				num = num3 + 1;
			}
		}
		byte[] byte_6 = byte_0;
		byte[] array7 = new byte[10];
		DEBEDD9C.smethod_0(array7, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int num6 = smethod_2(byte_6, array7);
		if (num6 != -1)
		{
			array[num6] = 0;
		}
		int num7 = smethod_2(byte_0, Class607.B630A78B.object_0[190](3221684228u));
		if (num7 != -1)
		{
			Class607.B630A78B.object_0[242](Class607.B630A78B.object_0[241](0), 0, array, num7, 4);
		}
		else
		{
			byte[] byte_7 = byte_0;
			byte[] array8 = new byte[8];
			DEBEDD9C.smethod_0(array8, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			if ((num7 = smethod_2(byte_7, array8)) != -1)
			{
				byte[] array_2 = new byte[8];
				DEBEDD9C.smethod_0(array_2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				E13FE3AF.smethod_0(array_2, 0, array, num7, 8);
			}
			else
			{
				byte[] byte_8 = byte_0;
				byte[] array9 = new byte[14];
				DEBEDD9C.smethod_0(array9, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				num7 = smethod_2(byte_8, array9);
				if (num7 != -1)
				{
					byte[] array_3 = new byte[14];
					DEBEDD9C.smethod_0(array_3, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
					E13FE3AF.smethod_0(array_3, 0, array, num7, 14);
				}
			}
		}
		byte[] byte_9 = byte_0;
		byte[] array10 = new byte[10];
		DEBEDD9C.smethod_0(array10, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int num8 = smethod_2(byte_9, array10);
		if (num8 != -1)
		{
			E13FE3AF.smethod_0(new byte[2] { 0, 35 }, 0, array, num8, 2);
		}
		int num9 = smethod_2(byte_0, Class607.B630A78B.object_0[190](3221356627u));
		if (num9 != -1)
		{
			Class607.B630A78B.object_0[242](Class607.B630A78B.object_0[241](0), 0, array, num9, 4);
		}
		byte[] byte_10 = byte_0;
		byte[] array11 = new byte[10];
		DEBEDD9C.smethod_0(array11, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		int num10 = smethod_2(byte_10, array11);
		if (num10 != -1)
		{
			E13FE3AF.smethod_0(new byte[4] { 79, 240, 0, 0 }, 0, array, num10 + 4, 4);
		}
		int num11 = smethod_2(byte_0, Class607.B630A78B.object_0[190](3221487629u));
		if (num11 != -1)
		{
			Class607.B630A78B.object_0[242](Class607.B630A78B.object_0[241](0), 0, array, num11, 4);
		}
		int num12 = 0;
		bool flag = false;
		while (num12 != -1)
		{
			byte[] array12 = new byte[8];
			DEBEDD9C.smethod_0(array12, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			num12 = GClass112.smethod_24(array, array12, 0);
			if (num12 != -1)
			{
				byte[] array_4 = new byte[8];
				DEBEDD9C.smethod_0(array_4, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				E13FE3AF.smethod_0(array_4, 0, array, num12, 8);
				flag = true;
				continue;
			}
			byte[] array13 = new byte[6];
			DEBEDD9C.smethod_0(array13, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
			num12 = GClass112.smethod_24(array, array13, 0);
			if (num12 != -1)
			{
				byte[] array_5 = new byte[6];
				DEBEDD9C.smethod_0(array_5, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				E13FE3AF.smethod_0(array_5, 0, array, num12, 6);
				byte[] array14 = new byte[6];
				DEBEDD9C.smethod_0(array14, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				int num13 = GClass112.smethod_24(array, array14, 0);
				if (num13 != -1)
				{
					byte[] array_6 = new byte[6];
					DEBEDD9C.smethod_0(array_6, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
					E13FE3AF.smethod_0(array_6, 0, array, num13, 6);
				}
				flag = true;
			}
		}
		if (flag)
		{
		}
		return array;
	}
}
