using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

public class GClass105 : Button, GInterface2
{
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private int int_0;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private GEnum36 genum36_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private bool bool_0;

	private readonly Class56 class56_0;

	private SizeF sizeF_0;

	private Image image_0;

	[Browsable(false)]
	public int Int32_0
	{
		[CompilerGenerated]
		get
		{
			return int_0;
		}
		[CompilerGenerated]
		set
		{
			int_0 = value;
		}
	}

	[Browsable(false)]
	public GClass106 DD8EF439 => GClass106.GClass106_0;

	[Browsable(false)]
	public GEnum36 C8B39594
	{
		[CompilerGenerated]
		get
		{
			return genum36_0;
		}
		[CompilerGenerated]
		set
		{
			genum36_0 = value;
		}
	}

	public bool D08C0818
	{
		[CompilerGenerated]
		get
		{
			return bool_0;
		}
		[CompilerGenerated]
		set
		{
			bool_0 = value;
		}
	}

	public Image CC180D97
	{
		get
		{
			return image_0;
		}
		set
		{
			image_0 = value;
			if (Class607.B630A78B.object_0[638](this))
			{
				Class607.B630A78B.object_0[1277](this, D53F613A());
			}
			Class607.B630A78B.object_0[583](this);
		}
	}

	public override string Text
	{
		get
		{
			return Class607.B630A78B.object_0[843](this);
		}
		set
		{
			Class607.B630A78B.object_0[864](this, value);
			sizeF_0 = Class607.B630A78B.object_0[1203](Class607.B630A78B.object_0[278](this), Class607.B630A78B.object_0[1019](value), DD8EF439.font_1);
			if (Class607.B630A78B.object_0[638](this))
			{
				Class607.B630A78B.object_0[1277](this, D53F613A());
			}
			Class607.B630A78B.object_0[583](this);
		}
	}

	public GClass105()
	{
		Class607.B630A78B.object_0[1113](this);
		D08C0818 = true;
		class56_0 = new Class56(E03D843F: false)
		{
			D31D243E = 0.03,
			F8AB7C94_0 = F8AB7C94.EFA5971F
		};
		class56_0.AE044886 += delegate
		{
			Class607.B630A78B.object_0[583](this);
		};
		Class607.B630A78B.object_0[308](this, Class607.B630A78B.object_0[268]());
	}

	protected override void OnMouseUp(MouseEventArgs mevent)
	{
		Class607.B630A78B.object_0[886](this, mevent);
		class56_0.F10037B0(Enum14.const_0, Class607.B630A78B.object_0[613](mevent));
	}

	public GraphicsPath DC019E81(float EE37A43D, float float_0, float float_1, float C0366C20, float float_2)
	{
		GraphicsPath graphicsPath = Class607.B630A78B.object_0[81]();
		Class607.B630A78B.object_0[786](graphicsPath, EE37A43D + float_2, float_0, EE37A43D + float_1 - float_2 * 2f, float_0);
		Class607.B630A78B.object_0[679](graphicsPath, EE37A43D + float_1 - float_2 * 2f, float_0, float_2 * 2f, float_2 * 2f, 270f, 90f);
		Class607.B630A78B.object_0[786](graphicsPath, EE37A43D + float_1, float_0 + float_2, EE37A43D + float_1, float_0 + C0366C20 - float_2 * 2f);
		Class607.B630A78B.object_0[679](graphicsPath, EE37A43D + float_1 - float_2 * 2f, float_0 + C0366C20 - float_2 * 2f, float_2 * 2f, float_2 * 2f, 0f, 90f);
		Class607.B630A78B.object_0[786](graphicsPath, EE37A43D + float_1 - float_2 * 2f, float_0 + C0366C20, EE37A43D + float_2, float_0 + C0366C20);
		Class607.B630A78B.object_0[679](graphicsPath, EE37A43D, float_0 + C0366C20 - float_2 * 2f, float_2 * 2f, float_2 * 2f, 90f, 90f);
		Class607.B630A78B.object_0[786](graphicsPath, EE37A43D, float_0 + C0366C20 - float_2 * 2f, EE37A43D, float_0 + float_2);
		Class607.B630A78B.object_0[679](graphicsPath, EE37A43D, float_0, float_2 * 2f, float_2 * 2f, 180f, 90f);
		Class607.B630A78B.object_0[608](graphicsPath);
		return graphicsPath;
	}

	protected override void OnPaint(PaintEventArgs B8B24A89)
	{
		Graphics graphics = Class607.B630A78B.object_0[340](B8B24A89);
		Class607.B630A78B.object_0[111](graphics, SmoothingMode.AntiAlias);
		Class607.B630A78B.object_0[423](graphics, TextRenderingHint.AntiAlias);
		Class607.B630A78B.object_0[1141](graphics, Class607.B630A78B.object_0[410](Class607.B630A78B.object_0[1161](this)));
		Rectangle rectangle_ = Class607.B630A78B.object_0[38](this);
		float eE37A43D = Class607.B630A78B.object_0[801](ref rectangle_);
		rectangle_ = Class607.B630A78B.object_0[38](this);
		float float_ = Class607.B630A78B.object_0[1263](ref rectangle_);
		rectangle_ = Class607.B630A78B.object_0[38](this);
		float float_2 = Class607.B630A78B.object_0[56](ref rectangle_) - 1;
		rectangle_ = Class607.B630A78B.object_0[38](this);
		using (GraphicsPath graphicsPath_ = DC019E81(eE37A43D, float_, float_2, Class607.B630A78B.object_0[970](ref rectangle_) - 1, 1f))
		{
			Class240.smethod_0(graphics, D08C0818 ? DD8EF439.CEBAB4AB.brush_0 : DD8EF439.EB0DA2B0(), graphicsPath_);
		}
		if (class56_0.method_1())
		{
			for (int i = 0; i < class56_0.B4A91C14(); i++)
			{
				double num = class56_0.method_5(i);
				Point point_ = class56_0.method_6(i);
				SolidBrush brush_ = Class607.B630A78B.object_0[1044](Class607.B630A78B.object_0[117]((int)(51.0 - num * 50.0), Class607.B630A78B.object_0[639]()));
				int num2 = (int)(num * (double)Class607.B630A78B.object_0[141](this) * 2.0);
				Class607.B630A78B.object_0[333](graphics, brush_, Class607.B630A78B.object_0[1023](Class607.B630A78B.object_0[372](ref point_) - num2 / 2, Class607.B630A78B.object_0[1229](ref point_) - num2 / 2, num2, num2));
			}
		}
		Rectangle rectangle_2 = default(Rectangle);
		Class607.B630A78B.object_0[1024](ref rectangle_2, 8, 6, 24, 24);
		if (Class607.B630A78B.object_0[1205](Class607.B630A78B.object_0[814](this)))
		{
			Class558.A93A2A97(ref rectangle_2, Class606.smethod_0(ref rectangle_2) + 2);
		}
		if (CC180D97 != null)
		{
			Class607.B630A78B.object_0[288](graphics, CC180D97, rectangle_2);
		}
		Rectangle CF143CB = Class607.B630A78B.object_0[38](this);
		if (CC180D97 != null)
		{
			Class556.smethod_0(ref CF143CB, Class119.smethod_0(ref CF143CB) - 44);
			Class558.A93A2A97(ref CF143CB, Class606.smethod_0(ref CF143CB) + 36);
		}
		string string_ = Class607.B630A78B.object_0[1019](Class607.B630A78B.object_0[814](this));
		Font font_ = DD8EF439.font_1;
		Brush brush_2 = DD8EF439.BE2B6333(D08C0818);
		RectangleF rectangleF_ = Class607.B630A78B.object_0[1188](CF143CB);
		StringFormat stringFormat = Class607.B630A78B.object_0[960]();
		Class245.C01A2A1E(stringFormat, StringAlignment.Center);
		Class859.smethod_0(stringFormat, StringAlignment.Center);
		D9985907.DE8FE29E(graphics, string_, font_, brush_2, rectangleF_, stringFormat);
	}

	private Size D53F613A()
	{
		return Class607.B630A78B.object_0[1235](this, Class607.B630A78B.object_0[174](0, 0));
	}

	public override Size GetPreferredSize(Size proposedSize)
	{
		int num = 16;
		if (CC180D97 != null)
		{
			num += 28;
		}
		return Class607.B630A78B.object_0[174]((int)Class607.B630A78B.object_0[1045](Class607.B630A78B.object_0[331](ref sizeF_0)) + num, 36);
	}

	[CompilerGenerated]
	private void FB0047AC(object object_0)
	{
		Class607.B630A78B.object_0[583](this);
	}
}
