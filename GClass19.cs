using System.Collections.Generic;

public class GClass19
{
	public uint uint_0 = 0u;

	public uint uint_1 = 4u;

	public uint uint_2 = 1024u;

	public uint uint_3 = 1028u;

	public uint uint_4 = 1032u;

	public uint A192D118 = 1040u;

	public uint uint_5 = 1044u;

	public uint E51F27BD = 1048u;

	public uint F3BA18B7 = 1052u;

	public uint uint_6 = 1056u;

	public uint uint_7 = 1088u;

	public uint uint_8 = 1144u;

	public uint uint_9 = 2048u;

	public uint D5A4C11B = 2052u;

	public uint C1BAF330 = 2056u;

	public uint uint_10 = 3072u;

	public uint uint_11 = 3076u;

	public uint uint_12 = 3080u;

	public uint uint_13 = 3084u;

	public uint uint_14 = 3088u;

	public uint B306021C = 3092u;

	public uint F1A9E6B0 = 3096u;

	public uint uint_15 = 3100u;

	public uint uint_16 = 3104u;

	public uint EB3CA588 = 3108u;

	public uint uint_17 = 3112u;

	public uint uint_18 = 3116u;

	public uint uint_19 = 3120u;

	public uint A8B9CD9F = 3124u;

	public uint uint_20 = 3128u;

	public uint A63DE02D = 3132u;

	public uint uint_21 = 3136u;

	public Dictionary<string, uint> dictionary_0;

	public uint uint_22;

	public GDelegate2 gdelegate2_0;

	public GDelegate1 gdelegate1_0;

	public bool AB91DA13(string string_0, uint uint_23)
	{
		if (dictionary_0.ContainsKey(string_0))
		{
			uint num = dictionary_0[string_0] + uint_22;
			return gdelegate2_0(num, uint_23);
		}
		return false;
	}

	public uint method_0(string string_0)
	{
		if (dictionary_0.ContainsKey(string_0))
		{
			uint d1ADCA = dictionary_0[string_0] + uint_22;
			List<uint> list = gdelegate1_0(d1ADCA);
			if (list.Count > 0)
			{
				return list[0];
			}
		}
		return 0u;
	}

	public GClass19(GClass11 E5025E32)
	{
		Class607.B630A78B.object_0[571](this);
		dictionary_0 = new Dictionary<string, uint>
		{
			{ "GCPU_REG_CTL", 0u },
			{ "GCPU_REG_MSC", 4u },
			{ "GCPU_REG_PC_CTL", 1024u },
			{ "GCPU_REG_MEM_ADDR", 1028u },
			{ "GCPU_REG_MEM_DATA", 1032u },
			{ "GCPU_REG_READ_REG", 1040u },
			{ "GCPU_REG_MONCTL", 1044u },
			{ "GCPU_REG_DRAM_MON", 1048u },
			{ "GCPU_REG_CYC", 1052u },
			{ "GCPU_REG_DRAM_INST_BASE", 1056u },
			{ "GCPU_REG_TRAP_START", 1088u },
			{ "GCPU_REG_TRAP_END", 1144u },
			{ "GCPU_REG_INT_SET", 2048u },
			{ "GCPU_REG_INT_CLR", 2052u },
			{ "GCPU_REG_INT_EN", 2056u },
			{ "GCPU_REG_MEM_CMD", 3072u },
			{ "GCPU_REG_MEM_P0", 3076u },
			{ "GCPU_REG_MEM_P1", 3080u },
			{ "GCPU_REG_MEM_P2", 3084u },
			{ "GCPU_REG_MEM_P3", 3088u },
			{ "GCPU_REG_MEM_P4", 3092u },
			{ "GCPU_REG_MEM_P5", 3096u },
			{ "GCPU_REG_MEM_P6", 3100u },
			{ "GCPU_REG_MEM_P7", 3104u },
			{ "GCPU_REG_MEM_P8", 3108u },
			{ "GCPU_REG_MEM_P9", 3112u },
			{ "GCPU_REG_MEM_P10", 3116u },
			{ "GCPU_REG_MEM_P11", 3120u },
			{ "GCPU_REG_MEM_P12", 3124u },
			{ "GCPU_REG_MEM_P13", 3128u },
			{ "GCPU_REG_MEM_P14", 3132u },
			{ "GCPU_REG_MEM_Slot", 3136u }
		};
		uint_22 = E5025E32.uint_1;
		gdelegate2_0 = E5025E32.F2B1FB97;
		gdelegate1_0 = E5025E32.gdelegate1_0;
	}
}
