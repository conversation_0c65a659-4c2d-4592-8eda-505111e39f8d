using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;
using MotoKingPro.Core;
using MotoKingPro.UI;
using MotoKingPro.Platforms;

namespace MotoKingPro
{
    /// <summary>
    /// Main application form for MotoKingPro - Mobile device repair and flashing tool
    /// Supports Qualcomm, MediaTek, Motorola devices and ADB operations
    /// </summary>
    public partial class MainForm : Form
    {
        #region Helper Classes for Operation Management

        /// <summary>
        /// Helper class for Qualcomm operations UI state management
        /// </summary>
        [CompilerGenerated]
        private sealed class QualcommOperationHelper
        {
            public MainForm ParentForm { get; set; }
            public bool IsOperationRunning { get; set; }

            internal void UpdateUIState()
            {
                ParentForm.tabSelector.Enabled = !IsOperationRunning;
                ParentForm.historyButton.Enabled = !IsOperationRunning;
                ParentForm.accountButton.Enabled = !IsOperationRunning;
            }

            internal void FocusQualcommTab()
            {
                ParentForm.qualcommPage.FocusTextOutput();
            }
        }

        /// <summary>
        /// Helper class for ADB/Services operations UI state management
        /// </summary>
        [CompilerGenerated]
        private sealed class ServicesOperationHelper
        {
            public MainForm ParentForm { get; set; }
            public bool IsOperationRunning { get; set; }

            internal void UpdateUIState()
            {
                ParentForm.tabSelector.Enabled = !IsOperationRunning;
                ParentForm.historyButton.Enabled = !IsOperationRunning;
                ParentForm.accountButton.Enabled = !IsOperationRunning;
            }

            internal void FocusServicesTab()
            {
                ParentForm.servicesPage.FocusTextOutput();
            }
        }

        /// <summary>
        /// Helper class for Motorola operations UI state management
        /// </summary>
        [CompilerGenerated]
        private sealed class MotorolaOperationHelper
        {
            public MainForm ParentForm { get; set; }
            public bool IsOperationRunning { get; set; }

            internal void UpdateUIState()
            {
                ParentForm.tabSelector.Enabled = !IsOperationRunning;
                ParentForm.historyButton.Enabled = !IsOperationRunning;
                ParentForm.accountButton.Enabled = !IsOperationRunning;
            }

            internal void FocusMotorolaTab()
            {
                ParentForm.motorolaPage.FocusTextOutput();
            }
        }

        /// <summary>
        /// Helper class for MediaTek operations UI state management
        /// </summary>
        [CompilerGenerated]
        private sealed class MediaTekOperationHelper
        {
            public MainForm ParentForm { get; set; }
            public bool IsOperationRunning { get; set; }

            internal void UpdateUIState()
            {
                ParentForm.tabSelector.Enabled = !IsOperationRunning;
                ParentForm.historyButton.Enabled = !IsOperationRunning;
                ParentForm.accountButton.Enabled = !IsOperationRunning;
            }

            internal void FocusMediaTekTab()
            {
                ParentForm.mediaTekPage.FocusTextOutput();
            }
        }

        #endregion

        #region Fields

        // Mouse drag support for borderless window
        private Point lastMousePosition = new Point();
        
        // Custom control for device status display
        private DeviceStatusControl deviceStatusControl = new DeviceStatusControl();

        // Operation tracking objects for each platform
        private OperationTracker servicesOperation;      // ADB/Services operations
        private OperationTracker qualcommOperation;      // Qualcomm operations  
        private OperationTracker mediaTekOperation;      // MediaTek operations
        private OperationTracker motorolaOperation;      // Motorola operations

        private bool isClosingForced = false;
        private IContainer components = null;

        #endregion

        #region UI Controls

        // Main UI Controls
        private Panel navigationPanel;
        private Button maximizeButton;
        private Button minimizeButton;
        private Button closeButton;
        private CustomLabel titleLabel;
        private TabSelector tabSelector;
        private CustomTabControl mainTabControl;
        
        // Tab Pages
        private TabPage motorolaTabPage;
        private TabPage qualcommTabPage;
        private TabPage servicesTabPage;
        private TabPage mediaTekTabPage;
        
        // Platform-specific pages
        private MediaTekPage mediaTekPage;
        private QualcommPage qualcommPage;
        private MotorolaPage motorolaPage;
        private Button historyButton;
        private ServicesPage servicesPage;
        private Panel mainContentPanel;
        private Button accountButton;
        private Label versionLabel;
        private Button signOutButton;

        #endregion

        #region Constructor

        public MainForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
            SetupEventHandlers();
            LoadApplicationSettings();
        }

        #endregion

        #region Initialization Methods

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "MotoKingPro";
            this.BackColor = Color.FromArgb(45, 45, 48);
            
            // Navigation panel (title bar)
            navigationPanel = new Panel
            {
                Size = new Size(1200, 40),
                Location = new Point(0, 0),
                BackColor = Color.FromArgb(37, 37, 38),
                Dock = DockStyle.Top
            };
            
            // Title label
            titleLabel = new CustomLabel
            {
                Text = "MotoKingPro - Mobile Device Repair Tool",
                Location = new Point(10, 10),
                Size = new Size(300, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            
            // Window control buttons
            closeButton = new Button
            {
                Text = "✕",
                Size = new Size(30, 30),
                Location = new Point(1170, 5),
                BackColor = Color.FromArgb(232, 17, 35),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            closeButton.FlatAppearance.BorderSize = 0;
            
            maximizeButton = new Button
            {
                Text = "□",
                Size = new Size(30, 30),
                Location = new Point(1135, 5),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            maximizeButton.FlatAppearance.BorderSize = 0;
            
            minimizeButton = new Button
            {
                Text = "─",
                Size = new Size(30, 30),
                Location = new Point(1100, 5),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            minimizeButton.FlatAppearance.BorderSize = 0;
            
            navigationPanel.Controls.AddRange(new Control[] { titleLabel, closeButton, maximizeButton, minimizeButton });
            
            // Tab selector
            tabSelector = new TabSelector
            {
                Size = new Size(400, 40),
                Location = new Point(10, 50)
            };
            
            // History and account buttons
            historyButton = new Button
            {
                Text = "History",
                Size = new Size(80, 30),
                Location = new Point(1020, 55),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            
            accountButton = new Button
            {
                Text = "Account",
                Size = new Size(80, 30),
                Location = new Point(1110, 55),
                BackColor = Color.FromArgb(60, 60, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            
            // Main content panel
            mainContentPanel = new Panel
            {
                Size = new Size(1180, 680),
                Location = new Point(10, 100),
                BackColor = Color.FromArgb(45, 45, 48),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };
            
            // Version label
            versionLabel = new Label
            {
                Text = ApplicationInfo.GetVersionString(),
                Location = new Point(10, 770),
                Size = new Size(200, 20),
                ForeColor = Color.Gray,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left
            };
            
            // Add all controls to form
            this.Controls.AddRange(new Control[] 
            { 
                navigationPanel, tabSelector, historyButton, accountButton, 
                mainContentPanel, versionLabel 
            });
            
            this.ResumeLayout(false);
        }

        private void InitializeCustomComponents()
        {
            // Initialize theme and styling
            new ThemeManager().ApplyTheme(this);
            
            // Create platform-specific pages
            qualcommPage = new QualcommPage { Dock = DockStyle.Fill };
            mediaTekPage = new MediaTekPage { Dock = DockStyle.Fill };
            motorolaPage = new MotorolaPage { Dock = DockStyle.Fill };
            servicesPage = new ServicesPage { Dock = DockStyle.Fill };
            
            // Setup platform-specific pages
            qualcommPage.SetParentForm(this);
            mediaTekPage.SetParentForm(this);
            motorolaPage.SetParentForm(this);
            servicesPage.SetParentForm(this);
            
            // Setup event handlers for operations
            qualcommPage.OperationStateChanged += OnQualcommOperationStateChanged;
            mediaTekPage.OperationStateChanged += OnMediaTekOperationStateChanged;
            motorolaPage.OperationStateChanged += OnMotorolaOperationStateChanged;
            servicesPage.OperationStateChanged += OnServicesOperationStateChanged;
            
            // Add device status control to main panel
            mainContentPanel.Controls.Add(deviceStatusControl);
            deviceStatusControl.Dock = DockStyle.Fill;
            deviceStatusControl.Visible = false;
            
            // Show default page (Motorola)
            ShowPage(motorolaPage);
        }

        private void SetupEventHandlers()
        {
            // Window control events
            closeButton.Click += (s, e) => this.Close();
            maximizeButton.Click += (s, e) => this.WindowState = (this.WindowState == FormWindowState.Maximized) ? FormWindowState.Normal : FormWindowState.Maximized;
            minimizeButton.Click += (s, e) => this.WindowState = FormWindowState.Minimized;
            
            // Tab selector events
            tabSelector.SelectedIndexChanged += TabSelector_SelectedIndexChanged;
            
            // Button events
            historyButton.Click += HistoryButton_Click;
            accountButton.Click += AccountButton_Click;
            
            // Window drag events
            navigationPanel.MouseDown += OnNavigationPanelMouseDown;
            navigationPanel.MouseMove += OnNavigationPanelMouseMove;
            navigationPanel.MouseUp += OnNavigationPanelMouseUp;
            titleLabel.MouseDown += OnNavigationPanelMouseDown;
            titleLabel.MouseMove += OnNavigationPanelMouseMove;
            titleLabel.MouseUp += OnNavigationPanelMouseUp;
        }

        private void LoadApplicationSettings()
        {
            // Load auto-loader files
            LoadAutoLoaderFiles();
            
            // Initialize MediaTek page
            mediaTekPage.InitializeAutoLoaders();
        }

        #endregion

        #region Event Handlers

        private void TabSelector_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch (tabSelector.SelectedIndex)
            {
                case 0: // Motorola
                    ShowPage(motorolaPage);
                    break;
                case 1: // Qualcomm
                    ShowPage(qualcommPage);
                    break;
                case 2: // Services
                    ShowPage(servicesPage);
                    break;
                case 3: // MediaTek
                    ShowPage(mediaTekPage);
                    break;
            }
        }

        private void HistoryButton_Click(object sender, EventArgs e)
        {
            // Show operation history
            MessageBox.Show("Operation History feature coming soon!", "History", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AccountButton_Click(object sender, EventArgs e)
        {
            // Show account management
            MessageBox.Show("Account Management feature coming soon!", "Account", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        #region Operation Event Handlers

        private void OnServicesOperationStateChanged(bool isRunning, string operationName)
        {
            var helper = new ServicesOperationHelper
            {
                ParentForm = this,
                IsOperationRunning = isRunning
            };

            servicesPage.SetOperationState(helper.IsOperationRunning);
            this.InvokeIfRequired(() => helper.UpdateUIState());

            if (helper.IsOperationRunning)
            {
                DateTime startTime = DateTime.Now;
                servicesOperation = new OperationTracker
                {
                    StartedAt = startTime,
                    OperationName = operationName,
                    Tab = "Services"
                };
                servicesPage.ClearLog();
                this.InvokeIfRequired(() => helper.FocusServicesTab());
            }
            else
            {
                DateTime endTime = DateTime.Now;
                servicesPage.LogMessage("Operation: ");
                servicesPage.LogMessage(operationName, LogLevel.Warning, false, true);
                servicesPage.LogMessage("Elapsed time: ");
                TimeSpan elapsed = endTime - servicesOperation.StartedAt;
                servicesPage.LogMessage(elapsed.ToString(@"hh\:mm\:ss"), LogLevel.Warning, false, true);
                servicesOperation.EndedAt = endTime;
                servicesOperation.Items = servicesPage.GetLogItems();
                OperationHistory.Instance.Operations.Add(servicesOperation);
                OperationHistory.SaveToFile();
            }
        }

        private void OnQualcommOperationStateChanged(bool isRunning, string operationName)
        {
            var helper = new QualcommOperationHelper
            {
                ParentForm = this,
                IsOperationRunning = isRunning
            };

            qualcommPage.SetOperationState(helper.IsOperationRunning);
            this.InvokeIfRequired(() => helper.UpdateUIState());

            if (helper.IsOperationRunning)
            {
                DateTime startTime = DateTime.Now;
                qualcommOperation = new OperationTracker
                {
                    StartedAt = startTime,
                    OperationName = operationName,
                    Tab = "Qualcomm"
                };
                qualcommPage.ClearLog();
                this.InvokeIfRequired(() => helper.FocusQualcommTab());
            }
            else
            {
                DateTime endTime = DateTime.Now;
                qualcommPage.LogMessage("Operation: ");
                qualcommPage.LogMessage(operationName, LogLevel.Warning, false, true);
                qualcommPage.LogMessage("Elapsed time: ");
                TimeSpan elapsed = endTime - qualcommOperation.StartedAt;
                qualcommPage.LogMessage(elapsed.ToString(@"hh\:mm\:ss"), LogLevel.Warning, false, true);
                qualcommOperation.EndedAt = endTime;
                qualcommOperation.Items = qualcommPage.GetLogItems();
                OperationHistory.Instance.Operations.Add(qualcommOperation);
                OperationHistory.SaveToFile();
            }
        }

        private void OnMediaTekOperationStateChanged(bool isRunning, string operationName)
        {
            var helper = new MediaTekOperationHelper
            {
                ParentForm = this,
                IsOperationRunning = isRunning
            };

            mediaTekPage.SetOperationState(helper.IsOperationRunning);
            this.InvokeIfRequired(() => helper.UpdateUIState());

            if (helper.IsOperationRunning)
            {
                DateTime startTime = DateTime.Now;
                mediaTekOperation = new OperationTracker
                {
                    StartedAt = startTime,
                    OperationName = operationName,
                    Tab = "MediaTek"
                };
                mediaTekPage.ClearLog();
                this.InvokeIfRequired(() => helper.FocusMediaTekTab());
            }
            else
            {
                DateTime endTime = DateTime.Now;
                mediaTekPage.LogMessage("Operation: ");
                mediaTekPage.LogMessage(operationName, LogLevel.Warning, false, true);
                mediaTekPage.LogMessage("Elapsed time: ");
                TimeSpan elapsed = endTime - mediaTekOperation.StartedAt;
                mediaTekPage.LogMessage(elapsed.ToString(@"hh\:mm\:ss"), LogLevel.Warning, false, true);
                mediaTekOperation.EndedAt = endTime;
                mediaTekOperation.Items = mediaTekPage.GetLogItems();
                OperationHistory.Instance.Operations.Add(mediaTekOperation);
                OperationHistory.SaveToFile();
            }
        }

        private void OnMotorolaOperationStateChanged(bool isRunning, string operationName)
        {
            var helper = new MotorolaOperationHelper
            {
                ParentForm = this,
                IsOperationRunning = isRunning
            };

            motorolaPage.SetOperationState(helper.IsOperationRunning);
            this.InvokeIfRequired(() => helper.UpdateUIState());

            if (helper.IsOperationRunning)
            {
                DateTime startTime = DateTime.Now;
                motorolaOperation = new OperationTracker
                {
                    StartedAt = startTime,
                    OperationName = operationName,
                    Tab = "Motorola"
                };
                motorolaPage.ClearLog();
                this.InvokeIfRequired(() => helper.FocusMotorolaTab());
            }
            else
            {
                DateTime endTime = DateTime.Now;
                motorolaPage.LogMessage("Operation: ");
                motorolaPage.LogMessage(operationName, LogLevel.Warning, false, true);
                motorolaPage.LogMessage("Elapsed time: ");
                TimeSpan elapsed = endTime - motorolaOperation.StartedAt;
                motorolaPage.LogMessage(elapsed.ToString(@"hh\:mm\:ss"), LogLevel.Warning, false, true);
                motorolaOperation.EndedAt = endTime;
                motorolaOperation.Items = motorolaPage.GetLogItems();
                OperationHistory.Instance.Operations.Add(motorolaOperation);
                OperationHistory.SaveToFile();
            }
        }

        #endregion

        #region Window Management

        private void OnNavigationPanelMouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                lastMousePosition = new Point(e.X, e.Y);
            }
        }

        private void OnNavigationPanelMouseMove(object sender, MouseEventArgs e)
        {
            if (lastMousePosition != Point.Empty)
            {
                this.Location = new Point(
                    this.Left + e.X - lastMousePosition.X,
                    this.Top + e.Y - lastMousePosition.Y);
            }
        }

        private void OnNavigationPanelMouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                lastMousePosition = Point.Empty;
            }
        }

        #endregion

        #region Helper Methods

        private void ShowPage(UserControl page)
        {
            mainContentPanel.Controls.Clear();
            mainContentPanel.Controls.Add(page);
            page.Dock = DockStyle.Fill;
        }

        private void LoadAutoLoaderFiles()
        {
            try
            {
                string autoLoaderPath = Path.Combine(ApplicationSettings.ApplicationPath, @"bin\loader\auto");
                if (Directory.Exists(autoLoaderPath))
                {
                    string[] files = Directory.GetFiles(autoLoaderPath, "*", SearchOption.AllDirectories);

                    foreach (string file in files)
                    {
                        string fileName = Path.GetFileName(file);
                        ApplicationSettings.AutoLoaderFiles[fileName] = file;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't crash the application
                System.Diagnostics.Debug.WriteLine($"Failed to load auto-loader files: {ex.Message}");
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (!isClosingForced)
            {
                var result = MessageBox.Show("Are you sure you want to exit MotoKingPro?", "Exit Confirmation",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            ApplicationSettings.IsExiting = true;
            base.OnFormClosing(e);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && components != null)
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
