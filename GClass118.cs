using System.Collections.Generic;

public static class GClass118
{
	public static EE18D411 ee18D411_0 = new EE18D411();

	private static List<EE18D411.GStruct97> C33FD006;

	public static void smethod_0()
	{
		GClass115.B7AC7521(GEnum39.Fastboot);
		C33FD006 = ee18D411_0.EF9D02A1();
		foreach (EE18D411.GStruct97 item in C33FD006)
		{
			GClass115.smethod_0(new GClass120
			{
				E903E03A = GEnum39.Fastboot,
				BF390303 = item.string_0,
				String_0 = item.string_0,
				String_2 = "Fastboot"
			});
		}
	}
}
