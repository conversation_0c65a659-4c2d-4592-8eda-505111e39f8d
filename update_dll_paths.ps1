# PowerShell script to update DLL paths in FlashToolLibrary.cs
$filePath = "MediaTek/FlashToolLibrary.cs"
$content = Get-Content $filePath -Raw

# Replace all FlashToolLib.dll references with lib/FlashToolLib.dll
$content = $content -replace 'DllImport\("FlashToolLib\.dll"', 'DllImport("lib/FlashToolLib.dll"'

# Write back to file
Set-Content $filePath $content -Encoding UTF8

Write-Host "Updated DLL paths in FlashToolLibrary.cs"
