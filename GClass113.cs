using System;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Net.Security;
using System.Runtime.CompilerServices;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;

public class GClass113
{
	[CompilerGenerated]
	private sealed class BB82EDBF : IAsyncStateMachine
	{
		public int int_0;

		public AsyncTaskMethodBuilder<bool> asyncTaskMethodBuilder_0;

		public GClass112.E39CAE0B e39CAE0B_0;

		public GClass113 gclass113_0;

		private string string_0;

		private string string_1;

		private string string_2;

		private string string_3;

		private long long_0;

		private GStruct91 B53F65B0;

		private string string_4;

		private string string_5;

		private StringContent stringContent_0;

		private HttpResponseMessage httpResponseMessage_0;

		private long CA10043E;

		private HttpResponseMessage httpResponseMessage_1;

		private string FB888FB7;

		private string string_6;

		private string[] C13EDF12;

		private string string_7;

		private long long_1;

		private TaskAwaiter<long> E0B789B6;

		private TaskAwaiter<HttpResponseMessage> D28792A9;

		private TaskAwaiter<string> AA24A593;

		public BB82EDBF()
		{
			Class607.B630A78B.object_0[571](this);
		}

		void IAsyncStateMachine.MoveNext()
		{
			int num = int_0;
			bool result;
			try
			{
				TaskAwaiter<long> awaiter3;
				TaskAwaiter<HttpResponseMessage> awaiter2;
				TaskAwaiter<string> awaiter;
				switch (num)
				{
				default:
					e39CAE0B_0?.Invoke("Connection to server: ", EF1F389C.Yellow, BB24BF3C: true, bool_0: true);
					string_0 = C8087599.E41A7B83.email;
					string_1 = C8087599.E41A7B83.password;
					string_2 = GClass112.smethod_1();
					string_3 = Class607.B630A78B.object_0[995]().ToString();
					if (!Class607.B630A78B.object_0[1205](string_0) && !Class607.B630A78B.object_0[1205](string_1))
					{
						awaiter3 = GClass112.smethod_0(gclass113_0.F4B37CA3).GetAwaiter();
						if (!awaiter3.IsCompleted)
						{
							num = 0;
							int_0 = 0;
							E0B789B6 = awaiter3;
							BB82EDBF stateMachine = this;
							asyncTaskMethodBuilder_0.AwaitUnsafeOnCompleted(ref awaiter3, ref stateMachine);
							return;
						}
						goto IL_0139;
					}
					e39CAE0B_0?.Invoke("Failed", EF1F389C.Error, BB24BF3C: false, bool_0: true);
					result = false;
					goto end_IL_0007;
				case 0:
					awaiter3 = E0B789B6;
					E0B789B6 = default(TaskAwaiter<long>);
					num = -1;
					int_0 = -1;
					goto IL_0139;
				case 1:
					awaiter2 = D28792A9;
					D28792A9 = default(TaskAwaiter<HttpResponseMessage>);
					num = -1;
					int_0 = -1;
					goto IL_029b;
				case 2:
					{
						awaiter = AA24A593;
						AA24A593 = default(TaskAwaiter<string>);
						num = -1;
						int_0 = -1;
						goto IL_0358;
					}
					IL_0139:
					CA10043E = awaiter3.GetResult();
					long_0 = CA10043E;
					B53F65B0 = new GStruct91
					{
						Usuario = string_0,
						Password = string_1,
						HWID = string_2,
						Nonce = string_3,
						Timestamp = long_0
					};
					string_4 = GClass112.B904D2B6.method_0(Class607.B630A78B.object_0[387](B53F65B0));
					string_5 = Class607.B630A78B.object_0[387](string_4);
					stringContent_0 = Class607.B630A78B.object_0[1172](string_5, Class607.B630A78B.object_0[1124](), "application/json");
					awaiter2 = Class607.B630A78B.object_0[616](gclass113_0.F4B37CA3, D09F0D3B.smethod_5(1224530u), (HttpContent)(object)stringContent_0).GetAwaiter();
					if (!awaiter2.IsCompleted)
					{
						num = 1;
						int_0 = 1;
						D28792A9 = awaiter2;
						BB82EDBF stateMachine = this;
						asyncTaskMethodBuilder_0.AwaitUnsafeOnCompleted(ref awaiter2, ref stateMachine);
						return;
					}
					goto IL_029b;
					IL_0358:
					string_7 = awaiter.GetResult();
					FB888FB7 = string_7;
					string_7 = null;
					string_6 = GClass112.B904D2B6.EC2264AF(FB888FB7);
					C13EDF12 = Class538.smethod_0(string_6, new char[1] { '|' });
					if (C13EDF12.Length != 3 || !GClass112.B904D2B6.method_1(C13EDF12[0], C13EDF12[1]) || !Class607.B630A78B.object_0[787](C13EDF12[0], D09F0D3B.smethod_5(1228830u)))
					{
						goto IL_0466;
					}
					long_1 = Class607.B630A78B.object_0[588](C13EDF12[2]);
					if (Class607.B630A78B.object_0[893](long_1 - long_0) > 30L)
					{
						goto IL_0466;
					}
					e39CAE0B_0?.Invoke("Ok", EF1F389C.Success, BB24BF3C: false, bool_0: true);
					result = true;
					goto end_IL_0007;
					IL_029b:
					httpResponseMessage_1 = awaiter2.GetResult();
					httpResponseMessage_0 = httpResponseMessage_1;
					httpResponseMessage_1 = null;
					if (Class607.B630A78B.object_0[866](httpResponseMessage_0) != HttpStatusCode.OK)
					{
						break;
					}
					awaiter = Class607.B630A78B.object_0[452](Class607.B630A78B.object_0[1232](httpResponseMessage_0)).GetAwaiter();
					if (!awaiter.IsCompleted)
					{
						num = 2;
						int_0 = 2;
						AA24A593 = awaiter;
						BB82EDBF stateMachine = this;
						asyncTaskMethodBuilder_0.AwaitUnsafeOnCompleted(ref awaiter, ref stateMachine);
						return;
					}
					goto IL_0358;
					IL_0466:
					FB888FB7 = null;
					string_6 = null;
					C13EDF12 = null;
					break;
				}
				e39CAE0B_0?.Invoke("Failed", EF1F389C.Error, BB24BF3C: false, bool_0: true);
				result = false;
				end_IL_0007:;
			}
			catch (Exception exception)
			{
				int_0 = -2;
				string_0 = null;
				string_1 = null;
				string_2 = null;
				string_3 = null;
				B53F65B0 = default(GStruct91);
				string_4 = null;
				string_5 = null;
				stringContent_0 = null;
				httpResponseMessage_0 = null;
				asyncTaskMethodBuilder_0.SetException(exception);
				return;
			}
			int_0 = -2;
			string_0 = null;
			string_1 = null;
			string_2 = null;
			string_3 = null;
			B53F65B0 = default(GStruct91);
			string_4 = null;
			string_5 = null;
			stringContent_0 = null;
			httpResponseMessage_0 = null;
			asyncTaskMethodBuilder_0.SetResult(result);
		}

		[DebuggerHidden]
		void IAsyncStateMachine.SetStateMachine(IAsyncStateMachine D3AC3D97)
		{
		}
	}

	private readonly HttpClient F4B37CA3;

	public GClass113()
	{
		Class607.B630A78B.object_0[571](this);
		HttpClientHandler obj = Class607.B630A78B.object_0[790]();
		Class345.smethod_0(obj, method_0);
		HttpClientHandler httpMessageHandler_ = obj;
		HttpClient obj2 = Class607.B630A78B.object_0[290]((HttpMessageHandler)(object)httpMessageHandler_);
		Class448.smethod_0(obj2, Class607.B630A78B.object_0[938](30.0));
		F4B37CA3 = obj2;
	}

	private bool method_0(object B30A10B6, X509Certificate x509Certificate_0, X509Chain x509Chain_0, SslPolicyErrors F5043D21)
	{
		return (bool)new GClass128().EF8D5E3B(new object[5] { this, B30A10B6, x509Certificate_0, x509Chain_0, F5043D21 }, 320768);
	}

	[AsyncStateMachine(typeof(BB82EDBF))]
	[DebuggerStepThrough]
	public Task<bool> method_1(GClass112.E39CAE0B e39CAE0B_0)
	{
		BB82EDBF stateMachine = new BB82EDBF();
		stateMachine.asyncTaskMethodBuilder_0 = AsyncTaskMethodBuilder<bool>.Create();
		stateMachine.gclass113_0 = this;
		stateMachine.e39CAE0B_0 = e39CAE0B_0;
		stateMachine.int_0 = -1;
		stateMachine.asyncTaskMethodBuilder_0.Start(ref stateMachine);
		return stateMachine.asyncTaskMethodBuilder_0.Task;
	}

	public bool C48D0DB3(GClass112.E39CAE0B e39CAE0B_0)
	{
		return (bool)new GClass128().DFB12B0F(new object[2] { this, e39CAE0B_0 }, 537656);
	}
}
