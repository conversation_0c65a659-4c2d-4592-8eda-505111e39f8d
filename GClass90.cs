using Newtonsoft.Json;

public class GClass90
{
	[JsonProperty("total_blocks")]
	public int int_0;

	[JsonProperty("block_size")]
	public int int_1;

	[JsonProperty("page_size")]
	public int D7BEBFA5;

	[JsonProperty("num_physical")]
	public int B2A4652F;

	[JsonProperty("manufacturer_id")]
	public int int_2;

	[JsonProperty("serial_num")]
	public long long_0;

	[JsonProperty("fw_version")]
	public string string_0;

	[JsonProperty("mem_type")]
	public string string_1;

	[JsonProperty("prod_name")]
	public string D939C697;

	public GClass90()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
