using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;

public class GClass27
{
	public class C788361A
	{
		public E5964FB0 F38C1081;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private Dictionary<string, uint> D8BD51B0 = new Dictionary<string, uint>();

		public Dictionary<string, uint> Dictionary_0
		{
			[CompilerGenerated]
			get
			{
				return D8BD51B0;
			}
			[CompilerGenerated]
			private set
			{
				D8BD51B0 = value;
			}
		}

		internal uint this[string string_0, uint uint_0 = 0u]
		{
			get
			{
				if (CB9A403F.TryGetValue(string_0, out var value))
				{
					return F38C1081.gclass51_0.EE83B4B7(value + GClass112.C78DEB29.A8054FBA.uint_3 + uint_0)[0];
				}
				if (Dictionary_0.TryGetValue(string_0, out var value2))
				{
					return value2;
				}
				return uint.MaxValue;
			}
			set
			{
				if (CB9A403F.TryGetValue(string_0, out var value2))
				{
					F38C1081.gclass51_0.method_2(value2 + GClass112.C78DEB29.A8054FBA.uint_3 + B4AD4102, value);
				}
				else
				{
					Dictionary_0[string_0] = value;
				}
			}
		}

		public C788361A(E5964FB0 A70B1519)
		{
			Class607.B630A78B.object_0[571](this);
			F38C1081 = A70B1519;
		}
	}

	public E5964FB0 C693AA0B;

	private const int int_0 = 1;

	private const int ECBB332A = 0;

	private const int C7AC57BE = 1;

	private const int int_1 = 2;

	private const int int_2 = 16;

	private const int int_3 = 32;

	private static IReadOnlyDictionary<string, ushort> CB9A403F = new Dictionary<string, ushort>
	{
		{ "HACC_CON", 0 },
		{ "HACC_ACON", 4 },
		{ "HACC_ACON2", 8 },
		{ "HACC_ACONK", 12 },
		{ "HACC_ASRC0", 16 },
		{ "HACC_ASRC1", 20 },
		{ "HACC_ASRC2", 24 },
		{ "HACC_ASRC3", 28 },
		{ "HACC_AKEY0", 32 },
		{ "HACC_AKEY1", 36 },
		{ "HACC_AKEY2", 40 },
		{ "HACC_AKEY3", 44 },
		{ "HACC_AKEY4", 48 },
		{ "HACC_AKEY5", 52 },
		{ "HACC_AKEY6", 56 },
		{ "HACC_AKEY7", 60 },
		{ "HACC_ACFG0", 64 },
		{ "HACC_ACFG1", 68 },
		{ "HACC_ACFG2", 72 },
		{ "HACC_ACFG3", 76 },
		{ "HACC_AOUT0", 80 },
		{ "HACC_AOUT1", 84 },
		{ "HACC_AOUT2", 88 },
		{ "HACC_AOUT3", 92 },
		{ "HACC_SW_OTP0", 96 },
		{ "HACC_SW_OTP1", 100 },
		{ "HACC_SW_OTP2", 104 },
		{ "HACC_SW_OTP3", 108 },
		{ "HACC_SW_OTP4", 112 },
		{ "HACC_SW_OTP5", 116 },
		{ "HACC_SW_OTP6", 120 },
		{ "HACC_SW_OTP7", 124 },
		{ "HACC_SECINIT0", 128 },
		{ "HACC_SECINIT1", 132 },
		{ "HACC_SECINIT2", 136 },
		{ "HACC_MKJ", 160 },
		{ "HACC_UNK", 188 }
	};

	private bool bool_0 = true;

	private const uint uint_0 = 0u;

	private const uint uint_1 = 1u;

	private const uint uint_2 = 2u;

	private const uint uint_3 = 0u;

	private const uint uint_4 = 2u;

	private const uint uint_5 = 48u;

	private const uint uint_6 = 0u;

	private const uint uint_7 = 16u;

	private const uint C79C3490 = 32u;

	private const uint uint_8 = 4096u;

	private const uint uint_9 = 0u;

	private const uint uint_10 = 4096u;

	private const uint uint_11 = 1u;

	private const uint uint_12 = 2u;

	private const uint D2B880B3 = 32768u;

	private const uint uint_13 = 16u;

	private const uint uint_14 = 256u;

	private const uint uint_15 = 2919943146u;

	private const uint B9352BBA = 3449122840u;

	private const uint BCA25E10 = 1177106705u;

	private static uint[] BB8B118B;

	private static uint[] DF9ADAB9;

	private static uint[] uint_16;

	private static uint[] uint_17;

	private readonly C788361A B185D0A1;

	public GClass27(E5964FB0 e5964FB0_0)
	{
		Class607.B630A78B.object_0[571](this);
		C693AA0B = e5964FB0_0;
		B185D0A1 = new C788361A(e5964FB0_0);
	}

	private int BA20530A(uint A90A0532)
	{
		uint num = A90A0532;
		int num2;
		if ((A90A0532 & 0xFFFF) != 0)
		{
			num2 = 1;
		}
		else
		{
			num >>= 16;
			num2 = 17;
		}
		if ((num & 0xFF) == 0)
		{
			num >>= 8;
			num2 += 8;
		}
		if (((ulong)(num << 28) & 0xFFFFFFFFuL) == 0L)
		{
			num >>= 4;
			num2 += 4;
		}
		if (((ulong)(num << 30) & 0xFFFFFFFFuL) == 0L)
		{
			num >>= 2;
			num2 += 2;
		}
		if ((num & 1) == 0)
		{
			num2++;
		}
		return num2;
	}

	private int C7B95604(int DF9EA898, int int_4)
	{
		int num = 1 << DF9EA898;
		int num2 = Class607.B630A78B.object_0[1130](C693AA0B.gclass51_0.EE83B4B7(268465408u)[0] & ~num);
		if (num != 0)
		{
			num = Class607.B630A78B.object_0[229](BA20530A(Class607.B630A78B.object_0[836](num)));
		}
		int a711A = num2 | (int_4 << num - 1);
		C693AA0B.gclass51_0.BE2E2DAC(268465408u, Class607.B630A78B.object_0[836](a711A));
		return num;
	}

	private void C59A7997(uint uint_18)
	{
		C693AA0B.gclass51_0.BE2E2DAC(270622756u, B9B10F3A.smethod_0((uint_18 != 0) ? 131074 : 0));
	}

	private void method_0()
	{
		C693AA0B.gclass51_0.BE2E2DAC(268467968u, 0u);
		uint num = C693AA0B.gclass51_0.EE83B4B7(268465152u)[0] & 0xFFFFFFFFu;
		C693AA0B.gclass51_0.BE2E2DAC(268465152u, (uint)(num | (1 << BA20530A(4026531840u) - 1)));
		_ = C693AA0B.gclass51_0.EE83B4B7(268465152u)[0];
		C693AA0B.gclass51_0.BE2E2DAC(268465152u, (uint)(num | (2 << BA20530A(4026531840u) - 1)));
	}

	private void method_1(int int_4)
	{
		B185D0A1["HACC_ACON", 0u] = B185D0A1["HACC_ACON", 0u] & 0xFFFFFFFDu;
		if (int_4 == 1)
		{
			B185D0A1["HACC_ACON", 0u] |= 2u;
		}
	}

	private void method_2(int int_4, int int_5, byte[] byte_0 = null)
	{
		uint num = 16u;
		switch (int_5)
		{
		case 32:
			num = 32u;
			break;
		case 24:
			num = 16u;
			break;
		}
		C693AA0B.gclass51_0.BE2E2DAC(1089124u, num);
		B185D0A1["HACC_ACON", 0u] = (B185D0A1["HACC_ACON", 0u] & 0xFFFFFFCFu) | num;
		B185D0A1["HACC_AKEY0", 0u] = 0u;
		B185D0A1["HACC_AKEY1", 0u] = 0u;
		B185D0A1["HACC_AKEY2", 0u] = 0u;
		B185D0A1["HACC_AKEY3", 0u] = 0u;
		B185D0A1["HACC_AKEY4", 0u] = 0u;
		B185D0A1["HACC_AKEY5", 0u] = 0u;
		B185D0A1["HACC_AKEY6", 0u] = 0u;
		B185D0A1["HACC_AKEY7", 0u] = 0u;
		if (int_4 == 1)
		{
			B185D0A1["HACC_ACONK", 0u] |= 16u;
			return;
		}
		List<uint> list = new List<uint> { 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u };
		using (IEnumerator<int> enumerator = GClass112.smethod_30(0, byte_0.Length, 4).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				list[current / 4] = Class607.B630A78B.object_0[40](GClass111.C3B9331C(">I", GClass112.smethod_14(byte_0, current, 4))[0]);
			}
		}
		B185D0A1["HACC_AKEY0", 0u] = list[0];
		B185D0A1["HACC_AKEY1", 0u] = list[1];
		B185D0A1["HACC_AKEY2", 0u] = list[2];
		B185D0A1["HACC_AKEY3", 0u] = list[3];
		B185D0A1["HACC_AKEY4", 0u] = list[4];
		B185D0A1["HACC_AKEY5", 0u] = list[5];
		B185D0A1["HACC_AKEY6", 0u] = list[6];
		B185D0A1["HACC_AKEY7", 0u] = list[7];
	}

	public void method_3()
	{
	}

	private byte[] method_4(byte[] byte_0)
	{
		List<uint> list = new List<uint>();
		uint[] array = byte_0.smethod_7();
		int num = array.Length;
		for (int i = 0; i < num; i += 4)
		{
			B185D0A1["HACC_ASRC0", 0u] = array[i];
			B185D0A1["HACC_ASRC1", 0u] = array[i + 1];
			B185D0A1["HACC_ASRC2", 0u] = array[i + 2];
			B185D0A1["HACC_ASRC3", 0u] = array[i + 3];
			B185D0A1["HACC_ACON2", 0u] = 1u;
			int num2 = 0;
			for (; i < 20; i++)
			{
				if ((B185D0A1["HACC_ACON2", 0u] & 0x8000) != 0)
				{
					break;
				}
			}
			if (num2 < 20)
			{
			}
			list.Add(B185D0A1["HACC_AOUT0", 0u]);
			list.Add(B185D0A1["HACC_AOUT1", 0u]);
			list.Add(B185D0A1["HACC_AOUT2", 0u]);
			list.Add(B185D0A1["HACC_AOUT3", 0u]);
		}
		return list.BE13F184();
	}

	private void CC15F305()
	{
		B185D0A1["HACC_ACON2", 0u] = 2u;
		B185D0A1["HACC_AKEY0", 0u] = 0u;
		B185D0A1["HACC_AKEY1", 0u] = 0u;
		B185D0A1["HACC_AKEY2", 0u] = 0u;
		B185D0A1["HACC_AKEY3", 0u] = 0u;
		B185D0A1["HACC_AKEY4", 0u] = 0u;
		B185D0A1["HACC_AKEY5", 0u] = 0u;
		B185D0A1["HACC_AKEY6", 0u] = 0u;
		B185D0A1["HACC_AKEY7", 0u] = 0u;
	}

	private uint FB86C728(bool CC28512C = true, uint[] uint_18 = null, bool bool_1 = false)
	{
		return (uint)new GClass128().AD84C1A2(new object[4] { this, CC28512C, uint_18, bool_1 }, 1149843);
	}

	internal byte[] A22EC724(byte[] byte_0, bool F6BB1F86 = true, uint[] uint_18 = null, bool bool_1 = false)
	{
		return (byte[])new GClass128().method_323(new object[5] { this, byte_0, F6BB1F86, uint_18, bool_1 }, 436481);
	}

	internal void A027F09F(byte[] byte_0)
	{
		uint[] array = byte_0.smethod_7();
		B185D0A1["HACC_SW_OTP0", 0u] = array[0];
		B185D0A1["HACC_SW_OTP1", 0u] = array[1];
		B185D0A1["HACC_SW_OTP2", 0u] = array[2];
		B185D0A1["HACC_SW_OTP3", 0u] = array[3];
		B185D0A1["HACC_SW_OTP4", 0u] = array[4];
		B185D0A1["HACC_SW_OTP5", 0u] = array[5];
		B185D0A1["HACC_SW_OTP6", 0u] = array[6];
		B185D0A1["HACC_SW_OTP7", 0u] = array[7];
	}

	private byte[] method_5(bool C1A54F18, byte[] byte_0, byte[] BF17A804, int int_4)
	{
		B185D0A1["HACC_ACON2", 0u] |= 2u;
		uint[] array = byte_0.smethod_7();
		B185D0A1["HACC_ACFG0", 0u] = array[0];
		B185D0A1["HACC_ACFG1", 0u] = array[1];
		B185D0A1["HACC_ACFG2", 0u] = array[2];
		B185D0A1["HACC_ACFG3", 0u] = array[3];
		if (C1A54F18)
		{
			B185D0A1["HACC_ACON", 0u] |= 1u;
		}
		else
		{
			B185D0A1["HACC_ACON", 0u] &= 4294967294u;
		}
		List<uint> list = new List<uint>();
		uint[] array2 = BF17A804.smethod_7();
		for (int i = 0; i < array2.Length; i += 4)
		{
			B185D0A1["HACC_ASRC0", 0u] = array2[i];
			B185D0A1["HACC_ASRC1", 0u] = array2[i + 1];
			B185D0A1["HACC_ASRC2", 0u] = array2[i + 2];
			B185D0A1["HACC_ASRC3", 0u] = array2[i + 3];
			B185D0A1["HACC_ACON2", 0u] |= 1u;
			int num = 0;
			while (20 > num++ && ((ulong)B185D0A1["HACC_ACON2", 0u] & 0x8000uL) == 0L)
			{
			}
			if (num < 20)
			{
			}
			list.Add(B185D0A1["HACC_AOUT0", 0u]);
			list.Add(B185D0A1["HACC_AOUT1", 0u]);
			list.Add(B185D0A1["HACC_AOUT2", 0u]);
			list.Add(B185D0A1["HACC_AOUT3", 0u]);
		}
		return list.BE13F184();
	}

	internal byte[] EA05D73E(byte[] byte_0, byte[] byte_1, int A8048BAC = 32)
	{
		A027F09F(byte_1);
		List<byte> list = new List<byte>();
		for (int i = 0; i < A8048BAC; i++)
		{
			list.Add(byte_0[i % byte_0.Length]);
		}
		return A22EC724(list.ToArray(), F6BB1F86: true, DF9ADAB9);
	}

	private byte[] EF12DF28(byte[] AF3FE4A3, bool bool_1, int int_4, bool E2BC4984, int BE121510, bool B0B05BB3)
	{
		byte[] result = null;
		switch (int_4)
		{
		case 0:
		{
			uint[] dF9ADAB = DF9ADAB9;
			FB86C728(B0B05BB3, dF9ADAB);
			result = method_4(AF3FE4A3);
			CC15F305();
			break;
		}
		case 1:
		{
			uint[] uint_2 = uint_16;
			FB86C728(B0B05BB3, uint_2);
			result = method_4(AF3FE4A3);
			CC15F305();
			break;
		}
		case 2:
		{
			method_2(2, 32);
			byte[] byte_ = "57325A5A125497661254976657325A5A".D72BFA2C();
			result = method_5(BE121510 != 0, byte_, AF3FE4A3, AF3FE4A3.Length);
			break;
		}
		case 3:
		{
			uint[] uint_ = uint_17;
			FB86C728(B0B05BB3, uint_);
			result = method_4(AF3FE4A3);
			CC15F305();
			break;
		}
		}
		return result;
	}

	private byte[] method_6(byte[] byte_0, int int_4 = 16)
	{
		List<byte[]> list = new List<byte[]>();
		byte[] array = new byte[16];
		for (int i = 0; i < int_4; i += 16)
		{
			Class607.B630A78B.object_0[242](byte_0, i, array, 0, array.Length);
			list.Add(EF12DF28(array, bool_1: true, 0, E2BC4984: false, 1, B0B05BB3: true));
		}
		return list.smethod_5();
	}

	internal byte[] method_7(byte[] byte_0 = null)
	{
		if (byte_0 != null)
		{
			A027F09F(byte_0);
		}
		byte[] byte_1 = "4B65796D61737465724D617374657200".D72BFA2C();
		return method_6(byte_1);
	}

	internal byte[] method_8(byte[] byte_0, bool DA31A1BE = true)
	{
		GClass22 gClass = new GClass22();
		return gClass.AD82759A("25A1763A21BC854CD569DC23B4782B63".D72BFA2C(), "57325A5A125497661254976657325A5A".D72BFA2C(), byte_0, !bool_0);
	}

	private byte[] method_9(byte[] byte_0)
	{
		uint[] array = byte_0.smethod_7();
		for (int i = 0; i < 4; i++)
		{
			array[i] ^= DF9ADAB9[i];
		}
		return array.BE13F184();
	}

	internal byte[] F911B912(byte[] FC9F5C94, bool E63DE020)
	{
		if (E63DE020)
		{
			FC9F5C94 = method_9(FC9F5C94);
		}
		FB86C728(E63DE020, DF9ADAB9, bool_1: true);
		byte[] array = method_4(FC9F5C94);
		CC15F305();
		if (E63DE020)
		{
			return array;
		}
		return method_9(array);
	}

	internal byte[] method_10(byte[] AA8DD817, bool B1B013AD = true, bool bool_1 = false)
	{
		return A22EC724(AA8DD817, B1B013AD, null, bool_1);
	}

	static GClass27()
	{
		uint[] array = new uint[12];
		DEBEDD9C.smethod_0(array, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		BB8B118B = array;
		uint[] array2 = new uint[8];
		DEBEDD9C.smethod_0(array2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		DF9ADAB9 = array2;
		uint[] array_ = new uint[8];
		DEBEDD9C.smethod_0(array_, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		uint_16 = array_;
		uint[] array_2 = new uint[8];
		DEBEDD9C.smethod_0(array_2, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		uint_17 = array_2;
	}
}
