using System;
using System.Collections.Generic;
using System.IO;
using K4os.Compression.LZ4.Streams;

public static class GClass117
{
	public static string smethod_0(this D43029AC d43029AC_0)
	{
		return d43029AC_0 switch
		{
			D43029AC.C7A2E9B6 => "getvar", 
			D43029AC.const_1 => "oem", 
			D43029AC.DBBF98AD => "flash", 
			D43029AC.const_3 => "erase", 
			_ => "UNKNOWN", 
		};
	}

	public static List<B9A6028D> C20FEE14(string D02B6235)
	{
		return (List<B9A6028D>)new GClass128().E8AA1C3C(new object[1] { D02B6235 }, 22090969);
	}

	private static List<B9A6028D> smethod_1(byte[] byte_0)
	{
		return (List<B9A6028D>)new GClass128().C5017C25(new object[1] { byte_0 }, 1273317);
	}

	public static void smethod_2(List<B9A6028D> list_0, string string_0, string string_1, GClass112.E39CAE0B e39CAE0B_0, ABA18D2C.E33840A1 C827C39D)
	{
		new GClass128().E6A33692(new object[5] { list_0, string_0, string_1, e39CAE0B_0, C827C39D }, 22563314);
	}

	public static bool B48F988D(B9A6028D DD3C2D1F, string string_0, string CCBE820D, GClass112.E39CAE0B DA878F02, ABA18D2C.E33840A1 e33840A1_0)
	{
		return (bool)new GClass128().B4154402(new object[5] { DD3C2D1F, string_0, CCBE820D, DA878F02, e33840A1_0 }, 229917);
	}

	public static List<B518BF25> F02E019D(string string_0)
	{
		return (List<B518BF25>)new GClass128().F3BD1601(new object[1] { string_0 }, 412336);
	}

	private static byte[] smethod_3(string string_0, B9A6028D b9A6028D_0)
	{
		using FileStream object_ = Class607.B630A78B.object_0[835](string_0, FileMode.Open);
		Class607.B630A78B.object_0[636](object_, b9A6028D_0.long_2);
		byte[] array = new byte[b9A6028D_0.long_1];
		Class607.B630A78B.object_0[53](object_, array, 0, (int)b9A6028D_0.long_1);
		using MemoryStream object_2 = Class607.B630A78B.object_0[939]();
		using MemoryStream stream_ = Class607.B630A78B.object_0[10](array);
		LZ4DecoderStream val = Class607.B630A78B.object_0[1084](stream_, null, D831573B: false);
		try
		{
			byte[] array2 = new byte[1048576];
			int num = 0;
			while ((num = Class607.B630A78B.object_0[53](val, array2, 0, array2.Length)) > 0)
			{
				Class607.B630A78B.object_0[132](object_2, array2, 0, num);
			}
			return Class607.B630A78B.object_0[1126](object_2);
		}
		finally
		{
			((IDisposable)val)?.Dispose();
		}
	}

	public static List<B518BF25> smethod_4(string A9B30F3D, B9A6028D b9A6028D_0)
	{
		return (List<B518BF25>)new GClass128().E8AA1C3C(new object[2] { A9B30F3D, b9A6028D_0 }, 11159989);
	}
}
