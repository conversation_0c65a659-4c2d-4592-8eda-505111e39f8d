using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

public class GClass103 : CheckBox, GInterface2
{
	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private int C194D434;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private GEnum36 genum36_0;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private Point point_0;

	private bool ED2A4319;

	private readonly Class56 A502618C;

	private readonly Class56 F2050E0F;

	private const int int_0 = 18;

	private const int int_1 = 9;

	private const int D221AC8C = 14;

	private int int_2;

	private Rectangle rectangle_0;

	private static readonly Point[] D5201121 = new Point[3]
	{
		Class607.B630A78B.object_0[760](3, 8),
		Class607.B630A78B.object_0[760](7, 12),
		Class607.B630A78B.object_0[760](14, 5)
	};

	private const int int_3 = 22;

	[Browsable(false)]
	public int Int32_0
	{
		[CompilerGenerated]
		get
		{
			return C194D434;
		}
		[CompilerGenerated]
		set
		{
			C194D434 = value;
		}
	}

	[Browsable(false)]
	public GClass106 DD8EF439 => GClass106.GClass106_0;

	[Browsable(false)]
	public GEnum36 C8B39594
	{
		[CompilerGenerated]
		get
		{
			return genum36_0;
		}
		[CompilerGenerated]
		set
		{
			genum36_0 = value;
		}
	}

	[Browsable(false)]
	public Point BD040C96
	{
		[CompilerGenerated]
		get
		{
			return point_0;
		}
		[CompilerGenerated]
		set
		{
			point_0 = value;
		}
	}

	[Category("Behavior")]
	public bool Boolean_0
	{
		get
		{
			return ED2A4319;
		}
		set
		{
			ED2A4319 = value;
			Class607.B630A78B.object_0[11](this, Class607.B630A78B.object_0[638](this));
			if (value)
			{
				Class607.B630A78B.object_0[350](this, Class607.B630A78B.object_0[1237](0));
			}
			Class607.B630A78B.object_0[583](this);
		}
	}

	public override bool AutoSize
	{
		get
		{
			return Class607.B630A78B.object_0[69](this);
		}
		set
		{
			Class607.B630A78B.object_0[952](this, value);
			if (value)
			{
				Class607.B630A78B.object_0[1277](this, Class607.B630A78B.object_0[174](10, 10));
			}
		}
	}

	public unsafe GClass103()
	{
		Class607.B630A78B.object_0[360](this);
		A502618C = new Class56
		{
			F8AB7C94_0 = F8AB7C94.const_1,
			D31D243E = 0.05
		};
		F2050E0F = new Class56(E03D843F: false)
		{
			F8AB7C94_0 = F8AB7C94.F41002BC,
			D31D243E = 0.1,
			Double_0 = 0.08
		};
		A502618C.AE044886 += delegate
		{
			Class607.B630A78B.object_0[583](this);
		};
		F2050E0F.AE044886 += delegate
		{
			Class607.B630A78B.object_0[583](this);
		};
		Class607.B630A78B.object_0[479](this, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GClass103.CB0C3505)));
		Boolean_0 = true;
		BD040C96 = Class607.B630A78B.object_0[760](-1, -1);
	}

	protected override void OnSizeChanged(EventArgs AB334BAD)
	{
		Class607.B630A78B.object_0[230](this, AB334BAD);
		int_2 = Class607.B630A78B.object_0[500](this) / 2 - 9;
		rectangle_0 = Class607.B630A78B.object_0[1023](int_2, int_2, 17, 17);
	}

	public override Size GetPreferredSize(Size C51AEA3B)
	{
		int num = int_2 + 18 + 2;
		SizeF sizeF_ = Class607.B630A78B.object_0[1203](Class607.B630A78B.object_0[278](this), Class607.B630A78B.object_0[814](this), DD8EF439.B39C48BD);
		int num2 = num + (int)Class607.B630A78B.object_0[331](ref sizeF_);
		return Boolean_0 ? Class607.B630A78B.object_0[174](num2, 30) : Class607.B630A78B.object_0[174](num2, 20);
	}

	protected override void OnPaint(PaintEventArgs DA8E34AE)
	{
		Graphics graphics = Class607.B630A78B.object_0[340](DA8E34AE);
		Class607.B630A78B.object_0[111](graphics, SmoothingMode.AntiAlias);
		Class607.B630A78B.object_0[423](graphics, TextRenderingHint.AntiAlias);
		Class607.B630A78B.object_0[1141](graphics, Class607.B630A78B.object_0[410](Class607.B630A78B.object_0[1161](this)));
		int num = int_2 + 9 - 1;
		double num2 = A502618C.D6187CAA();
		int num3;
		if (!Class607.B630A78B.object_0[735](this))
		{
			Color color_ = DD8EF439.method_1();
			num3 = Class607.B630A78B.object_0[430](ref color_);
		}
		else
		{
			num3 = (int)(num2 * 255.0);
		}
		int num4 = num3;
		int num5;
		if (!Class607.B630A78B.object_0[735](this))
		{
			Color color_ = DD8EF439.method_1();
			num5 = Class607.B630A78B.object_0[430](ref color_);
		}
		else
		{
			Color color_ = DD8EF439.method_4();
			num5 = (int)((double)(int)Class607.B630A78B.object_0[430](ref color_) * (1.0 - num2));
		}
		int num6 = num5;
		SolidBrush solidBrush = Class755.smethod_0(ECA9D598.smethod_0(num4, Class607.B630A78B.object_0[735](this) ? DD8EF439.CEBAB4AB.color_3 : DD8EF439.method_1()));
		SolidBrush object_ = Class755.smethod_0(Class607.B630A78B.object_0[735](this) ? DD8EF439.CEBAB4AB.color_3 : DD8EF439.method_1());
		Pen pen = Class607.B630A78B.object_0[277](Class607.B630A78B.object_0[1151](solidBrush));
		if (Boolean_0 && F2050E0F.method_1())
		{
			Point point = default(Point);
			for (int i = 0; i < F2050E0F.B4A91C14(); i++)
			{
				double num7 = F2050E0F.method_5(i);
				Class607.B630A78B.object_0[759](ref point, num, num);
				SolidBrush solidBrush2 = Class755.smethod_0(ECA9D598.smethod_0((int)(num7 * 40.0), ((bool)F2050E0F.method_10(i)[0]) ? Class607.B630A78B.object_0[620]() : Class607.B630A78B.object_0[1151](solidBrush)));
				int num8 = ((Class607.B630A78B.object_0[500](this) % 2 == 0) ? (Class607.B630A78B.object_0[500](this) - 3) : (Class607.B630A78B.object_0[500](this) - 2));
				int num9 = ((F2050E0F.method_8(i) == Enum14.const_2) ? ((int)((double)num8 * (0.8 + 0.2 * num7))) : num8);
				using (GraphicsPath graphicsPath_ = Class59.smethod_0(Class607.B630A78B.object_0[372](ref point) - num9 / 2, Class607.B630A78B.object_0[1229](ref point) - num9 / 2, num9, num9, num9 / 2))
				{
					Class607.B630A78B.object_0[251](graphics, solidBrush2, graphicsPath_);
				}
				Class607.B630A78B.object_0[529](solidBrush2);
			}
		}
		Class607.B630A78B.object_0[529](object_);
		Rectangle rectangle = default(Rectangle);
		Class607.B630A78B.object_0[1024](ref rectangle, int_2, int_2, (int)(17.0 * num2), 17);
		using (GraphicsPath graphicsPath_2 = Class59.smethod_0(int_2, int_2, 17f, 17f, 1f))
		{
			SolidBrush solidBrush3 = Class755.smethod_0(Class59.smethod_1(Class607.B630A78B.object_0[410](Class607.B630A78B.object_0[1161](this)), Class607.B630A78B.object_0[735](this) ? DD8EF439.method_4() : DD8EF439.method_1(), num6));
			Pen pen2 = Class607.B630A78B.object_0[277](Class607.B630A78B.object_0[1151](solidBrush3));
			Class607.B630A78B.object_0[251](graphics, solidBrush3, graphicsPath_2);
			Class607.B630A78B.object_0[67](graphics, pen2, graphicsPath_2);
			Class607.B630A78B.object_0[592](graphics, Class607.B630A78B.object_0[1044](Class607.B630A78B.object_0[410](Class607.B630A78B.object_0[1161](this))), int_2 + 2, int_2 + 2, 13, 13);
			Class607.B630A78B.object_0[1284](graphics, Class607.B630A78B.object_0[277](Class607.B630A78B.object_0[410](Class607.B630A78B.object_0[1161](this))), int_2 + 2, int_2 + 2, 13, 13);
			Class607.B630A78B.object_0[529](solidBrush3);
			Class607.B630A78B.object_0[747](pen2);
			if (Class607.B630A78B.object_0[735](this))
			{
				Class607.B630A78B.object_0[251](graphics, solidBrush, graphicsPath_2);
				Class607.B630A78B.object_0[67](graphics, pen, graphicsPath_2);
			}
			else if (Class607.B630A78B.object_0[164](this))
			{
				Class607.B630A78B.object_0[111](graphics, SmoothingMode.None);
				Class607.B630A78B.object_0[592](graphics, solidBrush, int_2 + 2, int_2 + 2, 14, 14);
				Class607.B630A78B.object_0[111](graphics, SmoothingMode.AntiAlias);
			}
			Class607.B630A78B.object_0[533](graphics, C4B8A207(), rectangle);
		}
		SizeF C6998C = Class607.B630A78B.object_0[1203](graphics, Class607.B630A78B.object_0[814](this), DD8EF439.B39C48BD);
		Class79.D48B2CB9(graphics, Class607.B630A78B.object_0[814](this), DD8EF439.B39C48BD, Class607.B630A78B.object_0[735](this) ? DD8EF439.C38AE18D() : DD8EF439.method_2(), int_2 + 22, (float)(Class607.B630A78B.object_0[500](this) / 2) - Class607.B630A78B.object_0[1238](ref C6998C) / 2f);
		Class607.B630A78B.object_0[747](pen);
		Class607.B630A78B.object_0[529](solidBrush);
	}

	private Bitmap C4B8A207()
	{
		Bitmap bitmap = Class607.B630A78B.object_0[74](18, 18);
		Graphics graphics = Class607.B630A78B.object_0[1082](bitmap);
		Class607.B630A78B.object_0[1141](graphics, Class607.B630A78B.object_0[300]());
		using (Pen pen_ = Class607.B630A78B.object_0[644](Class607.B630A78B.object_0[410](Class607.B630A78B.object_0[1161](this)), 2f))
		{
			Class607.B630A78B.object_0[696](graphics, pen_, D5201121);
		}
		return bitmap;
	}

	private bool method_0()
	{
		return Class607.B630A78B.object_0[9](ref rectangle_0, BD040C96);
	}

	protected unsafe override void OnCreateControl()
	{
		Class607.B630A78B.object_0[451](this);
		Class607.B630A78B.object_0[1267](this, DD8EF439.B39C48BD);
		if (!Class607.B630A78B.object_0[906](this))
		{
			C8B39594 = GEnum36.CFBD4335;
			Class607.B630A78B.object_0[840](this, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GClass103.D09AED3A)));
			Class607.B630A78B.object_0[682](this, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GClass103.B1AE62A6)));
			Class607.B630A78B.object_0[1018](this, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GClass103.method_3)));
			Class607.B630A78B.object_0[610](this, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GClass103.D525D786)));
			Class607.B630A78B.object_0[428](this, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GClass103.E8993AB9)));
		}
	}

	[CompilerGenerated]
	private void method_1(object A512A4A4)
	{
		Class607.B630A78B.object_0[583](this);
	}

	[CompilerGenerated]
	private void method_2(object object_0)
	{
		Class607.B630A78B.object_0[583](this);
	}

	[CompilerGenerated]
	private void CB0C3505(object sender, EventArgs e)
	{
		A502618C.method_2((!Class607.B630A78B.object_0[164](this)) ? Enum14.const_1 : Enum14.const_0);
	}

	[CompilerGenerated]
	private void D09AED3A(object sender, EventArgs e)
	{
		C8B39594 = GEnum36.A6BC4E23;
	}

	[CompilerGenerated]
	private void B1AE62A6(object sender, EventArgs e)
	{
		BD040C96 = Class607.B630A78B.object_0[760](-1, -1);
		C8B39594 = GEnum36.CFBD4335;
	}

	[CompilerGenerated]
	private void method_3(object sender, MouseEventArgs e)
	{
		C8B39594 = GEnum36.const_1;
		if (Boolean_0 && Class607.B630A78B.object_0[944](e) == MouseButtons.Left && method_0())
		{
			F2050E0F.Double_0 = 0.0;
			F2050E0F.method_2(Enum14.const_2, new object[1] { Class607.B630A78B.object_0[164](this) });
		}
	}

	[CompilerGenerated]
	private void D525D786(object sender, MouseEventArgs e)
	{
		C8B39594 = GEnum36.A6BC4E23;
		F2050E0F.Double_0 = 0.08;
	}

	[CompilerGenerated]
	private void E8993AB9(object sender, MouseEventArgs e)
	{
		BD040C96 = Class607.B630A78B.object_0[613](e);
		Class275.FBA01B9D(this, method_0() ? Class607.B630A78B.object_0[268]() : Class607.B630A78B.object_0[541]());
	}
}
