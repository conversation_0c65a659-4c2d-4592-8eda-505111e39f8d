using System;

public class GClass96
{
	[Flags]
	public enum GEnum32
	{
		flag_0 = 1,
		EF1A5826 = 2,
		A5A83906 = 4
	}

	public readonly Guid guid_0;

	public readonly Guid guid_1;

	public readonly ulong E49B1437;

	public readonly ulong ulong_0;

	public readonly GEnum32 genum32_0;

	public readonly string string_0;

	internal GClass96(byte[] byte_0)
	{
		Class607.B630A78B.object_0[571](this);
		guid_0 = Class607.B630A78B.object_0[284](smethod_0(byte_0, 0u, 16u));
		guid_1 = Class607.B630A78B.object_0[284](smethod_0(byte_0, 16u, 16u));
		E49B1437 = Class607.B630A78B.object_0[432](byte_0, 32);
		ulong_0 = Class607.B630A78B.object_0[432](byte_0, 40);
		genum32_0 = (GEnum32)Class607.B630A78B.object_0[432](byte_0, 48);
		string_0 = Class607.B630A78B.object_0[699](Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[1020](), byte_0, 56, 72), new char[1])[0];
	}

	internal static byte[] smethod_0(byte[] byte_0, uint uint_0, uint uint_1)
	{
		byte[] array = new byte[uint_1];
		Class607.B630A78B.object_0[912](byte_0, uint_0, array, 0L, array.Length);
		return array;
	}
}
