using System.Diagnostics;
using System.Runtime.CompilerServices;

public class GClass7
{
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string D8AB2C31;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private uint ECBADD31;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private ulong ulong_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private ulong ulong_1;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private ulong ulong_2;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private ulong DE9D2390;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private ulong ulong_3;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private ulong E51E6593;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private ulong D90C0D18;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private ulong E934FE91;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private ulong F31C329A;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private ulong F223B100;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private ulong E50F572B;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private ulong ulong_4;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private uint uint_0;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private uint uint_1;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private ulong A393E104;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private uint uint_2;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_1;

	public string DF94B90A
	{
		[CompilerGenerated]
		get
		{
			return (string)new GClass128().method_323(new object[1] { this }, 1274502);
		}
		[CompilerGenerated]
		private set
		{
			D8AB2C31 = value;
		}
	}

	public uint UInt32_0
	{
		[CompilerGenerated]
		get
		{
			return ECBADD31;
		}
		[CompilerGenerated]
		private set
		{
			ECBADD31 = value;
		}
	}

	public ulong UInt64_0
	{
		[CompilerGenerated]
		get
		{
			return ulong_0;
		}
		[CompilerGenerated]
		private set
		{
			ulong_0 = value;
		}
	}

	public ulong UInt64_1
	{
		[CompilerGenerated]
		get
		{
			return ulong_1;
		}
		[CompilerGenerated]
		private set
		{
			ulong_1 = value;
		}
	}

	public ulong UInt64_2
	{
		[CompilerGenerated]
		get
		{
			return ulong_2;
		}
		[CompilerGenerated]
		private set
		{
			ulong_2 = value;
		}
	}

	public ulong UInt64_3
	{
		[CompilerGenerated]
		get
		{
			return DE9D2390;
		}
		[CompilerGenerated]
		private set
		{
			DE9D2390 = value;
		}
	}

	public string String_0
	{
		[CompilerGenerated]
		get
		{
			return string_0;
		}
		[CompilerGenerated]
		private set
		{
			string_0 = value;
		}
	}

	public ulong UInt64_4
	{
		[CompilerGenerated]
		get
		{
			return ulong_3;
		}
		[CompilerGenerated]
		private set
		{
			ulong_3 = value;
		}
	}

	public ulong UInt64_5
	{
		[CompilerGenerated]
		get
		{
			return E51E6593;
		}
		[CompilerGenerated]
		private set
		{
			E51E6593 = value;
		}
	}

	public ulong E7BDF9A5
	{
		[CompilerGenerated]
		get
		{
			return D90C0D18;
		}
		[CompilerGenerated]
		private set
		{
			D90C0D18 = value;
		}
	}

	public ulong UInt64_6
	{
		[CompilerGenerated]
		get
		{
			return E934FE91;
		}
		[CompilerGenerated]
		private set
		{
			E934FE91 = value;
		}
	}

	public ulong UInt64_7
	{
		[CompilerGenerated]
		get
		{
			return F31C329A;
		}
		[CompilerGenerated]
		private set
		{
			F31C329A = value;
		}
	}

	public ulong UInt64_8
	{
		[CompilerGenerated]
		get
		{
			return F223B100;
		}
		[CompilerGenerated]
		private set
		{
			F223B100 = value;
		}
	}

	public ulong UInt64_9
	{
		[CompilerGenerated]
		get
		{
			return E50F572B;
		}
		[CompilerGenerated]
		private set
		{
			E50F572B = value;
		}
	}

	public ulong ADAF419E
	{
		[CompilerGenerated]
		get
		{
			return ulong_4;
		}
		[CompilerGenerated]
		private set
		{
			ulong_4 = value;
		}
	}

	public uint A4005E17
	{
		[CompilerGenerated]
		get
		{
			return uint_0;
		}
		[CompilerGenerated]
		private set
		{
			uint_0 = value;
		}
	}

	public uint A1182531
	{
		[CompilerGenerated]
		get
		{
			return uint_1;
		}
		[CompilerGenerated]
		private set
		{
			uint_1 = value;
		}
	}

	public ulong B3AA498E
	{
		[CompilerGenerated]
		get
		{
			return A393E104;
		}
		[CompilerGenerated]
		private set
		{
			A393E104 = value;
		}
	}

	public uint UInt32_1
	{
		[CompilerGenerated]
		get
		{
			return uint_2;
		}
		[CompilerGenerated]
		private set
		{
			uint_2 = value;
		}
	}

	public string String_1
	{
		[CompilerGenerated]
		get
		{
			return string_1;
		}
		[CompilerGenerated]
		private set
		{
			string_1 = value;
		}
	}

	public GClass7(GClass8 gclass8_0, GClass112.E39CAE0B e39CAE0B_0, string string_2, string string_3)
	{
		Class607.B630A78B.object_0[571](this);
		DF94B90A = string_2;
		if (Class607.B630A78B.object_0[787](DF94B90A, "UFS"))
		{
			UInt32_0 = Class607.B630A78B.object_0[825](gclass8_0.method_10(string_3, "block_size"), 16);
			UInt64_0 = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "lua0_size"), 16);
			UInt64_1 = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "lua1_size"), 16);
			UInt64_2 = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "lua2_size"), 16);
			UInt64_3 = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "lua3_size"), 16);
			String_0 = gclass8_0.method_10(string_3, "id");
		}
		else if (Class607.B630A78B.object_0[787](DF94B90A, "EMMC"))
		{
			UInt32_0 = Class607.B630A78B.object_0[825](gclass8_0.method_10(string_3, "block_size"), 16);
			UInt64_4 = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "boot1_size"), 16);
			UInt64_5 = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "boot2_size"), 16);
			E7BDF9A5 = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "rpmb_size"), 16);
			UInt64_6 = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "user_size"), 16);
			UInt64_7 = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "gp1_size"), 16);
			UInt64_8 = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "gp2_size"), 16);
			UInt64_9 = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "gp3_size"), 16);
			ADAF419E = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "gp4_size"), 16);
			String_0 = gclass8_0.method_10(string_3, "id");
		}
		else if (Class607.B630A78B.object_0[787](DF94B90A, "NAND"))
		{
			UInt32_0 = Class607.B630A78B.object_0[825](gclass8_0.method_10(string_3, "block_size"), 16);
			A4005E17 = Class607.B630A78B.object_0[825](gclass8_0.method_10(string_3, "page_size"), 16);
			A1182531 = Class607.B630A78B.object_0[825](gclass8_0.method_10(string_3, "spare_size"), 16);
			B3AA498E = Class607.B630A78B.object_0[1171](gclass8_0.method_10(string_3, "total_size"), 16);
			String_0 = gclass8_0.method_10(string_3, "id");
			UInt32_1 = Class607.B630A78B.object_0[825](gclass8_0.method_10(string_3, "page_parity_size"), 16);
			String_1 = gclass8_0.method_10(string_3, "sub_type");
		}
		else
		{
			e39CAE0B_0?.Invoke("Unknown storage type: ");
			e39CAE0B_0?.Invoke(DF94B90A, EF1F389C.Error);
		}
	}
}
