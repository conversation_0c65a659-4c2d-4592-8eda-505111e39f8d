using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Text;

public static class GClass35
{
	[CompilerGenerated]
	private sealed class Class10
	{
		public StringBuilder stringBuilder_0;

		public StringBuilder D1B2432B;

		public Class10()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void method_0(object sender, DataReceivedEventArgs e)
		{
			if (!Class607.B630A78B.object_0[1205](Class607.B630A78B.object_0[487](e)))
			{
				Class607.B630A78B.object_0[481](stringBuilder_0, Class607.B630A78B.object_0[487](e));
			}
		}

		internal void A58CD70B(object sender, DataReceivedEventArgs e)
		{
			if (!Class607.B630A78B.object_0[1205](Class607.B630A78B.object_0[487](e)))
			{
				Class607.B630A78B.object_0[481](D1B2432B, Class607.B630A78B.object_0[487](e));
			}
		}
	}

	public unsafe static string smethod_0(string B289BDB0, string string_0, string D6832F35 = "", int int_0 = 0)
	{
		Process process = Class607.B630A78B.object_0[497]();
		Class810.smethod_0(process, bool_0: true);
		ProcessStartInfo processStartInfo = Class607.B630A78B.object_0[204]();
		Class126.smethod_0(processStartInfo, B289BDB0);
		Class672.smethod_0(processStartInfo, string_0);
		DC00EB16.B720F028(processStartInfo, bool_0: true);
		B58BE3B1.smethod_0(processStartInfo, F6287798: true);
		Class872.CBB523A0(processStartInfo, ProcessWindowStyle.Hidden);
		Class746.smethod_0(processStartInfo, FA9B9EBE: false);
		Class463.E41FF0B7(processStartInfo, bool_0: true);
		Class486.D8034201(processStartInfo, B631192C: true);
		B93CF7B1.smethod_0(process, processStartInfo);
		Process process2 = process;
		if (!Class607.B630A78B.object_0[1205](D6832F35))
		{
			Class607.B630A78B.object_0[503](Class607.B630A78B.object_0[845](process2), D6832F35);
		}
		if (Class607.B630A78B.object_0[998](process2))
		{
			Class10 @class = new Class10();
			@class.stringBuilder_0 = Class607.B630A78B.object_0[1086]();
			Class607.B630A78B.object_0[745](process2, Class607.B630A78B.object_0[362](@class, (nint)__ldftn(Class10.method_0)));
			@class.D1B2432B = Class607.B630A78B.object_0[1086]();
			Class607.B630A78B.object_0[797](process2, Class607.B630A78B.object_0[362](@class, (nint)__ldftn(Class10.A58CD70B)));
			Class607.B630A78B.object_0[152](process2);
			Class607.B630A78B.object_0[737](process2);
			if (int_0 > 0)
			{
				Class607.B630A78B.object_0[1088](process2, int_0);
			}
			else
			{
				Class607.B630A78B.object_0[702](process2);
			}
			string text = @class.stringBuilder_0.ToString();
			string text2 = @class.D1B2432B.ToString();
			if (!Class607.B630A78B.object_0[1205](text))
			{
				return text;
			}
			if (!Class607.B630A78B.object_0[1205](text2))
			{
				return text2;
			}
		}
		return "";
	}
}
