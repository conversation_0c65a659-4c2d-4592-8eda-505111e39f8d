using System;
using System.Diagnostics;
using System.IO;

[DebuggerDisplay("{2+B630A78B.A2A5AFA5()}")]
public class GClass135
{
	[DebuggerDisplay("{2+B630A78B.A2A5AFA5()}")]
	private class BE2C958E
	{
		private Struct35 struct35_0;

		private FCAD9F30 E7A9201A = new FCAD9F30(8);

		private readonly FCAD9F30[] fcad9F30_0 = new FCAD9F30[16];

		private readonly FCAD9F30[] fcad9F30_1 = new FCAD9F30[16];

		private uint uint_0;

		private Struct35 B088F909;

		public uint method_0(Class76 class76_0, uint uint_1)
		{
			if (B088F909.method_0(class76_0) == 0)
			{
				return fcad9F30_1[uint_1].method_0(class76_0);
			}
			uint num = 8u;
			if (struct35_0.method_0(class76_0) == 0)
			{
				return num + fcad9F30_0[uint_1].method_0(class76_0);
			}
			num += 8;
			return num + E7A9201A.method_0(class76_0);
		}

		public void method_1()
		{
			B088F909.F13A0D25();
			for (uint num = 0u; num < uint_0; num++)
			{
				fcad9F30_1[num].method_1();
				fcad9F30_0[num].method_1();
			}
			struct35_0.F13A0D25();
			E7A9201A.method_1();
		}

		public void C8AE4E0D(uint C92E8BB1)
		{
			for (uint num = uint_0; num < C92E8BB1; num++)
			{
				fcad9F30_1[num] = new FCAD9F30(3);
				fcad9F30_0[num] = new FCAD9F30(3);
			}
			uint_0 = C92E8BB1;
		}
	}

	[DebuggerDisplay("{2+B630A78B.A2A5AFA5()}")]
	private class Class879
	{
		private struct Struct36
		{
			private Struct35[] E61C8F8A;

			public byte method_0(Class76 EDA7713C, byte byte_0)
			{
				uint num = 1u;
				do
				{
					uint num2 = (uint)((byte_0 >> 7) & 1);
					byte_0 <<= 1;
					uint num3 = E61C8F8A[(1 + num2 << 8) + num].method_0(EDA7713C);
					num = (num << 1) | num3;
					if (num2 != num3)
					{
						while (num < 256)
						{
							num = (num << 1) | E61C8F8A[num].method_0(EDA7713C);
						}
						break;
					}
				}
				while (num < 256);
				return (byte)num;
			}

			public byte method_1(Class76 class76_0)
			{
				uint num = 1u;
				do
				{
					num = (num << 1) | E61C8F8A[num].method_0(class76_0);
				}
				while (num < 256);
				return (byte)num;
			}

			public void DF9AA614()
			{
				for (int i = 0; i < 768; i++)
				{
					E61C8F8A[i].F13A0D25();
				}
			}

			public void method_2()
			{
				E61C8F8A = new Struct35[768];
			}
		}

		private int int_0;

		private Struct36[] B4BCBC23;

		private int int_1;

		private uint C7351AAE;

		private uint C601BB9A = 1u;

		private uint method_0(uint uint_0, byte D81A7636)
		{
			return ((uint_0 & C7351AAE) << int_0) + (uint)(D81A7636 >> 8 - int_0);
		}

		public byte method_1(Class76 class76_0, uint uint_0, byte F488CD3D, byte FC8EA438)
		{
			return B4BCBC23[method_0(uint_0, F488CD3D)].method_0(class76_0, FC8EA438);
		}

		public byte EE2D71A8(Class76 class76_0, uint uint_0, byte byte_0)
		{
			return B4BCBC23[method_0(uint_0, byte_0)].method_1(class76_0);
		}

		public void method_2()
		{
			uint num = (uint)(1 << int_0 + int_1);
			for (uint num2 = 0u; num2 < num; num2++)
			{
				B4BCBC23[num2].DF9AA614();
			}
		}

		public void method_3(int int_2, int int_3)
		{
			if (B4BCBC23 == null || int_0 != int_3 || int_1 != int_2)
			{
				int_1 = int_2;
				C7351AAE = (uint)((1 << int_2) - 1);
				int_0 = int_3;
				uint num = (uint)(1 << int_0 + int_1);
				B4BCBC23 = new Struct36[num];
				for (uint num2 = 0u; num2 < num; num2++)
				{
					B4BCBC23[num2].method_2();
				}
			}
		}
	}

	private readonly Struct35[] struct35_0 = new Struct35[12];

	private FCAD9F30 fcad9F30_0 = new FCAD9F30(4);

	private uint uint_0;

	private readonly BE2C958E be2C958E_0 = new BE2C958E();

	private readonly Class76 D835908D = new Class76();

	private readonly Struct35[] struct35_1 = new Struct35[12];

	private readonly Struct35[] EF3C29A0 = new Struct35[192];

	private readonly Struct35[] E2351383 = new Struct35[12];

	private readonly GClass127 gclass127_0 = new GClass127();

	private readonly Class879 class879_0 = new Class879();

	private readonly Struct35[] struct35_2 = new Struct35[12];

	private readonly BE2C958E EFA6BF34 = new BE2C958E();

	private readonly Struct35[] E12CE033 = new Struct35[114];

	private uint uint_1;

	private uint uint_2;

	private readonly FCAD9F30[] CCAC0390 = new FCAD9F30[4];

	private readonly Struct35[] struct35_3 = new Struct35[192];

	private uint uint_3 = 1u;

	public void D810E501(Stream stream_0, Stream DAA7FB9D, long long_0)
	{
		DD0DE21D(stream_0, DAA7FB9D);
		Class535.D6AF1516 d6AF = default(Class535.D6AF1516);
		d6AF.BE0CE420();
		uint num = 0u;
		uint num2 = 0u;
		uint num3 = 0u;
		uint num4 = 0u;
		ulong num5 = 0uL;
		if (0uL < (ulong)long_0)
		{
			if (EF3C29A0[d6AF.C726E98C << 4].method_0(D835908D) != 0)
			{
				throw new InvalidDataException();
			}
			d6AF.B28F472A();
			byte byte_ = class879_0.EE2D71A8(D835908D, 0u, 0);
			gclass127_0.CF252299(byte_);
			num5++;
		}
		while (num5 < (ulong)long_0)
		{
			uint num6 = (uint)(int)num5 & uint_1;
			if (EF3C29A0[(d6AF.C726E98C << 4) + num6].method_0(D835908D) == 0)
			{
				byte b = gclass127_0.A01421B2(0u);
				byte byte_2 = (d6AF.F93AB7A4() ? class879_0.EE2D71A8(D835908D, (uint)num5, b) : class879_0.method_1(D835908D, (uint)num5, b, gclass127_0.A01421B2(num)));
				gclass127_0.CF252299(byte_2);
				d6AF.B28F472A();
				num5++;
				continue;
			}
			uint num8;
			if (E2351383[d6AF.C726E98C].method_0(D835908D) == 1)
			{
				if (struct35_1[d6AF.C726E98C].method_0(D835908D) == 0)
				{
					if (struct35_3[(d6AF.C726E98C << 4) + num6].method_0(D835908D) == 0)
					{
						d6AF.A0B7B897();
						gclass127_0.CF252299(gclass127_0.A01421B2(num));
						num5++;
						continue;
					}
				}
				else
				{
					uint num7;
					if (struct35_2[d6AF.C726E98C].method_0(D835908D) == 0)
					{
						num7 = num2;
					}
					else
					{
						if (struct35_0[d6AF.C726E98C].method_0(D835908D) == 0)
						{
							num7 = num3;
						}
						else
						{
							num7 = num4;
							num4 = num3;
						}
						num3 = num2;
					}
					num2 = num;
					num = num7;
				}
				num8 = be2C958E_0.method_0(D835908D, num6) + 2;
				d6AF.method_0();
			}
			else
			{
				num4 = num3;
				num3 = num2;
				num2 = num;
				num8 = 2 + EFA6BF34.method_0(D835908D, num6);
				d6AF.DBA4540E();
				uint num9 = CCAC0390[Class535.EA0EEBA0(num8)].method_0(D835908D);
				if (num9 >= 4)
				{
					int num10 = (int)((num9 >> 1) - 1);
					num = (2 | (num9 & 1)) << num10;
					if (num9 < 14)
					{
						num += FCAD9F30.BB14F725(E12CE033, num - num9 - 1, D835908D, num10);
					}
					else
					{
						num += D835908D.E19CC239(num10 - 4) << 4;
						num += fcad9F30_0.AA3EA28E(D835908D);
					}
				}
				else
				{
					num = num9;
				}
			}
			if (num < gclass127_0.uint_3 + num5 && num < uint_0)
			{
				gclass127_0.method_1(num, num8);
				num5 += num8;
				continue;
			}
			if (num == uint.MaxValue)
			{
				break;
			}
			throw new InvalidDataException();
		}
		gclass127_0.method_2();
		gclass127_0.method_0();
		D835908D.method_0();
	}

	private void C5B74507(int int_0)
	{
		if (int_0 > 4)
		{
			throw new ArgumentException();
		}
		uint num = (uint)(1 << int_0);
		EFA6BF34.C8AE4E0D(num);
		be2C958E_0.C8AE4E0D(num);
		uint_1 = num - 1;
	}

	private void B787260C(int int_0, int int_1)
	{
		if (int_0 > 8)
		{
			throw new ArgumentException();
		}
		if (int_1 > 8)
		{
			throw new ArgumentException();
		}
		class879_0.method_3(int_0, int_1);
	}

	private void DD0DE21D(Stream FE88C123, Stream stream_0)
	{
		D835908D.method_1(FE88C123);
		gclass127_0.method_3(stream_0, bool_0: false);
		for (uint num = 0u; num < 12; num++)
		{
			for (uint num2 = 0u; num2 <= uint_1; num2++)
			{
				uint num3 = (num << 4) + num2;
				EF3C29A0[num3].F13A0D25();
				struct35_3[num3].F13A0D25();
			}
			E2351383[num].F13A0D25();
			struct35_1[num].F13A0D25();
			struct35_2[num].F13A0D25();
			struct35_0[num].F13A0D25();
		}
		class879_0.method_2();
		for (uint num = 0u; num < 4; num++)
		{
			CCAC0390[num].method_1();
		}
		for (uint num = 0u; num < 114; num++)
		{
			E12CE033[num].F13A0D25();
		}
		EFA6BF34.method_1();
		be2C958E_0.method_1();
		fcad9F30_0.method_1();
	}

	private void method_0(uint BE8B99AE)
	{
		if (uint_2 != BE8B99AE)
		{
			uint_2 = BE8B99AE;
			uint_0 = Math.Max(uint_2, 1u);
			uint fFA = Math.Max(uint_0, 4096u);
			gclass127_0.CC13C73E(fFA);
		}
	}

	public void method_1(byte[] byte_0)
	{
		if (byte_0.Length < 5)
		{
			throw new ArgumentException();
		}
		int int_ = byte_0[0] % 9;
		int num = byte_0[0] / 9;
		int int_2 = num % 5;
		int num2 = num / 5;
		if (num2 > 4)
		{
			throw new ArgumentException();
		}
		uint num3 = 0u;
		for (int i = 0; i < 4; i++)
		{
			num3 += (uint)(byte_0[1 + i] << i * 8);
		}
		method_0(num3);
		B787260C(int_2, int_);
		C5B74507(num2);
	}

	public GClass135()
	{
		uint_2 = uint.MaxValue;
		for (int i = 0; i < 4L; i++)
		{
			CCAC0390[i] = new FCAD9F30(6);
		}
	}
}
