using System;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;

public class GClass91
{
	public struct BC96EF01
	{
		public GStruct73 gstruct73_0;

		public int E48C8F2E;

		public int int_0;

		public int FA98F58F;

		public GEnum29 A529CD89;

		public int BA04423F;

		public int int_1;

		public int int_2;

		public int A58952B7;

		public int int_3;

		public int int_4;
	}

	public struct GStruct73
	{
		public AE105113 DAA8708F;

		public int C3ADB887;
	}

	public struct GStruct74
	{
		public AE105113 F5AECA09;

		public int DF9890B9;
	}

	public struct B50C8329
	{
		public string E90627A9;

		public string AB3931BE;

		public string string_0;

		public string AD96A52B;

		public int B31DDB87;
	}

	public enum GEnum29
	{
		AFAC618D,
		F439D3AA,
		const_2,
		const_3
	}

	public enum D2091826
	{
		const_0 = 0,
		A821B00F = 1,
		const_2 = 2,
		const_3 = 3,
		const_4 = 4,
		E7BAF92B = 5,
		const_6 = 6,
		const_7 = 7,
		EF9D8F07 = 8,
		const_9 = int.MaxValue
	}

	public struct GStruct75
	{
		public GStruct73 gstruct73_0;

		public GEnum29 genum29_0;
	}

	public struct GStruct76
	{
		public GStruct73 BF22A8BE;
	}

	public struct CD909AAF
	{
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private long long_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private long long_1;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private int E03BB0A3;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private int int_0;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private int int_1;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private long long_2;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private string string_0;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private string string_1;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private string string_2;

		[JsonProperty("total_blocks")]
		public long total_blocks
		{
			[CompilerGenerated]
			get
			{
				return long_0;
			}
			[CompilerGenerated]
			set
			{
				long_0 = value;
			}
		}

		[JsonProperty("block_size")]
		public long block_size
		{
			[CompilerGenerated]
			get
			{
				return long_1;
			}
			[CompilerGenerated]
			set
			{
				long_1 = value;
			}
		}

		[JsonProperty("page_size")]
		public int page_size
		{
			[CompilerGenerated]
			get
			{
				return E03BB0A3;
			}
			[CompilerGenerated]
			set
			{
				E03BB0A3 = value;
			}
		}

		[JsonProperty("num_physical")]
		public int num_physical
		{
			[CompilerGenerated]
			get
			{
				return int_0;
			}
			[CompilerGenerated]
			set
			{
				int_0 = value;
			}
		}

		[JsonProperty("manufacturer_id")]
		public int manufacturer_id
		{
			[CompilerGenerated]
			get
			{
				return int_1;
			}
			[CompilerGenerated]
			set
			{
				int_1 = value;
			}
		}

		[JsonProperty("serial_num")]
		public long serial_num
		{
			[CompilerGenerated]
			get
			{
				return long_2;
			}
			[CompilerGenerated]
			set
			{
				long_2 = value;
			}
		}

		[JsonProperty("fw_version")]
		public string fw_version
		{
			[CompilerGenerated]
			get
			{
				return string_0;
			}
			[CompilerGenerated]
			set
			{
				string_0 = value;
			}
		}

		[JsonProperty("mem_type")]
		public string mem_type
		{
			[CompilerGenerated]
			get
			{
				return string_1;
			}
			[CompilerGenerated]
			set
			{
				string_1 = value;
			}
		}

		[JsonProperty("prod_name")]
		public string prod_name
		{
			[CompilerGenerated]
			get
			{
				return string_2;
			}
			[CompilerGenerated]
			set
			{
				string_2 = value;
			}
		}
	}

	public struct GStruct77
	{
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private CD909AAF cd909AAF_0;

		[JsonProperty("storage_info")]
		public CD909AAF storage_info
		{
			[CompilerGenerated]
			get
			{
				return cd909AAF_0;
			}
			[CompilerGenerated]
			set
			{
				cd909AAF_0 = value;
			}
		}
	}

	public struct BB0E3FB3
	{
		public GStruct73 gstruct73_0;

		public GEnum31 B53B7708;
	}

	public struct CE1970B8
	{
		public GStruct74 gstruct74_0;

		public GEnum31 genum31_0;
	}

	public enum AE105113
	{
		D58F37BD = 1,
		B82557A2 = 2,
		BE84FA8C = 3,
		C890A69E = 4,
		A1846BAA = 5,
		F33CFB37 = 6,
		const_6 = 7,
		const_7 = 8,
		const_8 = 9,
		const_9 = 13,
		E73118B9 = 14,
		F88B21A5 = 15,
		const_12 = 10,
		const_13 = 11,
		const_14 = 12,
		const_15 = 13,
		C68DD5A5 = 14,
		const_17 = 15,
		const_18 = 16,
		B407F0BC = 17,
		const_20 = 18
	}

	public enum GEnum30
	{
		const_0 = 0,
		BFBA4A28 = 1,
		const_2 = 2,
		const_3 = 3,
		const_4 = 4,
		FD1BB103 = 5,
		const_6 = 6,
		const_7 = 7,
		const_8 = 8,
		const_9 = 9,
		const_10 = 10,
		A83FD932 = 11,
		F62CBBAC = 12,
		const_13 = 13,
		const_14 = 14,
		const_15 = 15,
		const_16 = 16,
		const_17 = 17,
		E49493BB = 18,
		const_19 = 19,
		const_20 = 20,
		D229BF84 = 21,
		DCA5C695 = 22,
		ECB05695 = 23,
		EF06762F = 24,
		const_25 = 25,
		const_26 = 26,
		const_27 = 27,
		const_28 = 28,
		const_29 = 29,
		const_30 = 30,
		const_31 = 31,
		const_32 = 31
	}

	public struct GStruct78
	{
		public GStruct73 A294D8AC;

		public GEnum30 genum30_0;

		public int int_0;

		public int F50F9D36;
	}

	public struct DB907DA1
	{
		public GStruct73 gstruct73_0;

		public GEnum30 genum30_0;

		public long long_0;

		public long long_1;
	}

	public struct GStruct79
	{
		public GStruct73 gstruct73_0;
	}

	public struct GStruct80
	{
		public GStruct74 gstruct74_0;
	}

	[Serializable]
	public struct E8110F9D
	{
		public GStruct73 header;

		public int version;

		public int minVersion;

		public int status;

		public GEnum29 mode;

		public int res1;

		public int res2;

		public int res3;

		public int res4;

		public int res5;

		public int res6;
	}

	public struct GStruct81
	{
		public GStruct73 gstruct73_0;

		public D2091826 d2091826_0;

		public int int_0;
	}

	public struct GStruct82
	{
		public GStruct73 D2A69AB8;

		public D2091826 d2091826_0;
	}

	public enum A339783A
	{
		E7227BAA = 1,
		const_1 = 0,
		C4B70984 = 2,
		C89C4B85 = 3,
		const_4 = 4,
		C7078D24 = 5,
		const_6 = 6,
		const_7 = 7,
		C18F391F = 8,
		const_9 = 9,
		const_10 = 10,
		const_11 = 11,
		const_12 = 12,
		BDAEE99E = 13,
		const_14 = 14,
		E014E18C = 15,
		const_16 = 16,
		DC11CABD = 17,
		const_18 = 18,
		FAA02E8F = 19,
		const_20 = 20,
		EFA7F4B1 = 21,
		const_22 = 22,
		const_23 = 23,
		BD2EA404 = 24,
		B7ADE90A = 25,
		const_26 = 26,
		DFA16A90 = 27,
		const_28 = 28,
		ED2A2981 = 29,
		const_30 = 30,
		AD3D2E37 = 31,
		const_32 = 32,
		const_33 = 33,
		const_34 = 34,
		CE229105 = 35,
		const_36 = 36,
		E70D2211 = int.MaxValue
	}

	public enum GEnum31
	{
		E38CD099 = 0,
		C50CD28A = 1,
		F5BD05B0 = 2,
		const_3 = 3,
		const_4 = 4,
		EE3F9484 = 5,
		DE328192 = 6,
		AD30990C = 7,
		const_8 = 8,
		BF1619B3 = 9,
		B3322F05 = 10,
		const_11 = 11,
		B22B2BB6 = 12,
		const_13 = 13,
		const_14 = 14,
		B0A4690C = 15,
		const_16 = 16,
		const_17 = 17,
		CE2E9217 = 18,
		AA8627AF = 19,
		const_20 = 20,
		const_21 = 21,
		const_22 = 22,
		const_23 = 23,
		BB04EE9B = 24,
		const_25 = 25,
		const_26 = 26,
		const_27 = 27,
		const_28 = 28,
		const_29 = 29,
		F3950108 = 30,
		const_31 = 31,
		const_32 = 32,
		const_33 = 33,
		AAA97C96 = 34,
		const_35 = 35,
		const_36 = 36,
		const_37 = int.MaxValue
	}

	public static object F3242317(byte[] byte_0, int int_0, Type type_0)
	{
		int num = Class607.B630A78B.object_0[146](type_0);
		if (num > byte_0.Length)
		{
			return null;
		}
		IntPtr intPtr = Class607.B630A78B.object_0[263](num);
		Class607.B630A78B.object_0[150](byte_0, int_0, intPtr, num);
		object result = Class607.B630A78B.object_0[378](intPtr, type_0);
		Class607.B630A78B.object_0[366](intPtr);
		return result;
	}

	public GClass91()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
