using System;
using System.Runtime.InteropServices;
using System.Text;

public class GClass28
{
	[Flags]
	public enum GEnum0 : uint
	{
		AC0F39AB = 0u,
		flag_1 = 1u,
		F58D1AB5 = 0x10u,
		EE812B35 = 2u,
		A33D4F9B = 0x40u,
		D112DC07 = 0x20u,
		flag_6 = 8u,
		AD2A7D98 = 0x100u,
		CEB0350B = 0x800u,
		flag_9 = 0x1000u
	}

	[DllImport("kernel32.dll", SetLastError = true)]
	public static extern bool SetDllDirectory(string string_0);

	[DllImport("kernel32.dll", SetLastError = true)]
	public static extern int GetDllDirectory(int D30FB792, StringBuilder DA3B8516);

	[DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
	public static extern IntPtr LoadLibrary(string string_0);

	[DllImport("mylibrary")]
	public static extern void InitMyLibrary();

	[DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
	public static extern IntPtr LoadLibraryEx(string string_0, IntPtr intptr_0, GEnum0 genum0_0);

	public GClass28()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
