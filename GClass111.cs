using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;

public class GClass111
{
	public static byte[] smethod_0<T>(T gparam_0, bool bool_0)
	{
		byte[] array = new byte[Marshal.SizeOf(gparam_0)];
		switch (Marshal.SizeOf(gparam_0))
		{
		case 8:
		{
			ulong ulong_ = Class607.B630A78B.object_0[750](gparam_0);
			if (bool_0)
			{
				Class607.B630A78B.object_0[1003](System.Span<byte>.op_Implicit(array), ulong_);
			}
			else
			{
				Class607.B630A78B.object_0[482](System.Span<byte>.op_Implicit(array), ulong_);
			}
			return array;
		}
		case 1:
			return new byte[1] { Class607.B630A78B.object_0[925](gparam_0) };
		case 2:
		{
			ushort num = Class607.B630A78B.object_0[504](gparam_0);
			if (bool_0)
			{
				Class607.B630A78B.object_0[685](System.Span<byte>.op_Implicit(array), num);
			}
			else
			{
				Class607.B630A78B.object_0[789](System.Span<byte>.op_Implicit(array), num);
			}
			return array;
		}
		default:
		{
			object obj = Class607.B630A78B.object_0[778];
			object obj2 = Class607.B630A78B.object_0[720];
			int int_ = Class607.B630A78B.object_0[146](Class607.B630A78B.object_0[6](typeof(T).TypeHandle));
			throw obj(obj2("unknow type with sizeof ", Class607.B630A78B.object_0[1070](ref int_)));
		}
		case 4:
		{
			uint uint_ = Class607.B630A78B.object_0[40](gparam_0);
			if (bool_0)
			{
				Class607.B630A78B.object_0[805](System.Span<byte>.op_Implicit(array), uint_);
			}
			else
			{
				Class607.B630A78B.object_0[103](System.Span<byte>.op_Implicit(array), uint_);
			}
			return array;
		}
		}
	}

	public static int B2AA20BB(string AEAD571B)
	{
		int num = 0;
		char[] array = Class607.B630A78B.object_0[210](AEAD571B);
		for (int i = 0; i < array.Length; i++)
		{
			switch (array[i])
			{
			case '?':
			case 'B':
			case 'b':
			case 'c':
				num++;
				break;
			case 'Q':
			case 'd':
			case 'q':
				num += 8;
				break;
			case 'H':
			case 'e':
			case 'h':
				num += 2;
				break;
			case 'I':
			case 'L':
			case 'f':
			case 'i':
			case 'l':
				num += 4;
				break;
			}
		}
		int num2 = 0;
		char[] array2 = Class607.B630A78B.object_0[210](AEAD571B);
		for (int j = 0; j < Class607.B630A78B.object_0[342](AEAD571B); j++)
		{
			char c = array2[j];
			char c2 = c;
			if (c2 == 'p' || c2 == 's' || c2 == 'x')
			{
				int num3 = 0;
				num2 = j;
				int num4 = num2 - 1;
				while (num4 >= 0 && Class607.B630A78B.object_0[996](array2[num4]))
				{
					num3 = num4;
					num4--;
				}
				string string_ = Class607.B630A78B.object_0[1260](Class607.B630A78B.object_0[31](AEAD571B, num3, num2 - num3).ToArray().Where(char.IsDigit).ToArray());
				if (!Class607.B630A78B.object_0[1205](string_))
				{
					num += Class607.B630A78B.object_0[754](string_);
				}
			}
		}
		return num;
	}

	public static T smethod_1<T>(byte[] byte_0, bool bool_0) where T : struct
	{
		if (bool_0)
		{
			Class607.B630A78B.object_0[250](byte_0);
		}
		T structure = new T();
		int num = Marshal.SizeOf(structure);
		IntPtr intPtr = Class607.B630A78B.object_0[263](num);
		Class607.B630A78B.object_0[150](byte_0, 0, intPtr, num);
		T result = (T)Class607.B630A78B.object_0[378](intPtr, structure.GetType());
		Class607.B630A78B.object_0[366](intPtr);
		return result;
	}

	public static object[] C3B9331C(string E7A82898, byte[] F81EF19F)
	{
		if (Class607.B630A78B.object_0[342](E7A82898) < 1)
		{
			throw Class607.B630A78B.object_0[1005]("String Cannot Empty");
		}
		bool bool_ = false;
		int num = 0;
		if (Class607.B630A78B.object_0[491](E7A82898, ">"))
		{
			bool_ = true;
			E7A82898 = Class607.B630A78B.object_0[1211](E7A82898, 1);
		}
		else if (Class607.B630A78B.object_0[491](E7A82898, "<"))
		{
			E7A82898 = Class607.B630A78B.object_0[1211](E7A82898, 1);
		}
		char[] array = Class607.B630A78B.object_0[210](E7A82898);
		for (int i = 0; i < array.Length; i++)
		{
			switch (array[i])
			{
			case 'H':
			case 'h':
				num += 2;
				break;
			case 'B':
			case 'b':
			case 'x':
				num++;
				break;
			case 'Q':
			case 'q':
				num += 8;
				break;
			case 'I':
			case 'i':
				num += 4;
				break;
			default:
				throw Class607.B630A78B.object_0[1005]("Invalid Char In String");
			}
		}
		if (F81EF19F == null || F81EF19F.Length != num)
		{
			throw Class607.B630A78B.object_0[1005]("Failed Unpack");
		}
		int num2 = 0;
		List<object> list = new List<object>();
		char[] array2 = Class607.B630A78B.object_0[210](E7A82898);
		for (int j = 0; j < array2.Length; j++)
		{
			switch (array2[j])
			{
			case 'Q':
			{
				byte[] array10 = new byte[8];
				Class607.B630A78B.object_0[242](F81EF19F, num2, array10, 0, 8);
				ulong num8 = smethod_1<ulong>(array10, bool_);
				list.Add(num8);
				num2 += 8;
				break;
			}
			case 'H':
			{
				byte[] array9 = new byte[2];
				Class607.B630A78B.object_0[242](F81EF19F, num2, array9, 0, 2);
				ushort num7 = smethod_1<ushort>(array9, bool_);
				list.Add(num7);
				num2 += 2;
				break;
			}
			case 'I':
			case 'L':
			{
				byte[] array8 = new byte[4];
				Class607.B630A78B.object_0[242](F81EF19F, num2, array8, 0, 4);
				uint num6 = smethod_1<uint>(array8, bool_);
				list.Add(num6);
				num2 += 4;
				break;
			}
			case 'B':
			{
				byte[] array7 = new byte[1];
				Class607.B630A78B.object_0[242](F81EF19F, num2, array7, 0, 1);
				byte b2 = smethod_1<byte>(array7, bool_);
				list.Add(b2);
				num2++;
				break;
			}
			case 'h':
			{
				byte[] array6 = new byte[2];
				Class607.B630A78B.object_0[242](F81EF19F, num2, array6, 0, 2);
				short num5 = smethod_1<short>(array6, bool_);
				list.Add(num5);
				num2 += 2;
				break;
			}
			case 'i':
			case 'l':
			{
				byte[] array5 = new byte[4];
				Class607.B630A78B.object_0[242](F81EF19F, num2, array5, 0, 4);
				int num4 = smethod_1<int>(array5, bool_);
				list.Add(num4);
				num2 += 4;
				break;
			}
			case 'b':
			{
				byte[] array4 = new byte[1];
				Class607.B630A78B.object_0[242](F81EF19F, num2, array4, 0, 1);
				sbyte b = smethod_1<sbyte>(array4, bool_);
				list.Add(b);
				num2++;
				break;
			}
			case 'x':
				num2++;
				break;
			case 'q':
			{
				byte[] array3 = new byte[8];
				Class607.B630A78B.object_0[242](F81EF19F, num2, array3, 0, 8);
				long num3 = smethod_1<long>(array3, bool_);
				list.Add(num3);
				num2 += 8;
				break;
			}
			default:
				throw Class607.B630A78B.object_0[1005]("FailedUnpack");
			}
		}
		return list.ToArray();
	}

	public static byte[] smethod_2(string string_0, object[] object_0)
	{
		bool bool_ = false;
		if (Class607.B630A78B.object_0[491](string_0, "<"))
		{
			bool_ = true;
		}
		List<byte> list = new List<byte>();
		foreach (object gparam_ in object_0)
		{
			byte[] collection = smethod_0(gparam_, bool_);
			list.AddRange(collection);
		}
		return list.ToArray();
	}

	public GClass111()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
