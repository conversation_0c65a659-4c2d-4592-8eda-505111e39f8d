internal struct FCAD9F30
{
	private readonly Struct35[] struct35_0;

	private readonly int D426E092;

	public uint method_0(Class76 class76_0)
	{
		uint num = 1u;
		for (int num2 = D426E092; num2 > 0; num2--)
		{
			num = (num << 1) + struct35_0[num].method_0(class76_0);
		}
		return num - (uint)(1 << D426E092);
	}

	public static uint BB14F725(Struct35[] struct35_1, uint uint_0, Class76 class76_0, int int_0)
	{
		uint num = 1u;
		uint num2 = 0u;
		for (int i = 0; i < int_0; i++)
		{
			uint num3 = struct35_1[uint_0 + num].method_0(class76_0);
			num <<= 1;
			num += num3;
			num2 |= num3 << i;
		}
		return num2;
	}

	public uint AA3EA28E(Class76 E408DDA1)
	{
		uint num = 1u;
		uint num2 = 0u;
		for (int i = 0; i < D426E092; i++)
		{
			uint num3 = struct35_0[num].method_0(E408DDA1);
			num <<= 1;
			num += num3;
			num2 |= num3 << i;
		}
		return num2;
	}

	public void method_1()
	{
		for (uint num = 1u; num < 1 << D426E092; num++)
		{
			struct35_0[num].F13A0D25();
		}
	}

	public FCAD9F30(int D426E092)
	{
		this.D426E092 = D426E092;
		struct35_0 = new Struct35[1 << D426E092];
	}
}
