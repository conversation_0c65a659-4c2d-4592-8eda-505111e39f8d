using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Windows.Forms;

[ToolboxItem(false)]
public class GClass100 : ToolStripDropDown, IMessageFilter
{
	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private Control DA064235;

	private ToolStripControlHost D120EF13;

	private Control control_0;

	private const int DDA9A0AC = 513;

	private const int int_0 = 516;

	private const int int_1 = 519;

	private const int DD36ECA9 = 161;

	private const int int_2 = 164;

	private const int C839DD1B = 167;

	public Control D893B180
	{
		[CompilerGenerated]
		get
		{
			return DA064235;
		}
		[CompilerGenerated]
		private set
		{
			DA064235 = value;
		}
	}

	public GClass100(Control control_1)
	{
		Class607.B630A78B.object_0[155](this);
		if (control_1 == null)
		{
			throw Class607.B630A78B.object_0[875]("content");
		}
		D893B180 = control_1;
		Class607.B630A78B.object_0[388](D893B180, Class607.B630A78B.object_0[1245]());
		D120EF13 = Class607.B630A78B.object_0[755](D893B180);
		Class607.B630A78B.object_0[1067](this, bool_0: false);
		Class607.B630A78B.object_0[11](this, E704E9A1: false);
		Class607.B630A78B.object_0[942](this, bool_0: true);
		Class607.B630A78B.object_0[1268](this, bool_0: false);
		ToolStripControlHost d120EF = D120EF13;
		Padding padding;
		Class590.smethod_0(D120EF13, padding = Class607.B630A78B.object_0[1039]());
		Padding padding2;
		Class187.smethod_0(d120EF, padding2 = padding);
		Padding padding_;
		Class303.smethod_0(this, padding_ = padding2);
		Class308.A51F5F89(this, padding_);
		Class607.B630A78B.object_0[1](this, Class607.B630A78B.object_0[598](D893B180));
		Class607.B630A78B.object_0[1](control_1, Class607.B630A78B.object_0[686](D893B180));
		Class607.B630A78B.object_0[1116](this, Class607.B630A78B.object_0[905](D893B180));
		Class607.B630A78B.object_0[1116](control_1, Class607.B630A78B.object_0[686](D893B180));
		Class607.B630A78B.object_0[1277](this, Class607.B630A78B.object_0[686](D893B180));
		Class194.E200818F(D893B180, CB03A105: true);
		Class481.smethod_0(this, bool_0: true);
		Class607.B630A78B.object_0[859](Class607.B630A78B.object_0[189](this), D120EF13);
		Class607.B630A78B.object_0[1034](this);
	}

	public void method_0(Control control_1, Size B91D4F8B = default(Size))
	{
		if (control_1 == null)
		{
			throw Class607.B630A78B.object_0[875]("opener");
		}
		control_0 = control_1;
		int num;
		Rectangle AC0D2B9C;
		if (Class607.B630A78B.object_0[676](ref B91D4F8B) != 0)
		{
			num = Class607.B630A78B.object_0[676](ref B91D4F8B);
		}
		else
		{
			AC0D2B9C = Class607.B630A78B.object_0[38](this);
			num = Class607.B630A78B.object_0[56](ref AC0D2B9C);
		}
		int num2 = num;
		int num3 = ((Class607.B630A78B.object_0[1008](ref B91D4F8B) == 0) ? Class607.B630A78B.object_0[499](D893B180) : Class607.B630A78B.object_0[1008](ref B91D4F8B));
		int num4 = num3;
		Padding F = Class607.B630A78B.object_0[948](this);
		Size D4139D = Class607.B630A78B.object_0[490](ref F);
		int num5 = Class607.B630A78B.object_0[1008](ref D4139D);
		F = Class607.B630A78B.object_0[246](D893B180);
		D4139D = Class607.B630A78B.object_0[490](ref F);
		num3 = num4 + (num5 + Class607.B630A78B.object_0[1008](ref D4139D));
		Rectangle rectangle_ = Class607.B630A78B.object_0[1254](Class607.B630A78B.object_0[1047](control_0));
		object obj = Class607.B630A78B.object_0[1037];
		Control c53212AA = control_0;
		AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
		object obj2 = Class607.B630A78B.object_0[1023];
		int num6 = Class607.B630A78B.object_0[133](ref AC0D2B9C);
		AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
		int bB9A = Class607.B630A78B.object_0[267](ref AC0D2B9C);
		AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
		int num7 = Class607.B630A78B.object_0[133](ref AC0D2B9C) + num2;
		AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
		Rectangle d310B5A = obj(c53212AA, obj2(num6, bB9A, num7, Class607.B630A78B.object_0[267](ref AC0D2B9C) + num3));
		object obj3 = Class607.B630A78B.object_0[759];
		AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
		int num8 = Class607.B630A78B.object_0[133](ref AC0D2B9C);
		AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
		Point point_ = default(Point);
		obj3(ref point_, num8, Class607.B630A78B.object_0[267](ref AC0D2B9C));
		if (!Class607.B630A78B.object_0[228](ref rectangle_, d310B5A))
		{
			object obj4 = Class607.B630A78B.object_0[1037];
			Control c53212AA2 = control_0;
			AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
			object obj5 = Class607.B630A78B.object_0[1023];
			int num9 = Class607.B630A78B.object_0[133](ref AC0D2B9C);
			AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
			int bB9A2 = Class607.B630A78B.object_0[338](ref AC0D2B9C) - num3;
			AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
			int num10 = Class607.B630A78B.object_0[133](ref AC0D2B9C) + num2;
			AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
			d310B5A = obj4(c53212AA2, obj5(num9, bB9A2, num10, Class607.B630A78B.object_0[338](ref AC0D2B9C)));
			if (Class607.B630A78B.object_0[228](ref rectangle_, d310B5A))
			{
				object obj6 = Class607.B630A78B.object_0[759];
				AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
				int num11 = Class607.B630A78B.object_0[133](ref AC0D2B9C);
				AC0D2B9C = Class607.B630A78B.object_0[37](control_0);
				obj6(ref point_, num11, Class607.B630A78B.object_0[338](ref AC0D2B9C) - num3);
			}
		}
		Class607.B630A78B.object_0[19](this, num2);
		Class607.B630A78B.object_0[975](this, num3);
		Class607.B630A78B.object_0[249](this, control_0, point_, ToolStripDropDownDirection.BelowRight);
	}

	protected override void Dispose(bool D10328A2)
	{
		if (D10328A2)
		{
			if (D893B180 != null)
			{
				Class607.B630A78B.object_0[199](D893B180);
				D893B180 = null;
			}
			Class607.B630A78B.object_0[981](this);
		}
		Class607.B630A78B.object_0[1134](this, D10328A2);
	}

	protected override void OnSizeChanged(EventArgs AB334BAD)
	{
		if (D893B180 != null)
		{
			Class607.B630A78B.object_0[1](D893B180, Class607.B630A78B.object_0[687](this));
			Class607.B630A78B.object_0[1116](D893B180, Class607.B630A78B.object_0[687](this));
			Class607.B630A78B.object_0[1276](D893B180, Class607.B630A78B.object_0[687](this));
			Class607.B630A78B.object_0[388](D893B180, Class607.B630A78B.object_0[1245]());
		}
		Class607.B630A78B.object_0[230](this, AB334BAD);
	}

	[DllImport("user32.dll", CharSet = CharSet.Auto, ExactSpelling = true)]
	public static extern int MapWindowPoints(IntPtr EE18B39B, IntPtr intptr_0, [In][Out] ref Point F3ACFF24, int int_3);

	public bool PreFilterMessage(ref Message DB26EF3D)
	{
		if (Class607.B630A78B.object_0[35](this))
		{
			switch (Class607.B630A78B.object_0[615](ref DB26EF3D))
			{
			case 161:
			case 164:
			case 167:
			case 513:
			case 516:
			case 519:
			{
				int num = (int)Class607.B630A78B.object_0[1118](Class607.B630A78B.object_0[400](ref DB26EF3D));
				short num2 = (short)(num & 0xFFFF);
				short num3 = (short)((num >> 16) & 0xFFFF);
				Point point_ = default(Point);
				Class607.B630A78B.object_0[759](ref point_, num2, num3);
				IntPtr eE18B39B = ((Class607.B630A78B.object_0[615](ref DB26EF3D) == 513 || Class607.B630A78B.object_0[615](ref DB26EF3D) == 516 || Class607.B630A78B.object_0[615](ref DB26EF3D) == 519) ? Class607.B630A78B.object_0[985](ref DB26EF3D) : Class607.B630A78B.object_0[120]());
				MapWindowPoints(eE18B39B, Class607.B630A78B.object_0[809](this), ref point_, 1);
				Rectangle C8BB14A = Class607.B630A78B.object_0[38](this);
				if (!Class607.B630A78B.object_0[9](ref C8BB14A, point_))
				{
					Class607.B630A78B.object_0[759](ref point_, num2, num3);
					MapWindowPoints(eE18B39B, Class607.B630A78B.object_0[810](control_0), ref point_, 1);
					C8BB14A = Class607.B630A78B.object_0[37](control_0);
					if (!Class607.B630A78B.object_0[9](ref C8BB14A, point_))
					{
						Class607.B630A78B.object_0[171](this);
					}
				}
				break;
			}
			}
		}
		return false;
	}
}
