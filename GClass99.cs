using System.Drawing;

public class GClass99
{
	public Color F3AEDD05;

	public Color color_0;

	public Color color_1;

	public Color color_2;

	public Color color_3;

	public Color color_4;

	public readonly Pen pen_0;

	public readonly Pen pen_1;

	public readonly Pen B885581B;

	public readonly Pen pen_2;

	public readonly Pen pen_3;

	public readonly Brush brush_0;

	public readonly Brush AA0EF6BF;

	public readonly Brush brush_1;

	public readonly Brush brush_2;

	public readonly Brush brush_3;

	public GClass99(GEnum34 genum34_0, GEnum34 DF974A8F, GEnum34 A50590BF, GEnum35 B8B387B6, GEnum33 C820F5B5)
	{
		Class607.B630A78B.object_0[571](this);
		F3AEDD05 = ((int)genum34_0).smethod_0();
		color_0 = 7936790.smethod_0();
		color_1 = ((int)DF974A8F).smethod_0();
		color_2 = ((int)A50590BF).smethod_0();
		color_3 = ((int)B8B387B6).smethod_0();
		color_4 = ((int)C820F5B5).smethod_0();
		pen_0 = Class607.B630A78B.object_0[277](F3AEDD05);
		pen_1 = Class607.B630A78B.object_0[277](color_1);
		B885581B = Class607.B630A78B.object_0[277](color_2);
		pen_2 = Class607.B630A78B.object_0[277](color_3);
		pen_3 = Class607.B630A78B.object_0[277](color_4);
		brush_0 = Class607.B630A78B.object_0[1044](F3AEDD05);
		AA0EF6BF = Class607.B630A78B.object_0[1044](color_1);
		brush_1 = Class607.B630A78B.object_0[1044](color_2);
		brush_2 = Class607.B630A78B.object_0[1044](color_3);
		brush_3 = Class607.B630A78B.object_0[1044](color_4);
	}
}
