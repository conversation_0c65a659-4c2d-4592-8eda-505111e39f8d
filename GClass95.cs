using System;
using System.Linq;

public class GClass95
{
	private const string string_0 = "EFI PART";

	internal readonly string string_1;

	internal readonly Version version_0;

	internal readonly uint C597679E;

	internal readonly uint uint_0;

	internal readonly ulong ulong_0;

	internal readonly ulong E6827325;

	internal readonly ulong ulong_1;

	internal readonly ulong ulong_2;

	internal readonly Guid B09DE43C;

	internal readonly ulong ulong_3;

	internal readonly uint uint_1;

	internal readonly uint uint_2;

	internal readonly byte[] byte_0;

	internal GClass95(byte[] byte_1)
	{
		Class607.B630A78B.object_0[571](this);
		string_1 = Class607.B630A78B.object_0[698](Class607.B630A78B.object_0[47](), byte_1, 0, 8);
		if (!Class607.B630A78B.object_0[787](string_1, "EFI PART"))
		{
			throw Class607.B630A78B.object_0[778]("Invalid GPT Signature");
		}
		version_0 = Class607.B630A78B.object_0[49](Class607.B630A78B.object_0[41](byte_1, 8), Class607.B630A78B.object_0[41](byte_1, 10));
		C597679E = Class607.B630A78B.object_0[1251](byte_1, 12);
		ulong_0 = Class607.B630A78B.object_0[432](byte_1, 24);
		if (ulong_0 != 1L)
		{
			throw Class607.B630A78B.object_0[778]("Invalid MyLBA property value");
		}
		E6827325 = Class607.B630A78B.object_0[432](byte_1, 32);
		ulong_1 = Class607.B630A78B.object_0[432](byte_1, 40);
		ulong_2 = Class607.B630A78B.object_0[432](byte_1, 48);
		B09DE43C = Class607.B630A78B.object_0[284](smethod_0(byte_1, 56u, 16u));
		ulong_3 = Class607.B630A78B.object_0[432](byte_1, 72);
		uint_1 = Class607.B630A78B.object_0[1251](byte_1, 80);
		uint_2 = Class607.B630A78B.object_0[1251](byte_1, 84);
		uint_0 = Class607.B630A78B.object_0[1251](byte_1.Skip(16).Take(4).ToArray(), 0);
	}

	internal static byte[] smethod_0(byte[] byte_1, uint D23A08A8, uint EA06942A)
	{
		byte[] array = new byte[EA06942A];
		Class607.B630A78B.object_0[912](byte_1, D23A08A8, array, 0L, array.Length);
		return array;
	}
}
