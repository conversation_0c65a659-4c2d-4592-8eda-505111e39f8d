using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

public class GControl0 : UserControl
{
	[Serializable]
	[CompilerGenerated]
	private sealed class B1914798
	{
		public static readonly B1914798 _003C_003E9 = new B1914798();

		public static Func<FD3D7D02, DateTime> _003C_003E9__2_0;

		public B1914798()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal DateTime method_0(FD3D7D02 fd3D7D02_0)
		{
			return fd3D7D02_0.StartedAt;
		}
	}

	[CompilerGenerated]
	private sealed class A33140AA
	{
		public GControl0 A436C60D;

		public string E51A1F30;

		public bool bool_0;

		public EF1F389C ef1F389C_0;

		public bool D009322A;

		public A33140AA()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void C7895B8A(object sender, EventArgs e)
		{
			A436C60D.C38E2C0A(E51A1F30, bool_0, ef1F389C_0, D009322A);
		}
	}

	private IContainer icontainer_0 = null;

	private Panel panel_0;

	private Panel C9957720;

	internal DataGridView B837112A;

	private RichTextBox richTextBox_0;

	private DataGridViewTextBoxColumn A2ADA602;

	private DataGridViewTextBoxColumn D2808436;

	private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_0;

	private DataGridViewButtonColumn dataGridViewButtonColumn_0;

	private DataGridViewTextBoxColumn E228A4B8;

	private DataGridViewTextBoxColumn F0B75517;

	private DataGridViewTextBoxColumn dataGridViewTextBoxColumn_1;

	private DataGridViewButtonColumn dataGridViewButtonColumn_1;

	public GControl0()
	{
		Class607.B630A78B.object_0[194](this);
		method_4();
		method_0();
		D8940986();
		method_1(D09F0D3B.smethod_5(1161902u), EF1F389C.Word, bool_0: false, bool_1: true);
		method_1("Version: ");
		method_1(D09F0D3B.smethod_5(1161364u), EF1F389C.Success, bool_0: false);
		method_1("Selected Tab: ");
		method_1("History", EF1F389C.Success, bool_0: false, bool_1: true);
	}

	private void method_0()
	{
		if (Class607.B630A78B.object_0[0](Class607.B630A78B.object_0[182](B837112A)) > 3)
		{
			if (!(Class607.B630A78B.object_0[596](Class607.B630A78B.object_0[182](B837112A), 3) is DataGridViewButtonColumn))
			{
				Class607.B630A78B.object_0[450](Class607.B630A78B.object_0[182](B837112A), 3);
				DataGridViewButtonColumn dataGridViewButtonColumn = Class607.B630A78B.object_0[601]();
				Class607.B630A78B.object_0[220](dataGridViewButtonColumn, "Logs");
				Class607.B630A78B.object_0[325](dataGridViewButtonColumn, "ViewLogColumn");
				Class607.B630A78B.object_0[1177](dataGridViewButtonColumn, "View Log");
				Class607.B630A78B.object_0[397](dataGridViewButtonColumn, C5154DBA: true);
				Class607.B630A78B.object_0[193](Class607.B630A78B.object_0[182](B837112A), 3, dataGridViewButtonColumn);
			}
			else
			{
				DataGridViewButtonColumn object_ = (DataGridViewButtonColumn)Class607.B630A78B.object_0[596](Class607.B630A78B.object_0[182](B837112A), 3);
				Class607.B630A78B.object_0[220](object_, "Logs");
				Class607.B630A78B.object_0[1177](object_, "View Log");
				Class607.B630A78B.object_0[397](object_, C5154DBA: true);
			}
		}
		else
		{
			DataGridViewButtonColumn dataGridViewButtonColumn2 = Class607.B630A78B.object_0[601]();
			Class607.B630A78B.object_0[220](dataGridViewButtonColumn2, "Logs");
			Class607.B630A78B.object_0[325](dataGridViewButtonColumn2, "ViewLogColumn");
			Class607.B630A78B.object_0[1177](dataGridViewButtonColumn2, "View Log");
			Class607.B630A78B.object_0[397](dataGridViewButtonColumn2, C5154DBA: true);
			Class607.B630A78B.object_0[33](Class607.B630A78B.object_0[182](B837112A), dataGridViewButtonColumn2);
		}
	}

	public void D8940986()
	{
		Class607.B630A78B.object_0[559](Class607.B630A78B.object_0[317](B837112A));
		if (C8087599.E41A7B83.Operations.Count <= 0)
		{
			return;
		}
		List<FD3D7D02> list = C8087599.E41A7B83.Operations.OrderByDescending((FD3D7D02 fd3D7D02_0) => fd3D7D02_0.StartedAt).ToList();
		foreach (FD3D7D02 item in list)
		{
			DataGridViewRowCollection cA = Class607.B630A78B.object_0[317](B837112A);
			object[] obj = new object[4] { item.OperationName, null, null, null };
			DateTime B = item.StartedAt;
			obj[1] = Class607.B630A78B.object_0[460](ref B, "yyyy-MM-ddTHH:mm:ss");
			B = item.EndedAt;
			obj[2] = Class607.B630A78B.object_0[460](ref B, "yyyy-MM-ddTHH:mm:ss");
			obj[3] = "";
			int f = Class825.F6039005(cA, obj);
			Class607.B630A78B.object_0[535](Class607.B630A78B.object_0[522](Class607.B630A78B.object_0[317](B837112A), f), item);
		}
	}

	public unsafe void method_1(string string_0, EF1F389C F7AD5830 = EF1F389C.Word, bool bool_0 = true, bool bool_1 = false)
	{
		A33140AA a33140AA = new A33140AA();
		a33140AA.A436C60D = this;
		a33140AA.E51A1F30 = string_0;
		a33140AA.bool_0 = bool_0;
		a33140AA.ef1F389C_0 = F7AD5830;
		a33140AA.D009322A = bool_1;
		if (Class607.B630A78B.object_0[1012](this))
		{
			Class607.B630A78B.object_0[1241](this, Class607.B630A78B.object_0[935](a33140AA, (nint)__ldftn(A33140AA.C7895B8A)));
		}
		else
		{
			C38E2C0A(a33140AA.E51A1F30, a33140AA.bool_0, a33140AA.ef1F389C_0, a33140AA.D009322A);
		}
	}

	private void C38E2C0A(string string_0, bool bool_0 = true, EF1F389C ef1F389C_0 = EF1F389C.Word, bool bool_1 = false)
	{
		Class607.B630A78B.object_0[335](richTextBox_0, Class607.B630A78B.object_0[206](richTextBox_0));
		Class607.B630A78B.object_0[1076](richTextBox_0, 0);
		if (bool_0 && ef1F389C_0 == EF1F389C.Word)
		{
			string_0 = Class607.B630A78B.object_0[720]("\r", string_0);
		}
		if (ef1F389C_0 == EF1F389C.Word)
		{
			Class607.B630A78B.object_0[218](richTextBox_0, Class607.B630A78B.object_0[542](Class607.B630A78B.object_0[129](richTextBox_0), FontStyle.Regular));
			Class607.B630A78B.object_0[551](richTextBox_0, Class607.B630A78B.object_0[223]());
		}
		else
		{
			Class452.smethod_0(richTextBox_0, (ef1F389C_0 == EF1F389C.Success) ? Class607.B630A78B.object_0[923]() : Class607.B630A78B.object_0[909]());
			Class607.B630A78B.object_0[218](richTextBox_0, Class607.B630A78B.object_0[542](Class607.B630A78B.object_0[129](richTextBox_0), FontStyle.Bold));
		}
		if (bool_1)
		{
			Class607.B630A78B.object_0[218](richTextBox_0, Class607.B630A78B.object_0[542](Class607.B630A78B.object_0[129](richTextBox_0), FontStyle.Bold));
		}
		Class607.B630A78B.object_0[327](richTextBox_0, string_0);
		Class607.B630A78B.object_0[551](richTextBox_0, Class607.B630A78B.object_0[546](richTextBox_0));
		Class607.B630A78B.object_0[183](richTextBox_0);
	}

	private void method_2(object sender, DataGridViewCellEventArgs e)
	{
		if (Class607.B630A78B.object_0[1264](e) != 3 || Class607.B630A78B.object_0[694](e) < 0)
		{
			return;
		}
		FD3D7D02 fD3D7D = (FD3D7D02)Class607.B630A78B.object_0[650](Class607.B630A78B.object_0[522](Class607.B630A78B.object_0[317](B837112A), Class607.B630A78B.object_0[694](e)));
		Class607.B630A78B.object_0[256](richTextBox_0);
		if (fD3D7D.Items != null && fD3D7D.Items.Count > 0)
		{
			foreach (A808BE0B item in fD3D7D.Items)
			{
				method_1(item.text, item.color, item.newline, item.bold);
			}
			return;
		}
		string string_ = Class607.B630A78B.object_0[351](Class607.B630A78B.object_0[1137](AppDomain.CurrentDomain), "Log");
		string string_2 = Class607.B630A78B.object_0[1140]("ADPT_", Class607.B630A78B.object_0[460](ref fD3D7D.StartedAt, "yyyyMMdd"), "*");
		try
		{
			string[] array = Class607.B630A78B.object_0[368](string_, string_2);
			if (array.Length != 0)
			{
				string text = FAA03D30(array, fD3D7D.StartedAt);
				if (!Class607.B630A78B.object_0[1205](text))
				{
					method_3(text);
					return;
				}
			}
			method_1("No log file found for this operation.", EF1F389C.Word, bool_0: true, bool_1: true);
			method_1("");
			method_1("Operation: ", EF1F389C.Word, bool_0: false);
			method_1("");
			method_1(fD3D7D.OperationName, EF1F389C.Success, bool_0: true, bool_1: true);
			method_1("Started: ", EF1F389C.Word, bool_0: false);
			method_1("");
			method_1(Class607.B630A78B.object_0[460](ref fD3D7D.StartedAt, "yyyy-MM-dd HH:mm:ss"), EF1F389C.Success);
			method_1("");
			method_1("Ended: ", EF1F389C.Word, bool_0: false);
			method_1("");
			method_1(Class607.B630A78B.object_0[460](ref fD3D7D.EndedAt, "yyyy-MM-dd HH:mm:ss"), EF1F389C.Success);
		}
		catch (Exception object_)
		{
			method_1("Error loading log file: ", EF1F389C.Word, bool_0: false, bool_1: true);
			method_1(Class607.B630A78B.object_0[1160](object_));
		}
	}

	private string FAA03D30(string[] EA868103, DateTime B8BA752B)
	{
		string result = null;
		TimeSpan timeSpan_ = Class607.B630A78B.object_0[380]();
		foreach (string text in EA868103)
		{
			try
			{
				string object_ = Class607.B630A78B.object_0[386](text);
				string[] array = Class538.smethod_0(object_, new char[1] { '_' });
				if (array.Length >= 2)
				{
					string d60B553F = array[1];
					DateTime dateTime = Class607.B630A78B.object_0[471](d60B553F, "yyyyMMdd-HHmmss", Class607.B630A78B.object_0[1163]());
					TimeSpan timeSpan = (Class607.B630A78B.object_0[1243](dateTime, B8BA752B) ? Class607.B630A78B.object_0[739](dateTime, B8BA752B) : Class607.B630A78B.object_0[739](B8BA752B, dateTime));
					if (Class607.B630A78B.object_0[911](timeSpan, timeSpan_))
					{
						timeSpan_ = timeSpan;
						result = text;
					}
				}
			}
			catch
			{
			}
		}
		return result;
	}

	private void method_3(string DF159C09)
	{
		try
		{
			string[] array = Class607.B630A78B.object_0[458](DF159C09);
			method_1("Showing log file: ", EF1F389C.Word, bool_0: false, bool_1: true);
			method_1(Class607.B630A78B.object_0[1055](DF159C09), EF1F389C.Success);
			method_1("");
			string[] array2 = array;
			foreach (string text in array2)
			{
				if (Class607.B630A78B.object_0[1240](text, "ERROR") || Class607.B630A78B.object_0[1240](text, "Exception"))
				{
					method_1(text, EF1F389C.Word, bool_0: true, bool_1: true);
				}
				else if (Class607.B630A78B.object_0[1240](text, "SUCCESS") || Class607.B630A78B.object_0[1240](text, "COMPLETED"))
				{
					method_1(text, EF1F389C.Success);
				}
				else
				{
					method_1(text);
				}
			}
		}
		catch (Exception object_)
		{
			method_1("Error reading log file: ", EF1F389C.Word, bool_0: false, bool_1: true);
			method_1(Class607.B630A78B.object_0[1160](object_));
		}
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && icontainer_0 != null)
		{
			icontainer_0.Dispose();
		}
		Class607.B630A78B.object_0[878](this, disposing);
	}

	private unsafe void method_4()
	{
		DataGridViewCellStyle dataGridViewCellStyle = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle2 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle3 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle4 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle5 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle6 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle7 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle8 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle9 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle10 = Class607.B630A78B.object_0[595]();
		DataGridViewCellStyle dataGridViewCellStyle11 = Class607.B630A78B.object_0[595]();
		panel_0 = Class607.B630A78B.object_0[853]();
		B837112A = Class607.B630A78B.object_0[1186]();
		E228A4B8 = Class607.B630A78B.object_0[156]();
		F0B75517 = Class607.B630A78B.object_0[156]();
		dataGridViewTextBoxColumn_1 = Class607.B630A78B.object_0[156]();
		dataGridViewButtonColumn_1 = Class607.B630A78B.object_0[601]();
		C9957720 = Class607.B630A78B.object_0[853]();
		richTextBox_0 = Class607.B630A78B.object_0[344]();
		A2ADA602 = Class607.B630A78B.object_0[156]();
		D2808436 = Class607.B630A78B.object_0[156]();
		dataGridViewTextBoxColumn_0 = Class607.B630A78B.object_0[156]();
		dataGridViewButtonColumn_0 = Class607.B630A78B.object_0[601]();
		Class607.B630A78B.object_0[549](panel_0);
		Class607.B630A78B.object_0[361](B837112A);
		Class607.B630A78B.object_0[549](C9957720);
		Class607.B630A78B.object_0[550](this);
		Class607.B630A78B.object_0[781](panel_0).Add(B837112A);
		Class607.B630A78B.object_0[1278](panel_0, DockStyle.Right);
		Class607.B630A78B.object_0[388](panel_0, Class607.B630A78B.object_0[760](306, 0));
		Class607.B630A78B.object_0[689](panel_0, "panel1");
		Class607.B630A78B.object_0[356](panel_0, Class607.B630A78B.object_0[1237](5));
		Class607.B630A78B.object_0[1276](panel_0, Class607.B630A78B.object_0[174](664, 545));
		Class607.B630A78B.object_0[334](panel_0, 0);
		Class607.B630A78B.object_0[884](B837112A, AB1299B1: false);
		Class607.B630A78B.object_0[280](B837112A, bool_0: false);
		Class607.B630A78B.object_0[437](B837112A, bool_0: false);
		Class607.B630A78B.object_0[1009](B837112A, A03EA011: false);
		Class607.B630A78B.object_0[949](dataGridViewCellStyle, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[1257](B837112A, dataGridViewCellStyle);
		Class607.B630A78B.object_0[852](B837112A, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[83](B837112A, DataGridViewHeaderBorderStyle.Single);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle2, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle2, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[772](dataGridViewCellStyle2, Class607.B630A78B.object_0[1237](3));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle2, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle2, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[352](dataGridViewCellStyle2, DataGridViewTriState.True);
		Class607.B630A78B.object_0[1228](B837112A, dataGridViewCellStyle2);
		Class607.B630A78B.object_0[505](B837112A, 32);
		Class607.B630A78B.object_0[459](B837112A, DataGridViewColumnHeadersHeightSizeMode.DisableResizing);
		Class374.AF266ABD(Class607.B630A78B.object_0[182](B837112A), new DataGridViewColumn[4] { E228A4B8, F0B75517, dataGridViewTextBoxColumn_1, dataGridViewButtonColumn_1 });
		Class607.B630A78B.object_0[308](B837112A, Class607.B630A78B.object_0[541]());
		Class607.B630A78B.object_0[949](dataGridViewCellStyle3, DataGridViewContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle3, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle3, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle3, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle3, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle3, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[352](dataGridViewCellStyle3, DataGridViewTriState.True);
		Class607.B630A78B.object_0[20](B837112A, dataGridViewCellStyle3);
		Class607.B630A78B.object_0[1278](B837112A, DockStyle.Fill);
		Class607.B630A78B.object_0[599](B837112A, DataGridViewEditMode.EditOnEnter);
		Class607.B630A78B.object_0[1283](B837112A, Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[388](B837112A, Class607.B630A78B.object_0[760](5, 5));
		Class607.B630A78B.object_0[662](B837112A, A8B78281: false);
		Class607.B630A78B.object_0[689](B837112A, "DGVItems");
		Class607.B630A78B.object_0[949](dataGridViewCellStyle4, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle4, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle4, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle4, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle4, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle4, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[42](B837112A, dataGridViewCellStyle4);
		Class607.B630A78B.object_0[733](B837112A, bool_0: false);
		Class607.B630A78B.object_0[962](B837112A, 51);
		Class607.B630A78B.object_0[949](dataGridViewCellStyle5, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle5, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle5, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle5, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle5, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle5, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[440](B837112A, dataGridViewCellStyle5);
		Class607.B630A78B.object_0[949](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](B837112A)), DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](B837112A)), Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](B837112A)), Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](B837112A)), Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](B837112A)), Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](Class607.B630A78B.object_0[232](Class607.B630A78B.object_0[537](B837112A)), Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[1276](B837112A, Class607.B630A78B.object_0[174](654, 535));
		Class607.B630A78B.object_0[334](B837112A, 20);
		Class607.B630A78B.object_0[1213](B837112A, Class607.B630A78B.object_0[557](this, (nint)__ldftn(GControl0.method_2)));
		Class607.B630A78B.object_0[617](E228A4B8, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle6, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle6, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle6, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle6, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle6, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[941](E228A4B8, dataGridViewCellStyle6);
		Class607.B630A78B.object_0[882](E228A4B8, 197.5309f);
		Class607.B630A78B.object_0[220](E228A4B8, "Operation name");
		Class607.B630A78B.object_0[818](E228A4B8, 6);
		Class607.B630A78B.object_0[325](E228A4B8, "dataGridViewTextBoxColumn5");
		Class607.B630A78B.object_0[12](E228A4B8, bool_0: true);
		Class607.B630A78B.object_0[94](E228A4B8, 127);
		Class607.B630A78B.object_0[617](F0B75517, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle7, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[46](dataGridViewCellStyle7, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle7, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle7, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[352](dataGridViewCellStyle7, DataGridViewTriState.False);
		Class607.B630A78B.object_0[941](F0B75517, dataGridViewCellStyle7);
		Class607.B630A78B.object_0[882](F0B75517, 67.48971f);
		Class607.B630A78B.object_0[220](F0B75517, "start time");
		Class607.B630A78B.object_0[818](F0B75517, 6);
		Class607.B630A78B.object_0[325](F0B75517, "dataGridViewTextBoxColumn6");
		Class607.B630A78B.object_0[12](F0B75517, bool_0: true);
		Class607.B630A78B.object_0[94](F0B75517, 88);
		Class607.B630A78B.object_0[617](dataGridViewTextBoxColumn_1, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[949](dataGridViewCellStyle8, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle8, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle8, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle8, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle8, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle8, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[941](dataGridViewTextBoxColumn_1, dataGridViewCellStyle8);
		Class607.B630A78B.object_0[882](dataGridViewTextBoxColumn_1, 67.48971f);
		Class607.B630A78B.object_0[220](dataGridViewTextBoxColumn_1, "end time");
		Class607.B630A78B.object_0[818](dataGridViewTextBoxColumn_1, 6);
		Class607.B630A78B.object_0[325](dataGridViewTextBoxColumn_1, "DGVMediatekFlashBeginAddress");
		Class607.B630A78B.object_0[12](dataGridViewTextBoxColumn_1, bool_0: true);
		Class607.B630A78B.object_0[94](dataGridViewTextBoxColumn_1, 86);
		Class607.B630A78B.object_0[220](dataGridViewButtonColumn_1, "show logs");
		Class607.B630A78B.object_0[325](dataGridViewButtonColumn_1, "Column1");
		Class607.B630A78B.object_0[1177](dataGridViewButtonColumn_1, "View Log");
		Class607.B630A78B.object_0[781](C9957720).Add(richTextBox_0);
		Class607.B630A78B.object_0[1278](C9957720, DockStyle.Fill);
		Class607.B630A78B.object_0[388](C9957720, Class607.B630A78B.object_0[760](0, 0));
		Class607.B630A78B.object_0[689](C9957720, "panel2");
		Class607.B630A78B.object_0[1276](C9957720, Class607.B630A78B.object_0[174](306, 545));
		Class607.B630A78B.object_0[334](C9957720, 1);
		Class607.B630A78B.object_0[823](richTextBox_0, Class607.B630A78B.object_0[235](33, 33, 33));
		Class607.B630A78B.object_0[1278](richTextBox_0, DockStyle.Fill);
		Class607.B630A78B.object_0[1267](richTextBox_0, Class607.B630A78B.object_0[1152]("Courier New", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[388](richTextBox_0, Class607.B630A78B.object_0[760](0, 0));
		Class607.B630A78B.object_0[689](richTextBox_0, "RichLog");
		Class607.B630A78B.object_0[664](richTextBox_0, B9A668BD: true);
		Class607.B630A78B.object_0[1276](richTextBox_0, Class607.B630A78B.object_0[174](306, 545));
		Class607.B630A78B.object_0[334](richTextBox_0, 6);
		Class607.B630A78B.object_0[1175](richTextBox_0, "");
		Class607.B630A78B.object_0[617](A2ADA602, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle9, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle9, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle9, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle9, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle9, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[941](A2ADA602, dataGridViewCellStyle9);
		Class607.B630A78B.object_0[882](A2ADA602, 197.5309f);
		Class607.B630A78B.object_0[220](A2ADA602, "Operation name");
		Class607.B630A78B.object_0[818](A2ADA602, 6);
		Class607.B630A78B.object_0[325](A2ADA602, "dataGridViewTextBoxColumn1");
		Class607.B630A78B.object_0[12](A2ADA602, bool_0: true);
		Class607.B630A78B.object_0[617](D2808436, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle10, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[46](dataGridViewCellStyle10, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle10, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle10, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[352](dataGridViewCellStyle10, DataGridViewTriState.False);
		Class607.B630A78B.object_0[941](D2808436, dataGridViewCellStyle10);
		Class607.B630A78B.object_0[882](D2808436, 67.48971f);
		Class607.B630A78B.object_0[220](D2808436, "start time");
		Class607.B630A78B.object_0[818](D2808436, 6);
		Class607.B630A78B.object_0[325](D2808436, "dataGridViewTextBoxColumn2");
		Class607.B630A78B.object_0[12](D2808436, bool_0: true);
		Class607.B630A78B.object_0[617](dataGridViewTextBoxColumn_0, DataGridViewAutoSizeColumnMode.DisplayedCells);
		Class607.B630A78B.object_0[949](dataGridViewCellStyle11, DataGridViewContentAlignment.TopLeft);
		Class607.B630A78B.object_0[669](dataGridViewCellStyle11, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[826](dataGridViewCellStyle11, Class607.B630A78B.object_0[1152]("Microsoft Sans Serif", 9f, FontStyle.Regular, GraphicsUnit.Point, 0));
		Class607.B630A78B.object_0[46](dataGridViewCellStyle11, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[1110](dataGridViewCellStyle11, Class607.B630A78B.object_0[235](55, 71, 79));
		Class607.B630A78B.object_0[751](dataGridViewCellStyle11, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[941](dataGridViewTextBoxColumn_0, dataGridViewCellStyle11);
		Class607.B630A78B.object_0[882](dataGridViewTextBoxColumn_0, 67.48971f);
		Class607.B630A78B.object_0[220](dataGridViewTextBoxColumn_0, "end time");
		Class607.B630A78B.object_0[818](dataGridViewTextBoxColumn_0, 6);
		Class607.B630A78B.object_0[325](dataGridViewTextBoxColumn_0, "dataGridViewTextBoxColumn3");
		Class607.B630A78B.object_0[12](dataGridViewTextBoxColumn_0, bool_0: true);
		Class607.B630A78B.object_0[220](dataGridViewButtonColumn_0, "show logs");
		Class607.B630A78B.object_0[325](dataGridViewButtonColumn_0, "dataGridViewButtonColumn1");
		Class607.B630A78B.object_0[1177](dataGridViewButtonColumn_0, "View Log");
		Class607.B630A78B.object_0[511](this, Class607.B630A78B.object_0[1223](6f, 13f));
		Class607.B630A78B.object_0[1043](this, AutoScaleMode.Font);
		Class607.B630A78B.object_0[782](this).Add(C9957720);
		Class607.B630A78B.object_0[782](this).Add(panel_0);
		Class607.B630A78B.object_0[690](this, "History");
		Class607.B630A78B.object_0[1277](this, Class607.B630A78B.object_0[174](970, 545));
		Class607.B630A78B.object_0[1149](panel_0, EB25F899: false);
		Class607.B630A78B.object_0[1040](B837112A);
		Class607.B630A78B.object_0[1149](C9957720, EB25F899: false);
		Class607.B630A78B.object_0[1150](this, bool_0: false);
	}
}
