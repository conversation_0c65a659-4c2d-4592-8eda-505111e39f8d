using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

public class GControl1 : TabControl, GInterface2
{
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private int A8BB3606;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private GEnum36 genum36_0;

	[Browsable(false)]
	public int Int32_0
	{
		[CompilerGenerated]
		get
		{
			return A8BB3606;
		}
		[CompilerGenerated]
		set
		{
			A8BB3606 = value;
		}
	}

	[Browsable(false)]
	public GClass106 DD8EF439 => GClass106.GClass106_0;

	[Browsable(false)]
	public GEnum36 C8B39594
	{
		[CompilerGenerated]
		get
		{
			return genum36_0;
		}
		[CompilerGenerated]
		set
		{
			genum36_0 = value;
		}
	}

	protected override void WndProc(ref Message B6B3AB12)
	{
		if (Class607.B630A78B.object_0[615](ref B6B3AB12) == 4904 && !Class607.B630A78B.object_0[906](this))
		{
			Class607.B630A78B.object_0[208](ref B6B3AB12, Class607.B630A78B.object_0[945](1));
		}
		else
		{
			Class607.B630A78B.object_0[547](this, ref B6B3AB12);
		}
	}

	public GControl1()
	{
		Class607.B630A78B.object_0[668](this);
	}
}
