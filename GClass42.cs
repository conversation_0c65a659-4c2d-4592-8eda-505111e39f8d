using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Text;

public class GClass42 : IEquatable<GClass42>
{
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private string string_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private int DD271CA6;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private int int_0;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private int int_1;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private int int_2;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private long long_0;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private long long_1;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private long long_2;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private long long_3;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private Guid guid_0;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private long long_4;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private int A5A29A00;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private int int_3;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private int int_4;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private CB1D8C31[] cb1D8C31_0;

	protected virtual Type Type_0 => Class607.B630A78B.object_0[6](typeof(GClass42).TypeHandle);

	public string String_0
	{
		[CompilerGenerated]
		get
		{
			return string_0;
		}
		[CompilerGenerated]
		set
		{
			string_0 = value;
		}
	}

	public int CF86CCBE
	{
		[CompilerGenerated]
		get
		{
			return DD271CA6;
		}
		[CompilerGenerated]
		set
		{
			DD271CA6 = value;
		}
	}

	public int F59C4E3C
	{
		[CompilerGenerated]
		get
		{
			return int_0;
		}
		[CompilerGenerated]
		set
		{
			int_0 = value;
		}
	}

	public int Int32_0
	{
		[CompilerGenerated]
		get
		{
			return int_1;
		}
		[CompilerGenerated]
		set
		{
			int_1 = value;
		}
	}

	public int Int32_1
	{
		[CompilerGenerated]
		get
		{
			return int_2;
		}
		[CompilerGenerated]
		set
		{
			int_2 = value;
		}
	}

	public long Int64_0
	{
		[CompilerGenerated]
		get
		{
			return long_0;
		}
		[CompilerGenerated]
		set
		{
			long_0 = value;
		}
	}

	public long Int64_1
	{
		[CompilerGenerated]
		get
		{
			return long_1;
		}
		[CompilerGenerated]
		set
		{
			long_1 = value;
		}
	}

	public long Int64_2
	{
		[CompilerGenerated]
		get
		{
			return long_2;
		}
		[CompilerGenerated]
		set
		{
			long_2 = value;
		}
	}

	public long B5124CB5
	{
		[CompilerGenerated]
		get
		{
			return long_3;
		}
		[CompilerGenerated]
		set
		{
			long_3 = value;
		}
	}

	public Guid Guid_0
	{
		[CompilerGenerated]
		get
		{
			return guid_0;
		}
		[CompilerGenerated]
		set
		{
			guid_0 = value;
		}
	}

	public long ADA29714
	{
		[CompilerGenerated]
		get
		{
			return long_4;
		}
		[CompilerGenerated]
		set
		{
			long_4 = value;
		}
	}

	public int Int32_2
	{
		[CompilerGenerated]
		get
		{
			return A5A29A00;
		}
		[CompilerGenerated]
		set
		{
			A5A29A00 = value;
		}
	}

	public int Int32_3
	{
		[CompilerGenerated]
		get
		{
			return int_3;
		}
		[CompilerGenerated]
		set
		{
			int_3 = value;
		}
	}

	public int Int32_4
	{
		[CompilerGenerated]
		get
		{
			return int_4;
		}
		[CompilerGenerated]
		set
		{
			int_4 = value;
		}
	}

	public CB1D8C31[] CB1D8C31_0
	{
		[CompilerGenerated]
		get
		{
			return cb1D8C31_0;
		}
		[CompilerGenerated]
		set
		{
			cb1D8C31_0 = value;
		}
	}

	public GClass42()
	{
		Class607.B630A78B.object_0[571](this);
		String_0 = "";
		CB1D8C31_0 = new CB1D8C31[0];
	}

	public override string ToString()
	{
		StringBuilder stringBuilder;
		AD8A5111.smethod_0(stringBuilder = Class607.B630A78B.object_0[1086](), "MtkGpt");
		Class607.B630A78B.object_0[426](stringBuilder, " { ");
		if (vmethod_0(stringBuilder))
		{
			Class607.B630A78B.object_0[761](stringBuilder, ' ');
		}
		Class607.B630A78B.object_0[761](stringBuilder, '}');
		return stringBuilder.ToString();
	}

	protected virtual bool vmethod_0(StringBuilder B91B80BC)
	{
		Class607.B630A78B.object_0[628]();
		Class607.B630A78B.object_0[426](B91B80BC, "Signature = ");
		Class607.B630A78B.object_0[857](B91B80BC, String_0);
		Class607.B630A78B.object_0[426](B91B80BC, ", Revision = ");
		object obj = Class607.B630A78B.object_0[426];
		int cF86CCBE = CF86CCBE;
		obj(B91B80BC, Class607.B630A78B.object_0[1070](ref cF86CCBE));
		Class607.B630A78B.object_0[426](B91B80BC, ", HeaderSize = ");
		object obj2 = Class607.B630A78B.object_0[426];
		cF86CCBE = F59C4E3C;
		obj2(B91B80BC, Class607.B630A78B.object_0[1070](ref cF86CCBE));
		Class607.B630A78B.object_0[426](B91B80BC, ", Crc32 = ");
		object obj3 = Class607.B630A78B.object_0[426];
		cF86CCBE = Int32_0;
		obj3(B91B80BC, Class607.B630A78B.object_0[1070](ref cF86CCBE));
		Class607.B630A78B.object_0[426](B91B80BC, ", Reserved = ");
		object obj4 = Class607.B630A78B.object_0[426];
		cF86CCBE = Int32_1;
		obj4(B91B80BC, Class607.B630A78B.object_0[1070](ref cF86CCBE));
		Class607.B630A78B.object_0[426](B91B80BC, ", CurrentLba = ");
		object obj5 = Class607.B630A78B.object_0[426];
		long DB1A9A9E = Int64_0;
		obj5(B91B80BC, Class607.B630A78B.object_0[125](ref DB1A9A9E));
		Class607.B630A78B.object_0[426](B91B80BC, ", BackupLba = ");
		object obj6 = Class607.B630A78B.object_0[426];
		DB1A9A9E = Int64_1;
		obj6(B91B80BC, Class607.B630A78B.object_0[125](ref DB1A9A9E));
		Class607.B630A78B.object_0[426](B91B80BC, ", FirstUsableLba = ");
		object obj7 = Class607.B630A78B.object_0[426];
		DB1A9A9E = Int64_2;
		obj7(B91B80BC, Class607.B630A78B.object_0[125](ref DB1A9A9E));
		Class607.B630A78B.object_0[426](B91B80BC, ", LastUsableLba = ");
		object obj8 = Class607.B630A78B.object_0[426];
		DB1A9A9E = B5124CB5;
		obj8(B91B80BC, Class607.B630A78B.object_0[125](ref DB1A9A9E));
		Class607.B630A78B.object_0[426](B91B80BC, ", Guid = ");
		Class607.B630A78B.object_0[426](B91B80BC, Guid_0.ToString());
		Class607.B630A78B.object_0[426](B91B80BC, ", PartitionEntryStartLba = ");
		object obj9 = Class607.B630A78B.object_0[426];
		DB1A9A9E = ADA29714;
		obj9(B91B80BC, Class607.B630A78B.object_0[125](ref DB1A9A9E));
		Class607.B630A78B.object_0[426](B91B80BC, ", PartitionEntriesCount = ");
		object obj10 = Class607.B630A78B.object_0[426];
		cF86CCBE = Int32_2;
		obj10(B91B80BC, Class607.B630A78B.object_0[1070](ref cF86CCBE));
		Class607.B630A78B.object_0[426](B91B80BC, ", PartitionEntrySize = ");
		object obj11 = Class607.B630A78B.object_0[426];
		cF86CCBE = Int32_3;
		obj11(B91B80BC, Class607.B630A78B.object_0[1070](ref cF86CCBE));
		Class607.B630A78B.object_0[426](B91B80BC, ", SectorSize = ");
		object obj12 = Class607.B630A78B.object_0[426];
		cF86CCBE = Int32_4;
		obj12(B91B80BC, Class607.B630A78B.object_0[1070](ref cF86CCBE));
		Class607.B630A78B.object_0[426](B91B80BC, ", Partitions = ");
		Class607.B630A78B.object_0[857](B91B80BC, CB1D8C31_0);
		return true;
	}

	[SpecialName]
	public static bool smethod_0(GClass42 gclass42_0, GClass42 A0202131)
	{
		return !smethod_1(gclass42_0, A0202131);
	}

	[SpecialName]
	public static bool smethod_1(GClass42 FC91C113, GClass42 EF240613)
	{
		if (FC91C113 != EF240613)
		{
			return FC91C113?.Equals(EF240613) ?? false;
		}
		return true;
	}

	public override int GetHashCode()
	{
		return ((((((((((((((EqualityComparer<Type>.Default.GetHashCode(Type_0) * -1521134295 + EqualityComparer<string>.Default.GetHashCode(String_0)) * -1521134295 + EqualityComparer<int>.Default.GetHashCode(CF86CCBE)) * -1521134295 + EqualityComparer<int>.Default.GetHashCode(F59C4E3C)) * -1521134295 + EqualityComparer<int>.Default.GetHashCode(Int32_0)) * -1521134295 + EqualityComparer<int>.Default.GetHashCode(Int32_1)) * -1521134295 + EqualityComparer<long>.Default.GetHashCode(Int64_0)) * -1521134295 + EqualityComparer<long>.Default.GetHashCode(Int64_1)) * -1521134295 + EqualityComparer<long>.Default.GetHashCode(Int64_2)) * -1521134295 + EqualityComparer<long>.Default.GetHashCode(B5124CB5)) * -1521134295 + EqualityComparer<Guid>.Default.GetHashCode(Guid_0)) * -1521134295 + EqualityComparer<long>.Default.GetHashCode(ADA29714)) * -1521134295 + EqualityComparer<int>.Default.GetHashCode(Int32_2)) * -1521134295 + EqualityComparer<int>.Default.GetHashCode(Int32_3)) * -1521134295 + EqualityComparer<int>.Default.GetHashCode(Int32_4)) * -1521134295 + EqualityComparer<CB1D8C31[]>.Default.GetHashCode(CB1D8C31_0);
	}

	public override bool Equals(object EE177D98)
	{
		return Equals(EE177D98 as GClass42);
	}

	public virtual bool Equals(GClass42 A030792B)
	{
		if (this != A030792B)
		{
			if (A030792B != null && Class607.B630A78B.object_0[795](Type_0, A030792B.Type_0) && EqualityComparer<string>.Default.Equals(String_0, A030792B.String_0) && EqualityComparer<int>.Default.Equals(CF86CCBE, A030792B.CF86CCBE) && EqualityComparer<int>.Default.Equals(F59C4E3C, A030792B.F59C4E3C) && EqualityComparer<int>.Default.Equals(Int32_0, A030792B.Int32_0) && EqualityComparer<int>.Default.Equals(Int32_1, A030792B.Int32_1) && EqualityComparer<long>.Default.Equals(Int64_0, A030792B.Int64_0) && EqualityComparer<long>.Default.Equals(Int64_1, A030792B.Int64_1) && EqualityComparer<long>.Default.Equals(Int64_2, A030792B.Int64_2) && EqualityComparer<long>.Default.Equals(B5124CB5, A030792B.B5124CB5) && EqualityComparer<Guid>.Default.Equals(Guid_0, A030792B.Guid_0) && EqualityComparer<long>.Default.Equals(ADA29714, A030792B.ADA29714) && EqualityComparer<int>.Default.Equals(Int32_2, A030792B.Int32_2) && EqualityComparer<int>.Default.Equals(Int32_3, A030792B.Int32_3) && EqualityComparer<int>.Default.Equals(Int32_4, A030792B.Int32_4))
			{
				return EqualityComparer<CB1D8C31[]>.Default.Equals(CB1D8C31_0, A030792B.CB1D8C31_0);
			}
			return false;
		}
		return true;
	}

	public virtual GClass42 AF09E5B5()
	{
		return new GClass42(this);
	}

	protected GClass42(GClass42 gclass42_0)
	{
		Class607.B630A78B.object_0[571](this);
		String_0 = gclass42_0.String_0;
		CF86CCBE = gclass42_0.CF86CCBE;
		F59C4E3C = gclass42_0.F59C4E3C;
		Int32_0 = gclass42_0.Int32_0;
		Int32_1 = gclass42_0.Int32_1;
		Int64_0 = gclass42_0.Int64_0;
		Int64_1 = gclass42_0.Int64_1;
		Int64_2 = gclass42_0.Int64_2;
		B5124CB5 = gclass42_0.B5124CB5;
		Guid_0 = gclass42_0.Guid_0;
		ADA29714 = gclass42_0.ADA29714;
		Int32_2 = gclass42_0.Int32_2;
		Int32_3 = gclass42_0.Int32_3;
		Int32_4 = gclass42_0.Int32_4;
		CB1D8C31_0 = gclass42_0.CB1D8C31_0;
	}
}
