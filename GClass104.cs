using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

public class GClass104 : RadioButton, GInterface2
{
	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private int F7034A1B;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private GEnum36 BB9B4A11;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private Point E5BFBF15;

	private bool EB163702;

	private readonly Class56 class56_0;

	private readonly Class56 class56_1;

	private Rectangle rectangle_0;

	private int int_0;

	private const int BB80B338 = 19;

	private const int int_1 = 9;

	private const int A7311F28 = 2;

	private const int int_2 = 15;

	[Browsable(false)]
	public int Int32_0
	{
		[CompilerGenerated]
		get
		{
			return F7034A1B;
		}
		[CompilerGenerated]
		set
		{
			F7034A1B = value;
		}
	}

	[Browsable(false)]
	public GClass106 DD8EF439 => GClass106.GClass106_0;

	[Browsable(false)]
	public GEnum36 C8B39594
	{
		[CompilerGenerated]
		get
		{
			return BB9B4A11;
		}
		[CompilerGenerated]
		set
		{
			BB9B4A11 = value;
		}
	}

	[Browsable(false)]
	public Point Point_0
	{
		[CompilerGenerated]
		get
		{
			return E5BFBF15;
		}
		[CompilerGenerated]
		set
		{
			E5BFBF15 = value;
		}
	}

	[Category("Behavior")]
	public bool Boolean_0
	{
		get
		{
			return EB163702;
		}
		set
		{
			EB163702 = value;
			Class607.B630A78B.object_0[11](this, Class607.B630A78B.object_0[638](this));
			if (value)
			{
				Class607.B630A78B.object_0[350](this, Class607.B630A78B.object_0[1237](0));
			}
			Class607.B630A78B.object_0[583](this);
		}
	}

	public unsafe GClass104()
	{
		Class607.B630A78B.object_0[399](this);
		Class607.B630A78B.object_0[824](this, ControlStyles.DoubleBuffer | ControlStyles.OptimizedDoubleBuffer, A5347B95: true);
		class56_0 = new Class56
		{
			F8AB7C94_0 = F8AB7C94.const_1,
			D31D243E = 0.06
		};
		class56_1 = new Class56(E03D843F: false)
		{
			F8AB7C94_0 = F8AB7C94.F41002BC,
			D31D243E = 0.1,
			Double_0 = 0.08
		};
		class56_0.AE044886 += delegate
		{
			Class607.B630A78B.object_0[583](this);
		};
		class56_1.AE044886 += delegate
		{
			Class607.B630A78B.object_0[583](this);
		};
		Class607.B630A78B.object_0[329](this, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GClass104.method_3)));
		Class607.B630A78B.object_0[758](this, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GClass104.method_0)));
		Boolean_0 = true;
		Point_0 = Class607.B630A78B.object_0[760](-1, -1);
	}

	private void method_0(object sender, EventArgs e)
	{
		int_0 = Class607.B630A78B.object_0[500](this) / 2 - (int)Class607.B630A78B.object_0[1045](9.5);
		rectangle_0 = Class607.B630A78B.object_0[1023](int_0, int_0, 19, 19);
	}

	public override Size GetPreferredSize(Size proposedSize)
	{
		int num = int_0 + 20;
		SizeF sizeF_ = Class607.B630A78B.object_0[1203](Class607.B630A78B.object_0[278](this), Class607.B630A78B.object_0[814](this), DD8EF439.B39C48BD);
		int num2 = num + (int)Class607.B630A78B.object_0[331](ref sizeF_);
		return Boolean_0 ? Class607.B630A78B.object_0[174](num2, 30) : Class607.B630A78B.object_0[174](num2, 20);
	}

	protected override void OnPaint(PaintEventArgs A40A110F)
	{
		Graphics graphics = Class607.B630A78B.object_0[340](A40A110F);
		Class607.B630A78B.object_0[111](graphics, SmoothingMode.AntiAlias);
		Class607.B630A78B.object_0[423](graphics, TextRenderingHint.AntiAlias);
		Class607.B630A78B.object_0[1141](graphics, Class607.B630A78B.object_0[410](Class607.B630A78B.object_0[1161](this)));
		int num = int_0 + 9;
		double num2 = class56_0.D6187CAA();
		int num3;
		if (!Class607.B630A78B.object_0[735](this))
		{
			Color color_ = DD8EF439.method_1();
			num3 = Class607.B630A78B.object_0[430](ref color_);
		}
		else
		{
			num3 = (int)(num2 * 255.0);
		}
		int num4 = num3;
		int num5;
		if (!Class607.B630A78B.object_0[735](this))
		{
			Color color_ = DD8EF439.method_1();
			num5 = Class607.B630A78B.object_0[430](ref color_);
		}
		else
		{
			Color color_ = DD8EF439.method_4();
			num5 = (int)((double)(int)Class607.B630A78B.object_0[430](ref color_) * (1.0 - num2));
		}
		int num6 = num5;
		float num7 = (float)(num2 * 8.0);
		float num8 = num7 / 2f;
		num7 = (float)(num2 * 9.0);
		SolidBrush solidBrush = Class755.smethod_0(ECA9D598.smethod_0(num4, Class607.B630A78B.object_0[735](this) ? DD8EF439.CEBAB4AB.color_3 : DD8EF439.method_1()));
		Pen object_ = Class607.B630A78B.object_0[277](Class607.B630A78B.object_0[1151](solidBrush));
		if (Boolean_0 && class56_1.method_1())
		{
			Point point_ = default(Point);
			for (int i = 0; i < class56_1.B4A91C14(); i++)
			{
				double num9 = class56_1.method_5(i);
				Class607.B630A78B.object_0[759](ref point_, num, num);
				SolidBrush solidBrush2 = Class755.smethod_0(ECA9D598.smethod_0((int)(num9 * 40.0), ((bool)class56_1.method_10(i)[0]) ? Class607.B630A78B.object_0[620]() : Class607.B630A78B.object_0[1151](solidBrush)));
				int num10 = ((Class607.B630A78B.object_0[500](this) % 2 == 0) ? (Class607.B630A78B.object_0[500](this) - 3) : (Class607.B630A78B.object_0[500](this) - 2));
				int num11 = ((class56_1.method_8(i) == Enum14.const_2) ? ((int)((double)num10 * (0.8 + 0.2 * num9))) : num10);
				using (GraphicsPath graphicsPath_ = Class59.smethod_0(Class607.B630A78B.object_0[372](ref point_) - num11 / 2, Class607.B630A78B.object_0[1229](ref point_) - num11 / 2, num11, num11, num11 / 2))
				{
					Class607.B630A78B.object_0[251](graphics, solidBrush2, graphicsPath_);
				}
				Class607.B630A78B.object_0[529](solidBrush2);
			}
		}
		Color color_2 = Class59.smethod_1(Class607.B630A78B.object_0[410](Class607.B630A78B.object_0[1161](this)), Class607.B630A78B.object_0[735](this) ? DD8EF439.method_4() : DD8EF439.method_1(), num6);
		using (GraphicsPath graphicsPath_2 = Class59.smethod_0(int_0, int_0, 19f, 19f, 9f))
		{
			Class607.B630A78B.object_0[251](graphics, Class607.B630A78B.object_0[1044](color_2), graphicsPath_2);
			if (Class607.B630A78B.object_0[735](this))
			{
				Class607.B630A78B.object_0[251](graphics, solidBrush, graphicsPath_2);
			}
		}
		Class607.B630A78B.object_0[339](graphics, Class607.B630A78B.object_0[1044](Class607.B630A78B.object_0[410](Class607.B630A78B.object_0[1161](this))), 2 + int_0, 2 + int_0, 15, 15);
		if (Class607.B630A78B.object_0[714](this))
		{
			using GraphicsPath graphicsPath_3 = Class59.smethod_0((float)num - num8, (float)num - num8, num7, num7, 4f);
			Class607.B630A78B.object_0[251](graphics, solidBrush, graphicsPath_3);
		}
		SizeF C6998C = Class607.B630A78B.object_0[1203](graphics, Class607.B630A78B.object_0[814](this), DD8EF439.B39C48BD);
		Class79.D48B2CB9(graphics, Class607.B630A78B.object_0[814](this), DD8EF439.B39C48BD, Class607.B630A78B.object_0[735](this) ? DD8EF439.C38AE18D() : DD8EF439.method_2(), int_0 + 22, (float)(Class607.B630A78B.object_0[500](this) / 2) - Class607.B630A78B.object_0[1238](ref C6998C) / 2f);
		Class607.B630A78B.object_0[529](solidBrush);
		Class607.B630A78B.object_0[747](object_);
	}

	private bool F41B4B0E()
	{
		return Class607.B630A78B.object_0[9](ref rectangle_0, Point_0);
	}

	protected unsafe override void OnCreateControl()
	{
		Class607.B630A78B.object_0[451](this);
		Class607.B630A78B.object_0[1267](this, DD8EF439.B39C48BD);
		if (!Class607.B630A78B.object_0[906](this))
		{
			C8B39594 = GEnum36.CFBD4335;
			Class607.B630A78B.object_0[840](this, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GClass104.method_4)));
			Class607.B630A78B.object_0[682](this, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GClass104.method_5)));
			Class607.B630A78B.object_0[1018](this, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GClass104.method_6)));
			Class607.B630A78B.object_0[610](this, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GClass104.B70BBC90)));
			Class607.B630A78B.object_0[428](this, Class607.B630A78B.object_0[384](this, (nint)__ldftn(GClass104.method_7)));
		}
	}

	[CompilerGenerated]
	private void method_1(object object_0)
	{
		Class607.B630A78B.object_0[583](this);
	}

	[CompilerGenerated]
	private void method_2(object object_0)
	{
		Class607.B630A78B.object_0[583](this);
	}

	[CompilerGenerated]
	private void method_3(object sender, EventArgs e)
	{
		class56_0.method_2((!Class607.B630A78B.object_0[714](this)) ? Enum14.const_1 : Enum14.const_0);
	}

	[CompilerGenerated]
	private void method_4(object sender, EventArgs e)
	{
		C8B39594 = GEnum36.A6BC4E23;
	}

	[CompilerGenerated]
	private void method_5(object sender, EventArgs e)
	{
		Point_0 = Class607.B630A78B.object_0[760](-1, -1);
		C8B39594 = GEnum36.CFBD4335;
	}

	[CompilerGenerated]
	private void method_6(object sender, MouseEventArgs e)
	{
		C8B39594 = GEnum36.const_1;
		if (Boolean_0 && Class607.B630A78B.object_0[944](e) == MouseButtons.Left && F41B4B0E())
		{
			class56_1.Double_0 = 0.0;
			class56_1.method_2(Enum14.const_2, new object[1] { Class607.B630A78B.object_0[714](this) });
		}
	}

	[CompilerGenerated]
	private void B70BBC90(object sender, MouseEventArgs e)
	{
		C8B39594 = GEnum36.A6BC4E23;
		class56_1.Double_0 = 0.08;
	}

	[CompilerGenerated]
	private void method_7(object sender, MouseEventArgs e)
	{
		Point_0 = Class607.B630A78B.object_0[613](e);
		Class275.FBA01B9D(this, F41B4B0E() ? Class607.B630A78B.object_0[268]() : Class607.B630A78B.object_0[541]());
	}
}
