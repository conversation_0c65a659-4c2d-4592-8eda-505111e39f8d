<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyTitle>MotoKingPro</AssemblyTitle>
    <AssemblyDescription>Mobile Device Repair and Flashing Tool</AssemblyDescription>
    <AssemblyConfiguration>Release</AssemblyConfiguration>
    <AssemblyCompany>MotoKingPro Team</AssemblyCompany>
    <AssemblyProduct>MotoKingPro</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <StartupObject>MotoKingPro.Program</StartupObject>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="bin\loader\auto\" />
    <Folder Include="logs\" />
    <Folder Include="temp\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="Core\BaseClasses.cs" />
    <Compile Include="UI\CustomControls.cs" />
    <Compile Include="Platforms\QualcommPage.cs" />
    <Compile Include="Platforms\MediaTekPage.cs" />
    <Compile Include="Platforms\MotorolaPage.cs" />
    <Compile Include="Platforms\ServicesPage.cs" />
    <Compile Include="MediaTek\FlashToolLibrary.cs" />
    <Compile Include="MediaTek\UsbManager.cs" />
    <Compile Include="MediaTek\ScatterFileManager.cs" />
    <Compile Include="MediaTek\AuthenticationBypass.cs" />
    <Compile Include="MainForm_Complete.cs" />
    <Compile Include="Program.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="System.Security.Permissions" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.ComponentModel" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="bin\FlashToolLib.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="bin\setupapi.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="bin\winusb.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
