using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

public class GClass110
{
	public delegate IntPtr GDelegate31(int int_0, IntPtr EB96638D, IntPtr B4AEE718);

	public delegate void CE29FF84(IntPtr F3BC6D3C, uint uint_0, UIntPtr uintptr_0, uint uint_1);

	public enum GEnum38
	{
		const_0,
		A9AA4E8A,
		DA9C20BF,
		const_3,
		F9809A10,
		const_5,
		const_6,
		CD8A529E,
		A13A0432,
		const_9
	}

	public struct GStruct93
	{
		public IntPtr D3A2D6BF;

		public IntPtr intptr_0;

		public IntPtr DA238E86;

		public uint uint_0;

		public IntPtr intptr_1;
	}

	private static IWin32Window F0B47B3B;

	private static GDelegate31 gdelegate31_0;

	private static IntPtr intptr_0;

	public const int DEB72897 = 12;

	static GClass110()
	{
		gdelegate31_0 = smethod_6;
		intptr_0 = Class607.B630A78B.object_0[120]();
	}

	public static DialogResult CD3668B8(string D51C531C)
	{
		smethod_5();
		return Class607.B630A78B.object_0[776](D51C531C);
	}

	public static DialogResult E8389E9A(string string_0, string string_1)
	{
		smethod_5();
		return Class607.B630A78B.object_0[1146](string_0, string_1);
	}

	public static DialogResult F50AAC23(string BA96031F, string string_0, MessageBoxButtons messageBoxButtons_0)
	{
		smethod_5();
		return Class607.B630A78B.object_0[565](BA96031F, string_0, messageBoxButtons_0);
	}

	public static DialogResult B338F027(string string_0, string C035908B, MessageBoxButtons messageBoxButtons_0, MessageBoxIcon messageBoxIcon_0)
	{
		smethod_5();
		return Class607.B630A78B.object_0[726](string_0, C035908B, messageBoxButtons_0, messageBoxIcon_0);
	}

	public static DialogResult smethod_0(string string_0, string string_1, MessageBoxButtons CABF6202, MessageBoxIcon messageBoxIcon_0, MessageBoxDefaultButton FF0404BB)
	{
		smethod_5();
		return Class607.B630A78B.object_0[197](string_0, string_1, CABF6202, messageBoxIcon_0, FF0404BB);
	}

	public static DialogResult C2AB1F9F(string string_0, string string_1, MessageBoxButtons F40DC49D, MessageBoxIcon C3941A81, MessageBoxDefaultButton AE9BC88A, MessageBoxOptions A6B537A5)
	{
		smethod_5();
		return Class607.B630A78B.object_0[436](string_0, string_1, F40DC49D, C3941A81, AE9BC88A, A6B537A5);
	}

	public static DialogResult smethod_1(IWin32Window iwin32Window_0, string A3B4CD0C)
	{
		F0B47B3B = iwin32Window_0;
		smethod_5();
		return Class607.B630A78B.object_0[252](iwin32Window_0, A3B4CD0C);
	}

	public static DialogResult smethod_2(IWin32Window B7B782B8, string string_0, string string_1)
	{
		F0B47B3B = B7B782B8;
		smethod_5();
		return Class607.B630A78B.object_0[192](B7B782B8, string_0, string_1);
	}

	public static DialogResult B490EF34(IWin32Window E5941A0B, string BE2D7D99, string FB3DFCA1, MessageBoxButtons messageBoxButtons_0)
	{
		F0B47B3B = E5941A0B;
		smethod_5();
		return Class607.B630A78B.object_0[509](E5941A0B, BE2D7D99, FB3DFCA1, messageBoxButtons_0);
	}

	public static DialogResult smethod_3(IWin32Window iwin32Window_0, string string_0, string string_1, MessageBoxButtons DAAE789B, MessageBoxIcon F58F6331)
	{
		F0B47B3B = iwin32Window_0;
		smethod_5();
		return Class607.B630A78B.object_0[5](iwin32Window_0, string_0, string_1, DAAE789B, F58F6331);
	}

	public static DialogResult smethod_4(IWin32Window iwin32Window_0, string string_0, string ********, MessageBoxButtons messageBoxButtons_0, MessageBoxIcon CD997481, MessageBoxDefaultButton messageBoxDefaultButton_0)
	{
		F0B47B3B = iwin32Window_0;
		smethod_5();
		return Class607.B630A78B.object_0[1157](iwin32Window_0, string_0, ********, messageBoxButtons_0, CD997481, messageBoxDefaultButton_0);
	}

	public static DialogResult A80EAF39(IWin32Window iwin32Window_0, string BB37070A, string string_0, MessageBoxButtons EE33608D, MessageBoxIcon CE25739E, MessageBoxDefaultButton F2B29598, MessageBoxOptions DFBD1AB5)
	{
		F0B47B3B = iwin32Window_0;
		smethod_5();
		return Class607.B630A78B.object_0[723](iwin32Window_0, BB37070A, string_0, EE33608D, CE25739E, F2B29598, DFBD1AB5);
	}

	[DllImport("user32.dll")]
	private static extern bool GetWindowRect(IntPtr intptr_1, ref Rectangle rectangle_0);

	[DllImport("user32.dll")]
	private static extern int MoveWindow(IntPtr intptr_1, int int_0, int int_1, int int_2, int int_3, bool bool_0);

	[DllImport("User32.dll")]
	public static extern UIntPtr SetTimer(IntPtr intptr_1, UIntPtr uintptr_0, uint AA9E4B03, CE29FF84 BCBD2C97);

	[DllImport("User32.dll")]
	public static extern IntPtr SendMessage(IntPtr A806E220, int CA128800, IntPtr D305F693, IntPtr FF0A6F3C);

	[DllImport("user32.dll")]
	public static extern IntPtr SetWindowsHookEx(int int_0, GDelegate31 gdelegate31_1, IntPtr intptr_1, int int_1);

	[DllImport("user32.dll")]
	public static extern int UnhookWindowsHookEx(IntPtr intptr_1);

	[DllImport("user32.dll")]
	public static extern IntPtr CallNextHookEx(IntPtr A59EB71D, int int_0, IntPtr E5842B13, IntPtr F23EB894);

	[DllImport("user32.dll")]
	public static extern int GetWindowTextLength(IntPtr intptr_1);

	[DllImport("user32.dll")]
	public static extern int GetWindowText(IntPtr intptr_1, StringBuilder stringBuilder_0, int int_0);

	[DllImport("user32.dll")]
	public static extern int EndDialog(IntPtr intptr_1, IntPtr intptr_2);

	private static void smethod_5()
	{
		if (Class607.B630A78B.object_0[322](intptr_0, Class607.B630A78B.object_0[120]()))
		{
			throw Class607.B630A78B.object_0[332]("multiple calls are not supported");
		}
		if (F0B47B3B != null)
		{
			intptr_0 = SetWindowsHookEx(12, gdelegate31_0, Class607.B630A78B.object_0[120](), Class607.B630A78B.object_0[1085]());
		}
	}

	private static IntPtr smethod_6(int ********, IntPtr intptr_1, IntPtr intptr_2)
	{
		if (******** < 0)
		{
			return CallNextHookEx(intptr_0, ********, intptr_1, intptr_2);
		}
		GStruct93 gStruct = (GStruct93)Class607.B630A78B.object_0[378](intptr_2, Class607.B630A78B.object_0[6](typeof(GStruct93).TypeHandle));
		IntPtr a59EB71D = intptr_0;
		if (gStruct.uint_0 == 5)
		{
			try
			{
				smethod_7(gStruct.intptr_1);
			}
			finally
			{
				UnhookWindowsHookEx(intptr_0);
				intptr_0 = Class607.B630A78B.object_0[120]();
			}
		}
		return CallNextHookEx(a59EB71D, ********, intptr_1, intptr_2);
	}

	private static void smethod_7(IntPtr intptr_1)
	{
		Rectangle rectangle_ = default(Rectangle);
		Class607.B630A78B.object_0[1024](ref rectangle_, 0, 0, 0, 0);
		GetWindowRect(intptr_1, ref rectangle_);
		int num = Class607.B630A78B.object_0[56](ref rectangle_) - Class607.B630A78B.object_0[801](ref rectangle_);
		int num2 = Class607.B630A78B.object_0[970](ref rectangle_) - Class607.B630A78B.object_0[1263](ref rectangle_);
		Rectangle rectangle_2 = default(Rectangle);
		Class607.B630A78B.object_0[1024](ref rectangle_2, 0, 0, 0, 0);
		GetWindowRect(Class607.B630A78B.object_0[1215](F0B47B3B), ref rectangle_2);
		Point point_ = default(Point);
		Class607.B630A78B.object_0[759](ref point_, 0, 0);
		Class607.B630A78B.object_0[871](ref point_, Class607.B630A78B.object_0[801](ref rectangle_2) + (Class607.B630A78B.object_0[56](ref rectangle_2) - Class607.B630A78B.object_0[801](ref rectangle_2)) / 2);
		Class607.B630A78B.object_0[819](ref point_, Class607.B630A78B.object_0[1263](ref rectangle_2) + (Class607.B630A78B.object_0[970](ref rectangle_2) - Class607.B630A78B.object_0[1263](ref rectangle_2)) / 2);
		Point point_2 = default(Point);
		Class607.B630A78B.object_0[759](ref point_2, 0, 0);
		Class607.B630A78B.object_0[871](ref point_2, Class607.B630A78B.object_0[372](ref point_) - num / 2);
		Class607.B630A78B.object_0[819](ref point_2, Class607.B630A78B.object_0[1229](ref point_) - num2 / 2);
		C8AED007.FA14CA95(ref point_2, (Class607.B630A78B.object_0[372](ref point_2) >= 0) ? Class607.B630A78B.object_0[372](ref point_2) : 0);
		B9BF6A16.A8A6F89E(ref point_2, (Class607.B630A78B.object_0[1229](ref point_2) >= 0) ? Class607.B630A78B.object_0[1229](ref point_2) : 0);
		MoveWindow(intptr_1, Class607.B630A78B.object_0[372](ref point_2), Class607.B630A78B.object_0[1229](ref point_2), num, num2, bool_0: false);
	}

	public GClass110()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
