using System;

public static class ED8E1F0E
{
	private static uint[] uint_0;

	private static ulong C10F3A09(ulong CA8FCD28)
	{
		uint num = (uint)(int)CA8FCD28 + uint_0[0];
		byte b = 1;
		b = 188;
		ushort num5 = default(ushort);
		short num4 = default(short);
		while (true)
		{
			IL_0115:
			b -= 187;
			uint num2 = (uint)(int)(CA8FCD28 >> (842002051 << (int)b) + b + -1684004071) + uint_0[0x29374C00 ^ ((uint)(691489793 * b) | ((uint)(b / b) % (uint)(short)b))];
			int num3 = (int)(((1545287689 / ((uint)b % 1396500657u) * b) ^ (uint)((int)(2871393026u % (uint)b) % (int)b)) - 1545287688);
			if (((-675795148 >> (int)b << 1572592659 - b) & (b >>> (-1768377336 >> (int)b))) >> (int)b == 0)
			{
				goto IL_0063;
			}
			goto IL_00e0;
			IL_00e0:
			num4 = (short)((((14681391 == b) ? 1 : 0) + (int)((((uint)b < 3114655893u) ? 1u : 0u) ^ 0x3FBCB59Au)) | -b);
			b = (byte)((-712349925 - b) ^ -712349784);
			goto IL_0063;
			IL_0063:
			while (true)
			{
				switch ((uint)b % 4u)
				{
				case 2u:
					break;
				case 1u:
					goto end_IL_0063;
				default:
					goto IL_0115;
				case 3u:
					b = (byte)(((short)(-1878675050 | num4) | num4) - -2);
					return ((ulong)num2 << (int)(((867163066u > (uint)((0x492D5B95 | (b % ~num5)) >>> 16)) ? 1u : 0u) ^ 0x21u)) | num;
				}
				b = (byte)((-211167675 >>> (int)b) + -15577);
				if (num3 > b / -560150512 / num4 - -15)
				{
					num5 = (ushort)(3895258555u % (uint)b * b);
					b = (byte)(171 + (num5 >>> (-904993891 & b)));
					continue;
				}
				num ^= num2;
				num5 = 51354;
				num = ((num << (int)num2) | (num >> (int)(32 - num2))) + uint_0[2 * num3];
				num4 = 0;
				num2 ^= num;
				num5 = 0;
				num2 = ((num2 << (int)num) | (num2 >> (int)(32 - num))) + uint_0[2 * num3 + 1];
				num4 = 32005;
				num3++;
				b = 1;
				break;
				continue;
				end_IL_0063:
				break;
			}
			goto IL_00e0;
		}
	}

	public unsafe static byte[] CB9F3737(IntPtr EE184020, int int_0)
	{
		byte* ptr = (byte*)EE184020.ToPointer();
		byte[] array = new byte[int_0];
		ushort num = 0;
		int num2 = 0;
		int num5 = default(int);
		ulong num4 = default(ulong);
		uint num3 = default(uint);
		int num6 = default(int);
		while (true)
		{
			if (775114533u % (uint)(~(num >> (int)num)) != 0)
			{
				num = (ushort)((((uint)num % (uint)(~(-500043993 % ~num))) ^ (uint)(((num ^ 0x222259A) + -1522627687) * (num >>> 26))) + 63538);
				goto IL_01fe;
			}
			goto IL_029e;
			IL_01fe:
			while (true)
			{
				switch ((uint)num % 9u)
				{
				case 7u:
					break;
				case 5u:
					num = (ushort)(21729073 + (-21695201 - num));
					if (num5 < (0x5887A52D ^ (0x5887A525 | (-603832648 / (-1445375745 >> (1027325984 >>> (int)num))))))
					{
						goto IL_0113;
					}
					if ((num % num >> 4) / 137919932 != -(num << ((num % -1884585051) ^ num)))
					{
						num = (ushort)(0xEB0505 ^ -((-476494676 - (int)(1781041810u % (uint)num)) % (int)((uint)(num * num) % 461573818u)));
						continue;
					}
					goto IL_0135;
				case 4u:
					num = (ushort)(4833 + 1465382652 / num);
					goto IL_0135;
				case 3u:
					num = (ushort)((0x5030F893 & num) - 6635);
					num4 = F90AE223(num4);
					num5 = (0x123AF58B & num) - 29827;
					num = (ushort)(-686622694 ^ (732832816 << num / -1071189202 + num));
					continue;
				case 2u:
					goto IL_01ac;
				case 1u:
					goto IL_0251;
				default:
					goto IL_0297;
				case 6u:
					goto end_IL_01fe;
				case 8u:
					{
						num = (ushort)(-((int)((num3 + num) ^ num3) * ((int)num3 + (-349361770 ^ num))) - 559094593);
						return array;
					}
					IL_0113:
					num3 = 0u;
					array[num2 + num5] = (byte)(num4 >> 8 * num5);
					num3 = 178782208u;
					num5++;
					num = 30375;
					goto IL_0135;
					IL_0135:
					if ((int)((uint)num % (uint)num) <= 1843916388)
					{
						num = (ushort)(~(-num) ^ 0x7B0F);
						continue;
					}
					goto IL_0113;
				}
				num = (ushort)((1193440476 + num) ^ 0x47236D0E);
				if (num2 >= int_0)
				{
					num3 = (uint)(num / ~num);
					num = (ushort)(890931593 / ~((int)num3 % ~num * 528379668 % -871432699) - -890931988);
					continue;
				}
				num = ushort.MaxValue;
				num4 = 0uL;
				num6 = 0;
				num = 60426;
				goto IL_0297;
				IL_01ac:
				num = (ushort)(((num < (0xFEAEAA3Eu & (2861236237u / ((2047014798 > num) ? 1u : 0u)))) ? 1u : 0u) - 1u);
				if (num6 >= (0xBAA0 ^ (0xBAA8 | num)))
				{
					num = (ushort)(-365201753 ^ num);
					num = (ushort)((((uint)(num + 22798) > (uint)(~(short)num)) ? 1u : 0u) - 4294928710u);
					continue;
				}
				num4 |= (ulong)ptr[num2 + num6] << 8 * num6;
				num = 54167;
				num = 11143;
				goto IL_0251;
				IL_0251:
				num = (ushort)(-1441507441 * num - 460217810);
				num6 += -54166 + num;
				num += 11368;
				goto IL_029e;
				IL_0297:
				num = ushort.MaxValue;
				goto IL_029e;
				continue;
				end_IL_01fe:
				break;
			}
			num = (ushort)((0x4FC50478 ^ ((num + 538224437) & ~num) ^ (405250463 - num - num)) + -2012625706);
			goto IL_0302;
			IL_029e:
			num = (ushort)(((num >>> (num >>> (int)num)) & -299193414) >> (int)(num | ((uint)(num / num) / (uint)(-num))));
			if (num % ~(num >> (int)num) == ~num)
			{
				goto IL_0302;
			}
			num = (ushort)(((0x47A01035 ^ num) >>> ((num / ~(num >> (int)num)) | 0x721B)) - -36732);
			goto IL_01fe;
			IL_0302:
			num2 += (int)(0 - ((uint)(num * num) & (((uint)(num % -1028448465) < 2073237421u) ? 1u : 0u))) - -9;
			num = (ushort)(num + -30375);
		}
	}

	private static ulong F90AE223(ulong C12D5DAD)
	{
		uint num = (uint)C12D5DAD;
		uint num2 = (uint)(C12D5DAD >> 32);
		int num3 = -1710697344;
		int num4 = 15;
		num3 = 187839882;
		byte b = default(byte);
		while (true)
		{
			num3 = (int)((uint)(((num3 >> 29) / num3) | 0x3C8EB688) % 790454170u) ^ -1753644434;
			while (true)
			{
				num3 >>>= ~(num3 % 1342795288);
				if (-651005934 / (-241382855 >> num3 % num3) == 0)
				{
					continue;
				}
				while (true)
				{
					switch ((uint)num3 % 6u)
					{
					case 5u:
						if (num4 > (int)((((uint)num3 % 3433881622u % 1933050940) ^ (uint)num3 ^ (uint)(-1925824211 % num3 - -556421042)) - 548609686))
						{
							num3 = 1864382233;
							goto case 1u;
						}
						return ((ulong)(num2 - uint_0[0x2D3B9BDE ^ (779070856 - num3)]) << ((4795 + num3) ^ 0x1342444)) | (num - uint_0[-920994002 ^ (-(574332810 << num3) ^ (-1887282898 - (19321859 << num3)))]);
					case 1u:
						num2 -= uint_0[(0x201429A2 ^ ~((sbyte)(num3 >>> num3) * num3)) * num4 + ((370498456 + (num3 + 1082744486) % -1489024091) ^ -977342122)];
						num2 = ((num2 >> (int)(num & (uint)((num3 ^ 0xDF02BD3) - 1657805995))) | (num2 << (((0x65C260 ^ ((-1707229000 >> (437037837 << num3 % -307880009)) / ~(num3 % num3))) - (int)num) & (0x6F203706 ^ num3)))) ^ num;
						num3 = ~(~(0x5ABCB408 ^ num3) << 30) % (sbyte)((uint)num3 % uint.MaxValue);
						num3 = (num3 << -80224079 / num3 << (int)(3423634080u % (uint)(num3 << 16) / 4294936965u)) - -2115020568;
						continue;
					case 2u:
						num3 = (int)(16 + (uint)(num3 | num3 | (num3 | 0x2AADEC0D) | -num3) / 655653888u);
						num -= uint_0[(int)(3 ^ (1u % (uint)(~(-628089671 % num3)))) * num4];
						b = (byte)num3;
						num3 = (num3 - 1006491383) ^ -912133340;
						continue;
					case 3u:
						num3 = 22;
						num = ((num >> (int)num2) | (num << (((0xA9 ^ (b - -115)) - (int)num2) & ((b >>> (int)b) - -31)))) ^ num2;
						b = (byte)(0 << (int)b);
						goto end_IL_014b;
					case 4u:
						goto end_IL_014b;
					}
					goto end_IL_01bd;
					continue;
					end_IL_014b:
					break;
				}
				num4 -= (num3 >>> (int)b >> (139576700 + b >> 15)) - 1;
				num3 ^= -1710697322;
				continue;
				end_IL_01bd:
				break;
			}
		}
	}

	public static byte[] smethod_0(byte[] byte_0)
	{
		int num = -1024756896;
		uint num3 = default(uint);
		ulong num4 = default(ulong);
		sbyte b = default(sbyte);
		int num5 = default(int);
		int num6 = default(int);
		byte[] array = default(byte[]);
		while (true)
		{
			IL_023a:
			int num2 = byte_0.Length;
			if ((((int)(3649844641u / (uint)num) >> 25 << 8) | (sbyte)num) != 0)
			{
				num = -1733132882 ^ (int)((uint)num / 587912094u);
				goto IL_00e7;
			}
			goto IL_0203;
			IL_01a2:
			num3 = 0xD7260019u | num3;
			if (3022002060u / (808194236 - num3 >> (int)num3) != 0)
			{
				num4 = F90AE223(num4);
				b = (sbyte)(num3 % (num3 & num3) - (num3 & ~num3));
				num5 = (int)(0x2B06A037 | (0 - num3)) + -735491831;
			}
			goto IL_0203;
			IL_0203:
			num = 1180214023 << (int)(num3 << (int)b) >>> (int)(num3 | (uint)((int)(num3 << 18) / ~b));
			goto IL_00e7;
			IL_00e7:
			while (true)
			{
				switch ((uint)num % 5u)
				{
				case 3u:
					num = 0x28D016C3 ^ (((int)b - (int)(num3 / 3936337843u)) ^ (b & (b % -1534583665)));
					num6 += (int)((num3 % 2838142225u >> (int)num3) + 7);
					num -= 1709482851;
					goto IL_0043;
				case 2u:
					break;
				case 1u:
					num = -1024756896 + 1007114910 / (num | 0);
					array = new byte[num2];
					num6 = (int)(0x5F89F1 ^ ((uint)(num * 337427245) / (uint)((num ^ -1103253759) / (-1365598827 % num) >>> 24)));
					goto IL_0043;
				case 4u:
					goto end_IL_00e7;
				default:
					goto IL_023a;
					IL_0043:
					num = 0x3F02 ^ num;
					num = ((1312166672 << (1301195145 >>> num) - 1948239613) - -1004834396) ^ -1632204175;
					continue;
				}
				if (num5 >= (int)((((int)((uint)(num * -1640329061) % (uint)(~b)) < (int)num3 / num) ? 1u : 0u) / (uint)(~(~(~num / num)))) - -8)
				{
					num = (int)(0 - num3);
					num = (int)((uint)b / 11667u) ^ -150365288;
					continue;
				}
				goto IL_01e5;
				continue;
				end_IL_00e7:
				break;
			}
			num = (int)(((num == 26264) ? 1u : 0u) & (3951488407u / (uint)(num >> num) >> ~(0x123C673C & num))) ^ -1024766878;
			if (num6 >= num2)
			{
				break;
			}
			num4 = 0uL;
			num3 = 2877733u;
			int num7 = 0;
			while (-1045358155 * (int)(~num3 | (uint)((int)(num3 / num3) / 1862765500)) != (int)((num3 * (531898395 * (num3 << (int)num3))) | 0xF31CFEB6u))
			{
				if (num7 < (int)(0xD93DA0AEu ^ ~(num3 * num3)))
				{
					num4 |= (ulong)byte_0[num6 + num7] << 8 * num7;
					num7++;
					num3 = 2877733u;
					continue;
				}
				goto IL_01a2;
			}
			goto IL_01e5;
			IL_01e5:
			array[num6 + num5] = (byte)(num4 >> 8 * num5);
			num5++;
			b = 0;
			num3 = 3610241341u;
			goto IL_0203;
		}
		return array;
	}

	public static byte[] smethod_1(byte[] byte_0)
	{
		ushort num = 2048;
		num = 61060;
		num = 2048;
		int num2 = byte_0.Length;
		num = 9632;
		num = 1571;
		int num4 = default(int);
		byte b = default(byte);
		int num5 = default(int);
		ulong num6 = default(ulong);
		int num7 = default(int);
		while (true)
		{
			num = (ushort)(-189014497 ^ (0x5493EAAD | (-1339405416 + num)));
			while (true)
			{
				IL_0324:
				byte[] array = new byte[num2];
				int num3 = 1 / num >>> 0;
				if ((-383921119 ^ (byte)(307540762u / (uint)num)) != 1116733824 * num)
				{
					goto IL_0277;
				}
				goto IL_0310;
				IL_0310:
				num3 += num4 + 39;
				num ^= 0xD8FA;
				goto IL_02cd;
				IL_02cd:
				b = (byte)(num * (num & num) / num);
				if ((b & -684570102) == (int)(sbyte)b - (int)((uint)(b >> (0x4C2F5408 | num)) % (uint)(-138690637 % (num * 253969440))))
				{
					goto IL_0028;
				}
				num = (ushort)((((-558778196 ^ -num) / b) ^ (-980918358 << (int)num)) - -977527305);
				goto IL_0277;
				IL_0046:
				num5 += (int)(((uint)b % 1832224189u << 10 >> -1838372409 - b * b) - 3327);
				num = 64402;
				goto IL_0071;
				IL_0028:
				if (1979794214 * (-(num << num4) >> (int)num) >= num4 + -1635055594)
				{
					goto IL_0046;
				}
				num = (ushort)((num4 & 0x4FA9778) - 83472570);
				goto IL_0277;
				IL_0071:
				num4 = num << ~num - num % -1440909388;
				num = (ushort)((uint)((-1456037470 | num4) >> 26) % (uint)(num4 >>> (int)num) - 4294909893u);
				goto IL_0277;
				IL_0277:
				while (true)
				{
					switch ((uint)num % 10u)
					{
					case 8u:
						break;
					case 6u:
						goto IL_00da;
					case 4u:
						goto IL_012f;
					case 3u:
						goto IL_0184;
					case 5u:
						goto IL_0220;
					case 2u:
						goto end_IL_0277;
					case 7u:
						goto IL_0305;
					default:
						num = (ushort)(num + -59012);
						num2 = byte_0.Length;
						num = (ushort)((num - 3242115766u / (uint)num << 20) ^ 0x9C2B25A0u);
						num = 1571;
						goto end_IL_0324;
					case 1u:
						goto end_IL_0324;
					case 9u:
						num = (ushort)(-47103 + (int)(((uint)b % (uint)num) ^ num) % -1198009214);
						return array;
					}
					num = 9632;
					if (num3 >= num2)
					{
						if ((-num << (int)(short)(2980667064u / (uint)num)) * b != 0)
						{
							num = (ushort)(0xDD3F ^ ((uint)(num % num) / 3107382169u));
							continue;
						}
						goto IL_0252;
					}
					num = 64402;
					num6 = 0uL;
					num5 = 0;
					goto IL_0071;
					IL_0220:
					num = (ushort)((int)((uint)(((1486968222u > (uint)num4) ? 1 : 0) - (num - num4)) % (uint)num) - -num4 - 25905);
					goto IL_0028;
					IL_0184:
					num -= 151;
					if (num5 < (int)((num % ~(((uint)num % (uint)num4 > (uint)(num << (int)num)) ? 1u : 0u)) ^ 0xFB9A))
					{
						goto IL_0204;
					}
					num4 = (int)(0 - (~((uint)num4 % (uint)num) >> 27));
					if ((((num4 << num4) % num > -704249727) ? 1u : 0u) << num / -1 != 0)
					{
						num = (ushort)((((uint)num4 > (uint)(1385191455 << -1892962135 / num % (sbyte)num4)) ? 1u : 0u) + 44703u);
						continue;
					}
					goto IL_0252;
					IL_0252:
					num7 += b ^ 0xD;
					num = 1134;
					num4 = -31;
					goto IL_0028;
					IL_00da:
					num = (ushort)(1166 + (((uint)num4 ^ ((uint)num4 / (uint)num4)) >> (3 << ~(num4 * num))));
					if (num7 >= (8 ^ (((num == num) ? 1 : 0) >> (int)num)))
					{
						num = (ushort)(~(4047467807u % (uint)num));
						if (num4 - 1166993458 != 0)
						{
							num = (ushort)(0x73FAA2CD ^ (num | 0x73FA1456));
							continue;
						}
						goto IL_0071;
					}
					array[num3 + num7] = (byte)(num6 >> 8 * num7);
					b = 12;
					goto IL_0252;
					IL_012f:
					num = (ushort)(64372 + (byte)(~num4));
					num6 = C10F3A09(num6);
					num = (ushort)(-num);
					num7 = (int)((((-num > num) ? 1u : 0u) | (uint)(55 + ~(-1753086973 & num4))) - 1753087029);
					if (num + -(num4 >> num4 >> num4) != 0)
					{
						num = (ushort)((0x1C0EC80F | num4) - -30502);
						continue;
					}
					goto IL_0324;
					continue;
					end_IL_0277:
					break;
				}
				goto IL_02cd;
				IL_0305:
				num = (ushort)(64889 + -(-num4));
				goto IL_0310;
				IL_0204:
				num6 |= (ulong)byte_0[num3 + num5] << 8 * num5;
				b = 26;
				goto IL_0046;
				continue;
				end_IL_0324:
				break;
			}
		}
	}

	static ED8E1F0E()
	{
		new GClass128().C5017C25(null, 340025);
	}
}
