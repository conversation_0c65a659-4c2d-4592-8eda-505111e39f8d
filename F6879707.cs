public class F6879707
{
	internal const Enum0 enum0_0 = Enum0.FileType | Enum0.NeedsRecovery | Enum0.Extents | Enum0.SixtyFourBit | Enum0.FlexBlockGroups;

	private A8AAAD35 B99CEF04;

	private F7020D24 f7020D24_0;

	private GClass37.B6BA8237 A5A2C590;

	private ulong ulong_0;

	private Class0[] class0_0;

	public F6879707(F7020D24 f7020D24_1, A8AAAD35 A027479C, GClass37.B6BA8237 b6BA8237_0, ulong ulong_1)
	{
		Class607.B630A78B.object_0[571](this);
		B99CEF04 = A027479C;
		f7020D24_0 = f7020D24_1;
		A5A2C590 = b6BA8237_0;
		ulong_0 = A5A2C590.D78F1C10 * ulong_1;
	}

	public void method_0()
	{
		ulong num = 1024uL;
		byte[] bF101B2F = B32FEB02(1024uL, 1024uL);
		EBBF449F eBBF449F = new EBBF449F();
		eBBF449F.B0B6FA3F(bF101B2F, 0);
		if (eBBF449F.C8340584 != 61267)
		{
			throw Class607.B630A78B.object_0[1178]("Invalid superblock magic - probably not an Ext file system");
		}
		if (eBBF449F.uint_23 == 0)
		{
			throw Class607.B630A78B.object_0[1178]("Old ext revision - not supported");
		}
		if ((eBBF449F.A381CC37 & ~(Enum0.FileType | Enum0.NeedsRecovery | Enum0.Extents | Enum0.SixtyFourBit | Enum0.FlexBlockGroups)) != 0)
		{
			throw Class607.B630A78B.object_0[1178](Class607.B630A78B.object_0[720]("Incompatible ext features present: ", (eBBF449F.A381CC37 & ~(Enum0.FileType | Enum0.NeedsRecovery | Enum0.Extents | Enum0.SixtyFourBit | Enum0.FlexBlockGroups)).ToString()));
		}
		uint num2 = E73C2A8D.smethod_3(eBBF449F.uint_1, eBBF449F.D2B2518C);
		long long_ = (long)(eBBF449F.uint_4 + 1) * (long)eBBF449F.DB3035A7;
		num = Class607.B630A78B.object_0[243](long_);
		int num3 = (eBBF449F.Boolean_0 ? eBBF449F.ushort_2 : 32);
		byte[] bF101B2F2 = B32FEB02(num, Class607.B630A78B.object_0[243](num2 * num3));
		class0_0 = new Class0[num2];
		for (int i = 0; i < num2; i++)
		{
			Class0 @class = (eBBF449F.Boolean_0 ? new ED1C1F34(num3) : new Class0());
			@class.B0B6FA3F(bF101B2F2, i * num3);
			class0_0[i] = @class;
		}
		new Class5();
	}

	public byte[] B32FEB02(ulong D7B76791, ulong ulong_1)
	{
		GClass51.GStruct57 gStruct = B99CEF04(ulong_0 + D7B76791, ulong_1, "", "user");
		if (!gStruct.BD147F02)
		{
			throw Class607.B630A78B.object_0[778]("Failed to read data from device");
		}
		return gStruct.byte_0;
	}

	private Class4 D72CF627(uint AB05DF95, EBBF449F ebbf449F_0)
	{
		uint num = AB05DF95 - 1;
		uint num2 = num / ebbf449F_0.EA825A24;
		uint num3 = num - num2 * ebbf449F_0.EA825A24;
		Class0 @class = method_1(num2);
		uint num4 = ebbf449F_0.DB3035A7 / ebbf449F_0.ushort_4;
		uint num5 = num3 / num4;
		uint num6 = num3 - num5 * num4;
		ulong d7B = Class607.B630A78B.object_0[243]((long)(@class.uint_2 + num5) * (long)ebbf449F_0.DB3035A7 + num6 * ebbf449F_0.ushort_4);
		byte[] byte_ = B32FEB02(d7B, ebbf449F_0.ushort_4);
		return GClass3.smethod_20<Class4>(byte_, 0);
	}

	private Class0 method_1(uint uint_0)
	{
		return class0_0[uint_0];
	}
}
