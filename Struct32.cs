using System;

internal struct Struct32
{
	private int A73D0E2F;

	private uint uint_0;

	private uint[] uint_1;

	private bool D40760A3;

	private void method_0(Struct32 struct32_0, int int_0)
	{
		if (struct32_0.A73D0E2F == 0)
		{
			uint_0 = struct32_0.uint_0;
			A73D0E2F = 0;
			return;
		}
		if (!D40760A3 || uint_1.Length <= struct32_0.A73D0E2F)
		{
			uint_1 = new uint[struct32_0.A73D0E2F + 1 + int_0];
			D40760A3 = true;
		}
		A73D0E2F = struct32_0.A73D0E2F;
		Array.Copy(struct32_0.uint_1, uint_1, A73D0E2F + 1);
	}

	private void method_1(int int_0, out int C79B4AA1, out uint[] uint_2)
	{
		if (A73D0E2F == 0)
		{
			if (uint_0 <= int.MaxValue)
			{
				C79B4AA1 = int_0 * (int)uint_0;
				uint_2 = null;
				return;
			}
			if (uint_1 == null)
			{
				uint_1 = new uint[1] { uint_0 };
			}
			else if (D40760A3)
			{
				uint_1[0] = uint_0;
			}
			else if (uint_1[0] != uint_0)
			{
				uint_1 = new uint[1] { uint_0 };
			}
		}
		C79B4AA1 = int_0;
		int num = uint_1.Length - A73D0E2F - 1;
		if (num <= 1)
		{
			if (num == 0 || uint_1[A73D0E2F + 1] == 0)
			{
				D40760A3 = false;
				uint_2 = uint_1;
				return;
			}
			if (D40760A3)
			{
				uint_1[A73D0E2F + 1] = 0u;
				D40760A3 = false;
				uint_2 = uint_1;
				return;
			}
		}
		uint_2 = uint_1;
		Array.Resize(ref uint_2, A73D0E2F + 1);
		if (!D40760A3)
		{
			uint_1 = uint_2;
		}
	}

	private void BB1DBC87(uint B0103EA9)
	{
		if (B0103EA9 == 1)
		{
			method_6(0u);
			return;
		}
		if (A73D0E2F == 0)
		{
			method_6(uint_0 % B0103EA9);
			return;
		}
		ulong num = 0uL;
		for (int num2 = A73D0E2F; num2 >= 0; num2--)
		{
			num = (num << 32) | uint_1[num2];
			num %= B0103EA9;
		}
		method_6((uint)num);
	}

	private void E2B8A181(ulong ulong_0)
	{
		uint num = (uint)(ulong_0 >> 32);
		if (num == 0)
		{
			uint_0 = (uint)ulong_0;
			A73D0E2F = 0;
		}
		else
		{
			method_3(2);
			uint_1[0] = (uint)ulong_0;
			uint_1[1] = num;
		}
	}

	internal static int F7387A8A(uint uint_2)
	{
		if (uint_2 == 0)
		{
			return 32;
		}
		int num = 0;
		if ((uint_2 & 0xFFFF0000u) == 0)
		{
			num += 16;
			uint_2 <<= 16;
		}
		if ((uint_2 & 0xFF000000u) == 0)
		{
			num += 8;
			uint_2 <<= 8;
		}
		if ((uint_2 & 0xF0000000u) == 0)
		{
			num += 4;
			uint_2 <<= 4;
		}
		if ((uint_2 & 0xC0000000u) == 0)
		{
			num += 2;
			uint_2 <<= 2;
		}
		if ((uint_2 & 0x80000000u) == 0)
		{
			num++;
		}
		return num;
	}

	private void method_2(uint uint_2)
	{
		switch (uint_2)
		{
		case 0u:
			method_6(0u);
			return;
		case 1u:
			return;
		}
		if (A73D0E2F == 0)
		{
			E2B8A181((ulong)uint_0 * (ulong)uint_2);
			return;
		}
		method_9(1);
		uint num = 0u;
		for (int i = 0; i <= A73D0E2F; i++)
		{
			ulong num2 = (ulong)((long)uint_1[i] * (long)uint_2 + num);
			uint_1[i] = (uint)num2;
			num = (uint)(num2 >> 32);
		}
		if (num != 0)
		{
			AE8488B7(A73D0E2F + 2, 0);
			uint_1[A73D0E2F] = num;
		}
	}

	private void method_3(int int_0)
	{
		if (int_0 <= 1)
		{
			A73D0E2F = 0;
			return;
		}
		if (!D40760A3 || uint_1.Length < int_0)
		{
			uint_1 = new uint[int_0];
			D40760A3 = true;
		}
		A73D0E2F = int_0 - 1;
	}

	private int FD9AD41F()
	{
		int num = 0;
		for (int num2 = A73D0E2F; num2 >= 0; num2--)
		{
			if (uint_1[num2] != 0)
			{
				num++;
			}
		}
		return num;
	}

	internal Struct14 method_4(int int_0)
	{
		method_1(int_0, out int_0, out var uint_);
		return new Struct14(int_0, uint_);
	}

	internal int method_5()
	{
		return A73D0E2F + 1;
	}

	private void method_6(uint uint_2)
	{
		uint_0 = uint_2;
		A73D0E2F = 0;
	}

	private void AE8488B7(int int_0, int A2368A31)
	{
		if (int_0 <= 1)
		{
			if (A73D0E2F > 0)
			{
				uint_0 = uint_1[0];
			}
			A73D0E2F = 0;
			return;
		}
		if (D40760A3 && uint_1.Length >= int_0)
		{
			if (A73D0E2F + 1 < int_0)
			{
				Array.Clear(uint_1, A73D0E2F + 1, int_0 - A73D0E2F - 1);
				if (A73D0E2F == 0)
				{
					uint_1[0] = uint_0;
				}
			}
		}
		else
		{
			uint[] array = new uint[int_0 + A2368A31];
			if (A73D0E2F == 0)
			{
				array[0] = uint_0;
			}
			else
			{
				Array.Copy(uint_1, array, Math.Min(int_0, A73D0E2F + 1));
			}
			uint_1 = array;
			D40760A3 = true;
		}
		A73D0E2F = int_0 - 1;
	}

	internal Struct32(int int_0, uint[] uint_2)
	{
		D40760A3 = false;
		uint_1 = uint_2;
		int num = int_0 >> 31;
		if (uint_1 == null)
		{
			A73D0E2F = 0;
			uint_0 = (uint)((int_0 ^ num) - num);
			return;
		}
		A73D0E2F = uint_1.Length - 1;
		uint_0 = uint_1[0];
		while (A73D0E2F > 0 && uint_1[A73D0E2F] == 0)
		{
			A73D0E2F--;
		}
	}

	private void CDBB0F05()
	{
		if (A73D0E2F > 0 && uint_1[A73D0E2F] == 0)
		{
			uint_0 = uint_1[0];
			while (--A73D0E2F > 0 && uint_1[A73D0E2F] == 0)
			{
			}
		}
	}

	internal void method_7(Struct32 struct32_0, Struct32 C909129A)
	{
		if (struct32_0.A73D0E2F == 0)
		{
			if (C909129A.A73D0E2F == 0)
			{
				E2B8A181((ulong)struct32_0.uint_0 * (ulong)C909129A.uint_0);
				return;
			}
			method_0(C909129A, 1);
			method_2(struct32_0.uint_0);
			return;
		}
		if (C909129A.A73D0E2F == 0)
		{
			method_0(struct32_0, 1);
			method_2(C909129A.uint_0);
			return;
		}
		method_8(struct32_0.A73D0E2F + C909129A.A73D0E2F + 2);
		uint[] array;
		int num;
		uint[] array2;
		int num2;
		if (struct32_0.FD9AD41F() <= C909129A.FD9AD41F())
		{
			array = struct32_0.uint_1;
			num = struct32_0.A73D0E2F + 1;
			array2 = C909129A.uint_1;
			num2 = C909129A.A73D0E2F + 1;
		}
		else
		{
			array = C909129A.uint_1;
			num = C909129A.A73D0E2F + 1;
			array2 = struct32_0.uint_1;
			num2 = struct32_0.A73D0E2F + 1;
		}
		for (int i = 0; i < num; i++)
		{
			uint num3 = array[i];
			if (num3 != 0)
			{
				uint num4 = 0u;
				int num5 = i;
				int num6 = 0;
				while (num6 < num2)
				{
					ulong num7 = (ulong)(uint_1[num5] + (long)num3 * (long)array2[num6] + num4);
					uint_1[num5] = (uint)num7;
					num4 = (uint)(num7 >> 32);
					num6++;
					num5++;
				}
				while (num4 != 0)
				{
					ulong num8 = (ulong)uint_1[num5] + (ulong)num4;
					uint_1[num5++] = (uint)num8;
					num4 = (uint)(num8 >> 32);
				}
			}
		}
		CDBB0F05();
	}

	private void method_8(int DD097495)
	{
		if (DD097495 <= 1)
		{
			A73D0E2F = 0;
			uint_0 = 0u;
			return;
		}
		if (D40760A3 && uint_1.Length >= DD097495)
		{
			Array.Clear(uint_1, 0, DD097495);
		}
		else
		{
			uint_1 = new uint[DD097495];
			D40760A3 = true;
		}
		A73D0E2F = DD097495 - 1;
	}

	private void method_9(int D13BDB8A = 0)
	{
		if (!D40760A3)
		{
			uint[] destinationArray = new uint[A73D0E2F + 1 + D13BDB8A];
			Array.Copy(uint_1, destinationArray, A73D0E2F + 1);
			uint_1 = destinationArray;
			D40760A3 = true;
		}
	}

	internal void A905BE24(Struct32 EA935710)
	{
		if (EA935710.A73D0E2F == 0)
		{
			BB1DBC87(EA935710.uint_0);
		}
		else
		{
			if (A73D0E2F == 0 || A73D0E2F < EA935710.A73D0E2F)
			{
				return;
			}
			int num = EA935710.A73D0E2F + 1;
			int num2 = A73D0E2F - EA935710.A73D0E2F;
			int num3 = num2;
			int num4 = A73D0E2F;
			while (true)
			{
				if (num4 >= num2)
				{
					if (EA935710.uint_1[num4 - num2] == uint_1[num4])
					{
						num4--;
						continue;
					}
					if (EA935710.uint_1[num4 - num2] < uint_1[num4])
					{
						num3++;
					}
					break;
				}
				num3++;
				break;
			}
			if (num3 == 0)
			{
				return;
			}
			uint num5 = EA935710.uint_1[num - 1];
			uint num6 = EA935710.uint_1[num - 2];
			int num7 = F7387A8A(num5);
			int num8 = 32 - num7;
			if (num7 > 0)
			{
				num5 = (num5 << num7) | (num6 >> num8);
				num6 <<= num7;
				if (num > 2)
				{
					num6 |= EA935710.uint_1[num - 3] >> num8;
				}
			}
			method_9();
			int num9 = num3;
			while (--num9 >= 0)
			{
				uint num10 = ((num9 + num <= A73D0E2F) ? uint_1[num9 + num] : 0u);
				ulong num11 = ((ulong)num10 << 32) | uint_1[num9 + num - 1];
				uint num12 = uint_1[num9 + num - 2];
				if (num7 > 0)
				{
					num11 = (num11 << num7) | (num12 >> num8);
					num12 <<= num7;
					if (num9 + num >= 3)
					{
						num12 |= uint_1[num9 + num - 3] >> num8;
					}
				}
				ulong num13 = num11 / num5;
				ulong num14 = (uint)(num11 % num5);
				if (num13 > 4294967295L)
				{
					num14 += num5 * (num13 - 4294967295L);
					num13 = 4294967295uL;
				}
				for (; num14 <= 4294967295L && num13 * num6 > ((num14 << 32) | num12); num14 += num5)
				{
					num13--;
				}
				if (num13 <= 0L)
				{
					continue;
				}
				ulong num15 = 0uL;
				for (int i = 0; i < num; i++)
				{
					num15 += EA935710.uint_1[i] * num13;
					uint num16 = (uint)num15;
					num15 >>= 32;
					if (uint_1[num9 + i] < num16)
					{
						num15++;
					}
					uint_1[num9 + i] -= num16;
				}
				if (num10 < num15)
				{
					uint num17 = 0u;
					for (int j = 0; j < num; j++)
					{
						ulong num18 = (ulong)((long)uint_1[num9 + j] + (long)EA935710.uint_1[j] + num17);
						uint_1[num9 + j] = (uint)num18;
						num17 = (uint)(num18 >> 32);
					}
				}
				A73D0E2F = num9 + num - 1;
			}
			A73D0E2F = num - 1;
			CDBB0F05();
		}
	}

	internal Struct32(int DC8CB5AD)
	{
		A73D0E2F = 0;
		uint_0 = 0u;
		if (DC8CB5AD > 1)
		{
			uint_1 = new uint[DC8CB5AD];
			D40760A3 = true;
		}
		else
		{
			uint_1 = null;
			D40760A3 = false;
		}
	}
}
