public class GClass120
{
	private string string_0;

	private GEnum39 genum39_0;

	private string B1ACB43C;

	private string A626DD97;

	private GStruct96 gstruct96_0;

	private GStruct98 gstruct98_0;

	private string string_1;

	public string String_0
	{
		get
		{
			return string_1;
		}
		set
		{
			string_1 = value;
		}
	}

	public GEnum39 E903E03A
	{
		get
		{
			return genum39_0;
		}
		set
		{
			genum39_0 = value;
		}
	}

	public string String_1
	{
		get
		{
			return A626DD97;
		}
		set
		{
			A626DD97 = value;
		}
	}

	public string String_2
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public string BF390303
	{
		get
		{
			return B1ACB43C;
		}
		set
		{
			B1ACB43C = value;
		}
	}

	public GStruct98 GStruct98_0
	{
		get
		{
			return gstruct98_0;
		}
		set
		{
			gstruct98_0 = value;
		}
	}

	public GStruct96 GStruct96_0
	{
		get
		{
			return gstruct96_0;
		}
		set
		{
			gstruct96_0 = value;
		}
	}

	public GClass120()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
