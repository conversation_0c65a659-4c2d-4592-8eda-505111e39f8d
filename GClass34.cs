using System;
using System.Collections.Generic;

public class GClass34
{
	public enum AB881C92
	{
		const_0 = 3,
		const_1 = 5,
		D03BC921 = 6
	}

	public struct GStruct38
	{
		public int int_0;

		public int int_1;

		public int int_2;

		public uint uint_0;

		public uint uint_1;

		public uint A2947F13;

		public uint EDB8851A;

		public uint uint_2;

		public uint AB96C4A7;

		public uint uint_3;

		public uint uint_4;

		public uint EB11D8A7;

		public uint uint_5;

		public int int_3;

		public uint F52144AA;

		public uint uint_6;

		public uint uint_7;

		public uint uint_8;

		public List<Tuple<uint, uint>> ********;

		public Tuple<uint, uint> A53A653F;

		public Tuple<uint, uint> tuple_0;

		public uint EC16A503;

		public uint AC007701;

		public AB881C92 B72DA2BE;

		public uint F1016B18;

		public string D30F1103;

		public string A1187E38;

		public byte[] CDB8E484;

		public int int_4;

		public int D4355B1E;

		public uint FF39232F;
	}

	public byte[] byte_0;

	public GStruct38 A8054FBA;

	public Dictionary<int, GStruct38> dictionary_0;

	public int int_0;

	public int int_1;

	public int EEAA3694;

	public GClass34()
	{
		Class607.B630A78B.object_0[571](this);
		byte[] array_ = new byte[4];
		DEBEDD9C.smethod_0(array_, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
		byte_0 = array_;
		dictionary_0 = new Dictionary<int, GStruct38>
		{
			{
				1393,
				new GStruct38
				{
					uint_0 = 268464128u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 1393u,
					D30F1103 = "MT0571"
				}
			},
			{
				1432,
				new GStruct38
				{
					uint_0 = 270602240u,
					uint_1 = 285343744u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					AB96C4A7 = 270680064u,
					uint_3 = 268476416u,
					EB11D8A7 = 270609408u,
					uint_5 = 285213088u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 1432u,
					D30F1103 = "ELBRUS/MT0598"
				}
			},
			{
				2450,
				new GStruct38
				{
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 2450u,
					D30F1103 = "MT0992"
				}
			},
			{
				9729,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285233152u,
					A2947F13 = 1051136u,
					EDB8851A = 33587200u,
					uint_2 = 2178940928u,
					uint_3 = 268476416u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(286531340u, 0u),
						Tuple.Create(286542788u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(286531404u, 47720u),
					uint_6 = 286534624u,
					uint_8 = 4244911u,
					tuple_0 = Tuple.Create(4242760u, 4243196u),
					EC16A503 = 286534708u,
					F1016B18 = 9729u,
					B72DA2BE = AB881C92.const_0,
					D30F1103 = "MT2601",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt2601_payload.bin"))
				}
			},
			{
				14695,
				new GStruct38
				{
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1073872896u,
					F1016B18 = 14695u,
					B72DA2BE = AB881C92.const_0,
					D30F1103 = "MT3967"
				}
			},
			{
				25173,
				new GStruct38
				{
					uint_3 = 2148794368u,
					B72DA2BE = AB881C92.const_0,
					D30F1103 = "MT6255"
				}
			},
			{
				25185,
				new GStruct38
				{
					int_2 = 40,
					uint_0 = 2684551168u,
					uint_1 = 2684878848u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_3 = 2685468672u,
					******** = new List<Tuple<uint, uint>> { Tuple.Create(3758357635u, 0u) },
					A53A653F = Tuple.Create(1879065776u, 1879070956u),
					uint_6 = 1879065000u,
					uint_8 = 1879073270u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 25185u,
					D30F1103 = "MT6261",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6261_payload.bin"))
				}
			},
			{
				25216,
				new GStruct38
				{
					uint_3 = 2148007936u,
					B72DA2BE = AB881C92.const_0,
					D30F1103 = "MT6280"
				}
			},
			{
				25878,
				new GStruct38
				{
					uint_0 = 268447744u,
					uint_1 = 268578816u,
					EDB8851A = 2101248u,
					uint_3 = 268619776u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 25878u,
					D30F1103 = "MT6516"
				}
			},
			{
				1587,
				new GStruct38
				{
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 2147487744u,
					AB96C4A7 = 270585856u,
					uint_3 = 268476416u,
					EB11D8A7 = 270576640u,
					uint_5 = 285213088u,
					int_3 = 268472320,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 25968u,
					D30F1103 = "MT6570/MT8321"
				}
			},
			{
				25969,
				new GStruct38
				{
					uint_0 = 268465152u,
					EDB8851A = 33591296u,
					uint_2 = 2147487744u,
					uint_7 = 268440604u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 25969u,
					D30F1103 = "MT6571"
				}
			},
			{
				25970,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285233152u,
					A2947F13 = 16791200u,
					EDB8851A = 33587200u,
					uint_2 = 2178940928u,
					uint_5 = 285213084u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(286531340u, 0u),
						Tuple.Create(286542788u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(286531404u, 4242024u),
					uint_6 = 286534624u,
					uint_8 = 4244911u,
					tuple_0 = Tuple.Create(4242760u, 4243196u),
					EC16A503 = 286534708u,
					uint_7 = 268440604u,
					int_3 = 268472320,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 25970u,
					D30F1103 = "MT6572",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6572_payload.bin"))
				}
			},
			{
				25971,
				new GStruct38
				{
					uint_0 = 1879199744u,
					EDB8851A = 2415943680u,
					uint_2 = 4043440128u,
					uint_3 = 1879220224u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 25971u,
					D30F1103 = "MT6573/MT6260"
				}
			},
			{
				25973,
				new GStruct38
				{
					uint_0 = 3221225472u,
					uint_1 = 3238039552u,
					EDB8851A = 3254784000u,
					uint_2 = 3255140352u,
					uint_3 = 3238109184u,
					uint_5 = 3238007196u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 25970u,
					D30F1103 = "MT6575/77"
				}
			},
			{
				25975,
				new GStruct38
				{
					uint_0 = 3221225472u,
					uint_1 = 3238039552u,
					EDB8851A = 3254784000u,
					uint_2 = 3255140352u,
					uint_3 = 3238109184u,
					uint_5 = 3238007196u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 25975u,
					D30F1103 = "MT6577"
				}
			},
			{
				25984,
				new GStruct38
				{
					int_2 = 172,
					uint_0 = 268464128u,
					uint_1 = 285233152u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 2147487744u,
					uint_3 = 268476416u,
					EB11D8A7 = 270576640u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058660u, 0u),
						Tuple.Create(1077716u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058724u, 46604u),
					uint_6 = 1060960u,
					uint_8 = 49427u,
					tuple_0 = Tuple.Create(47328u, 47764u),
					int_3 = 270557184,
					uint_7 = 268441656u,
					EC16A503 = 1061044u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 25984u,
					D30F1103 = "MT6580",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6580_payload.bin"))
				}
			},
			{
				25986,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 2147487744u,
					AB96C4A7 = 285323264u,
					uint_3 = 268476416u,
					uint_5 = 285213472u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058696u, 0u),
						Tuple.Create(1072100u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058760u, 42492u),
					uint_6 = 1060984u,
					uint_8 = 45799u,
					tuple_0 = Tuple.Create(43216u, 43652u),
					int_3 = 270557184,
					EC16A503 = 1061068u,
					uint_7 = 268443728u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 25986u,
					D30F1103 = "MT6582/MT6574/MT8382",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6582_payload.bin"))
				}
			},
			{
				25987,
				new GStruct38
				{
					uint_0 = 268435456u,
					uint_1 = 285237248u,
					A2947F13 = 1051136u,
					EDB8851A = 301993984u,
					uint_2 = 2147487744u,
					AB96C4A7 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270606336u,
					uint_5 = 285213472u,
					uint_7 = 268443728u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 25993u,
					D30F1103 = "MT6583/6589"
				}
			},
			{
				26002,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 1118208u,
					uint_2 = 2147487744u,
					AB96C4A7 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270606336u,
					uint_5 = 285213472u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058660u, 0u),
						Tuple.Create(1072112u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058724u, 42340u),
					uint_6 = 1060948u,
					uint_8 = 45215u,
					tuple_0 = Tuple.Create(43064u, 43500u),
					EC16A503 = 1061032u,
					uint_7 = 268443728u,
					int_3 = 270557184,
					F1016B18 = 26002u,
					B72DA2BE = AB881C92.const_0,
					D30F1103 = "MT6592/MT8392",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6592_payload.bin"))
				}
			},
			{
				26005,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 1118208u,
					uint_3 = 268476416u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058664u, 0u),
						Tuple.Create(1076360u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058728u, 45592u),
					uint_6 = 1060944u,
					uint_8 = 48467u,
					tuple_0 = Tuple.Create(46316u, 46752u),
					EC16A503 = 1061028u,
					int_3 = 270557184,
					F1016B18 = 26005u,
					B72DA2BE = AB881C92.const_0,
					D30F1103 = "MT6595",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6595_payload.bin"))
				}
			},
			{
				801,
				new GStruct38
				{
					int_2 = 40,
					uint_0 = 270606336u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 270622720u,
					uint_3 = 268468224u,
					EB11D8A7 = 270629888u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058656u, 0u),
						Tuple.Create(1070852u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058720u, 38392u),
					uint_6 = 1060956u,
					uint_8 = 41343u,
					tuple_0 = Tuple.Create(39116u, 39572u),
					EC16A503 = 1061040u,
					uint_7 = 268441656u,
					int_3 = 298123264,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 26421u,
					D30F1103 = "MT6735/T,MT8735A",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6735_payload.bin"))
				}
			},
			{
				821,
				new GStruct38
				{
					int_2 = 40,
					uint_0 = 270606336u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 270622720u,
					uint_3 = 268468224u,
					EB11D8A7 = 270629888u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058656u, 0u),
						Tuple.Create(1070852u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058720u, 38408u),
					uint_6 = 1060956u,
					uint_8 = 41359u,
					tuple_0 = Tuple.Create(39132u, 39588u),
					EC16A503 = 1061040u,
					int_3 = 270557184,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 26421u,
					D30F1103 = "MT6737M/MT6735G",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6737_payload.bin"))
				}
			},
			{
				1689,
				new GStruct38
				{
					int_2 = 180,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_3 = 268476416u,
					uint_4 = 270598144u,
					EB11D8A7 = 270606336u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058860u, 0u),
						Tuple.Create(1078956u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058928u, 57116u),
					uint_6 = 1059368u,
					uint_8 = 60489u,
					tuple_0 = Tuple.Create(58160u, 58344u),
					EC16A503 = 1059576u,
					AC007701 = 1059592u,
					FF39232F = 1077772u,
					uint_7 = 268542208u,
					int_3 = 297795584,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26425u,
					D30F1103 = "MT6739/MT6731/MT8765",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6739_payload.bin"))
				}
			},
			{
				1537,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270609408u,
					uint_5 = 285213088u,
					int_3 = 270557184,
					uint_7 = 268441656u,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26453u,
					D30F1103 = "MT6750"
				}
			},
			{
				26450,
				new GStruct38
				{
					int_2 = 40,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1073745920u,
					AB96C4A7 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270609408u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058660u, 0u),
						Tuple.Create(1070852u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058724u, 39180u),
					uint_6 = 1060960u,
					uint_8 = 42131u,
					tuple_0 = Tuple.Create(39904u, 40360u),
					int_3 = 270557184,
					EC16A503 = 1061044u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 26450u,
					D30F1103 = "MT6752",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6752_payload.bin"))
				}
			},
			{
				823,
				new GStruct38
				{
					int_2 = 40,
					uint_0 = 270606336u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 270622720u,
					uint_3 = 268468224u,
					EB11D8A7 = 270629888u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058656u, 0u),
						Tuple.Create(1070852u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058720u, 38504u),
					uint_6 = 1060956u,
					uint_8 = 41455u,
					tuple_0 = Tuple.Create(39228u, 39684u),
					EC16A503 = 1061040u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 26421u,
					uint_7 = 268441656u,
					D30F1103 = "MT6753",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6753_payload.bin"))
				}
			},
			{
				806,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270609408u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058668u, 0u),
						Tuple.Create(1070852u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058736u, 39532u),
					uint_6 = 1060952u,
					uint_8 = 42495u,
					tuple_0 = Tuple.Create(40268u, 40724u),
					EC16A503 = 1061036u,
					uint_7 = 268441656u,
					int_3 = 270557184,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26453u,
					D30F1103 = "MT6755/MT6750/M/T/S",
					A1187E38 = "Helio P10/P15/P18",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6755_payload.bin"))
				}
			},
			{
				1361,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270609408u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058676u, 0u),
						Tuple.Create(1070852u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058744u, 39980u),
					uint_6 = 1060960u,
					uint_8 = 43259u,
					tuple_0 = Tuple.Create(41008u, 41192u),
					EC16A503 = 1061044u,
					uint_7 = 268441656u,
					int_3 = 270557184,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26455u,
					D30F1103 = "MT6757/MT6757D",
					A1187E38 = "Helio P20",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6757_payload.bin"))
				}
			},
			{
				1672,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 270602240u,
					uint_1 = 285343744u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_3 = 268959744u,
					uint_4 = 287571968u,
					EB11D8A7 = 270532608u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058864u, 0u),
						Tuple.Create(1075808u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058932u, 55392u),
					uint_6 = 1059624u,
					uint_8 = 58765u,
					tuple_0 = Tuple.Create(56436u, 56620u),
					EC16A503 = 1059832u,
					AC007701 = 1059848u,
					int_3 = 272957440,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26456u,
					D30F1103 = "MT6758",
					A1187E38 = "Helio P30",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6758_payload.bin"))
				}
			},
			{
				1287,
				new GStruct38
				{
					uint_0 = 270598144u,
					uint_1 = 285343744u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					AB96C4A7 = 270598144u,
					uint_5 = 16974240u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 26456u,
					D30F1103 = "MT6759",
					A1187E38 = "Helio P30"
				}
			},
			{
				1815,
				new GStruct38
				{
					int_2 = 37,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_3 = 268476416u,
					uint_4 = 270598144u,
					EB11D8A7 = 270606336u,
					uint_5 = 285215776u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058856u, 0u),
						Tuple.Create(1071508u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058924u, 48268u),
					uint_6 = 1059368u,
					uint_8 = 51641u,
					tuple_0 = Tuple.Create(49312u, 49496u),
					EC16A503 = 1059576u,
					AC007701 = 1059592u,
					FF39232F = 1070324u,
					uint_7 = 268542208u,
					int_3 = 298123264,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26465u,
					D30F1103 = "MT6761/MT6762/MT3369/MT8766B",
					A1187E38 = "Helio A20/P22/A22/A25/G25",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6761_payload.bin"))
				}
			},
			{
				1680,
				new GStruct38
				{
					int_2 = 127,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_4 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270606336u,
					uint_5 = 285215776u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058868u, 0u),
						Tuple.Create(1076388u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058936u, 54892u),
					uint_6 = 1059472u,
					uint_8 = 58243u,
					tuple_0 = Tuple.Create(55936u, 56120u),
					EC16A503 = 1059704u,
					AC007701 = 1059720u,
					FF39232F = 1075204u,
					uint_7 = 268542208u,
					int_3 = 301006848,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26467u,
					D30F1103 = "MT6763",
					A1187E38 = "Helio P23",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6763_payload.bin"))
				}
			},
			{
				1894,
				new GStruct38
				{
					int_2 = 37,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_3 = 268476416u,
					uint_4 = 270598144u,
					EB11D8A7 = 270606336u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058856u, 0u),
						Tuple.Create(1071508u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058924u, 48576u),
					uint_6 = 1059368u,
					uint_8 = 51949u,
					tuple_0 = Tuple.Create(49620u, 49804u),
					EC16A503 = 1059576u,
					AC007701 = 1059592u,
					FF39232F = 1070324u,
					uint_7 = 268542208u,
					int_3 = 298123264,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26469u,
					D30F1103 = "MT6765/MT8768t",
					A1187E38 = "Helio P35/G35",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6765_payload.bin"))
				}
			},
			{
				1799,
				new GStruct38
				{
					int_2 = 37,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_3 = 268476416u,
					uint_4 = 270598144u,
					EB11D8A7 = 270606336u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058860u, 0u),
						Tuple.Create(1071508u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058924u, 49552u),
					uint_6 = 1059368u,
					uint_8 = 53013u,
					tuple_0 = Tuple.Create(50584u, 50768u),
					EC16A503 = 1059576u,
					AC007701 = 1059592u,
					FF39232F = 1070324u,
					uint_7 = 268542208u,
					int_3 = 298713088,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26472u,
					D30F1103 = "MT6768/MT6769",
					A1187E38 = "Helio P65/G85 k68v1",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6768_payload.bin"))
				}
			},
			{
				1928,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_3 = 268476416u,
					uint_4 = 270598144u,
					EB11D8A7 = 270606336u,
					uint_5 = 285213016u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058868u, 0u),
						Tuple.Create(1075808u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058936u, 57020u),
					uint_6 = 1059456u,
					uint_8 = 60393u,
					tuple_0 = Tuple.Create(58064u, 58248u),
					EC16A503 = 1059640u,
					AC007701 = 1059656u,
					FF39232F = 1074624u,
					uint_7 = 268542208u,
					int_3 = 301006848,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26481u,
					D30F1103 = "MT6771/MT8385/MT8183/MT8666",
					A1187E38 = "Helio P60/P70/G80",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6771_payload.bin"))
				}
			},
			{
				1829,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_3 = 268476416u,
					uint_4 = 270598144u,
					EB11D8A7 = 270606336u,
					uint_5 = 285213016u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058872u, 0u),
						Tuple.Create(1075808u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058936u, 57420u),
					uint_6 = 1059456u,
					uint_8 = 60781u,
					tuple_0 = Tuple.Create(58452u, 58636u),
					EC16A503 = 1059640u,
					AC007701 = 1059656u,
					FF39232F = 1074624u,
					uint_7 = 268542208u,
					int_3 = 297861120,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26489u,
					D30F1103 = "MT6779",
					A1187E38 = "Helio P90 k79v1",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6779_payload.bin"))
				}
			},
			{
				4198,
				new GStruct38
				{
					int_2 = 115,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_3 = 268476416u,
					uint_4 = 270598144u,
					******** = new List<Tuple<uint, uint>> { Tuple.Create(1058892u, 1076052u) },
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058960u, 58840u),
					uint_6 = 1059508u,
					uint_8 = 62401u,
					tuple_0 = Tuple.Create(59868u, 60052u),
					EC16A503 = 1059736u,
					AC007701 = 1059752u,
					int_3 = 298516480,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26497u,
					D30F1103 = "MT6781",
					A1187E38 = "Helio G96",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6781_payload.bin"))
				}
			},
			{
				2067,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_3 = 268476416u,
					uint_4 = 270598144u,
					EB11D8A7 = 270606336u,
					uint_5 = 285213016u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058872u, 0u),
						Tuple.Create(1075808u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058936u, 58020u),
					uint_6 = 1059456u,
					uint_8 = 61481u,
					tuple_0 = Tuple.Create(59052u, 59236u),
					EC16A503 = 1059640u,
					AC007701 = 1059656u,
					FF39232F = 1074624u,
					uint_7 = 268542208u,
					int_3 = 297861120,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26501u,
					D30F1103 = "MT6785",
					A1187E38 = "Helio G90",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6785_payload.bin"))
				}
			},
			{
				26517,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 1114112u,
					uint_2 = 1075838976u,
					AB96C4A7 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270609408u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058660u, 0u),
						Tuple.Create(1070852u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058724u, 38796u),
					uint_6 = 1060940u,
					uint_8 = 41747u,
					tuple_0 = Tuple.Create(39520u, 39976u),
					EC16A503 = 1061024u,
					int_3 = 270557184,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 26517u,
					D30F1103 = "MT6795",
					A1187E38 = "Helio X10",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6795_payload.bin"))
				}
			},
			{
				633,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270609408u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058668u, 0u),
						Tuple.Create(1070852u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058736u, 40620u),
					uint_6 = 1060952u,
					uint_8 = 43583u,
					tuple_0 = Tuple.Create(41356u, 41812u),
					EC16A503 = 1061036u,
					uint_7 = 268443728u,
					int_3 = 270557184,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26519u,
					D30F1103 = "MT6797/MT6767",
					A1187E38 = "Helio X23/X25/X27",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6797_payload.bin"))
				}
			},
			{
				1378,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 270602240u,
					uint_1 = 285343744u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 270598144u,
					EB11D8A7 = 296943616u,
					uint_5 = 285213088u,
					uint_4 = 296878080u,
					uint_3 = 268476416u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058928u, 0u),
						Tuple.Create(1077360u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058996u, 62892u),
					uint_6 = 1061616u,
					uint_8 = 66243u,
					tuple_0 = Tuple.Create(63936u, 64120u),
					EC16A503 = 1061816u,
					AC007701 = 1061832u,
					int_3 = 301006848,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26521u,
					D30F1103 = "MT6799",
					A1187E38 = "Helio X30/X35",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6799_payload.bin"))
				}
			},
			{
				2441,
				new GStruct38
				{
					int_2 = 115,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_4 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270606336u,
					uint_5 = 270629920u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058884u, 0u),
						Tuple.Create(1076052u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058948u, 57312u),
					uint_6 = 1059492u,
					uint_8 = 60845u,
					tuple_0 = Tuple.Create(58344u, 58528u),
					EC16A503 = 1059736u,
					AC007701 = 1059752u,
					FF39232F = 1074868u,
					int_3 = 297861120,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26675u,
					D30F1103 = "MT6833",
					A1187E38 = "Dimensity 700 5G k6833",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6833_payload.bin"))
				}
			},
			{
				2454,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_4 = 270598144u,
					EB11D8A7 = 270606336u,
					uint_3 = 268476416u,
					uint_5 = 270629920u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058892u, 0u),
						Tuple.Create(1076064u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058956u, 60004u),
					uint_6 = 1059488u,
					uint_8 = 63537u,
					tuple_0 = Tuple.Create(61036u, 61220u),
					EC16A503 = 1059704u,
					AC007701 = 1059720u,
					FF39232F = 1074880u,
					uint_7 = 268542208u,
					int_3 = 297861120,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26707u,
					D30F1103 = "MT6853",
					A1187E38 = "Dimensity 720 5G",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6853_payload.bin"))
				}
			},
			{
				2182,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_4 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270606336u,
					uint_5 = 270629920u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058892u, 0u),
						Tuple.Create(1076064u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058956u, 60024u),
					uint_6 = 1059488u,
					uint_8 = 63485u,
					tuple_0 = Tuple.Create(61056u, 61240u),
					EC16A503 = 1059704u,
					AC007701 = 1059720u,
					FF39232F = 1074880u,
					uint_7 = 268542208u,
					int_3 = 297861120,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26739u,
					D30F1103 = "MT6873",
					A1187E38 = "Dimensity 800/820 5G",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6873_payload.bin"))
				}
			},
			{
				2393,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_3 = 268476416u,
					uint_4 = 270598144u,
					EB11D8A7 = 270606336u,
					uint_5 = 270629920u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058888u, 0u),
						Tuple.Create(1076064u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058952u, 59600u),
					uint_6 = 1059484u,
					uint_8 = 63133u,
					tuple_0 = Tuple.Create(60632u, 60816u),
					EC16A503 = 1059736u,
					AC007701 = 1059752u,
					FF39232F = 1074880u,
					int_3 = 301006848,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26743u,
					D30F1103 = "MT6877/MT6877V",
					A1187E38 = "Dimensity 900/1080",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6877_payload.bin"))
				}
			},
			{
				2070,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_4 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270606336u,
					uint_5 = 285215776u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058888u, 0u),
						Tuple.Create(1076064u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058952u, 59132u),
					uint_6 = 1059484u,
					uint_8 = 62593u,
					tuple_0 = Tuple.Create(60164u, 60348u),
					EC16A503 = 1059704u,
					AC007701 = 1059720u,
					FF39232F = 1074880u,
					uint_7 = 268542208u,
					int_3 = 297861120,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26757u,
					D30F1103 = "MT6885/MT6883/MT6889/MT6880/MT6890",
					A1187E38 = "Dimensity 1000L/1000",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6885_payload.bin"))
				}
			},
			{
				2384,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_4 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270606336u,
					uint_5 = 285215776u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058888u, 0u),
						Tuple.Create(1076064u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1058952u, 59292u),
					uint_6 = 1059484u,
					uint_8 = 62825u,
					tuple_0 = Tuple.Create(60324u, 60508u),
					EC16A503 = 1059736u,
					AC007701 = 1059752u,
					FF39232F = 1074880u,
					int_3 = 297861120,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 26771u,
					D30F1103 = "MT6893",
					A1187E38 = "Dimensity 1200",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt6893_payload.bin"))
				}
			},
			{
				2311,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 469790720u,
					uint_1 = 285216768u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_4 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270606336u,
					uint_5 = 288360864u,
					EC16A503 = 1050860u,
					AC007701 = 1050932u,
					B72DA2BE = AB881C92.D03BC921,
					F1016B18 = 27011u,
					D30F1103 = "MT6983",
					A1187E38 = "Dimensity 9000/9000+"
				}
			},
			{
				4466,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 469790720u,
					uint_1 = 285216768u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					AB96C4A7 = 268763136u,
					uint_4 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270606336u,
					uint_5 = 288360864u,
					EC16A503 = 1050860u,
					AC007701 = 1050932u,
					B72DA2BE = AB881C92.D03BC921,
					F1016B18 = 26773u,
					D30F1103 = "MT6895",
					A1187E38 = "Dimensity 8100"
				}
			},
			{
				4616,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					uint_4 = 270598144u,
					uint_3 = 268476416u,
					******** = new List<Tuple<uint, uint>> { Tuple.Create(1060188u, 0u) },
					uint_6 = 1060900u,
					uint_8 = 66024u,
					tuple_0 = Tuple.Create(63898u, 64012u),
					EC16A503 = 1050860u,
					AC007701 = 1050932u,
					int_3 = 297861120,
					B72DA2BE = AB881C92.D03BC921,
					F1016B18 = 4616u,
					D30F1103 = "MT6789",
					A1187E38 = "MTK Helio G99"
				}
			},
			{
				4649,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 469790720u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 33558528u,
					uint_2 = 1075838976u,
					uint_4 = 270598144u,
					EB11D8A7 = 270606336u,
					B72DA2BE = AB881C92.D03BC921,
					F1016B18 = 4649u,
					D30F1103 = "MT6886",
					A1187E38 = "Dimensity 7200 Ultra"
				}
			},
			{
				4758,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					uint_4 = 270598144u,
					uint_3 = 268476416u,
					EC16A503 = 1050860u,
					AC007701 = 1050932u,
					B72DA2BE = AB881C92.D03BC921,
					F1016B18 = 4758u,
					D30F1103 = "MT6985",
					A1187E38 = "Dimensity 9200/9200+"
				}
			},
			{
				33063,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 2147487744u,
					AB96C4A7 = 285278208u,
					uint_3 = 268476416u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058928u, 0u),
						Tuple.Create(1076348u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058992u, 45752u),
					uint_6 = 1061240u,
					uint_8 = 48627u,
					tuple_0 = Tuple.Create(46476u, 46912u),
					EC16A503 = 1061324u,
					uint_7 = 268443728u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 33063u,
					D30F1103 = "MT8127/MT3367",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt8127_payload.bin"))
				}
			},
			{
				33077,
				new GStruct38
				{
					uint_0 = 268435456u,
					uint_1 = 285220864u,
					EDB8851A = 301993984u,
					uint_2 = 2147487744u,
					AB96C4A7 = 285310976u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 33077u,
					D30F1103 = "MT8135"
				}
			},
			{
				33123,
				new GStruct38
				{
					int_2 = 177,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1073745920u,
					AB96C4A7 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270609408u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058920u, 0u),
						Tuple.Create(1077980u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058984u, 49452u),
					uint_6 = 1061228u,
					uint_8 = 52403u,
					tuple_0 = Tuple.Create(50176u, 50632u),
					EC16A503 = 1061312u,
					uint_7 = 268443728u,
					int_3 = 270557184,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 33123u,
					D30F1103 = "MT8163",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt8163_payload.bin"))
				}
			},
			{
				33127,
				new GStruct38
				{
					int_2 = 204,
					uint_0 = 268464128u,
					uint_1 = 285233152u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1073745920u,
					AB96C4A7 = 270585856u,
					uint_3 = 268476416u,
					EB11D8A7 = 270609408u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1059176u, 0u),
						Tuple.Create(1079636u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1059244u, 53988u),
					uint_6 = 1061788u,
					uint_8 = 57335u,
					tuple_0 = Tuple.Create(55026u, 55212u),
					EC16A503 = 1062008u,
					AC007701 = 1062024u,
					int_3 = 268472320,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 33127u,
					D30F1103 = "MT8167/MT8516/MT8362",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt8167_payload.bin"))
				}
			},
			{
				33128,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1073745920u,
					AB96C4A7 = 270798848u,
					uint_3 = 268476416u,
					uint_5 = 285213728u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1060924u, 0u),
						Tuple.Create(1090880u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1060992u, 79924u),
					uint_6 = 1074044u,
					uint_8 = 82799u,
					tuple_0 = Tuple.Create(80920u, 81272u),
					EC16A503 = 1074232u,
					AC007701 = 1074248u,
					int_3 = 268472320,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 33128u,
					D30F1103 = "MT8168/MT6357",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt8168_payload.bin"))
				}
			},
			{
				33138,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1182208u,
					EDB8851A = 786432u,
					uint_2 = 1073745920u,
					AB96C4A7 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270609408u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1189748u, 0u),
						Tuple.Create(1202436u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1189812u, 41188u),
					uint_6 = 1192028u,
					uint_8 = 44139u,
					tuple_0 = Tuple.Create(41912u, 42368u),
					EC16A503 = 1192112u,
					uint_7 = 18882640u,
					B72DA2BE = AB881C92.const_0,
					F1016B18 = 33139u,
					D30F1103 = "MT8173",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt8173_payload.bin"))
				}
			},
			{
				33142,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1182208u,
					EDB8851A = 786432u,
					uint_2 = 1075838976u,
					AB96C4A7 = 270598144u,
					uint_3 = 268476416u,
					EB11D8A7 = 270609408u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1189748u, 0u),
						Tuple.Create(1202436u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1189812u, 41188u),
					uint_6 = 1192028u,
					uint_8 = 44139u,
					tuple_0 = Tuple.Create(41912u, 42368u),
					EC16A503 = 1192112u,
					uint_7 = 18882640u,
					int_3 = 270557184,
					F1016B18 = 33139u,
					B72DA2BE = AB881C92.const_0,
					D30F1103 = "MT8176",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt8176_payload.bin"))
				}
			},
			{
				2352,
				new GStruct38
				{
					uint_0 = 268464128u,
					uint_1 = 285217280u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1075838976u,
					int_3 = 297861120,
					uint_7 = 268542208u,
					F1016B18 = 33173u,
					B72DA2BE = AB881C92.const_1,
					D30F1103 = "MT8195 Chromebook"
				}
			},
			{
				34066,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 1118208u,
					uint_2 = 1075838976u,
					AB96C4A7 = 270594048u,
					uint_3 = 268476416u,
					EB11D8A7 = 270614528u,
					uint_5 = 285213088u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1065444u, 0u),
						Tuple.Create(1092228u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1065560u, 52292u),
					uint_6 = 1066352u,
					uint_8 = 55211u,
					tuple_0 = Tuple.Create(53300u, 53652u),
					EC16A503 = 1066552u,
					AC007701 = 1066568u,
					int_3 = 298123264,
					F1016B18 = 34066u,
					B72DA2BE = AB881C92.const_1,
					D30F1103 = "MT8512",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt8512_payload.bin"))
				}
			},
			{
				34072,
				new GStruct38
				{
					int_3 = 268472320,
					F1016B18 = 34072u,
					B72DA2BE = AB881C92.const_1,
					D30F1103 = "MT8518 VoiceAssistant"
				}
			},
			{
				34192,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 2147487744u,
					AB96C4A7 = 285323264u,
					uint_3 = 268476416u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1058928u, 0u),
						Tuple.Create(1076348u, 0u)
					},
					F52144AA = 8u,
					A53A653F = Tuple.Create(1058992u, 48100u),
					uint_6 = 1061252u,
					uint_8 = 50975u,
					tuple_0 = Tuple.Create(48824u, 49260u),
					EC16A503 = 1061336u,
					F1016B18 = 34192u,
					B72DA2BE = AB881C92.const_0,
					D30F1103 = "MT8590/MT7683/MT8521/MT7623",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt8590_payload.bin"))
				}
			},
			{
				34453,
				new GStruct38
				{
					int_2 = 10,
					uint_0 = 268464128u,
					uint_1 = 285220864u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					uint_2 = 1073745920u,
					uint_3 = 268476416u,
					uint_5 = 285213728u,
					******** = new List<Tuple<uint, uint>>
					{
						Tuple.Create(1060936u, 0u),
						Tuple.Create(1076932u, 0u)
					},
					F52144AA = 10u,
					A53A653F = Tuple.Create(1061000u, 48876u),
					uint_6 = 1061356u,
					uint_8 = 51879u,
					tuple_0 = Tuple.Create(49816u, 50168u),
					EC16A503 = 1061560u,
					int_3 = 270557184,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 34453u,
					D30F1103 = "MT8695",
					CDB8E484 = Class607.B630A78B.object_0[649](Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\payloads\\mt8695_payload.bin"))
				}
			},
			{
				2312,
				new GStruct38
				{
					uint_0 = 268464128u,
					A2947F13 = 1051136u,
					EDB8851A = 2101248u,
					int_3 = 297861120,
					B72DA2BE = AB881C92.const_1,
					F1016B18 = 34454u,
					D30F1103 = "MT8696"
				}
			}
		};
	}

	public Tuple<int, int, int> method_0(int int_2, string DF205836)
	{
		int item = 1;
		int item2 = 0;
		int num = 0;
		if (int_2 == 26002 || int_2 == 33063 || int_2 == 25969)
		{
			if (Class607.B630A78B.object_0[787](DF205836, "emmc"))
			{
				item = 1;
				item2 = 168;
				num = 22020096;
			}
		}
		else if (int_2 == 25968 || int_2 == 33127 || int_2 == 25984 || int_2 == 26421 || int_2 == 26451 || int_2 == 26453 || int_2 == 26450 || int_2 == 26005 || int_2 == 26517 || int_2 == 26471 || int_2 == 26519 || int_2 == 33123 || int_2 == 33063)
		{
			item = 1;
			num = 0;
		}
		else if (int_2 == 25969)
		{
			if (Class607.B630A78B.object_0[787](DF205836, "nand"))
			{
				item = 0;
				item2 = 56;
				num = 14680064;
			}
			else if (Class607.B630A78B.object_0[787](DF205836, "emmc"))
			{
				item = 1;
				item2 = 168;
				num = 22020096;
			}
		}
		else if (int_2 == 25973)
		{
			if (Class607.B630A78B.object_0[787](DF205836, "nand"))
			{
				item = 0;
				item2 = 80;
			}
			else if (Class607.B630A78B.object_0[787](DF205836, "emmc"))
			{
				item = 1;
				item2 = 168;
				num = 22020096;
			}
		}
		else if (int_2 == 25986)
		{
			if (Class607.B630A78B.object_0[787](DF205836, "emmc"))
			{
				item = 2;
				item2 = 168;
				num = 22020096;
			}
		}
		else if (int_2 == 25970)
		{
			if (Class607.B630A78B.object_0[787](DF205836, "nand"))
			{
				item = 0;
				num = 22020096;
				item2 = 168;
			}
			else if (Class607.B630A78B.object_0[787](DF205836, "emmc"))
			{
				item = 0;
				num = 168;
				item2 = 80;
			}
		}
		else if ((int_2 == 25975 || int_2 == 25987 || int_2 == 25993) && Class607.B630A78B.object_0[787](DF205836, "nand"))
		{
			item = 0;
			num = 10485760;
			item2 = 168;
		}
		int_0 = item;
		int_1 = item2;
		EEAA3694 = num;
		return Tuple.Create(item, item2, num);
	}

	public bool method_1(int int_2)
	{
		if (A8054FBA.int_0 == int_2 && A8054FBA.CDB8E484 != null && A8054FBA.CDB8E484.Length != 0)
		{
			return true;
		}
		if (A8054FBA.CDB8E484 != null)
		{
			Class607.B630A78B.object_0[979](A8054FBA.CDB8E484, 0, A8054FBA.CDB8E484.Length);
		}
		if (dictionary_0.ContainsKey(int_2))
		{
			A8054FBA = dictionary_0[int_2];
			if (A8054FBA.CDB8E484 == null)
			{
				string string_ = Class607.B630A78B.object_0[720](GClass112.A6360694, "\\\\bin\\payloads\\generic_patcher_payload.bin");
				if (!Class607.B630A78B.object_0[695](string_))
				{
					return false;
				}
				A8054FBA.CDB8E484 = Class607.B630A78B.object_0[649](string_);
			}
			A8054FBA.int_0 = int_2;
			return true;
		}
		return false;
	}
}
