internal sealed class EFAA2A1F
{
	internal static void smethod_0(object object_0, float float_0, float float_1, float float_2, float float_3)
	{
		short num = 14414;
		do
		{
			Class607.B630A78B.object_0[0x42233C5C ^ ((((num >> 2) + 1109590325) | num) >>> ((num < (int)((uint)num % 355130399u) % -937032415) ? 1 : 0))](object_0, float_0, float_1, float_2, float_3);
		}
		while (0 >= (-1140246507 << (int)(sbyte)num) / (int)((uint)num % ~(((uint)num < (uint)num) ? 1u : 0u)) >> 16);
	}
}
