using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using MotoKingPro.Core;
using MotoKingPro.UI;

namespace MotoKingPro.Platforms
{
    /// <summary>
    /// Motorola platform page (AA16C934 -> MotorolaPage)
    /// Handles Motorola-specific operations and bootloader management
    /// </summary>
    public partial class MotorolaPage : UserControl
    {
        #region Events
        public event Action<bool, string> OperationStateChanged;
        public event Action<string, LogLevel, bool, bool> LogMessage;
        #endregion

        #region Fields
        private MainForm parentForm;
        private MotorolaManager motorolaManager;
        private LogRichTextBox textOutput;
        private bool isOperationRunning = false;
        #endregion

        #region UI Controls
        private Panel mainPanel;
        private GroupBox deviceInfoGroup;
        private GroupBox operationsGroup;
        private GroupBox bootloaderGroup;
        
        private Button connectButton;
        private Button disconnectButton;
        private Button readInfoButton;
        private Button getUnlockDataButton;
        private Button unlockBootloaderButton;
        private Button lockBootloaderButton;
        private Button rebootButton;
        
        private ComboBox rebootModeCombo;
        private TextBox unlockCodeTextBox;
        private Button applyUnlockCodeButton;
        private CheckBox bypassMDMCheckBox;
        
        private Label deviceModelLabel;
        private Label bootloaderVersionLabel;
        private Label unlockStatusLabel;
        private Label connectionStatusLabel;
        #endregion

        #region Constructor
        public MotorolaPage()
        {
            InitializeComponent();
            InitializeCustomComponents();
            SetupEventHandlers();
        }
        #endregion

        #region Initialization
        private void InitializeComponent()
        {
            mainPanel = new Panel { Dock = DockStyle.Fill, BackColor = Color.FromArgb(45, 45, 48) };
            
            // Device info group
            deviceInfoGroup = new GroupBox 
            { 
                Text = "Device Information", 
                ForeColor = Color.White,
                Size = new Size(300, 120),
                Location = new Point(10, 10)
            };
            
            deviceModelLabel = new Label { Text = "Model: Not Connected", ForeColor = Color.White, Location = new Point(10, 20) };
            bootloaderVersionLabel = new Label { Text = "Bootloader: Unknown", ForeColor = Color.White, Location = new Point(10, 40) };
            unlockStatusLabel = new Label { Text = "Unlock Status: Unknown", ForeColor = Color.White, Location = new Point(10, 60) };
            connectionStatusLabel = new Label { Text = "Status: Disconnected", ForeColor = Color.Red, Location = new Point(10, 80) };
            
            deviceInfoGroup.Controls.AddRange(new Control[] { deviceModelLabel, bootloaderVersionLabel, unlockStatusLabel, connectionStatusLabel });
            
            // Operations group
            operationsGroup = new GroupBox 
            { 
                Text = "Operations", 
                ForeColor = Color.White,
                Size = new Size(300, 160),
                Location = new Point(10, 140)
            };
            
            connectButton = new Button { Text = "Connect Device", Size = new Size(120, 30), Location = new Point(10, 20) };
            disconnectButton = new Button { Text = "Disconnect", Size = new Size(120, 30), Location = new Point(140, 20), Enabled = false };
            readInfoButton = new Button { Text = "Read Device Info", Size = new Size(120, 30), Location = new Point(10, 60), Enabled = false };
            getUnlockDataButton = new Button { Text = "Get Unlock Data", Size = new Size(120, 30), Location = new Point(140, 60), Enabled = false };
            
            rebootModeCombo = new ComboBox 
            { 
                Size = new Size(120, 25), 
                Location = new Point(10, 100),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Enabled = false
            };
            rebootModeCombo.Items.AddRange(new[] { "Normal", "Bootloader", "Recovery", "Download" });
            rebootModeCombo.SelectedIndex = 0;
            
            rebootButton = new Button { Text = "Reboot", Size = new Size(120, 30), Location = new Point(140, 100), Enabled = false };
            
            operationsGroup.Controls.AddRange(new Control[] 
            { 
                connectButton, disconnectButton, readInfoButton, getUnlockDataButton,
                rebootModeCombo, rebootButton
            });
            
            // Bootloader group
            bootloaderGroup = new GroupBox 
            { 
                Text = "Bootloader Management", 
                ForeColor = Color.White,
                Size = new Size(300, 140),
                Location = new Point(320, 10)
            };
            
            Label unlockCodeLabel = new Label { Text = "Unlock Code:", ForeColor = Color.White, Location = new Point(10, 20) };
            unlockCodeTextBox = new TextBox 
            { 
                Size = new Size(280, 25), 
                Location = new Point(10, 40),
                BackColor = Color.FromArgb(30, 30, 30),
                ForeColor = Color.White,
                PlaceholderText = "Enter unlock code from Motorola"
            };
            
            applyUnlockCodeButton = new Button { Text = "Apply Unlock Code", Size = new Size(130, 30), Location = new Point(10, 75), Enabled = false };
            
            unlockBootloaderButton = new Button { Text = "Unlock Bootloader", Size = new Size(120, 30), Location = new Point(10, 110), Enabled = false };
            lockBootloaderButton = new Button { Text = "Lock Bootloader", Size = new Size(120, 30), Location = new Point(140, 110), Enabled = false };
            
            bypassMDMCheckBox = new CheckBox 
            { 
                Text = "Bypass MDM", 
                ForeColor = Color.White, 
                Location = new Point(150, 80),
                Enabled = false
            };
            
            bootloaderGroup.Controls.AddRange(new Control[] 
            { 
                unlockCodeLabel, unlockCodeTextBox, applyUnlockCodeButton,
                unlockBootloaderButton, lockBootloaderButton, bypassMDMCheckBox
            });
            
            // Text output
            textOutput = new LogRichTextBox 
            { 
                Size = new Size(610, 200), 
                Location = new Point(10, 310),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };
            
            mainPanel.Controls.AddRange(new Control[] { deviceInfoGroup, operationsGroup, bootloaderGroup, textOutput });
            Controls.Add(mainPanel);
        }

        private void InitializeCustomComponents()
        {
            motorolaManager = new MotorolaManager();
            
            // Log initial messages
            LogMessage("Motorola Platform", LogLevel.Word, true, true);
            LogMessage("Version: " + ApplicationInfo.GetVersionString(), LogLevel.Success);
            LogMessage("Selected Tab: Motorola", LogLevel.Success, true, true);
            LogMessage("Ready for device connection...", LogLevel.Info);
        }

        private void SetupEventHandlers()
        {
            connectButton.Click += ConnectButton_Click;
            disconnectButton.Click += DisconnectButton_Click;
            readInfoButton.Click += ReadInfoButton_Click;
            getUnlockDataButton.Click += GetUnlockDataButton_Click;
            applyUnlockCodeButton.Click += ApplyUnlockCodeButton_Click;
            unlockBootloaderButton.Click += UnlockBootloaderButton_Click;
            lockBootloaderButton.Click += LockBootloaderButton_Click;
            rebootButton.Click += RebootButton_Click;
            
            motorolaManager.OperationStateChanged += OnOperationStateChanged;
            motorolaManager.LogMessage += OnLogMessage;
        }
        #endregion

        #region Public Methods
        public void SetParentForm(MainForm form)
        {
            parentForm = form;
        }

        public void FocusTextOutput()
        {
            textOutput?.Focus();
        }

        public void ClearLog()
        {
            textOutput?.ClearLog();
        }

        public List<LogItem> GetLogItems()
        {
            return new List<LogItem>();
        }

        public void SetOperationState(bool isRunning)
        {
            isOperationRunning = isRunning;
            UpdateUIState();
        }
        #endregion

        #region Event Handlers
        private void ConnectButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Connect Motorola Device");
            
            bool success = motorolaManager.ConnectDevice();
            if (success)
            {
                connectionStatusLabel.Text = "Status: Connected";
                connectionStatusLabel.ForeColor = Color.Green;
                EnableConnectedControls(true);
                LogMessage("Motorola device connected successfully", LogLevel.Success);
            }
            else
            {
                LogMessage("Failed to connect Motorola device", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Connect Motorola Device");
        }

        private void DisconnectButton_Click(object sender, EventArgs e)
        {
            motorolaManager.DisconnectDevice();
            connectionStatusLabel.Text = "Status: Disconnected";
            connectionStatusLabel.ForeColor = Color.Red;
            EnableConnectedControls(false);
            LogMessage("Device disconnected", LogLevel.Warning);
        }

        private void ReadInfoButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Read Device Information");
            
            var deviceInfo = motorolaManager.ReadDeviceInfo();
            if (deviceInfo != null)
            {
                deviceModelLabel.Text = $"Model: {deviceInfo.Model}";
                bootloaderVersionLabel.Text = $"Bootloader: {deviceInfo.BuildNumber}";
                LogMessage($"Device Model: {deviceInfo.Model}", LogLevel.Success);
                LogMessage($"Bootloader Version: {deviceInfo.BuildNumber}", LogLevel.Success);
            }
            
            OnOperationStateChanged(false, "Read Device Information");
        }

        private void GetUnlockDataButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Get Unlock Data");
            
            string unlockData = motorolaManager.GetUnlockData();
            if (!string.IsNullOrEmpty(unlockData))
            {
                LogMessage("Unlock data retrieved:", LogLevel.Success);
                LogMessage(unlockData, LogLevel.Word, true, true);
                LogMessage("Visit https://motorola-global-portal.custhelp.com/ to get unlock code", LogLevel.Info);
            }
            else
            {
                LogMessage("Failed to retrieve unlock data", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Get Unlock Data");
        }

        private void ApplyUnlockCodeButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(unlockCodeTextBox.Text))
            {
                LogMessage("Please enter unlock code first", LogLevel.Error);
                return;
            }

            OnOperationStateChanged(true, "Apply Unlock Code");
            
            bool success = motorolaManager.ApplyUnlockCode(unlockCodeTextBox.Text);
            if (success)
            {
                unlockStatusLabel.Text = "Unlock Status: Code Applied";
                LogMessage("Unlock code applied successfully", LogLevel.Success);
            }
            else
            {
                LogMessage("Failed to apply unlock code", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Apply Unlock Code");
        }

        private void UnlockBootloaderButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Unlock Bootloader");
            
            bool bypassMDM = bypassMDMCheckBox.Checked;
            bool success = motorolaManager.UnlockBootloader(bypassMDM);
            
            if (success)
            {
                unlockStatusLabel.Text = "Unlock Status: Unlocked";
                LogMessage("Bootloader unlocked successfully", LogLevel.Success);
                if (bypassMDM)
                {
                    LogMessage("MDM bypass applied", LogLevel.Warning);
                }
            }
            else
            {
                LogMessage("Failed to unlock bootloader", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Unlock Bootloader");
        }

        private void LockBootloaderButton_Click(object sender, EventArgs e)
        {
            OnOperationStateChanged(true, "Lock Bootloader");
            
            bool success = motorolaManager.LockBootloader();
            if (success)
            {
                unlockStatusLabel.Text = "Unlock Status: Locked";
                LogMessage("Bootloader locked successfully", LogLevel.Success);
            }
            else
            {
                LogMessage("Failed to lock bootloader", LogLevel.Error);
            }
            
            OnOperationStateChanged(false, "Lock Bootloader");
        }

        private void RebootButton_Click(object sender, EventArgs e)
        {
            string mode = rebootModeCombo.SelectedItem.ToString();
            OnOperationStateChanged(true, $"Reboot to {mode}");
            
            bool success = motorolaManager.RebootDevice(mode);
            LogMessage(success ? $"Device rebooted to {mode} mode" : $"Failed to reboot to {mode} mode", 
                success ? LogLevel.Success : LogLevel.Error);
            
            OnOperationStateChanged(false, $"Reboot to {mode}");
        }

        private void OnOperationStateChanged(bool isRunning, string operationName)
        {
            OperationStateChanged?.Invoke(isRunning, operationName);
        }

        private void OnLogMessage(string message, LogLevel level, bool newLine, bool bold)
        {
            LogMessage?.Invoke(message, level, newLine, bold);
            textOutput.LogMessage(message, level, newLine, bold);
        }
        #endregion

        #region Helper Methods
        private void EnableConnectedControls(bool enabled)
        {
            disconnectButton.Enabled = enabled;
            readInfoButton.Enabled = enabled;
            getUnlockDataButton.Enabled = enabled;
            applyUnlockCodeButton.Enabled = enabled;
            unlockBootloaderButton.Enabled = enabled;
            lockBootloaderButton.Enabled = enabled;
            rebootModeCombo.Enabled = enabled;
            rebootButton.Enabled = enabled;
            bypassMDMCheckBox.Enabled = enabled;
            
            connectButton.Enabled = !enabled;
        }

        private void UpdateUIState()
        {
            // Update UI based on operation state
        }

        private void LogMessage(string message, LogLevel level = LogLevel.Info, bool newLine = true, bool bold = false)
        {
            textOutput?.LogMessage(message, level, newLine, bold);
        }
        #endregion
    }

    /// <summary>
    /// Motorola device manager for bootloader operations
    /// </summary>
    public class MotorolaManager : BasePlatformManager
    {
        private DeviceConnectionState connectionState = DeviceConnectionState.Disconnected;
        private DeviceInfo currentDevice;

        public override bool ConnectDevice()
        {
            try
            {
                OnLogMessage("Attempting to connect Motorola device...", LogLevel.Info);
                OnLogMessage("Waiting for device in fastboot mode...", LogLevel.Info);

                System.Threading.Thread.Sleep(1200);

                connectionState = DeviceConnectionState.Fastboot;
                currentDevice = new DeviceInfo
                {
                    Model = "Motorola Edge",
                    Manufacturer = "Motorola",
                    Chipset = "Snapdragon 765G",
                    Platform = PlatformType.Motorola,
                    SerialNumber = "MOTO123456789",
                    BuildNumber = "Bootloader 1.0.0"
                };

                OnLogMessage("Motorola device connected in fastboot mode", LogLevel.Success);
                return true;
            }
            catch (Exception ex)
            {
                OnLogMessage($"Connection failed: {ex.Message}", LogLevel.Error);
                return false;
            }
        }

        public override bool DisconnectDevice()
        {
            connectionState = DeviceConnectionState.Disconnected;
            currentDevice = null;
            OnLogMessage("Device disconnected", LogLevel.Warning);
            return true;
        }

        public override DeviceConnectionState GetConnectionState()
        {
            return connectionState;
        }

        public DeviceInfo ReadDeviceInfo()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return null;

            OnLogMessage("Reading Motorola device information...", LogLevel.Info);
            System.Threading.Thread.Sleep(600);

            return currentDevice;
        }

        public string GetUnlockData()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return null;

            OnLogMessage("Retrieving bootloader unlock data...", LogLevel.Info);
            System.Threading.Thread.Sleep(1500);

            // Simulate unlock data
            string unlockData = "0x12345678#MOTOROLA#UNLOCK#DATA#EXAMPLE#0x87654321";
            OnLogMessage("Unlock data retrieved successfully", LogLevel.Success);

            return unlockData;
        }

        public bool ApplyUnlockCode(string unlockCode)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage($"Applying unlock code: {unlockCode.Substring(0, 8)}...", LogLevel.Info);
            System.Threading.Thread.Sleep(2000);
            OnLogMessage("Unlock code applied successfully", LogLevel.Success);
            return true;
        }

        public bool UnlockBootloader(bool bypassMDM = false)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage("Starting bootloader unlock process...", LogLevel.Warning);

            if (bypassMDM)
            {
                OnLogMessage("Applying MDM bypass...", LogLevel.Warning);
                System.Threading.Thread.Sleep(1500);
                OnLogMessage("MDM bypass applied successfully", LogLevel.Success);
            }

            OnLogMessage("Unlocking bootloader...", LogLevel.Info);
            System.Threading.Thread.Sleep(3000);
            OnLogMessage("Bootloader unlocked successfully", LogLevel.Success);
            OnLogMessage("WARNING: Device warranty may be voided!", LogLevel.Error);

            return true;
        }

        public bool LockBootloader()
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage("Locking bootloader...", LogLevel.Warning);
            OnLogMessage("WARNING: This may brick your device if custom firmware is installed!", LogLevel.Error);
            System.Threading.Thread.Sleep(2500);
            OnLogMessage("Bootloader locked successfully", LogLevel.Success);
            return true;
        }

        public bool RebootDevice(string mode)
        {
            if (connectionState == DeviceConnectionState.Disconnected)
                return false;

            OnLogMessage($"Rebooting device to {mode} mode...", LogLevel.Info);
            System.Threading.Thread.Sleep(1000);
            OnLogMessage($"Device reboot to {mode} initiated", LogLevel.Success);
            return true;
        }
    }
}
