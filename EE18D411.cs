using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using LibUsbDotNet;
using LibUsbDotNet.Main;

public class EE18D411
{
	public enum GEnum41
	{
		D2300699,
		C71064B5,
		D41B6AB5,
		const_3,
		CEA42D94
	}

	public enum GEnum42
	{
		const_0,
		const_1,
		AB00423A
	}

	public class GClass119
	{
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private GEnum41 C4BA2790;

		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		[CompilerGenerated]
		private string D18A0C91;

		[CompilerGenerated]
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private byte[] DFBEC42D;

		public GEnum41 GEnum41_0
		{
			[CompilerGenerated]
			get
			{
				return C4BA2790;
			}
			[CompilerGenerated]
			set
			{
				C4BA2790 = value;
			}
		}

		public string String_0
		{
			[CompilerGenerated]
			get
			{
				return D18A0C91;
			}
			[CompilerGenerated]
			set
			{
				D18A0C91 = value;
			}
		}

		public byte[] Byte_0
		{
			[CompilerGenerated]
			get
			{
				return DFBEC42D;
			}
			[CompilerGenerated]
			set
			{
				DFBEC42D = value;
			}
		}

		public GClass119(GEnum41 CEB424B1, string string_0)
		{
			Class607.B630A78B.object_0[571](this);
			GEnum41_0 = CEB424B1;
			String_0 = string_0;
		}
	}

	public delegate void D08CB61A(double double_0, double double_1);

	public struct GStruct97
	{
		public string string_0;

		public UsbDevice usbDevice_0;
	}

	[Serializable]
	[CompilerGenerated]
	private sealed class FF09DB0C
	{
		public static readonly FF09DB0C _003C_003E9 = new FF09DB0C();

		public static Func<string, bool> _003C_003E9__33_0;

		public FF09DB0C()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool E1213F38(string E695AC06)
		{
			return Class607.B630A78B.object_0[491](E695AC06, "(bootloader)");
		}
	}

	[CompilerGenerated]
	private sealed class Class71
	{
		public StringBuilder stringBuilder_0;

		public StringBuilder stringBuilder_1;

		public Class71()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal void method_0(object sender, DataReceivedEventArgs e)
		{
			if (!Class607.B630A78B.object_0[1205](Class607.B630A78B.object_0[487](e)))
			{
				Class607.B630A78B.object_0[481](stringBuilder_0, Class607.B630A78B.object_0[487](e));
			}
		}

		internal void BA94F4BC(object sender, DataReceivedEventArgs e)
		{
			if (!Class607.B630A78B.object_0[1205](Class607.B630A78B.object_0[487](e)))
			{
				Class607.B630A78B.object_0[481](stringBuilder_1, Class607.B630A78B.object_0[487](e));
			}
		}
	}

	[CompilerGenerated]
	private sealed class Class72
	{
		public int int_0;

		public Class72()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool C9A1C405(int D4197A27)
		{
			return D4197A27 == int_0;
		}
	}

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private ABA18D2C.E33840A1 e33840A1_0;

	private const int CC246299 = 53261;

	private const int D08E0B85 = 4;

	private const int int_0 = 1048576;

	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[CompilerGenerated]
	private int A719080F = 3000;

	private UsbDevice usbDevice_0;

	public string FC9F3208;

	private UsbEndpointWriter C5359489;

	private UsbEndpointReader B4935636;

	private string string_0;

	private List<int> EE9F9016 => new List<int> { 6353, 4817, 8888, 10864, 4046, 1478, 1256, 11669, 7871, 2996 };

	public int Int32_0
	{
		[CompilerGenerated]
		get
		{
			return A719080F;
		}
		[CompilerGenerated]
		set
		{
			A719080F = value;
		}
	}

	public event ABA18D2C.E33840A1 Event_0
	{
		[CompilerGenerated]
		add
		{
			ABA18D2C.E33840A1 e33840A = e33840A1_0;
			ABA18D2C.E33840A1 e33840A2;
			do
			{
				e33840A2 = e33840A;
				ABA18D2C.E33840A1 value2 = (ABA18D2C.E33840A1)Class607.B630A78B.object_0[752](e33840A2, value);
				e33840A = Interlocked.CompareExchange(ref e33840A1_0, value2, e33840A2);
			}
			while ((object)e33840A != e33840A2);
		}
		[CompilerGenerated]
		remove
		{
			ABA18D2C.E33840A1 e33840A = e33840A1_0;
			ABA18D2C.E33840A1 e33840A2;
			do
			{
				e33840A2 = e33840A;
				ABA18D2C.E33840A1 value2 = (ABA18D2C.E33840A1)Class607.B630A78B.object_0[629](e33840A2, value);
				e33840A = Interlocked.CompareExchange(ref e33840A1_0, value2, e33840A2);
			}
			while ((object)e33840A != e33840A2);
		}
	}

	private GEnum41 E0BA8B88(string string_1)
	{
		if (!Class607.B630A78B.object_0[787](string_1, "INFO"))
		{
			if (!Class607.B630A78B.object_0[787](string_1, "OKAY"))
			{
				if (!Class607.B630A78B.object_0[787](string_1, "DATA"))
				{
					if (!Class607.B630A78B.object_0[787](string_1, "FAIL"))
					{
						return GEnum41.CEA42D94;
					}
					return GEnum41.D2300699;
				}
				return GEnum41.D41B6AB5;
			}
			return GEnum41.C71064B5;
		}
		return GEnum41.const_3;
	}

	public GClass119 AC1490BD(byte[] byte_0)
	{
		//IL_0017: Unknown result type (might be due to invalid IL or missing references)
		//IL_0104: Unknown result type (might be due to invalid IL or missing references)
		int int_ = default(int);
		Class607.B630A78B.object_0[82](C5359489, byte_0, Int32_0, ref int_);
		if (int_ != byte_0.Length)
		{
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[1105]("Failed to write command! Transfered: {0} of {1} bytes", int_, byte_0.Length));
		}
		StringBuilder stringBuilder = Class607.B630A78B.object_0[1086]();
		byte[] array = new byte[64];
		int num = default(int);
		string text;
		GEnum41 gEnum;
		do
		{
			Class607.B630A78B.object_0[62](B4935636, array, Int32_0, ref num);
			text = Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[47](), array);
			if (Class607.B630A78B.object_0[342](text) >= 4)
			{
				string string_ = Class607.B630A78B.object_0[1260](text.Take(4).ToArray());
				gEnum = E0BA8B88(string_);
			}
			else
			{
				gEnum = GEnum41.CEA42D94;
			}
			Class607.B630A78B.object_0[729](stringBuilder, text.Skip(4).Take(num - 4).ToArray());
			Class607.B630A78B.object_0[426](stringBuilder, "\n");
		}
		while (gEnum == GEnum41.const_3);
		string text2 = Class607.B630A78B.object_0[1233](Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[99](stringBuilder.ToString(), "\r", Class607.B630A78B.object_0[244]()), "\0", Class607.B630A78B.object_0[244]()));
		return new GClass119(gEnum, text2)
		{
			Byte_0 = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[47](), text)
		};
	}

	private void method_0(long A22CB0A6)
	{
		if (method_9(Class607.B630A78B.object_0[1217]("download:{0:X8}", A22CB0A6)).GEnum41_0 != GEnum41.D41B6AB5)
		{
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[1217]("Invalid response from device! (data size: {0})", A22CB0A6));
		}
	}

	private void method_1(Stream E12FBA9B, UsbEndpointWriter usbEndpointWriter_0, byte[] B29BF025, int int_1)
	{
		//IL_0025: Unknown result type (might be due to invalid IL or missing references)
		Class607.B630A78B.object_0[53](E12FBA9B, B29BF025, 0, int_1);
		int int_2 = default(int);
		Class607.B630A78B.object_0[82](usbEndpointWriter_0, B29BF025, Int32_0, ref int_2);
		if (int_2 != int_1)
		{
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[1105]("Failed to transfer block (sent {0} of {1})", int_2, int_1));
		}
	}

	public void method_2(FileStream fileStream_0)
	{
		//IL_0098: Unknown result type (might be due to invalid IL or missing references)
		long num = Class607.B630A78B.object_0[61](fileStream_0);
		byte[] b29BF = new byte[1048576];
		method_0(num);
		while (num >= 1048576L)
		{
			method_1(fileStream_0, C5359489, b29BF, 1048576);
			num -= 1048576L;
		}
		if (num > 0L)
		{
			b29BF = new byte[num];
			method_1(fileStream_0, C5359489, b29BF, (int)num);
		}
		byte[] array = new byte[64];
		int num2 = default(int);
		Class607.B630A78B.object_0[62](B4935636, array, Int32_0, ref num2);
		string text = Class607.B630A78B.object_0[920](Class607.B630A78B.object_0[47](), array);
		if (Class607.B630A78B.object_0[342](text) < 4)
		{
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[720]("Invalid response from device: ", text));
		}
		string string_ = Class607.B630A78B.object_0[1260](text.Take(4).ToArray());
		GEnum41 gEnum = E0BA8B88(string_);
		if (gEnum != GEnum41.C71064B5)
		{
			throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[720]("Invalid status: ", text));
		}
	}

	public void method_3(string string_1)
	{
		using FileStream fileStream_ = Class607.B630A78B.object_0[835](string_1, FileMode.Open);
		method_2(fileStream_);
	}

	public void method_4(string A4A467B2, B9A6028D C329AF2D, string string_1)
	{
		new GClass128().E6A33692(new object[4] { this, A4A467B2, C329AF2D, string_1 }, 1282765);
	}

	public (bool result, string message) method_5(string string_1, string string_2, B9A6028D CFB4EF86)
	{
		method_15();
		method_4(string_1, CFB4EF86, string_2);
		GClass119 gClass = method_9(Class607.B630A78B.object_0[720]("flash:", string_2));
		return (result: gClass.GEnum41_0 == GEnum41.C71064B5, message: gClass.String_0);
	}

	public void method_6(string string_1, string BFAC813D, B9A6028D b9A6028D_0, GClass112.E39CAE0B e39CAE0B_0, ABA18D2C.E33840A1 C0B1C79F, GClass112.GDelegate32 gdelegate32_0, bool bool_0 = true)
	{
		string text = "";
		try
		{
			if (bool_0)
			{
				e39CAE0B_0("Parsing firmware: ");
			}
			string cCBE820D = Class62.smethod_0(b9A6028D_0.long_0);
			if (!GClass117.B48F988D(b9A6028D_0, string_1, cCBE820D, e39CAE0B_0, C0B1C79F))
			{
				e39CAE0B_0("Parse failed #1", EF1F389C.Error, BB24BF3C: false, bool_0: true);
				return;
			}
			text = Class607.B630A78B.object_0[351](cCBE820D, b9A6028D_0.string_0);
			if (Class607.B630A78B.object_0[695](text))
			{
				if (bool_0)
				{
					e39CAE0B_0("Ok", EF1F389C.Success, BB24BF3C: false, bool_0: true);
					e39CAE0B_0(Class607.B630A78B.object_0[1140]("Flashing ", BFAC813D, ": "));
				}
				gdelegate32_0(bool_0: true);
				GClass119 gClass = method_11(method_8(Class525.smethod_0(new string[5] { "flash ", BFAC813D, " \"", text, "\"" })));
				if (gClass.GEnum41_0 == GEnum41.C71064B5)
				{
					e39CAE0B_0("Ok", EF1F389C.Success, BB24BF3C: false, bool_0: true);
				}
				else
				{
					e39CAE0B_0(gClass.String_0, EF1F389C.Error, BB24BF3C: false, bool_0: true);
				}
			}
			else
			{
				e39CAE0B_0("Parse failed", EF1F389C.Success, BB24BF3C: false, bool_0: true);
			}
		}
		finally
		{
			method_15();
			gdelegate32_0(bool_0: false);
			if (Class607.B630A78B.object_0[695](text))
			{
				Class607.B630A78B.object_0[353](text);
			}
		}
	}

	public void method_7(string string_1, string string_2, B9A6028D B58A1431, GClass112.E39CAE0B e39CAE0B_0, ABA18D2C.E33840A1 e33840A1_1, GClass112.GDelegate32 F5031586)
	{
		method_6(string_1, string_2, B58A1431, e39CAE0B_0, e33840A1_1, F5031586);
	}

	public unsafe string method_8(string D3175BB1)
	{
		method_16();
		string dC06460F = Class607.B630A78B.object_0[720](GClass112.A6360694, "\\bin\\data\\adb\\fastboot.exe");
		if (Class607.B630A78B.object_0[695](dC06460F))
		{
			Class71 @class = new Class71();
			ProcessStartInfo processStartInfo = Class607.B630A78B.object_0[204]();
			Class126.smethod_0(processStartInfo, dC06460F);
			Class672.smethod_0(processStartInfo, D3175BB1);
			Class746.smethod_0(processStartInfo, FA9B9EBE: false);
			Class486.D8034201(processStartInfo, B631192C: true);
			Class463.E41FF0B7(processStartInfo, bool_0: true);
			DC00EB16.B720F028(processStartInfo, bool_0: true);
			ProcessStartInfo processStartInfo_ = processStartInfo;
			@class.stringBuilder_0 = Class607.B630A78B.object_0[1086]();
			@class.stringBuilder_1 = Class607.B630A78B.object_0[1086]();
			using (Process process = Class607.B630A78B.object_0[497]())
			{
				Class607.B630A78B.object_0[442](process, processStartInfo_);
				Class607.B630A78B.object_0[745](process, Class607.B630A78B.object_0[362](@class, (nint)__ldftn(Class71.method_0)));
				Class607.B630A78B.object_0[797](process, Class607.B630A78B.object_0[362](@class, (nint)__ldftn(Class71.BA94F4BC)));
				Class607.B630A78B.object_0[998](process);
				Class607.B630A78B.object_0[152](process);
				Class607.B630A78B.object_0[737](process);
				Class607.B630A78B.object_0[702](process);
			}
			string result = Class607.B630A78B.object_0[1233](@class.stringBuilder_0.ToString());
			string text = Class607.B630A78B.object_0[1233](@class.stringBuilder_1.ToString());
			if (!Class607.B630A78B.object_0[1205](text))
			{
				return text ?? "";
			}
			return result;
		}
		return "fastboot.exe not found in PATH";
	}

	public GClass119 method_9(string DA804598)
	{
		return AC1490BD(Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[47](), DA804598));
	}

	public GClass119 A219ED2A(GEnum42 genum42_0 = GEnum42.const_0)
	{
		string text = "reboot";
		switch (genum42_0)
		{
		case GEnum42.AB00423A:
			text = Class607.B630A78B.object_0[720](text, "-recovery");
			break;
		case GEnum42.const_1:
			text = Class607.B630A78B.object_0[720](text, "-bootloader");
			break;
		}
		try
		{
			return method_9(text);
		}
		catch
		{
			return method_11(method_8(Class607.B630A78B.object_0[99](text, "-", " ")));
		}
	}

	public GClass119 E71359A9(string B28ED20B)
	{
		try
		{
			return method_9(Class607.B630A78B.object_0[720]("erase:", B28ED20B));
		}
		catch
		{
			return method_11(method_8(Class607.B630A78B.object_0[720]("erase ", B28ED20B)));
		}
	}

	public bool F8B07CA8(string string_1)
	{
		return !Class607.B630A78B.object_0[1205](string_1) && Class607.B630A78B.object_0[1240](string_1, "Finished. Total time") && !Class607.B630A78B.object_0[1240](Class607.B630A78B.object_0[1050](string_1), "failed");
	}

	private static string B139E69A(string string_1)
	{
		string[] source = Class538.smethod_0(string_1, new char[1] { '\n' });
		IEnumerable<string> ienumerable_ = source.Where((string E695AC06) => Class607.B630A78B.object_0[491](E695AC06, "(bootloader)"));
		return Class607.B630A78B.object_0[95](Class607.B630A78B.object_0[526]("\n", ienumerable_), Class607.B630A78B.object_0[209]("\r\n"));
	}

	private static string smethod_0(string F0969CB1)
	{
		Match match = Class607.B630A78B.object_0[602](F0969CB1, "^[^:]+:\\s*(.+)$", RegexOptions.Multiline);
		if (Class607.B630A78B.object_0[785](match))
		{
			string text = Class607.B630A78B.object_0[1233](Class607.B630A78B.object_0[896](Class607.B630A78B.object_0[1212](Class607.B630A78B.object_0[1090](match), 1)));
			if (!Class607.B630A78B.object_0[1240](text, "Finished"))
			{
				return text;
			}
		}
		return "";
	}

	public GClass119 method_10(string EAB42616, string AC208683)
	{
		if (F8B07CA8(EAB42616))
		{
			string text = "";
			text = ((!Class607.B630A78B.object_0[787](AC208683, "all")) ? smethod_0(EAB42616) : B139E69A(EAB42616));
			if (Class607.B630A78B.object_0[1205](text))
			{
				return new GClass119(GEnum41.D2300699, text);
			}
			return new GClass119(GEnum41.C71064B5, text);
		}
		return new GClass119(GEnum41.D2300699, smethod_1(EAB42616));
	}

	public GClass119 method_11(string string_1)
	{
		if (F8B07CA8(string_1))
		{
			return new GClass119(GEnum41.C71064B5, "Ok");
		}
		return new GClass119(GEnum41.D2300699, smethod_1(string_1));
	}

	private static string smethod_1(string string_1)
	{
		Match match = Class607.B630A78B.object_0[602](string_1, "FAILED \\(remote: '([^']+)'\\)", RegexOptions.IgnoreCase);
		if (Class607.B630A78B.object_0[785](match))
		{
			return Class607.B630A78B.object_0[896](Class607.B630A78B.object_0[1212](Class607.B630A78B.object_0[1090](match), 1));
		}
		return "Failed";
	}

	public GClass119 method_12(string FB810839)
	{
		string text = Class607.B630A78B.object_0[720]("oem ", FB810839);
		try
		{
			return method_9(text);
		}
		catch
		{
			return method_11(method_8(text));
		}
	}

	public GClass119 method_13(string D4B771A6)
	{
		try
		{
			return method_9(Class607.B630A78B.object_0[720]("getvar:", D4B771A6));
		}
		catch
		{
			return method_10(method_8(Class607.B630A78B.object_0[720]("getvar ", D4B771A6)), D4B771A6);
		}
	}

	public EE18D411(string FC9F3208)
	{
		Class607.B630A78B.object_0[571](this);
		this.FC9F3208 = FC9F3208;
	}

	public EE18D411()
	{
		Class607.B630A78B.object_0[571](this);
		FC9F3208 = null;
	}

	public string method_14(int int_1)
	{
		Stopwatch stopwatch = Class607.B630A78B.object_0[681]();
		try
		{
			Class607.B630A78B.object_0[887](stopwatch);
			while (Class607.B630A78B.object_0[1097](stopwatch) < int_1 && !GClass112.E31AABAB)
			{
				List<GStruct97> list = EF9D02A1();
				if (list != null && list.Count != 0)
				{
					return list[0].string_0;
				}
			}
		}
		finally
		{
			GClass112.E31AABAB = false;
			Class607.B630A78B.object_0[221](stopwatch);
		}
		return null;
	}

	public void method_15()
	{
		if (usbDevice_0 == null || !Class607.B630A78B.object_0[1035](usbDevice_0) || Class607.B630A78B.object_0[1205](FC9F3208) || !Class607.B630A78B.object_0[787](FC9F3208, string_0))
		{
			method_16();
			List<GStruct97> list = EF9D02A1();
			usbDevice_0 = list.Find((GStruct97 gstruct97_0) => Class607.B630A78B.object_0[787](gstruct97_0.string_0, FC9F3208)).usbDevice_0;
			if (usbDevice_0 == null || !Class607.B630A78B.object_0[827](usbDevice_0))
			{
				throw Class607.B630A78B.object_0[778]("No Device Available");
			}
			UsbDevice obj = usbDevice_0;
			IUsbDevice val = (IUsbDevice)(object)((obj is IUsbDevice) ? obj : null);
			if (val != null)
			{
				Class607.B630A78B.object_0[1193](val, 1);
				Class607.B630A78B.object_0[1098](val, 0);
			}
			C5359489 = Class607.B630A78B.object_0[59](usbDevice_0, (WriteEndpointID)1);
			B4935636 = Class607.B630A78B.object_0[817](usbDevice_0, (ReadEndpointID)129);
			string_0 = FC9F3208;
		}
	}

	public void method_16()
	{
		if (usbDevice_0 != null)
		{
			Class607.B630A78B.object_0[555](usbDevice_0);
		}
	}

	public string method_17()
	{
		return null;
	}

	public int method_18(byte byte_0)
	{
		if ((byte_0 & 0x80) == 0)
		{
			return byte_0;
		}
		return (byte)(~(byte_0 - 1)) * -1;
	}

	private long E7978D3D(long long_0)
	{
		long num = 0L;
		GClass119 gClass = method_9("getvar:max-download-size");
		if (gClass.GEnum41_0 == GEnum41.C71064B5)
		{
			num = Class607.B630A78B.object_0[588](gClass.String_0);
		}
		if (long_0 > num)
		{
			return num;
		}
		return 0L;
	}

	public byte[] method_19(ulong E10E96B4)
	{
		//IL_0033: Unknown result type (might be due to invalid IL or missing references)
		UsbEndpointReader object_ = Class607.B630A78B.object_0[817](usbDevice_0, (ReadEndpointID)129);
		byte[] array = new byte[E10E96B4];
		int num = 0;
		Class607.B630A78B.object_0[62](object_, array, 0, ref num);
		return GClass112.smethod_14(array, 0, num);
	}

	public List<GStruct97> EF9D02A1()
	{
		//IL_003d: Unknown result type (might be due to invalid IL or missing references)
		//IL_0043: Expected O, but got Unknown
		List<GStruct97> list = new List<GStruct97>();
		try
		{
			UsbRegDeviceList object_ = Class607.B630A78B.object_0[1173]();
			IEnumerator object_2 = Class607.B630A78B.object_0[895](object_);
			try
			{
				UsbDevice B3119A0D = default(UsbDevice);
				while (Class607.B630A78B.object_0[212](object_2))
				{
					UsbRegistry object_3 = (UsbRegistry)Class607.B630A78B.object_0[107](object_2);
					if (Class607.B630A78B.object_0[457](object_3, ref B3119A0D))
					{
						int int_ = Class607.B630A78B.object_0[768](Class607.B630A78B.object_0[1179](Class607.B630A78B.object_0[677](B3119A0D)));
						if (method_20(int_))
						{
							list.Add(new GStruct97
							{
								string_0 = Class607.B630A78B.object_0[80](Class607.B630A78B.object_0[677](B3119A0D)),
								usbDevice_0 = B3119A0D
							});
						}
						Class607.B630A78B.object_0[555](B3119A0D);
					}
				}
			}
			finally
			{
				IDisposable disposable = object_2 as IDisposable;
				if (disposable != null)
				{
					disposable.Dispose();
				}
			}
		}
		catch
		{
		}
		return list;
	}

	private bool method_20(int int_1)
	{
		Class72 CS_0024_003C_003E8__locals2 = new Class72();
		CS_0024_003C_003E8__locals2.int_0 = int_1;
		int[] array = EE9F9016.ToArray();
		return Array.Exists(array, (int D4197A27) => D4197A27 == CS_0024_003C_003E8__locals2.int_0);
	}

	public bool method_21(string string_1)
	{
		GClass119 gClass = method_9(Class607.B630A78B.object_0[720]("getvar:partition-type:", string_1));
		if (gClass.GEnum41_0 != GEnum41.C71064B5)
		{
			return false;
		}
		return Class607.B630A78B.object_0[787](gClass.String_0, "ext4");
	}

	[CompilerGenerated]
	private bool method_22(GStruct97 gstruct97_0)
	{
		return Class607.B630A78B.object_0[787](gstruct97_0.string_0, FC9F3208);
	}
}
