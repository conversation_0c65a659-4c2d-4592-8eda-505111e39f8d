using System.IO.Ports;

public class GClass116
{
	private SerialPort serialPort_0 = Class607.B630A78B.object_0[703]();

	public GClass116()
	{
		Class607.B630A78B.object_0[571](this);
		Class607.B630A78B.object_0[454](serialPort_0, 921600);
		Class607.B630A78B.object_0[581](serialPort_0, 115200);
		Class607.B630A78B.object_0[624](serialPort_0, 1500);
		Class607.B630A78B.object_0[30](serialPort_0, 1500);
		Class607.B630A78B.object_0[597](serialPort_0, 8);
		Class607.B630A78B.object_0[1244](serialPort_0, Parity.None);
		Class607.B630A78B.object_0[264](serialPort_0, StopBits.One);
		Class607.B630A78B.object_0[793](serialPort_0, bool_0: true);
		Class607.B630A78B.object_0[640](serialPort_0, DC3E1432: true);
		Class607.B630A78B.object_0[701](serialPort_0, C28D088C: true);
	}

	public bool A3B4389E(string string_0)
	{
		E3123B31();
		Class607.B630A78B.object_0[594](serialPort_0, string_0);
		Class607.B630A78B.object_0[70](serialPort_0);
		return Class607.B630A78B.object_0[1184](serialPort_0);
	}

	public void E3123B31()
	{
		if (Class607.B630A78B.object_0[1184](serialPort_0))
		{
			Class607.B630A78B.object_0[674](serialPort_0);
		}
	}

	public BDBAC319 method_0(string string_0)
	{
		BDBAC319 result = new BDBAC319
		{
			AC9E95BC = false
		};
		Class607.B630A78B.object_0[851](serialPort_0, string_0);
		string text = "";
		long num = 0L;
		while (Class607.B630A78B.object_0[1184](serialPort_0))
		{
			text = Class607.B630A78B.object_0[354](serialPort_0);
			if (Class607.B630A78B.object_0[1240](text, "OK") || Class607.B630A78B.object_0[1240](text, "ERROR"))
			{
				break;
			}
			Class607.B630A78B.object_0[299](50);
			num += 50L;
			if (num > 3000L)
			{
				break;
			}
		}
		result.AC9E95BC = Class607.B630A78B.object_0[1240](text, "OK");
		result.FD1A3E06 = text;
		return result;
	}

	public bool method_1(string string_0)
	{
		Class607.B630A78B.object_0[851](serialPort_0, string_0);
		string object_ = "";
		long num = 0L;
		while (Class607.B630A78B.object_0[1184](serialPort_0))
		{
			object_ = Class607.B630A78B.object_0[354](serialPort_0);
			if (Class607.B630A78B.object_0[1240](object_, "OK") || Class607.B630A78B.object_0[1240](object_, "ERROR"))
			{
				break;
			}
			Class607.B630A78B.object_0[299](50);
			num += 50L;
			if (num > 3000L)
			{
				break;
			}
		}
		return Class607.B630A78B.object_0[1240](object_, "OK");
	}

	public A33F311C method_2(GClass112.E39CAE0B E59A3838)
	{
		return (A33F311C)new GClass128().E8AA1C3C(new object[2] { this, E59A3838 }, 22491127);
	}
}
