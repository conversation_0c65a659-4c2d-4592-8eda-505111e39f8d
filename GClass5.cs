using System;
using System.Collections.Generic;

public static class GClass5
{
	public const uint C0073490 = 4277071599u;

	public static string D0B07A98(int int_0 = 134217728, int BF13F30D = 1048576)
	{
		Dictionary<string, List<string>> dictionary_ = new Dictionary<string, List<string>> { 
		{
			"arg",
			new List<string> { Class607.B630A78B.object_0[1105]("<source_file>MEM://{0:x}:{1:x}</source_file>", int_0, BF13F30D) }
		} };
		return smethod_11("SECURITY-SET-FLASH-POLICY", dictionary_);
	}

	public static string smethod_0(int int_0 = 134217728, int AF9AEB3C = 1048576)
	{
		return (string)new GClass128().F3BD1601(new object[2] { int_0, AF9AEB3C }, 11126134);
	}

	public static string smethod_1(long long_0 = 140635421247264L, int D993DB01 = 2097152)
	{
		return (string)new GClass128().method_68(new object[2] { long_0, D993DB01 }, 22571876);
	}

	public static string smethod_2(string E731B109 = "EMMC-USER", ulong ulong_0 = 0uL, ulong ulong_1 = 134217728uL, ulong CB055B86 = 1048576uL)
	{
		return (string)new GClass128().B4154402(new object[4] { E731B109, ulong_0, ulong_1, CB055B86 }, 21717734);
	}

	public static string smethod_3()
	{
		return (string)new GClass128().C5017C25(null, 561791);
	}

	public static string D093DC35(List<string> list_0)
	{
		string text = "";
		foreach (string item in list_0)
		{
			text = Class525.smethod_0(new string[6] { text, "    <pt name=", item, ">", item, ".img</pt>\n" });
		}
		List<string> value = new List<string>
		{
			"<source_file>D:/scatter.xml</source_file>",
			Class607.B630A78B.object_0[1140]("<flash_list>", text, "</flash_list>")
		};
		Dictionary<string, List<string>> dictionary_ = new Dictionary<string, List<string>> { { "arg", value } };
		return smethod_11("WRITE-PARTITIONS", dictionary_);
	}

	public static string smethod_4(string string_0 = "EMMC-USER", ulong ulong_0 = 0uL, ulong DD017228 = 1048576uL)
	{
		return (string)new GClass128().AD84C1A2(new object[3] { string_0, ulong_0, DD017228 }, 11171417);
	}

	public static string smethod_5()
	{
		return (string)new GClass128().method_68(null, 11023676);
	}

	public static string FC12CAA6(string A017CD06 = "system")
	{
		List<string> value = new List<string> { Class607.B630A78B.object_0[1140]("<partition>", A017CD06, "</partition>") };
		Dictionary<string, List<string>> dictionary_ = new Dictionary<string, List<string>> { { "arg", value } };
		return smethod_11("ERASE-PARTITION", dictionary_);
	}

	public static string smethod_6(string string_0 = "EMMC-USER", ulong ulong_0 = 0uL, ulong ulong_1 = 1048576uL)
	{
		return (string)new GClass128().EF8D5E3B(new object[3] { string_0, ulong_0, ulong_1 }, 46520);
	}

	public static string smethod_7(string string_0 = "GET-RPMB-STATUS", ulong ulong_0 = 140635419150096uL, ulong A62826B5 = 2097152uL)
	{
		List<string> value = new List<string>
		{
			Class607.B630A78B.object_0[1140]("<function>", string_0, "</function>"),
			Class607.B630A78B.object_0[1105]("<target_file>MEM://{0:X}:{1:X}</target_file>", ulong_0, A62826B5)
		};
		Dictionary<string, List<string>> dictionary_ = new Dictionary<string, List<string>> { { "arg", value } };
		return smethod_11("EMMC-CONTROL", dictionary_);
	}

	public static string A80E0304(long FA842D88 = 140635417052928L, int D789021E = 2097152)
	{
		Dictionary<string, List<string>> dictionary_ = new Dictionary<string, List<string>> { ["arg"] = new List<string> { Class607.B630A78B.object_0[1105]("<target_file>MEM://{0:X}:{1:X}</target_file>", FA842D88, D789021E) } };
		return smethod_11("GET-HW-INFO", dictionary_);
	}

	public static string C0865F21(string E2B7ECA7 = "DA.SLA", long FEA57AA6 = 140635417052928L, int CBAB66BB = 2097152)
	{
		Dictionary<string, List<string>> dictionary_ = new Dictionary<string, List<string>> { 
		{
			"arg",
			new List<string>
			{
				Class607.B630A78B.object_0[1140]("<key>", E2B7ECA7, "</key>"),
				Class607.B630A78B.object_0[1105]("<target_file>MEM://{0:X}:{1:X}</target_file>", FEA57AA6, CBAB66BB)
			}
		} };
		return smethod_11("GET-SYS-PROPERTY", dictionary_);
	}

	public static string smethod_8(long long_0 = 140635587662400L, int int_0 = 64)
	{
		return (string)new GClass128().C5017C25(new object[2] { long_0, int_0 }, 1282765);
	}

	public static string smethod_9(uint uint_0 = 1073741824u, uint C7A0DC2D = 1073741824u, long C614918E = 140635416404044L, int D82EC933 = 330872)
	{
		return (string)new GClass128().method_323(new object[4] { uint_0, C7A0DC2D, C614918E, D82EC933 }, 178924);
	}

	public static string smethod_10(string DF227A35 = "")
	{
		if (Class607.B630A78B.object_0[787](DF227A35, ""))
		{
			DateTime B = Class607.B630A78B.object_0[402]();
			DF227A35 = Class607.B630A78B.object_0[460](ref B, "yyyyMMddTHHmmss");
		}
		Dictionary<string, List<string>> dictionary_ = new Dictionary<string, List<string>> { 
		{
			"arg",
			new List<string> { Class607.B630A78B.object_0[1140]("<info>", DF227A35, "</info>") }
		} };
		return smethod_11("SET-HOST-INFO", dictionary_);
	}

	public static string B53FC42E()
	{
		return smethod_11("NOTIFY-INIT-HW");
	}

	public static string F9989A0E(string E9B3A630 = "CMD:DOWNLOAD-FILE^1@CMD:FILE-SYS-OPERATION^1@CMD:PROGRESS-REPORT^1@CMD:UPLOAD-FILE^1@")
	{
		Dictionary<string, List<string>> dictionary_ = new Dictionary<string, List<string>> { ["arg"] = new List<string> { Class607.B630A78B.object_0[1140]("<host_capability>", E9B3A630, "</host_capability>") } };
		return smethod_11("HOST-SUPPORTED-COMMANDS", dictionary_);
	}

	public static string smethod_11(string AD19ED8E, Dictionary<string, List<string>> dictionary_0 = null, string A7B69088 = "1.0")
	{
		string string_ = Class525.smethod_0(new string[5] { "<?xml version=\"1.0\" encoding=\"utf-8\"?><da><version>", A7B69088, "</version><command>CMD:", AD19ED8E, "</command>" });
		if (dictionary_0 != null)
		{
			foreach (KeyValuePair<string, List<string>> item in dictionary_0)
			{
				string_ = Class607.B630A78B.object_0[398](string_, "<", item.Key, ">");
				foreach (string item2 in item.Value)
				{
					string_ = Class607.B630A78B.object_0[720](string_, item2);
				}
				string_ = Class607.B630A78B.object_0[398](string_, "</", item.Key, ">");
			}
		}
		return Class607.B630A78B.object_0[720](string_, "</da>");
	}

	public static string smethod_12(string string_0 = "NONE", string string_1 = "AUTO-DETECT", string E7329FAF = "INFO", string string_2 = "UART", string string_3 = "LINUX", string string_4 = "1.1", bool bool_0 = true)
	{
		string text = Class525.smethod_0(new string[13]
		{
			"<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<da>\r\n    <version>1.1</version>\r\n    <command>CMD:SET-RUNTIME-PARAMETER</command>\r\n    <arg>\r\n        <checksum_level>",
			string_0,
			"</checksum_level>\r\n        <battery_exist>",
			string_1,
			"</battery_exist>\r\n        <da_log_level>",
			E7329FAF,
			"</da_log_level>\r\n        <log_channel>",
			string_2,
			"</log_channel>\r\n        <system_os>",
			string_3,
			"</system_os>\r\n    </arg>\r\n    <adv>\r\n        <initialize_dram>",
			bool_0 ? "YES" : "NO",
			"</initialize_dram>\r\n    </adv>\r\n</da>0"
		});
		Dictionary<string, List<string>> dictionary_ = new Dictionary<string, List<string>>
		{
			{
				"arg",
				new List<string>
				{
					Class607.B630A78B.object_0[1140]("<checksum_level>", string_0, "</checksum_level>"),
					Class607.B630A78B.object_0[1140]("<battery_exist>", string_1, "</battery_exist>"),
					Class607.B630A78B.object_0[1140]("<da_log_level>", E7329FAF, "</da_log_level>"),
					Class607.B630A78B.object_0[1140]("<log_channel>", string_2, "</log_channel>"),
					Class607.B630A78B.object_0[1140]("<system_os>", string_3, "</system_os>")
				}
			},
			{
				"adv",
				new List<string> { FB07919D.smethod_0("<initialize_dram>", bool_0 ? "YES" : "NO", "</initialize_dram>") }
			}
		};
		return smethod_11("SET-RUNTIME-PARAMETER", dictionary_, string_4);
	}
}
