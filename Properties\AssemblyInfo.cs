using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Security;
using System.Security.Permissions;

[assembly: AssemblyTitle("MotoKingPro")]
[assembly: AssemblyDescription("MotoKingPro tool , powered by alephgsm")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("MotoKingPro co")]
[assembly: AssemblyProduct("MotoKingPro")]
[assembly: AssemblyCopyright("Copyright ©  2025")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("5bd2d440-5d05-4f41-bb8a-f21c17b8ba07")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyVersion("*******")]
