using System.Collections.Generic;

public class GClass60
{
	public B42DC004 b42DC004_0;

	public uint uint_0;

	public uint uint_1;

	public uint uint_2;

	public uint uint_3;

	public uint uint_4;

	public uint E19FAAA8;

	public uint uint_5;

	public byte[] F23AD1AE;

	public GClass60(B42DC004 b42DC004_1)
	{
		Class607.B630A78B.object_0[571](this);
		b42DC004_0 = b42DC004_1;
		uint_0 = 1296911693u;
		uint_5 = 1162167621u;
	}

	public bool E93AE39C(byte[] byte_0)
	{
		object[] array = GClass111.C3B9331C("<IIIIIII", GClass112.smethod_14(byte_0, 0, 28));
		uint_0 = Class607.B630A78B.object_0[40](array[0]);
		uint_1 = Class607.B630A78B.object_0[40](array[1]);
		uint_2 = Class607.B630A78B.object_0[40](array[2]);
		uint_3 = Class607.B630A78B.object_0[40](array[3]);
		uint_4 = Class607.B630A78B.object_0[40](array[4]);
		E19FAAA8 = Class607.B630A78B.object_0[40](array[5]);
		uint_5 = Class607.B630A78B.object_0[40](array[6]);
		F23AD1AE = GClass112.smethod_14(byte_0, 28, 32);
		if ((uint_0 != 1296911693) | (uint_5 != 1162167621))
		{
			return false;
		}
		return true;
	}

	public byte[] F01168B3(GClass60 gclass60_0, string string_0, string string_1 = "unlock", bool bool_0 = false)
	{
		if (gclass60_0 == null)
		{
			if (Class607.B630A78B.object_0[787](string_1, "unlock"))
			{
				uint_3 = 3u;
				uint_4 = 1u;
				uint_1 = 4u;
				uint_2 = 60u;
				E19FAAA8 = 0u;
			}
			else if (Class607.B630A78B.object_0[787](string_1, "lock"))
			{
				uint_3 = 1u;
				uint_4 = 1u;
				uint_1 = 4u;
				uint_2 = 60u;
				E19FAAA8 = 0u;
			}
		}
		else
		{
			uint_3 = gclass60_0.uint_3;
			uint_4 = gclass60_0.uint_4;
			uint_2 = gclass60_0.uint_2;
			E19FAAA8 = gclass60_0.E19FAAA8;
			uint_1 = gclass60_0.uint_1;
		}
		byte[] array = GClass111.smethod_2("<IIIIIII", new object[7] { uint_0, uint_1, uint_2, uint_3, uint_4, E19FAAA8, 1162167621 });
		byte[] array2 = GClass112.DB37D01D(array);
		byte[] collection = (F23AD1AE = (Class607.B630A78B.object_0[787](string_0, "sw") ? b42DC004_0.GClass27_0.method_8(array2) : (bool_0 ? b42DC004_0.GClass27_0.method_10(array2) : b42DC004_0.GClass27_0.F911B912(array2, E63DE020: true))));
		List<byte> list = new List<byte>();
		list.AddRange(array);
		list.AddRange(collection);
		list.AddRange(new byte[512 - list.Count]);
		return list.ToArray();
	}
}
