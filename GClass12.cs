using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;

public class GClass12
{
	public class D5A71E34
	{
		public const int int_0 = 0;

		public const int int_1 = 1;

		public const int E582AB3C = 2;

		public const int BC03E9BB = 3;

		public const int F903BFBA = 4;

		public const int int_2 = 5;

		public const int DB9E9131 = 6;

		public const int EF8E4F96 = 7;

		public const int BAA03203 = 9;

		public const int D5810B3F = 10;

		public const int DE33D8A8 = 11;

		public D5A71E34()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass13
	{
		public const uint uint_0 = uint.MaxValue;

		public const uint A9978639 = 0u;

		public const uint uint_1 = 1u;

		public const uint uint_2 = 2u;

		public const uint uint_3 = 3u;

		public const uint BE0ED392 = 4u;

		public const uint BFA9C910 = 5u;

		public const uint uint_4 = 6u;

		public const uint uint_5 = 7u;

		public const uint E8AC3509 = 8u;

		public const uint uint_6 = 11u;

		public const uint E5A83EB9 = 12u;

		public const uint A6A3F735 = 2147483647u;

		public GClass13()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class B31A45B1
	{
		public const uint uint_0 = uint.MaxValue;

		public const uint E38BF688 = 0u;

		public const uint uint_1 = 1u;

		public const uint uint_2 = 3u;

		public const uint A73226A4 = 2147483647u;

		public B31A45B1()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass14
	{
		public const uint uint_0 = uint.MaxValue;

		public const uint E93A7A1F = 0u;

		public const uint uint_1 = 1u;

		public const uint uint_2 = 2u;

		public const uint uint_3 = 3u;

		public GClass14()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass15
	{
		public const uint uint_0 = 0u;

		public const uint F525FA07 = 1u;

		public const uint uint_1 = 2u;

		public const uint uint_2 = 3u;

		public const uint uint_3 = 4u;

		public const uint uint_4 = 5u;

		public const uint DC8FEBBF = 6u;

		public const uint uint_5 = 7u;

		public const uint F8846C92 = 8u;

		public const uint uint_6 = 9u;

		public const uint uint_7 = 10u;

		public const uint C80A9634 = 11u;

		public const uint uint_8 = 12u;

		public const uint uint_9 = 13u;

		public const uint uint_10 = 14u;

		public const uint uint_11 = 15u;

		public const uint A7BE55A5 = 16u;

		public const uint uint_12 = 17u;

		public const uint A188F6BF = 18u;

		public const uint ED0A770C = 32u;

		public const uint uint_13 = 33u;

		public const uint uint_14 = 34u;

		public const uint D60EB5AC = 35u;

		public const uint uint_15 = 36u;

		public const uint uint_16 = 37u;

		public const uint uint_17 = 38u;

		public const uint uint_18 = 39u;

		public const uint AD17FD34 = 41u;

		public const uint uint_19 = 42u;

		public const uint C13B02BF = 43u;

		public const uint uint_20 = 44u;

		public GClass15()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class AE00742E
	{
		public const uint BF2BCAB5 = 0u;

		public const uint uint_0 = 1u;

		public const uint uint_1 = 2u;

		public const uint uint_2 = 3u;

		public const uint uint_3 = 4u;

		public const uint uint_4 = 5u;

		public const uint uint_5 = 8u;

		public const uint C525383F = 9u;

		public const uint uint_6 = 10u;

		public const uint EB928E20 = 11u;

		public AE00742E()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass16
	{
		public const uint BA1F4B0B = 0u;

		public const uint E683DBA6 = 1u;

		public const uint EE304F9D = 2u;

		public const uint AB331C86 = 10u;

		public const uint C9AB09B7 = 4u;

		public const uint uint_0 = 12u;

		public const uint uint_1 = 6u;

		public const uint uint_2 = 2147483647u;

		public GClass16()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	public class GClass17
	{
		public const uint uint_0 = uint.MaxValue;

		public const uint EC870835 = 0u;

		public const uint uint_1 = 1u;

		public const uint uint_2 = 2u;

		public const uint F112DA80 = 3u;

		public const uint uint_3 = 4u;

		public const uint uint_4 = 5u;

		public const uint uint_5 = 6u;

		public const uint uint_6 = 7u;

		public const uint uint_7 = 8u;

		public const uint AF378294 = 9u;

		public const uint BA1ECE34 = 2147483647u;

		public GClass17()
		{
			Class607.B630A78B.object_0[571](this);
		}
	}

	[Serializable]
	[CompilerGenerated]
	private sealed class E39ADA3E
	{
		public static readonly E39ADA3E _003C_003E9 = new E39ADA3E();

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__91_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__91_1;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__92_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__92_1;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__92_2;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__92_3;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__92_4;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__93_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__93_1;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__94_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__94_1;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__94_2;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__94_3;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__94_4;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__96_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__97_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__98_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__99_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__99_1;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__99_2;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__100_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__100_1;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__101_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__102_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__103_0;

		public static Predicate<Tuple<string, List<int>>> _003C_003E9__104_0;

		public E39ADA3E()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool CC2F5B2A(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_DMA_MODE");
		}

		internal bool method_0(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_SIZE");
		}

		internal bool A9099DBD(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DOUT_ADDR_HIGH");
		}

		internal bool method_1(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DOUT_DMA_MODE");
		}

		internal bool method_2(Tuple<string, List<int>> CCB4B51A)
		{
			return Class607.B630A78B.object_0[787](CCB4B51A.Item1, "DOUT_SIZE");
		}

		internal bool method_3(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DOUT_LAST_IND");
		}

		internal bool method_4(Tuple<string, List<int>> EC99490A)
		{
			return Class607.B630A78B.object_0[787](EC99490A.Item1, "NS_BIT");
		}

		internal bool method_5(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DOUT_DMA_MODE");
		}

		internal bool A53FE211(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DOUT_SIZE");
		}

		internal bool method_6(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_ADDR_HIGH");
		}

		internal bool F3251507(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_DMA_MODE");
		}

		internal bool AC90BCBC(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_SIZE");
		}

		internal bool F53692BD(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_VIRTUAL_HOST");
		}

		internal bool C41DE63B(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "NS_BIT");
		}

		internal bool method_7(Tuple<string, List<int>> DFB7D29B)
		{
			return Class607.B630A78B.object_0[787](DFB7D29B.Item1, "CIPHER_DO");
		}

		internal bool method_8(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "SETUP_OPERATION");
		}

		internal bool A1BF9F9F(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DATA_FLOW_MODE");
		}

		internal bool method_9(Tuple<string, List<int>> DE18CB15)
		{
			return Class607.B630A78B.object_0[787](DE18CB15.Item1, "DIN_CONST_VALUE");
		}

		internal bool D92C7001(Tuple<string, List<int>> B00083A2)
		{
			return Class607.B630A78B.object_0[787](B00083A2.Item1, "DIN_DMA_MODE");
		}

		internal bool A799A50A(Tuple<string, List<int>> A49EAD08)
		{
			return Class607.B630A78B.object_0[787](A49EAD08.Item1, "DIN_SIZE");
		}

		internal bool FF2D02A9(Tuple<string, List<int>> B28089A8)
		{
			return Class607.B630A78B.object_0[787](B28089A8.Item1, "DIN_DMA_MODE");
		}

		internal bool ED9A3A39(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_SIZE");
		}

		internal bool method_10(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "KEY_SIZE");
		}

		internal bool method_11(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "CIPHER_CONF0");
		}

		internal bool method_12(Tuple<string, List<int>> tuple_0)
		{
			return Class607.B630A78B.object_0[787](tuple_0.Item1, "CIPHER_CONF1");
		}

		internal bool method_13(Tuple<string, List<int>> FEB26D08)
		{
			return Class607.B630A78B.object_0[787](FEB26D08.Item1, "CIPHER_MODE");
		}
	}

	public const int int_0 = 2560;

	public const int B1993F89 = 2568;

	public const int FD0586B1 = 3712;

	public const int int_1 = 3716;

	public const int int_2 = 3720;

	public const int int_3 = 3724;

	public const int AF2F4E0C = 3728;

	public const int E91F66A4 = 3732;

	public const int int_4 = 3740;

	public const int int_5 = 2688;

	public const int A2A1751B = 2696;

	public const int A32B8F19 = 2704;

	public const int int_6 = 2716;

	public const int int_7 = 2720;

	public const int D034BF03 = int.MaxValue;

	public const int C93E242C = 0;

	public const int int_8 = 0;

	public const int int_9 = 16;

	public const int F90400AB = 20;

	public const int A8BD219D = 28;

	public const int int_10 = 32;

	public const int F1363D38 = 48;

	public const int E9396094 = 64;

	public const int E9A864A5 = 64;

	public const int D7A6C933 = 64;

	public const int int_11 = 64;

	public const int B5239799 = 64;

	public const int CF18EC3B = 128;

	public const int int_12 = 128;

	public const int int_13 = 16;

	public const int FD133097 = 16;

	public const int int_14 = 7;

	public const int int_15 = 13;

	public const int F2BD7EAD = 4;

	public const int int_16 = 16;

	public const int int_17 = 8;

	public const int int_18 = 0;

	public const int CA919182 = 4;

	public const int B1B54D8D = 4;

	public const int int_19 = 16;

	public const int int_20 = 4;

	public const int int_21 = 16;

	public const int int_22 = 4;

	public const int int_23 = 16;

	public const int int_24 = 8;

	public const int E5942614 = 32;

	public const int int_25 = 8;

	public const int int_26 = 32;

	public const int D8217213 = 4;

	public const int C0014A28 = 16;

	public uint B689D20B;

	public uint DF3451B6;

	public uint FA84731C;

	public uint uint_0;

	public List<Tuple<uint, uint>> ADA08A0E;

	public uint uint_1;

	public uint uint_2;

	public GDelegate1 E03D6B31;

	public GDelegate2 gdelegate2_0;

	public EA0398BD ea0398BD_0;

	public string C5B1209D;

	public int D9285715;

	public Dictionary<string, object> dictionary_0;

	public bool A506F0A8()
	{
		return gdelegate2_0(B689D20B + 2568, new List<uint> { 4u });
	}

	public GClass12(GClass11 A1A4EA90)
	{
		Class607.B630A78B.object_0[571](this);
		D9285715 = A1A4EA90.int_0;
		B689D20B = A1A4EA90.uint_0;
		DF3451B6 = A1A4EA90.uint_1;
		FA84731C = A1A4EA90.uint_2;
		uint_0 = A1A4EA90.uint_3;
		ADA08A0E = A1A4EA90.list_0;
		uint_1 = A1A4EA90.uint_4;
		uint_2 = A1A4EA90.F126D59A;
		E03D6B31 = A1A4EA90.gdelegate1_0;
		gdelegate2_0 = A1A4EA90.F2B1FB97;
		ea0398BD_0 = A1A4EA90.F41AE714;
		C5B1209D = "DACD8B5FDA8A766FB7BCAA43F0B16915CE7B47714F1395FDEBCF12A2D41155B0FB587A51FECCCB4DDA1C8E5EB9EB69B86DAF2C620F6C2735215A5F22C0B6CE377AA0D07EB38ED340B5629FC2890494B078A63D6D07FDEACDBE3E7F27FDE4B143F49DB4971437E6D00D9E18B56F02DABEB0000B6E79516D0C8074B5A42569FD0D9196655D2A4030D42DFE05E9F64883E6D5F79A5BFA3E7014C9A62853DC1F21D5D626F4D0846DB16452187DD776E8886B48C210C9E208059E7CAFC997FD2CA210775C1A5D9AA261252FB975268D970C62733871D57814098A453DF92BC6CA19025CD9D430F02EE46F80DE6C63EA802BEF90673AAC4C6667F2883FB4501FA77455";
		dictionary_0 = new Dictionary<string, object>();
		dictionary_0.Add("DSCRPTR_COMPLETION_COUNTER0", Tuple.Create(3584, new List<Tuple<string, List<int>>>
		{
			Tuple.Create("COMPLETION_COUNTER", new List<int> { 0, 6 }),
			Tuple.Create("COMPLETION_COUNTER", new List<int> { 6, 1 })
		}));
		dictionary_0.Add("DSCRPTR_COMPLETION_COUNTER1", Tuple.Create(3588, new List<Tuple<string, List<int>>>
		{
			Tuple.Create("COMPLETION_COUNTER", new List<int> { 0, 6 }),
			Tuple.Create("COMPLETION_COUNTER", new List<int> { 6, 1 })
		}));
		dictionary_0.Add("DSCRPTR_COMPLETION_STATUS", new List<int> { 3644, 0, 2 });
		dictionary_0.Add("DSCRPTR_SW_RESET", new List<int> { 3648, 0, 1 });
		dictionary_0.Add("DSCRPTR_CNTX_SWITCH_COUNTER_VAL", new List<int> { 3652, 0, 32 });
		dictionary_0.Add("DSCRPTR_DISABLE_CNTX_SWITCH", new List<int> { 3656, 0, 1 });
		dictionary_0.Add("DSCRPTR_DEBUG_MODE", new List<int> { 3660, 0, 1 });
		dictionary_0.Add("DSCRPTR_FILTER_DROPPED_CNT", new List<int> { 3664, 0, 32 });
		dictionary_0.Add("DSCRPTR_FILTER_DROPPED_MEM_CNT", new List<int> { 3668, 0, 32 });
		dictionary_0.Add("DSCRPTR_FILTER_DEBUG", new List<int> { 3672, 0, 8 });
		dictionary_0.Add("DSCRPTR_FILTER_DROPPED_ADDRESS", new List<int> { 3676, 0, 32 });
		dictionary_0.Add("DSCRPTR_QUEUE_SRAM_SIZE", new List<int> { 3680, 0, 10 });
		dictionary_0.Add("DSCRPTR_SINGLE_ADDR_EN", new List<int> { 3684, 0, 1 });
		dictionary_0.Add("DSCRPTR_MEASURE_CNTR", new List<int> { 3688, 0, 32 });
		dictionary_0.Add("DSCRPTR_FILTER_DROPPED_ADDRESS_HIGH", new List<int> { 3692, 0, 16 });
		dictionary_0.Add("DSCRPTR_QUEUE0_WORD0", new List<int> { 3712, 0, 32 });
		dictionary_0.Add("DSCRPTR_QUEUE0_WORD1", Tuple.Create(3716, new List<Tuple<string, List<int>>>
		{
			Tuple.Create("DIN_DMA_MODE", new List<int> { 0, 2 }),
			Tuple.Create("DIN_SIZE", new List<int> { 2, 24 }),
			Tuple.Create("NS_BIT", new List<int> { 26, 1 }),
			Tuple.Create("DIN_CONST_VALUE", new List<int> { 27, 1 }),
			Tuple.Create("NOT_LAST", new List<int> { 28, 1 }),
			Tuple.Create("LOCK_QUEUE", new List<int> { 29, 1 }),
			Tuple.Create("DIN_VIRTUAL_HOST", new List<int> { 30, 2 })
		}));
		dictionary_0.Add("DSCRPTR_QUEUE0_WORD2", new List<int> { 3720, 0, 32 });
		dictionary_0.Add("DSCRPTR_QUEUE0_WORD3", Tuple.Create(3724, new List<Tuple<string, List<int>>>
		{
			Tuple.Create("DOUT_DMA_MODE", new List<int> { 0, 2 }),
			Tuple.Create("DOUT_SIZE", new List<int> { 2, 24 }),
			Tuple.Create("NS_BIT", new List<int> { 26, 1 }),
			Tuple.Create("DOUT_LAST_IND", new List<int> { 27, 1 }),
			Tuple.Create("HASH_XOR_BIT", new List<int> { 29, 1 }),
			Tuple.Create("DOUT_VIRTUAL_HOST", new List<int> { 30, 2 })
		}));
		dictionary_0.Add("DSCRPTR_QUEUE0_WORD4", Tuple.Create(3728, new List<Tuple<string, List<int>>>
		{
			Tuple.Create("DATA_FLOW_MODE", new List<int> { 0, 6 }),
			Tuple.Create("AES_SEL_N_HASH", new List<int> { 6, 1 }),
			Tuple.Create("AES_XOR_CRYPTO_KEY", new List<int> { 7, 1 }),
			Tuple.Create("ACK_NEEDED", new List<int> { 8, 2 }),
			Tuple.Create("CIPHER_MODE", new List<int> { 10, 4 }),
			Tuple.Create("CMAC_SIZE0", new List<int> { 14, 1 }),
			Tuple.Create("CIPHER_DO", new List<int> { 15, 2 }),
			Tuple.Create("CIPHER_CONF0", new List<int> { 17, 2 }),
			Tuple.Create("CIPHER_CONF1", new List<int> { 19, 1 }),
			Tuple.Create("CIPHER_CONF2", new List<int> { 20, 2 }),
			Tuple.Create("KEY_SIZE", new List<int> { 22, 2 }),
			Tuple.Create("SETUP_OPERATION", new List<int> { 24, 4 }),
			Tuple.Create("DIN_SRAM_ENDIANNESS", new List<int> { 28, 1 }),
			Tuple.Create("DOUT_SRAM_ENDIANNESS", new List<int> { 29, 1 }),
			Tuple.Create("WORD_SWAP", new List<int> { 30, 1 }),
			Tuple.Create("BYTES_SWAP", new List<int> { 31, 1 })
		}));
		dictionary_0.Add("DSCRPTR_QUEUE0_WORD5", Tuple.Create(3732, new List<Tuple<string, List<int>>>
		{
			Tuple.Create("DIN_ADDR_HIGH", new List<int> { 0, 16 }),
			Tuple.Create("DOUT_ADDR_HIGH", new List<int> { 16, 16 })
		}));
		dictionary_0.Add("DSCRPTR_QUEUE0_WATERMARK", new List<int> { 3736, 0, 10 });
		dictionary_0.Add("DSCRPTR_QUEUE0_CONTENT", new List<int> { 3740, 0, 10 });
		dictionary_0.Add("DSCRPTR_QUEUE1_WORD0", new List<int> { 3744, 0, 32 });
		dictionary_0.Add("DSCRPTR_QUEUE1_WORD1", Tuple.Create(3748, new List<Tuple<string, List<int>>>
		{
			Tuple.Create("DIN_DMA_MODE", new List<int> { 0, 2 }),
			Tuple.Create("DIN_SIZE", new List<int> { 2, 24 }),
			Tuple.Create("NS_BIT", new List<int> { 26, 1 }),
			Tuple.Create("DIN_CONST", new List<int> { 27, 1 }),
			Tuple.Create("NOT_LAST", new List<int> { 28, 1 }),
			Tuple.Create("LOCK_QUEUE", new List<int> { 29, 1 }),
			Tuple.Create("DIN_VIRTUAL_HOST", new List<int> { 30, 2 })
		}));
		dictionary_0.Add("DSCRPTR_QUEUE1_WORD2", new List<int> { 3752, 0, 32 });
		dictionary_0.Add("DSCRPTR_QUEUE1_WORD3", Tuple.Create(3756, new List<Tuple<string, List<int>>>
		{
			Tuple.Create("DOUT_DMA_MODE", new List<int> { 0, 2 }),
			Tuple.Create("DOUT_SIZE", new List<int> { 2, 24 }),
			Tuple.Create("NS_BIT", new List<int> { 26, 1 }),
			Tuple.Create("DOUT_LAST_IND", new List<int> { 27, 1 }),
			Tuple.Create("HASH_XOR_BIT", new List<int> { 29, 1 }),
			Tuple.Create("DOUT_VIRTUAL_HOST", new List<int> { 30, 2 })
		}));
		dictionary_0.Add("DSCRPTR_QUEUE1_WORD4", Tuple.Create(3760, new List<Tuple<string, List<int>>>
		{
			Tuple.Create("DATA_FLOW_MODE", new List<int> { 0, 6 }),
			Tuple.Create("AES_SEL_N_HASH", new List<int> { 6, 1 }),
			Tuple.Create("AES_XOR_CRYPTO_KEY", new List<int> { 7, 1 }),
			Tuple.Create("ACK_NEEDED", new List<int> { 8, 2 }),
			Tuple.Create("CIPHER_MODE", new List<int> { 10, 4 }),
			Tuple.Create("CMAC_SIZE0", new List<int> { 14, 1 }),
			Tuple.Create("CIPHER_DO", new List<int> { 15, 2 }),
			Tuple.Create("CIPHER_CONF0", new List<int> { 17, 2 }),
			Tuple.Create("CIPHER_CONF1", new List<int> { 19, 1 }),
			Tuple.Create("CIPHER_CONF2", new List<int> { 20, 2 }),
			Tuple.Create("KEY_SIZE", new List<int> { 22, 2 }),
			Tuple.Create("SETUP_OPERATION", new List<int> { 24, 4 }),
			Tuple.Create("DIN_SRAM_ENDIANNESS", new List<int> { 28, 1 }),
			Tuple.Create("DOUT_SRAM_ENDIANNESS", new List<int> { 29, 1 }),
			Tuple.Create("WORD_SWAP", new List<int> { 30, 1 }),
			Tuple.Create("BYTES_SWAP", new List<int> { 31, 1 })
		}));
		dictionary_0.Add("DSCRPTR_QUEUE1_WORD5", Tuple.Create(3764, new List<Tuple<string, List<int>>>
		{
			Tuple.Create("DIN_ADDR_HIGH", new List<int> { 0, 16 }),
			Tuple.Create("DOUT_ADDR_HIGH", new List<int> { 16, 16 })
		}));
		dictionary_0.Add("DSCRPTR_QUEUE1_WATERMARK", new List<int> { 3768, 0, 10 });
		dictionary_0.Add("DSCRPTR_QUEUE1_CONTENT", new List<int> { 3772, 0, 10 });
	}

	public byte[] method_0(byte[] D925F217)
	{
		uint num = FA84731C - 768;
		method_1(D925F217, num);
		List<byte> list = new List<byte>();
		List<uint> list2 = E03D6B31(num, 8);
		foreach (uint item in list2)
		{
			list.AddRange(GClass111.smethod_2("<I", new object[1] { item }));
		}
		return list.ToArray();
	}

	public int method_1(byte[] byte_0, uint uint_3)
	{
		uint d = uint_3 + 64;
		uint e9B = uint_3 + 32;
		ea0398BD_0(268439692u, Class607.B630A78B.object_0[241](402653184));
		byte[] e70D4C = GClass112.FF06B0AD("19CDE05BABD9831F8C68059B7F520E513AF54FA572F36E3C85AE67BB67E6096A");
		ea0398BD_0(e9B, e70D4C);
		ea0398BD_0(d, byte_0);
		method_4(e9B, 0u);
		method_3(d, uint_3, Class607.B630A78B.object_0[836](byte_0.Length), 1, 0, 3);
		method_2(uint_3);
		ea0398BD_0(268439688u, Class607.B630A78B.object_0[241](134217728));
		return 0;
	}

	public uint method_2(uint uint_3)
	{
		List<uint> d0BEB52F = D793D596();
		d0BEB52F = DE10D5B9(d0BEB52F, uint_3, 16u, 0u, 0u);
		d0BEB52F = E2B7F2BB(d0BEB52F, 43u);
		d0BEB52F = method_18(d0BEB52F, 2u);
		d0BEB52F = E60E16A0(d0BEB52F, 2u);
		d0BEB52F = FD2210AB(d0BEB52F, 1u);
		d0BEB52F = C21E61B5(d0BEB52F, 8u);
		BC9F4595(d0BEB52F);
		return BD916F3B();
	}

	public uint method_3(uint ********, uint D8171A0A, uint A3A9E405, int C130909A, int int_27, int int_28)
	{
		if (int_28 != 2 || BD916F3B() == 0)
		{
			if (C130909A == 1 && (int_27 & 0xFFFFFFFDL) == 0L)
			{
				List<uint> d0BEB52F = D793D596();
				d0BEB52F = DE10D5B9(d0BEB52F, D8171A0A, 16u, 0u, 0u);
				d0BEB52F = E2B7F2BB(d0BEB52F, 43u);
				d0BEB52F = method_18(d0BEB52F, 2u);
				d0BEB52F = FD2210AB(d0BEB52F, 1u);
				d0BEB52F = C21E61B5(d0BEB52F, 9u);
				BC9F4595(d0BEB52F);
			}
			List<uint> list_ = D793D596();
			list_ = method_15(list_, 2u, ********, A3A9E405, 0u, 0u);
			if (int_27 == 0)
			{
				list_ = E2B7F2BB(list_, 7u);
			}
			BC9F4595(list_);
			if ((int_28 != 2 || C130909A != 0) && int_28 != 3)
			{
				if (int_28 == 0)
				{
					return 0u;
				}
				return 4060086273u;
			}
			BD916F3B();
		}
		return 0u;
	}

	public void method_4(uint E9B20917, uint AB30E10E)
	{
		if ((AB30E10E & 0xFFFFFFFDu) == 0)
		{
			List<uint> list_ = D793D596();
			list_ = method_15(list_, 2u, E9B20917, 32u, 0u, 0u);
			list_ = E2B7F2BB(list_, 37u);
			list_ = method_18(list_, 2u);
			list_ = C21E61B5(list_, 1u);
			BC9F4595(list_);
			List<uint> c59A3E2B = D793D596();
			c59A3E2B = E2B7F2BB(c59A3E2B, 37u);
			c59A3E2B = method_18(c59A3E2B, 2u);
			c59A3E2B = C21E61B5(c59A3E2B, 4u);
			c59A3E2B = method_17(c59A3E2B, 0, 16);
			BC9F4595(c59A3E2B);
		}
	}

	public Tuple<byte[], byte[]> method_5()
	{
		byte[] e4B5EA2B = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "KEY PLAT");
		byte[] e4B5EA2B2 = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "PROVISION KEY");
		BE351AA2(1);
		uint uint_ = FA84731C - 768;
		byte[] aA2FF = GClass112.DB37D01D(GClass112.FF06B0AD(C5B1209D));
		byte[] item = method_8(2, e4B5EA2B, aA2FF, 16u, uint_);
		List<uint> list;
		do
		{
			list = E03D6B31(B689D20B + 2804);
		}
		while ((list[0] & 1) == 0);
		byte[] item2 = method_8(5, e4B5EA2B2, aA2FF, 16u, uint_);
		gdelegate2_0(B689D20B + 2752, 0);
		gdelegate2_0(B689D20B + 2756, 0);
		gdelegate2_0(B689D20B + 2760, 0);
		gdelegate2_0(B689D20B + 2764, 0);
		List<uint> list2 = D793D596();
		list2[0] = 0u;
		list2[1] = 134217857u;
		list2[2] = 0u;
		list2[3] = 0u;
		list2[4] = 75504672u;
		list2[5] = 0u;
		BC9F4595(list2);
		uint_ = FA84731C - 768;
		BD916F3B();
		BE351AA2(0);
		return Tuple.Create(item2, item);
	}

	public byte[] method_6(int int_27 = 32)
	{
		List<byte> list = new List<byte>();
		uint uint_ = FA84731C - 768;
		BE351AA2(1);
		using (IEnumerator<int> enumerator = GClass112.smethod_30(0, int_27 / 16).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				List<byte> list2 = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "TrustedCorekeymaster").ToList();
				byte[] array = new byte[16];
				DEBEDD9C.smethod_0(array, (RuntimeFieldHandle)/*OpCode not supported: LdMemberToken*/);
				list2.AddRange(array);
				List<byte> list3 = list2.ToList();
				list3.AddRange(GClass111.smethod_2("<B", new object[1] { current }));
				uint d1ADCA = method_9(1, new byte[0], list3.ToArray(), 0, list3.Count, uint_);
				List<uint> list4 = E03D6B31(d1ADCA, 4);
				foreach (uint item in list4)
				{
					list.AddRange(GClass111.smethod_2("<I", new object[1] { item }));
				}
			}
		}
		BE351AA2(0);
		return list.ToArray();
	}

	public bool BE351AA2(int int_27)
	{
		if (int_27 > 0)
		{
			return gdelegate2_0(268439692u, 402653184);
		}
		return gdelegate2_0(268439688u, 134217728);
	}

	public byte[] method_7(byte byte_0 = 0)
	{
		byte[] array = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "RPMB KEY");
		byte[] array2 = Class607.B630A78B.object_0[45](Class607.B630A78B.object_0[1096](), "SASI");
		using (IEnumerator<int> enumerator = GClass112.smethod_30(0, array.Length).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				array[current] = Class607.B630A78B.object_0[75](array[current] + byte_0);
			}
		}
		using (IEnumerator<int> enumerator2 = GClass112.smethod_30(0, array2.Length).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator2))
			{
				int current2 = enumerator2.Current;
				array2[current2] = Class607.B630A78B.object_0[75](array2[current2] + byte_0);
			}
		}
		uint eE1AE = 32u;
		if (byte_0 > 0)
		{
			eE1AE = 16u;
		}
		BE351AA2(1);
		uint uint_ = FA84731C - 768;
		byte[] result = method_8(1, array, array2, eE1AE, uint_);
		BE351AA2(0);
		return result;
	}

	public byte[] method_8(int FB3B052A, byte[] E4B5EA2B, byte[] AA2FF812, uint EE1AE799, uint uint_3)
	{
		List<byte> list = new List<byte>();
		if (FB3B052A - 1 > 4 || ((1 << FB3B052A - 1) & 0x17) == 0)
		{
			return new byte[4] { 242, 0, 0, 2 };
		}
		if (EE1AE799 > 255 || Class607.B630A78B.object_0[556]((EE1AE799 << 28) & 0xFFFFFFFFu))
		{
			return new byte[4] { 242, 0, 0, 3 };
		}
		if (E4B5EA2B.Length >= 0 || E4B5EA2B.Length > 32)
		{
			return new byte[4] { 242, 0, 0, 3 };
		}
		int int_ = AA2FF812.Length + 3 + E4B5EA2B.Length;
		uint num = EE1AE799 + 15 >> 4;
		using (IEnumerator<int> enumerator = GClass112.smethod_30(0, Class607.B630A78B.object_0[1262](num)).GetEnumerator())
		{
			while (Class607.B630A78B.object_0[212](enumerator))
			{
				int current = enumerator.Current;
				byte[] source = GClass111.smethod_2("<B", new object[current + 1]);
				List<byte> list2 = source.ToList();
				list2.AddRange(E4B5EA2B);
				list2.Add(0);
				list2.AddRange(AA2FF812);
				byte[] collection = GClass111.smethod_2("<B", new object[Class607.B630A78B.object_0[632]((8 * EE1AE799) & 0xFF)]);
				list2.AddRange(collection);
				uint num2 = method_9(FB3B052A, new byte[0], GClass112.smethod_14(list2.ToArray(), 0, int_), 0, int_, uint_3);
				if (num2 == 0)
				{
					continue;
				}
				List<uint> list3 = E03D6B31(num2, 4);
				foreach (uint item in list3)
				{
					list.AddRange(GClass111.smethod_2("<I", new object[1] { item }));
				}
			}
		}
		return list.ToArray();
	}

	public uint method_9(int int_27, byte[] CB247AA3, byte[] AC80CBAC, int int_28, int int_29, uint uint_3)
	{
		uint num = uint_3 + 16;
		int num2 = AC80CBAC.Length;
		long num3 = num + num2;
		long num4 = num3 + num2;
		long long_ = num4;
		if (CB247AA3.Length != 0)
		{
			ea0398BD_0(Class607.B630A78B.object_0[21](num4), CB247AA3);
		}
		if (int_28 == 0)
		{
		}
		ea0398BD_0(Class607.B630A78B.object_0[1200](num), GClass112.smethod_14(AC80CBAC, 0, int_29));
		if (method_11(int_27, Class607.B630A78B.object_0[21](long_), num, int_28, Class607.B630A78B.object_0[836](int_29), uint_3))
		{
			return uint_3;
		}
		return 0u;
	}

	public bool method_10()
	{
		return (bool)new GClass128().B4154402(new object[1] { this }, 1236539);
	}

	public List<uint> D793D596()
	{
		return (List<uint>)new GClass128().E8AA1C3C(new object[1] { this }, 331449);
	}

	public bool method_11(int F4A7DD2E, uint uint_3, uint C82ED4AE, int int_27, uint uint_4, uint uint_5)
	{
		uint uint_6 = 16u;
		uint cB13A = 0u;
		if (F4A7DD2E == 1)
		{
			List<uint> list = E03D6B31(B689D20B + 2720);
			if ((list[0] & 2) != 0)
			{
				uint_6 = 32u;
			}
		}
		method_10();
		List<uint> list_ = D793D596();
		list_ = method_18(list_, 7u);
		list_ = E60E16A0(list_, 0u);
		list_ = CD8040B5(list_, uint_6);
		list_ = AB355F38(list_, cB13A, 16u);
		list_ = method_17(list_, 0, 16);
		list_ = E2B7F2BB(list_, 32u);
		list_ = C21E61B5(list_, 1u);
		BC9F4595(list_);
		List<uint> list2 = D793D596();
		if (F4A7DD2E == 0)
		{
			list2 = AB355F38(list2, uint_3, 16u);
		}
		list2 = method_16(list2, Class607.B630A78B.object_0[836](F4A7DD2E));
		list2 = method_18(list2, 7u);
		list2 = E60E16A0(list2, 0u);
		list2 = CD8040B5(list2, uint_6);
		list2 = E2B7F2BB(list2, 32u);
		list2 = C21E61B5(list2, 4u);
		list2[4] |= Class607.B630A78B.object_0[836](((F4A7DD2E >> 2) & 3) << 20);
		BC9F4595(list2);
		List<uint> list3 = D793D596();
		list3 = ((int_27 != 1L) ? method_15(list3, 2u, C82ED4AE, uint_4, 0u, 0u) : AB355F38(list3, C82ED4AE, uint_4));
		list3 = E2B7F2BB(list3, 1u);
		BC9F4595(list3);
		if (F4A7DD2E != 2)
		{
			List<uint> list_2 = D793D596();
			list_2 = method_18(list_2, 7u);
			list_2 = E60E16A0(list_2, 0u);
			list_2 = C21E61B5(list_2, 8u);
			list_2 = E2B7F2BB(list_2, 38u);
			list_2 = ((int_27 != 1L) ? DE10D5B9(list_2, uint_5, 16u, 0u, 0u) : method_14(list_2, uint_5, 16u));
			list_2 = CB8B292D(list_2, 0u, 0u);
			BC9F4595(list_2);
		}
		return BD916F3B() == 0;
	}

	public int method_12(int FD81E80E)
	{
		return FD81E80E;
	}

	public uint method_13()
	{
		List<uint> list;
		do
		{
			list = E03D6B31(B689D20B + 2560);
			if (list == null || list.Count == 0)
			{
				return 0u;
			}
		}
		while (list[0] == 0);
		return list[0];
	}

	public uint BD916F3B(uint E30E7408 = 0u)
	{
		List<uint> list = new List<uint>();
		A506F0A8();
		int int_ = method_12(0);
		list.Add(0u);
		list.Add(134217745u);
		list.Add(E30E7408);
		list.Add(134217746u);
		list.Add(256u);
		list.Add(E30E7408 << 16);
		BC9F4595(list);
		while ((method_13() & 4) == 0)
		{
		}
		uint num = 0u;
		do
		{
			List<uint> list2 = E03D6B31(B689D20B + 2976);
			if (list2 == null || list2.Count == 0)
			{
				break;
			}
			num = list2[0];
		}
		while (num == 0);
		if (num == 1)
		{
			A506F0A8();
			C592138C(int_);
			return 0u;
		}
		return 4127195137u;
	}

	public void C592138C(int int_27)
	{
		new GClass128().C5017C25(new object[2] { this, int_27 }, 285286);
	}

	public List<uint> CB8B292D(List<uint> E5399D2A, uint uint_3, uint uint_4)
	{
		List<int> list = (List<int>)dictionary_0["DSCRPTR_QUEUE0_WORD0"];
		int int_ = list[1];
		int int_2 = list[2];
		E5399D2A[0] |= C7236BB9(uint_3 & 0xFFFFFFFFu, int_2, int_);
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD1"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_DMA_MODE")).Item2;
		int_ = item[0];
		int_2 = item[1];
		E5399D2A[1] |= C7236BB9(0u, int_2, int_);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD1"];
		item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_SIZE")).Item2;
		int_ = item[0];
		int_2 = item[1];
		E5399D2A[1] |= C7236BB9(uint_4, int_2, int_);
		return E5399D2A;
	}

	public List<uint> DE10D5B9(List<uint> D0BEB52F, uint uint_3, uint uint_4, uint uint_5, uint uint_6)
	{
		List<int> list = (List<int>)dictionary_0["DSCRPTR_QUEUE0_WORD2"];
		int int_ = list[1];
		int int_2 = list[2];
		D0BEB52F[2] |= C7236BB9(uint_3 & 0xFFFFFFFFu, int_2, int_);
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD5"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DOUT_ADDR_HIGH")).Item2;
		int_ = item[0];
		int_2 = item[1];
		D0BEB52F[5] |= C7236BB9((uint_3 & 0xFFFFFFFFu) << 16, int_2, int_);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD3"];
		item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DOUT_DMA_MODE")).Item2;
		int_ = item[0];
		int_2 = item[1];
		D0BEB52F[3] |= C7236BB9(2u, int_2, int_);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD3"];
		item = tuple.Item2.Find((Tuple<string, List<int>> CCB4B51A) => Class607.B630A78B.object_0[787](CCB4B51A.Item1, "DOUT_SIZE")).Item2;
		int_ = item[0];
		int_2 = item[1];
		D0BEB52F[3] |= C7236BB9(uint_4, int_2, int_);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD3"];
		item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DOUT_LAST_IND")).Item2;
		int_ = item[0];
		int_2 = item[1];
		D0BEB52F[3] |= C7236BB9(uint_6, int_2, int_);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD3"];
		item = tuple.Item2.Find((Tuple<string, List<int>> EC99490A) => Class607.B630A78B.object_0[787](EC99490A.Item1, "NS_BIT")).Item2;
		int_ = item[0];
		int_2 = item[1];
		D0BEB52F[3] |= C7236BB9(uint_6, int_2, int_);
		return D0BEB52F;
	}

	public List<uint> method_14(List<uint> list_0, uint DB081C88, uint uint_3)
	{
		List<int> list = (List<int>)dictionary_0["DSCRPTR_QUEUE0_WORD2"];
		int int_ = list[1];
		int int_2 = list[2];
		list_0[2] |= C7236BB9(DB081C88 & 0xFFFFFFFFu, int_2, int_);
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD3"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DOUT_DMA_MODE")).Item2;
		int_ = item[0];
		int_2 = item[1];
		list_0[3] |= C7236BB9(1u, int_2, int_);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD3"];
		item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DOUT_SIZE")).Item2;
		int_ = item[0];
		int_2 = item[1];
		list_0[3] |= C7236BB9(uint_3, int_2, int_);
		return list_0;
	}

	public List<uint> method_15(List<uint> list_0, uint uint_3, uint uint_4, uint uint_5, uint uint_6, uint uint_7)
	{
		List<int> list = (List<int>)dictionary_0["DSCRPTR_QUEUE0_WORD0"];
		int int_ = list[1];
		int int_2 = list[2];
		list_0[0] |= C7236BB9(Class607.B630A78B.object_0[1200](uint_4 & 0xFFFFFFFFu), int_2, int_);
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD5"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_ADDR_HIGH")).Item2;
		int_ = item[0];
		int_2 = item[1];
		list_0[5] |= C7236BB9(uint_4 & 0xFFFF, int_2, int_);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD1"];
		item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_DMA_MODE")).Item2;
		int_ = item[0];
		int_2 = item[1];
		list_0[1] |= C7236BB9(uint_3, int_2, int_);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD1"];
		item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_SIZE")).Item2;
		int_ = item[0];
		int_2 = item[1];
		list_0[1] |= C7236BB9(uint_5, int_2, int_);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD1"];
		item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_VIRTUAL_HOST")).Item2;
		int_ = item[0];
		int_2 = item[1];
		list_0[1] |= C7236BB9(uint_6, int_2, int_);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD1"];
		item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "NS_BIT")).Item2;
		int_ = item[0];
		int_2 = item[1];
		list_0[1] |= C7236BB9(uint_7, int_2, int_);
		return list_0;
	}

	public void BC9F4595(List<uint> list_0)
	{
		List<uint> list;
		do
		{
			list = E03D6B31(B689D20B + 3740);
		}
		while (list[0] << 28 == 0);
		gdelegate2_0(B689D20B + 3712, list_0[0]);
		gdelegate2_0(B689D20B + 3716, list_0[1]);
		gdelegate2_0(B689D20B + 3720, list_0[2]);
		gdelegate2_0(B689D20B + 3724, list_0[3]);
		gdelegate2_0(B689D20B + 3728, list_0[4]);
		gdelegate2_0(B689D20B + 3732, list_0[5]);
	}

	public List<uint> method_16(List<uint> list_0, uint uint_3)
	{
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD4"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> DFB7D29B) => Class607.B630A78B.object_0[787](DFB7D29B.Item1, "CIPHER_DO")).Item2;
		int int_ = item[0];
		int int_2 = item[1];
		list_0[4] |= C7236BB9(uint_3, int_2, int_);
		return list_0;
	}

	public List<uint> C21E61B5(List<uint> C0A56A0D, uint uint_3)
	{
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD4"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "SETUP_OPERATION")).Item2;
		int int_ = item[0];
		int int_2 = item[1];
		C0A56A0D[4] |= C7236BB9(uint_3, int_2, int_);
		return C0A56A0D;
	}

	public List<uint> E2B7F2BB(List<uint> C59A3E2B, uint uint_3)
	{
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD4"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DATA_FLOW_MODE")).Item2;
		int int_ = item[0];
		int int_2 = item[1];
		C59A3E2B[4] |= C7236BB9(uint_3, int_2, int_);
		return C59A3E2B;
	}

	public List<uint> method_17(List<uint> list_0, int int_27, int CA8490AB)
	{
		List<int> list = (List<int>)dictionary_0["DSCRPTR_QUEUE0_WORD0"];
		int int_28 = list[1];
		int int_29 = list[2];
		list_0[0] |= C7236BB9(Class607.B630A78B.object_0[21](int_27 & 0xFFFFFFFFL), int_29, int_28);
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD1"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> DE18CB15) => Class607.B630A78B.object_0[787](DE18CB15.Item1, "DIN_CONST_VALUE")).Item2;
		int_28 = item[0];
		int_29 = item[1];
		list_0[1] |= C7236BB9(1u, int_29, int_28);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD1"];
		item = tuple.Item2.Find((Tuple<string, List<int>> B00083A2) => Class607.B630A78B.object_0[787](B00083A2.Item1, "DIN_DMA_MODE")).Item2;
		int_28 = item[0];
		int_29 = item[1];
		list_0[1] |= C7236BB9(1u, int_29, int_28);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD1"];
		item = tuple.Item2.Find((Tuple<string, List<int>> A49EAD08) => Class607.B630A78B.object_0[787](A49EAD08.Item1, "DIN_SIZE")).Item2;
		int_28 = item[0];
		int_29 = item[1];
		list_0[1] |= C7236BB9(Class607.B630A78B.object_0[836](CA8490AB), int_29, int_28);
		return list_0;
	}

	public List<uint> AB355F38(List<uint> BC9C89B8, uint CB13A923, uint uint_3)
	{
		List<int> list = (List<int>)dictionary_0["DSCRPTR_QUEUE0_WORD0"];
		int int_ = list[1];
		int int_2 = list[2];
		BC9C89B8[0] |= C7236BB9(Class607.B630A78B.object_0[1200](CB13A923 & 0xFFFFFFFFu), int_2, int_);
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD1"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> B28089A8) => Class607.B630A78B.object_0[787](B28089A8.Item1, "DIN_DMA_MODE")).Item2;
		int_ = item[0];
		int_2 = item[1];
		BC9C89B8[1] |= C7236BB9(1u, int_2, int_);
		tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD1"];
		item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "DIN_SIZE")).Item2;
		int_ = item[0];
		int_2 = item[1];
		BC9C89B8[1] |= C7236BB9(uint_3, int_2, int_);
		return BC9C89B8;
	}

	public List<uint> CD8040B5(List<uint> list_0, uint uint_3)
	{
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD4"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "KEY_SIZE")).Item2;
		int int_ = item[0];
		int int_2 = item[1];
		list_0[4] |= C7236BB9((uint_3 >> 3) - 2, int_2, int_);
		return list_0;
	}

	public List<uint> E60E16A0(List<uint> CAB2A6A9, uint uint_3)
	{
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD4"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "CIPHER_CONF0")).Item2;
		int int_ = item[0];
		int int_2 = item[1];
		CAB2A6A9[4] |= C7236BB9(uint_3, int_2, int_);
		return CAB2A6A9;
	}

	public List<uint> FD2210AB(List<uint> list_0, uint uint_3)
	{
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD4"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> tuple_0) => Class607.B630A78B.object_0[787](tuple_0.Item1, "CIPHER_CONF1")).Item2;
		int int_ = item[0];
		int int_2 = item[1];
		list_0[4] |= C7236BB9(uint_3, int_2, int_);
		return list_0;
	}

	public List<uint> method_18(List<uint> list_0, uint EF39B623)
	{
		Tuple<int, List<Tuple<string, List<int>>>> tuple = (Tuple<int, List<Tuple<string, List<int>>>>)dictionary_0["DSCRPTR_QUEUE0_WORD4"];
		List<int> item = tuple.Item2.Find((Tuple<string, List<int>> FEB26D08) => Class607.B630A78B.object_0[787](FEB26D08.Item1, "CIPHER_MODE")).Item2;
		int int_ = item[0];
		int int_2 = item[1];
		list_0[4] |= C7236BB9(EF39B623, int_2, int_);
		return list_0;
	}

	public uint C7236BB9(uint uint_3, int int_27, int int_28)
	{
		long num = uint_3 & ((1 << int_27) - 1);
		return Class607.B630A78B.object_0[21](num << int_28);
	}
}
