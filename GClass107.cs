using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.CompilerServices;

public class GClass107
{
	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string C80A329B;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private List<string> E7AC3303;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private bool BAB88383;

	public string String_0
	{
		[CompilerGenerated]
		get
		{
			return C80A329B;
		}
		[CompilerGenerated]
		internal set
		{
			C80A329B = value;
		}
	}

	public List<string> AC2ABAA9
	{
		[CompilerGenerated]
		get
		{
			return E7AC3303;
		}
		[CompilerGenerated]
		internal set
		{
			E7AC3303 = value;
		}
	}

	public bool Boolean_0
	{
		[CompilerGenerated]
		get
		{
			return BAB88383;
		}
		[CompilerGenerated]
		internal set
		{
			BAB88383 = value;
		}
	}

	public GClass107()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
