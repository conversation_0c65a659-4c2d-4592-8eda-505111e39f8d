using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using ICSharpCode.SharpZipLib.Zip;

public static class GClass112
{
	public delegate GClass51.GStruct57 EABA4BBE(ulong ulong_0, ulong ulong_1, string string_0, string A024AFA0 = "");

	public delegate void D3A6F5AB(bool bool_0, string string_0);

	public delegate void GDelegate32(bool bool_0);

	public delegate void E39CAE0B(string B8ABF2AA, EF1F389C ef1F389C_0 = EF1F389C.Word, bool BB24BF3C = true, bool bool_0 = false);

	[Serializable]
	[CompilerGenerated]
	private sealed class ACACD9B8
	{
		public static readonly ACACD9B8 _003C_003E9 = new ACACD9B8();

		public static Func<int, bool> _003C_003E9__48_0;

		public ACACD9B8()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal bool F9278625(int A29DE41C)
		{
			return A29DE41C % 2 == 0;
		}
	}

	[CompilerGenerated]
	private sealed class Class65
	{
		public string string_0;

		public Class65()
		{
			Class607.B630A78B.object_0[571](this);
		}

		internal byte CDB27120(int int_0)
		{
			return Class607.B630A78B.object_0[276](Class607.B630A78B.object_0[31](string_0, int_0, 2), 16);
		}
	}

	[CompilerGenerated]
	private sealed class BC80E03C : IAsyncStateMachine
	{
		public int A23B363C;

		public AsyncTaskMethodBuilder<long> ACAA6931;

		public HttpClient httpClient_0;

		private int C3127CA2;

		private HttpResponseMessage httpResponseMessage_0;

		private string D3B83138;

		private HttpResponseMessage httpResponseMessage_1;

		private string string_0;

		private string string_1;

		private Exception exception_0;

		private TaskAwaiter<HttpResponseMessage> FDB27213;

		private TaskAwaiter<string> CB214AB9;

		public BC80E03C()
		{
			Class607.B630A78B.object_0[571](this);
		}

		private void AFB8C50C()
		{
			int num = A23B363C;
			long result;
			try
			{
				if ((uint)num <= 1u)
				{
				}
				try
				{
					TaskAwaiter<string> awaiter;
					if (num != 0)
					{
						if (num != 1)
						{
							C3127CA2 = 0;
							goto IL_00eb;
						}
						awaiter = CB214AB9;
						CB214AB9 = default(TaskAwaiter<string>);
						num = -1;
						A23B363C = -1;
						goto IL_005f;
					}
					TaskAwaiter<HttpResponseMessage> awaiter2 = FDB27213;
					FDB27213 = default(TaskAwaiter<HttpResponseMessage>);
					num = -1;
					A23B363C = -1;
					goto IL_012c;
					IL_00cb:
					httpResponseMessage_0 = null;
					D3B83138 = null;
					C3127CA2++;
					goto IL_00eb;
					IL_00eb:
					if (C3127CA2 < 5)
					{
						awaiter2 = Class607.B630A78B.object_0[616](httpClient_0, D09F0D3B.smethod_5(1223916u), null).GetAwaiter();
						if (!awaiter2.IsCompleted)
						{
							num = 0;
							A23B363C = 0;
							FDB27213 = awaiter2;
							BC80E03C stateMachine = this;
							ACAA6931.AwaitUnsafeOnCompleted(ref awaiter2, ref stateMachine);
							return;
						}
						goto IL_012c;
					}
					throw Class607.B630A78B.object_0[778]("Failed to receiving TimeStamp, check your internet connection or contact with developer");
					IL_012c:
					httpResponseMessage_1 = awaiter2.GetResult();
					httpResponseMessage_0 = httpResponseMessage_1;
					httpResponseMessage_1 = null;
					awaiter = Class607.B630A78B.object_0[452](Class607.B630A78B.object_0[1232](httpResponseMessage_0)).GetAwaiter();
					if (!awaiter.IsCompleted)
					{
						num = 1;
						A23B363C = 1;
						CB214AB9 = awaiter;
						BC80E03C stateMachine = this;
						ACAA6931.AwaitUnsafeOnCompleted(ref awaiter, ref stateMachine);
						return;
					}
					goto IL_005f;
					IL_005f:
					string_0 = awaiter.GetResult();
					D3B83138 = string_0;
					string_0 = null;
					if (Class607.B630A78B.object_0[866](httpResponseMessage_0) != HttpStatusCode.OK)
					{
						goto IL_00cb;
					}
					string_1 = B904D2B6.EC2264AF(D3B83138);
					if (!smethod_3(string_1))
					{
						string_1 = null;
						goto IL_00cb;
					}
					result = Class607.B630A78B.object_0[771](string_1, 16);
				}
				catch (Exception ex)
				{
					exception_0 = ex;
					throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[720](" Error: ", Class607.B630A78B.object_0[1160](exception_0)));
				}
			}
			catch (Exception ex)
			{
				A23B363C = -2;
				ACAA6931.SetException(ex);
				return;
			}
			A23B363C = -2;
			ACAA6931.SetResult(result);
		}

		void IAsyncStateMachine.MoveNext()
		{
			//ILSpy generated this explicit interface implementation from .override directive in AFB8C50C
			this.AFB8C50C();
		}

		[DebuggerHidden]
		private void B58E933C(IAsyncStateMachine stateMachine)
		{
		}

		void IAsyncStateMachine.SetStateMachine(IAsyncStateMachine stateMachine)
		{
			//ILSpy generated this explicit interface implementation from .override directive in B58E933C
			this.B58E933C(stateMachine);
		}
	}

	[CompilerGenerated]
	private sealed class A82C7207 : IAsyncStateMachine
	{
		public int int_0;

		public AsyncTaskMethodBuilder<string> asyncTaskMethodBuilder_0;

		public HttpClient A72B0C8A;

		private int int_1;

		private string B435AF2B;

		private Exception exception_0;

		private TaskAwaiter<string> C687608C;

		public A82C7207()
		{
			Class607.B630A78B.object_0[571](this);
		}

		private void C030C0AB()
		{
			int num = int_0;
			string result;
			try
			{
				if (num == 0)
				{
				}
				try
				{
					TaskAwaiter<string> awaiter;
					if (num != 0)
					{
						int_1 = 0;
						if (int_1 >= 5)
						{
							throw Class607.B630A78B.object_0[778]("check your internet connection or contact with developer");
						}
						awaiter = Class607.B630A78B.object_0[273](A72B0C8A, D09F0D3B.smethod_5(1234904u)).GetAwaiter();
						if (!awaiter.IsCompleted)
						{
							num = 0;
							int_0 = 0;
							C687608C = awaiter;
							A82C7207 stateMachine = this;
							asyncTaskMethodBuilder_0.AwaitUnsafeOnCompleted(ref awaiter, ref stateMachine);
							return;
						}
					}
					else
					{
						awaiter = C687608C;
						C687608C = default(TaskAwaiter<string>);
						num = -1;
						int_0 = -1;
					}
					B435AF2B = awaiter.GetResult();
					result = B435AF2B;
				}
				catch (Exception ex)
				{
					exception_0 = ex;
					throw Class607.B630A78B.object_0[778](Class607.B630A78B.object_0[720]("Error: ", Class607.B630A78B.object_0[1160](exception_0)));
				}
			}
			catch (Exception ex)
			{
				int_0 = -2;
				asyncTaskMethodBuilder_0.SetException(ex);
				return;
			}
			int_0 = -2;
			asyncTaskMethodBuilder_0.SetResult(result);
		}

		void IAsyncStateMachine.MoveNext()
		{
			//ILSpy generated this explicit interface implementation from .override directive in C030C0AB
			this.C030C0AB();
		}

		[DebuggerHidden]
		private void EE1DF683(IAsyncStateMachine AC24BB0A)
		{
		}

		void IAsyncStateMachine.SetStateMachine(IAsyncStateMachine AC24BB0A)
		{
			//ILSpy generated this explicit interface implementation from .override directive in EE1DF683
			this.EE1DF683(AC24BB0A);
		}
	}

	[CompilerGenerated]
	private sealed class B29F5C8B : IEnumerator<int>, IEnumerable<int>, IEnumerator, IDisposable, IEnumerable
	{
		private int CD2AB2B2;

		private int BE103824;

		private int C816EFAE;

		private int int_0;

		public int int_1;

		private int C897EC3A;

		public int int_2;

		private int B89219A3;

		public int D2BB0692;

		int IEnumerator<int>.Current
		{
			[DebuggerHidden]
			get
			{
				return BE103824;
			}
		}

		object IEnumerator.Current
		{
			[DebuggerHidden]
			get
			{
				return BE103824;
			}
		}

		[DebuggerHidden]
		public B29F5C8B(int int_3)
		{
			Class607.B630A78B.object_0[571](this);
			CD2AB2B2 = int_3;
			C816EFAE = Class607.B630A78B.object_0[112]();
		}

		[DebuggerHidden]
		void IDisposable.Dispose()
		{
		}

		private bool AA8C57B1()
		{
			switch (CD2AB2B2)
			{
			default:
				return false;
			case 1:
				CD2AB2B2 = -1;
				int_0 += B89219A3;
				break;
			case 0:
				CD2AB2B2 = -1;
				if (B89219A3 == 0)
				{
					throw Class607.B630A78B.object_0[1005]("step");
				}
				break;
			}
			if ((B89219A3 <= 0 || int_0 >= C897EC3A) && (B89219A3 >= 0 || int_0 <= C897EC3A))
			{
				return false;
			}
			BE103824 = int_0;
			CD2AB2B2 = 1;
			return true;
		}

		bool IEnumerator.MoveNext()
		{
			//ILSpy generated this explicit interface implementation from .override directive in AA8C57B1
			return this.AA8C57B1();
		}

		[DebuggerHidden]
		void IEnumerator.Reset()
		{
			throw Class607.B630A78B.object_0[670]();
		}

		[DebuggerHidden]
		private IEnumerator<int> DDA40D8A()
		{
			B29F5C8B b29F5C8B;
			if (CD2AB2B2 == -2 && C816EFAE == Class607.B630A78B.object_0[112]())
			{
				CD2AB2B2 = 0;
				b29F5C8B = this;
			}
			else
			{
				b29F5C8B = new B29F5C8B(0);
			}
			b29F5C8B.int_0 = int_1;
			b29F5C8B.C897EC3A = int_2;
			b29F5C8B.B89219A3 = D2BB0692;
			return b29F5C8B;
		}

		IEnumerator<int> IEnumerable<int>.GetEnumerator()
		{
			//ILSpy generated this explicit interface implementation from .override directive in DDA40D8A
			return this.DDA40D8A();
		}

		[DebuggerHidden]
		IEnumerator IEnumerable.GetEnumerator()
		{
			return DDA40D8A();
		}
	}

	public const string D33A3887 = "2025.05.13";

	public static bool E31AABAB;

	public static GClass109 B904D2B6 = new GClass109();

	public static bool FD237E8B = false;

	public static Dictionary<string, string> dictionary_0 = new Dictionary<string, string>();

	public static GClass34 C78DEB29;

	public static string A6360694 => Class607.B630A78B.object_0[321](Class607.B630A78B.object_0[1164](Class607.B630A78B.object_0[858]()));

	public static void FA89759F(this ZipOutputStream DF0DE904, string C5AB4112)
	{
		ZipEntry val = Class607.B630A78B.object_0[525](Class607.B630A78B.object_0[1055](C5AB4112));
		Class607.B630A78B.object_0[418](val, Class607.B630A78B.object_0[402]());
		Class607.B630A78B.object_0[530](DF0DE904, val);
		using (FileStream a5864A = Class607.B630A78B.object_0[123](C5AB4112))
		{
			Class607.B630A78B.object_0[115](a5864A, (Stream)(object)DF0DE904);
		}
		Class607.B630A78B.object_0[1100](DF0DE904);
	}

	public static long D085B880(HttpClient httpClient_0)
	{
		return (long)new GClass128().EF8D5E3B(new object[1] { httpClient_0 }, 11173162);
	}

	[DebuggerStepThrough]
	[AsyncStateMachine(typeof(BC80E03C))]
	public static Task<long> smethod_0(HttpClient httpClient_0)
	{
		return (Task<long>)new GClass128().E8AA1C3C(new object[1] { httpClient_0 }, 1272488);
	}

	[AsyncStateMachine(typeof(A82C7207))]
	[DebuggerStepThrough]
	public static Task<string> FCAC8C37(HttpClient B4A88C16)
	{
		return (Task<string>)new GClass128().E6A33692(new object[1] { B4A88C16 }, 1286983);
	}

	public static string smethod_1()
	{
		return (string)new GClass128().method_68(null, 22418368);
	}

	public static bool EABD3531(string string_0)
	{
		if (Class607.B630A78B.object_0[217](string_0))
		{
			return false;
		}
		string string_1 = "^[^@\\s]+@[^@\\s]+\\.[^@\\s]+$";
		return Class607.B630A78B.object_0[575](string_0, string_1);
	}

	public static string[] smethod_2(string string_0, string string_1)
	{
		List<string> list = new List<string>();
		FileInfo[] array = Class607.B630A78B.object_0[98](Class607.B630A78B.object_0[222](string_0));
		FileInfo[] array2 = array;
		foreach (FileInfo fileInfo in array2)
		{
			Match eE1499A = Class607.B630A78B.object_0[498](Class607.B630A78B.object_0[1002](string_1), Class607.B630A78B.object_0[738](fileInfo));
			if (Class607.B630A78B.object_0[52](Class607.B630A78B.object_0[1090](eE1499A)) > 0 && Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[896](Class607.B630A78B.object_0[1212](Class607.B630A78B.object_0[1090](eE1499A), 0)), Class607.B630A78B.object_0[738](fileInfo)))
			{
				list.Add(Class607.B630A78B.object_0[1093](fileInfo));
			}
		}
		return list.ToArray();
	}

	public static bool smethod_3(string string_0)
	{
		if (Class607.B630A78B.object_0[1205](string_0))
		{
			return false;
		}
		if (Class607.B630A78B.object_0[586](string_0, "0x", StringComparison.OrdinalIgnoreCase))
		{
			string_0 = Class607.B630A78B.object_0[1211](string_0, 2);
		}
		string text = string_0;
		int num = 0;
		while (true)
		{
			if (num < Class607.B630A78B.object_0[342](text))
			{
				char c = Class607.B630A78B.object_0[1092](text, num);
				if ((c < '0' || c > '9') && (c < 'A' || c > 'F') && (c < 'a' || c > 'f'))
				{
					break;
				}
				num++;
				continue;
			}
			return true;
		}
		return false;
	}

	public static bool D2A17E03()
	{
		return (bool)new GClass128().method_323(null, 22563349);
	}

	private static byte[] BD34700B(string B12521B5)
	{
		return (byte[])new GClass128().AD84C1A2(new object[1] { B12521B5 }, 377666);
	}

	public static void FA1A7E1F()
	{
		new GClass128().E6A33692(null, 583498);
	}

	private static byte[] smethod_4(byte[] BD96EAB0)
	{
		return (byte[])new GClass128().method_323(new object[1] { BD96EAB0 }, 22984613);
	}

	public static DialogResult E2008A2D(this Form form_0, Form A61D622B)
	{
		DialogResult result = DialogResult.None;
		try
		{
			using E528B706 e528B = new E528B706(form_0, A61D622B);
			Class607.B630A78B.object_0[1222](A61D622B, FormStartPosition.CenterParent);
			Class607.B630A78B.object_0[359](e528B, Class607.B630A78B.object_0[293](form_0));
			Class607.B630A78B.object_0[604](e528B);
			DialogResult dialogResult = Class607.B630A78B.object_0[281](A61D622B, e528B);
			Class607.B630A78B.object_0[199](e528B);
			result = dialogResult;
			return result;
		}
		catch
		{
		}
		finally
		{
			Class607.B630A78B.object_0[420]();
		}
		return result;
	}

	public static string AD93E63A(string string_0)
	{
		if (Class607.B630A78B.object_0[787](Class607.B630A78B.object_0[1050](string_0), "none"))
		{
			string_0 = "0";
		}
		long DB1A9A9E = Class607.B630A78B.object_0[771](string_0, 16);
		return Class607.B630A78B.object_0[125](ref DB1A9A9E);
	}

	public static string F4B2F6BE(long long_0)
	{
		if (long_0 < 0L)
		{
			return "";
		}
		string[] array = new string[5] { "bytes", "KB", "MB", "GB", "TB" };
		double num = long_0;
		int num2 = 0;
		while (num >= 1024.0 && num2 < array.Length - 1)
		{
			num /= 1024.0;
			num2++;
		}
		return Class607.B630A78B.object_0[1105]("{0:N2} {1}", num, array[num2]);
	}

	public static string F0AC4400(uint FF2DCC38)
	{
		return Class607.B630A78B.object_0[993](ref FF2DCC38, "X");
	}

	public static bool smethod_5(byte[] byte_0, byte[] byte_1, int int_0)
	{
		int num = 0;
		while (true)
		{
			if (num < byte_1.Length)
			{
				if (int_0 + num >= byte_0.Length || byte_0[int_0 + num] != byte_1[num])
				{
					break;
				}
				num++;
				continue;
			}
			return true;
		}
		return false;
	}

	public static string smethod_6(string D317D19A, string string_0)
	{
		while (D317D19A != null && string_0 != null && Class607.B630A78B.object_0[247](D317D19A, string_0))
		{
			D317D19A = Class607.B630A78B.object_0[31](D317D19A, 0, Class607.B630A78B.object_0[342](D317D19A) - Class607.B630A78B.object_0[342](string_0));
		}
		return D317D19A;
	}

	public static byte[] smethod_7(byte[] F6AB2599, byte byte_0)
	{
		List<byte> list = F6AB2599.ToList();
		while (list[list.Count - 1] == byte_0)
		{
			if (list[list.Count - 1] == byte_0)
			{
				list.RemoveAt(list.Count - 1);
			}
		}
		return list.ToArray();
	}

	public static string smethod_8(double B10F5480)
	{
		string[] array = new string[5] { "B", "KB", "MB", "GB", "TB" };
		int num = 0;
		while (B10F5480 >= 1024.0 && num < array.Length - 1)
		{
			num++;
			B10F5480 /= 1024.0;
		}
		return Class607.B630A78B.object_0[1105]("{0:0.##} {1}", B10F5480, array[num]);
	}

	public static List<byte[]> smethod_9(byte[] byte_0, byte byte_1)
	{
		List<byte[]> list = new List<byte[]>();
		List<byte> list2 = new List<byte>();
		foreach (byte b in byte_0)
		{
			if (b == byte_1)
			{
				list.Add(list2.ToArray());
				list2.Clear();
			}
			else
			{
				list2.Add(b);
			}
		}
		if (list2.Count > 0)
		{
			list.Add(list2.ToArray());
		}
		return list;
	}

	public static byte[][] smethod_10(byte[] BDA253B9, byte[] byte_0)
	{
		List<byte[]> list = new List<byte[]>();
		int num = 0;
		byte[] array;
		for (int i = 0; i < BDA253B9.Length; i++)
		{
			if (smethod_5(BDA253B9, byte_0, i))
			{
				array = new byte[i - num];
				Class607.B630A78B.object_0[242](BDA253B9, num, array, 0, array.Length);
				list.Add(array);
				num = i + byte_0.Length;
				i += byte_0.Length - 1;
			}
		}
		array = new byte[BDA253B9.Length - num];
		Class607.B630A78B.object_0[242](BDA253B9, num, array, 0, array.Length);
		list.Add(array);
		return list.ToArray();
	}

	public static byte[] smethod_11(this byte[] A29AA325)
	{
		int num = A29AA325.Length;
		while (num > 0 && A29AA325[num - 1] == 0)
		{
			num--;
		}
		byte[] array = new byte[num];
		Class607.B630A78B.object_0[242](A29AA325, 0, array, 0, num);
		return array;
	}

	public static string smethod_12(this string E036E29D, int int_0)
	{
		if (Class607.B630A78B.object_0[1205](E036E29D))
		{
			return Class607.B630A78B.object_0[244]();
		}
		return (Class607.B630A78B.object_0[342](E036E29D) <= int_0) ? E036E29D : Class607.B630A78B.object_0[1211](E036E29D, Class607.B630A78B.object_0[342](E036E29D) - int_0);
	}

	public static bool C9302A2B(byte[] D83B632F, byte[] D507913F, int int_0)
	{
		if (D507913F.Length + int_0 > D83B632F.Length)
		{
			return false;
		}
		int num = 0;
		while (true)
		{
			if (num < D507913F.Length)
			{
				if (D507913F[num] != D83B632F[num + int_0])
				{
					break;
				}
				num++;
				continue;
			}
			return true;
		}
		return false;
	}

	public static int smethod_13(byte[] C6986920, byte[] byte_0, int AE9B6335)
	{
		int num = AE9B6335;
		while (true)
		{
			if (num <= C6986920.Length - byte_0.Length)
			{
				if (C9302A2B(C6986920, byte_0, num))
				{
					break;
				}
				num++;
				continue;
			}
			return -1;
		}
		return num;
	}

	public static bool F02F0138(this string string_0)
	{
		if (Class607.B630A78B.object_0[1205](string_0))
		{
			return false;
		}
		int num = 0;
		while (true)
		{
			if (num < Class607.B630A78B.object_0[342](string_0))
			{
				char c = Class607.B630A78B.object_0[1092](string_0, num);
				if (!Class607.B630A78B.object_0[996](c) && c != '.')
				{
					break;
				}
				num++;
				continue;
			}
			return true;
		}
		return false;
	}

	public static void E209C304(string B8A8131C)
	{
		try
		{
			if (!Class607.B630A78B.object_0[488](B8A8131C))
			{
				Class607.B630A78B.object_0[643](B8A8131C);
			}
		}
		catch
		{
		}
	}

	public static byte[] C3BE5516(Stream B1948E18, int int_0)
	{
		byte[] array = new byte[int_0];
		Class607.B630A78B.object_0[53](B1948E18, array, 0, int_0);
		return array;
	}

	public static byte[] smethod_14(byte[] AA23FA36, int AB857812, int int_0)
	{
		if (AA23FA36 == null)
		{
			return null;
		}
		if (int_0 > AA23FA36.Length - AB857812)
		{
			int_0 = AA23FA36.Length - AB857812;
		}
		if (int_0 < 0)
		{
			int_0 = 0;
		}
		byte[] array = new byte[int_0];
		Class607.B630A78B.object_0[242](AA23FA36, AB857812, array, 0, int_0);
		return array;
	}

	public static object smethod_15(byte[] byte_0, Type type_0)
	{
		int num = Class607.B630A78B.object_0[146](type_0);
		if (num > byte_0.Length)
		{
			return null;
		}
		IntPtr intPtr = Class607.B630A78B.object_0[263](num);
		Class607.B630A78B.object_0[150](byte_0, 0, intPtr, num);
		object result = Class607.B630A78B.object_0[378](intPtr, type_0);
		Class607.B630A78B.object_0[366](intPtr);
		return result;
	}

	public static byte[] smethod_16(object D90AEF94, int int_0)
	{
		byte[] array = new byte[int_0];
		IntPtr intptr_ = Class607.B630A78B.object_0[263](int_0);
		Class607.B630A78B.object_0[1107](D90AEF94, intptr_, bool_0: false);
		Class607.B630A78B.object_0[1266](intptr_, array, 0, int_0);
		Class607.B630A78B.object_0[366](intptr_);
		return array;
	}

	public static string smethod_17(Enum enum_0)
	{
		MemberInfo[] array = Class607.B630A78B.object_0[195](enum_0.GetType(), enum_0.ToString());
		DescriptionAttribute customAttribute = array[0].GetCustomAttribute<DescriptionAttribute>();
		return Class607.B630A78B.object_0[1006](customAttribute);
	}

	public static byte[] smethod_18<CFAB7924>(CFAB7924 gparam_0) where CFAB7924 : struct
	{
		int num = Marshal.SizeOf(gparam_0);
		byte[] array = new byte[num];
		IntPtr intPtr = Class607.B630A78B.object_0[263](num);
		Marshal.StructureToPtr(gparam_0, intPtr, fDeleteOld: true);
		Class607.B630A78B.object_0[1266](intPtr, array, 0, num);
		Class607.B630A78B.object_0[366](intPtr);
		return array;
	}

	public static IntPtr E988E723<A2A96E26>(A2A96E26 gparam_0) where A2A96E26 : struct
	{
		IntPtr intPtr = Class607.B630A78B.object_0[263](Marshal.SizeOf(gparam_0));
		Marshal.StructureToPtr(gparam_0, intPtr, fDeleteOld: false);
		return intPtr;
	}

	public static bool smethod_19(object object_0)
	{
		return object_0 is sbyte || object_0 is byte || object_0 is short || object_0 is ushort || object_0 is int || object_0 is uint || object_0 is long || object_0 is ulong || object_0 is float || object_0 is double || object_0 is decimal;
	}

	public static byte[] FF06B0AD(string string_0)
	{
		Class65 CS_0024_003C_003E8__locals9 = new Class65();
		CS_0024_003C_003E8__locals9.string_0 = string_0;
		if (Class607.B630A78B.object_0[342](CS_0024_003C_003E8__locals9.string_0) % 2 > 0)
		{
			string string_1 = Class607.B630A78B.object_0[720]("0", Class607.B630A78B.object_0[1211](CS_0024_003C_003E8__locals9.string_0, Class607.B630A78B.object_0[342](CS_0024_003C_003E8__locals9.string_0) - 1));
			CS_0024_003C_003E8__locals9.string_0 = Class607.B630A78B.object_0[720](Class607.B630A78B.object_0[31](CS_0024_003C_003E8__locals9.string_0, 0, Class607.B630A78B.object_0[342](CS_0024_003C_003E8__locals9.string_0) - 1), string_1);
		}
		return (from int_0 in Class607.B630A78B.object_0[159](0, Class607.B630A78B.object_0[342](CS_0024_003C_003E8__locals9.string_0))
			where int_0 % 2 == 0
			select Class607.B630A78B.object_0[276](Class607.B630A78B.object_0[31](CS_0024_003C_003E8__locals9.string_0, int_0, 2), 16)).ToArray();
	}

	public static int smethod_20(byte[] byte_0)
	{
		int num = byte_0.Length;
		int num2 = 0;
		for (int num3 = num - 1; num3 >= 0; num3--)
		{
			num2 |= byte_0[num3] << num3 * 8;
		}
		return num2;
	}

	public static int smethod_21(byte[] byte_0, int int_0)
	{
		return (byte_0[int_0 + 3] << 24) | (byte_0[int_0 + 2] << 16) | (byte_0[int_0 + 1] << 8) | byte_0[int_0];
	}

	public static byte[] DB37D01D(byte[] byte_0)
	{
		SHA256 d436CDBB = Class607.B630A78B.object_0[286]();
		return Class607.B630A78B.object_0[245](d436CDBB, Class607.B630A78B.object_0[10](byte_0));
	}

	public static string smethod_22(long BD3EEE8D)
	{
		long num = ((BD3EEE8D < 0L) ? (-BD3EEE8D) : BD3EEE8D);
		string string_;
		double num2;
		if (num >= 1152921504606846976L)
		{
			string_ = "EB";
			num2 = BD3EEE8D >> 50;
		}
		else if (num >= 1125899906842624L)
		{
			string_ = "PB";
			num2 = BD3EEE8D >> 40;
		}
		else if (num >= 1099511627776L)
		{
			string_ = "TB";
			num2 = BD3EEE8D >> 30;
		}
		else if (num >= 1073741824L)
		{
			string_ = "GB";
			num2 = BD3EEE8D >> 20;
		}
		else if (num >= 1048576L)
		{
			string_ = "MB";
			num2 = BD3EEE8D >> 10;
		}
		else
		{
			if (num < 1024L)
			{
				return Class607.B630A78B.object_0[1001](ref BD3EEE8D, "0 B");
			}
			string_ = "KB";
			num2 = BD3EEE8D;
		}
		num2 /= 1024.0;
		return Class607.B630A78B.object_0[720](Class607.B630A78B.object_0[26](ref num2, "0.### "), string_);
	}

	public static int smethod_23(byte[] byte_0, byte[] A90F3DAB, int int_0)
	{
		int num = 0;
		int num2 = byte_0.Length - 1;
		int num3 = int_0;
		while (true)
		{
			if (num3 > num2)
			{
				return -1;
			}
			num = ((byte_0[num3] == A90F3DAB[num]) ? (num + 1) : 0);
			if (A90F3DAB.Length == num)
			{
				break;
			}
			num3++;
		}
		return num3 + 1;
	}

	public static int smethod_24(byte[] byte_0, byte[] byte_1, int int_0)
	{
		int num = byte_0.Length - byte_1.Length;
		byte b = byte_1[0];
		while (int_0 <= num)
		{
			if (byte_0[int_0] == b)
			{
				int num2 = 1;
				while (true)
				{
					if (num2 != byte_1.Length)
					{
						if (byte_0[int_0 + num2] != byte_1[num2])
						{
							break;
						}
						num2++;
						continue;
					}
					return int_0;
				}
			}
			int_0++;
		}
		return -1;
	}

	public static byte[] smethod_25(byte[] byte_0)
	{
		SHA1 d436CDBB = Class607.B630A78B.object_0[287]();
		return Class607.B630A78B.object_0[245](d436CDBB, Class607.B630A78B.object_0[10](byte_0));
	}

	public static byte[] ACBA2890(byte[] BF9F5A05, int B8041584, int int_0)
	{
		byte[] array = new byte[int_0];
		if (BF9F5A05.Length < int_0)
		{
			Array.Resize(ref BF9F5A05, int_0);
		}
		Class607.B630A78B.object_0[242](BF9F5A05, B8041584, array, 0, int_0);
		return array;
	}

	public static void smethod_26(string string_0)
	{
		if (Class607.B630A78B.object_0[695](string_0))
		{
			B50FF0A3(Class607.B630A78B.object_0[321](string_0), Class607.B630A78B.object_0[1055](string_0));
		}
	}

	public static void B50FF0A3(string D5021A39, string string_0)
	{
		SHParseDisplayName(D5021A39, Class607.B630A78B.object_0[120](), out var intptr_, 0u, out var uint_);
		if (!Class607.B630A78B.object_0[1182](intptr_, Class607.B630A78B.object_0[120]()))
		{
			SHParseDisplayName(Class607.B630A78B.object_0[351](D5021A39, string_0), Class607.B630A78B.object_0[120](), out var intptr_2, 0u, out uint_);
			IntPtr[] array = ((!Class607.B630A78B.object_0[1182](intptr_2, Class607.B630A78B.object_0[120]())) ? new IntPtr[1] { intptr_2 } : new IntPtr[0]);
			SHOpenFolderAndSelectItems(intptr_, (uint)array.Length, array, 0u);
			Class607.B630A78B.object_0[126](intptr_);
			if (Class607.B630A78B.object_0[322](intptr_2, Class607.B630A78B.object_0[120]()))
			{
				Class607.B630A78B.object_0[126](intptr_2);
			}
		}
	}

	[DllImport("shell32.dll", SetLastError = true)]
	public static extern int SHOpenFolderAndSelectItems(IntPtr A61D7AA1, uint uint_0, [In][MarshalAs(UnmanagedType.LPArray)] IntPtr[] intptr_0, uint uint_1);

	[DllImport("shell32.dll", SetLastError = true)]
	public static extern void SHParseDisplayName([MarshalAs(UnmanagedType.LPWStr)] string string_0, IntPtr C82640BE, out IntPtr intptr_0, uint uint_0, out uint uint_1);

	public static string smethod_27(byte[] byte_0)
	{
		return Class607.B630A78B.object_0[99](Class607.B630A78B.object_0[1058](byte_0), "-", null);
	}

	public static void smethod_28(int EEA4AAB4)
	{
		DateTime dateTime_ = Class607.B630A78B.object_0[1258]();
		while (Class607.B630A78B.object_0[911](Class607.B630A78B.object_0[739](DateTime.UtcNow, dateTime_), Class607.B630A78B.object_0[1148](EEA4AAB4)))
		{
			Class607.B630A78B.object_0[167]();
		}
	}

	public static int CC905B09(int B30E25A0, byte[] byte_0)
	{
		foreach (byte b in byte_0)
		{
			for (int num = 128; num != 0; num >>= 1)
			{
				if ((B30E25A0 & 0x8000) != 0)
				{
					B30E25A0 <<= 1;
					B30E25A0 ^= 0x1021;
				}
				else
				{
					B30E25A0 <<= 1;
				}
				if ((b & num) != 0)
				{
					B30E25A0 ^= 0x1021;
				}
			}
		}
		return B30E25A0;
	}

	public static string smethod_29(ushort[] ushort_0)
	{
		char[] array = new char[ushort_0.Length];
		for (int i = 0; i < ushort_0.Length / 2; i++)
		{
			array[i] = Class607.B630A78B.object_0[1094](ushort_0[i]);
		}
		return Class607.B630A78B.object_0[100](Class607.B630A78B.object_0[1260](array), "\0", "");
	}

	public static int AF9F71A4(byte ADA28A13, byte BCA20FA8)
	{
		return ((ADA28A13 & 0xFF) << 8) | (BCA20FA8 & 0xFF);
	}

	[IteratorStateMachine(typeof(B29F5C8B))]
	public static IEnumerable<int> smethod_30(int int_0, int int_1, int int_2 = 1)
	{
		//yield-return decompiler failed: Method not found
		return new B29F5C8B(-2)
		{
			int_1 = int_0,
			int_2 = int_1,
			D2BB0692 = int_2
		};
	}
}
