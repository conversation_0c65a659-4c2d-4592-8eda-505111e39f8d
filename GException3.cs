public class GException3 : GException2
{
	protected object FA83AEAB;

	protected object object_0;

	public GException3(byte[] FA83AEAB, byte[] byte_0, E3348281 F82730BE, string D329E413)
		: base(Class607.B630A78B.object_0[398]("not equal, expected ", GException2.smethod_0(FA83AEAB), ", but got ", GException2.smethod_0(byte_0)), F82730BE, D329E413)
	{
		this.FA83AEAB = FA83AEAB;
		object_0 = byte_0;
	}

	public GException3(object object_1, object object_2, E3348281 e3348281_0, string CFA07BA6)
		: base(Class334.smethod_0("not equal, expected ", object_1?.ToString(), ", but got ", object_2?.ToString()), e3348281_0, CFA07BA6)
	{
		FA83AEAB = object_1;
		object_0 = object_2;
	}
}
