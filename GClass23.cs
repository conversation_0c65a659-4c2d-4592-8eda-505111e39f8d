using System.IO;
using System.Security.Cryptography;

public class GClass23
{
	public static byte[] smethod_0(byte[] DD02A39A, CipherMode cipherMode_0, byte[] DB109E33, byte[] byte_0)
	{
		return (byte[])new GClass128().F3BD1601(new object[4] { DD02A39A, cipherMode_0, DB109E33, byte_0 }, 22980548);
	}

	private static byte[] smethod_1(byte[] F31AB60E, ICryptoTransform icryptoTransform_0)
	{
		using MemoryStream memoryStream = Class607.B630A78B.object_0[939]();
		using CryptoStream object_ = Class607.B630A78B.object_0[394](memoryStream, icryptoTransform_0, CryptoStreamMode.Write);
		Class607.B630A78B.object_0[132](object_, F31AB60E, 0, F31AB60E.Length);
		Class607.B630A78B.object_0[931](object_);
		return Class607.B630A78B.object_0[1126](memoryStream);
	}

	public static byte[] EF097B2C(byte[] CDBD2C83, CipherMode F9138808, byte[] byte_0, byte[] byte_1)
	{
		return (byte[])new GClass128().method_323(new object[4] { CDBD2C83, F9138808, byte_0, byte_1 }, 270324);
	}

	public GClass23()
	{
		Class607.B630A78B.object_0[571](this);
	}
}
