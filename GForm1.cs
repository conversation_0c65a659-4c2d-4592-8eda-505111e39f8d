using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

public class GForm1 : Form
{
	public string D69CFAA1;

	private IContainer icontainer_0 = null;

	private Panel panel_0;

	private A09F0208 a09F0208_0;

	private ComboBox comboBox_0;

	private Label label_0;

	private GroupBox F315D39A;

	private Button button_0;

	private Label label_1;

	public GForm1()
	{
		Class607.B630A78B.object_0[1016](this);
		********();
	}

	private void FDA3349E(object sender, EventArgs e)
	{
		new GClass128().E8AA1C3C(new object[3] { this, sender, e }, 345053);
	}

	private void method_0(object sender, EventArgs e)
	{
		Class607.B630A78B.object_0[1143](comboBox_0, 0);
	}

	private void method_1(object sender, EventArgs e)
	{
		new GClass128().C5017C25(new object[3] { this, sender, e }, 22497343);
	}

	protected override void Dispose(bool disposing)
	{
		if (disposing && icontainer_0 != null)
		{
			icontainer_0.Dispose();
		}
		Class607.B630A78B.object_0[1072](this, disposing);
	}

	private unsafe void ********()
	{
		ComponentResourceManager object_ = Class607.B630A78B.object_0[566](Class607.B630A78B.object_0[6](typeof(GForm1).TypeHandle));
		panel_0 = Class607.B630A78B.object_0[853]();
		a09F0208_0 = new A09F0208();
		comboBox_0 = Class607.B630A78B.object_0[829]();
		label_0 = Class607.B630A78B.object_0[113]();
		F315D39A = Class607.B630A78B.object_0[1206]();
		button_0 = Class607.B630A78B.object_0[1112]();
		label_1 = Class607.B630A78B.object_0[113]();
		Class607.B630A78B.object_0[549](panel_0);
		Class607.B630A78B.object_0[549](F315D39A);
		Class607.B630A78B.object_0[550](this);
		Class607.B630A78B.object_0[1242](panel_0, AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[823](panel_0, Class607.B630A78B.object_0[639]());
		Class607.B630A78B.object_0[781](panel_0).Add(a09F0208_0);
		Class607.B630A78B.object_0[388](panel_0, Class607.B630A78B.object_0[760](6, 2));
		Class607.B630A78B.object_0[689](panel_0, "NavBar");
		Class607.B630A78B.object_0[1276](panel_0, Class607.B630A78B.object_0[174](783, 38));
		Class607.B630A78B.object_0[334](panel_0, 3);
		Class607.B630A78B.object_0[11](a09F0208_0, E704E9A1: true);
		a09F0208_0.Int32_0 = 0;
		Class607.B630A78B.object_0[1267](a09F0208_0, Class607.B630A78B.object_0[1269]("Roboto Medium", 11f));
		Class607.B630A78B.object_0[326](a09F0208_0, Class607.B630A78B.object_0[1194](222, 0, 0, 0));
		Class607.B630A78B.object_0[388](a09F0208_0, Class607.B630A78B.object_0[760](335, 9));
		a09F0208_0.C8B39594 = GEnum36.A6BC4E23;
		Class607.B630A78B.object_0[689](a09F0208_0, "lblnav");
		Class607.B630A78B.object_0[1276](a09F0208_0, Class607.B630A78B.object_0[174](139, 18));
		Class607.B630A78B.object_0[334](a09F0208_0, 2);
		Class607.B630A78B.object_0[1175](a09F0208_0, "Select Storage type");
		Class607.B630A78B.object_0[1242](comboBox_0, AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[672](comboBox_0, ComboBoxStyle.DropDownList);
		Class607.B630A78B.object_0[274](comboBox_0, B9103837: true);
		Class607.B630A78B.object_0[1060](comboBox_0).AddRange(new object[2] { "ufs", "emmc" });
		Class607.B630A78B.object_0[388](comboBox_0, Class607.B630A78B.object_0[760](82, 38));
		Class607.B630A78B.object_0[689](comboBox_0, "CmbBxStorageType");
		Class607.B630A78B.object_0[1276](comboBox_0, Class607.B630A78B.object_0[174](692, 21));
		Class607.B630A78B.object_0[334](comboBox_0, 26);
		Class607.B630A78B.object_0[1242](label_0, AnchorStyles.Bottom | AnchorStyles.Left);
		Class607.B630A78B.object_0[11](label_0, E704E9A1: true);
		Class607.B630A78B.object_0[388](label_0, Class607.B630A78B.object_0[760](6, 42));
		Class607.B630A78B.object_0[689](label_0, "label3");
		Class607.B630A78B.object_0[1276](label_0, Class607.B630A78B.object_0[174](70, 13));
		Class607.B630A78B.object_0[334](label_0, 23);
		Class607.B630A78B.object_0[1175](label_0, "Storage type:");
		Class607.B630A78B.object_0[781](F315D39A).Add(label_1);
		Class607.B630A78B.object_0[781](F315D39A).Add(button_0);
		Class607.B630A78B.object_0[781](F315D39A).Add(comboBox_0);
		Class607.B630A78B.object_0[781](F315D39A).Add(label_0);
		Class607.B630A78B.object_0[388](F315D39A, Class607.B630A78B.object_0[760](6, 46));
		Class607.B630A78B.object_0[689](F315D39A, "groupBox1");
		Class607.B630A78B.object_0[1276](F315D39A, Class607.B630A78B.object_0[174](783, 104));
		Class607.B630A78B.object_0[334](F315D39A, 28);
		Class607.B630A78B.object_0[1183](F315D39A, bool_0: false);
		Class607.B630A78B.object_0[1175](F315D39A, "Select Stoprage");
		Class607.B630A78B.object_0[1242](button_0, AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right);
		Class607.B630A78B.object_0[823](button_0, Class607.B630A78B.object_0[477]());
		Class607.B630A78B.object_0[308](button_0, Class607.B630A78B.object_0[268]());
		Class607.B630A78B.object_0[39](Class607.B630A78B.object_0[227](button_0), Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[540](button_0, FlatStyle.Flat);
		Class607.B630A78B.object_0[326](button_0, Class607.B630A78B.object_0[235](64, 64, 64));
		Class607.B630A78B.object_0[953](button_0, A282DB38.ok);
		Class607.B630A78B.object_0[997](button_0, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[388](button_0, Class607.B630A78B.object_0[760](9, 68));
		Class607.B630A78B.object_0[689](button_0, "BtnReturn");
		Class607.B630A78B.object_0[1276](button_0, Class607.B630A78B.object_0[174](765, 29));
		Class607.B630A78B.object_0[334](button_0, 28);
		Class607.B630A78B.object_0[1175](button_0, "return");
		Class607.B630A78B.object_0[494](button_0, ContentAlignment.MiddleLeft);
		Class607.B630A78B.object_0[347](button_0, TextImageRelation.ImageBeforeText);
		Class607.B630A78B.object_0[270](button_0, CA2A151B: false);
		Class607.B630A78B.object_0[1239](button_0, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GForm1.FDA3349E)));
		Class607.B630A78B.object_0[11](label_1, E704E9A1: true);
		Class607.B630A78B.object_0[388](label_1, Class607.B630A78B.object_0[760](6, 16));
		Class607.B630A78B.object_0[689](label_1, "label1");
		Class607.B630A78B.object_0[1276](label_1, Class607.B630A78B.object_0[174](299, 13));
		Class607.B630A78B.object_0[334](label_1, 29);
		Class607.B630A78B.object_0[1175](label_1, "Faild to reading storage type of device, please select manually");
		Class607.B630A78B.object_0[511](this, Class607.B630A78B.object_0[1223](6f, 13f));
		Class607.B630A78B.object_0[1043](this, AutoScaleMode.Font);
		Class607.B630A78B.object_0[485](this, Class607.B630A78B.object_0[174](794, 155));
		Class607.B630A78B.object_0[782](this).Add(F315D39A);
		Class607.B630A78B.object_0[782](this).Add(panel_0);
		Class607.B630A78B.object_0[591](this, FormBorderStyle.None);
		Class607.B630A78B.object_0[376](this, (Icon)Class607.B630A78B.object_0[656](object_, "$this.Icon"));
		Class607.B630A78B.object_0[690](this, "IsUFS");
		Class607.B630A78B.object_0[1175](this, "Customloader");
		Class607.B630A78B.object_0[161](this, Class607.B630A78B.object_0[935](this, (nint)__ldftn(GForm1.method_0)));
		Class607.B630A78B.object_0[1149](panel_0, EB25F899: false);
		Class607.B630A78B.object_0[63](panel_0);
		Class607.B630A78B.object_0[1149](F315D39A, EB25F899: false);
		Class607.B630A78B.object_0[63](F315D39A);
		Class607.B630A78B.object_0[1150](this, bool_0: false);
	}
}
