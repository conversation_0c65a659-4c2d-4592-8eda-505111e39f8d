using System.Diagnostics;
using System.Runtime.CompilerServices;

public class EBB8EB1D
{
	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string D090F333;

	[CompilerGenerated]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	private string string_0;

	public string A6146205
	{
		[CompilerGenerated]
		get
		{
			return D090F333;
		}
		[CompilerGenerated]
		set
		{
			D090F333 = value;
		}
	}

	public string String_0
	{
		[CompilerGenerated]
		get
		{
			return string_0;
		}
		[CompilerGenerated]
		set
		{
			string_0 = value;
		}
	}

	public EBB8EB1D(string C9126C96, string string_1)
	{
		Class607.B630A78B.object_0[571](this);
		A6146205 = C9126C96;
		String_0 = string_1;
	}
}
