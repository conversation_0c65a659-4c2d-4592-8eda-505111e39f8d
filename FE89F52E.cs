using System;
using System.Collections.Generic;
using System.Diagnostics;

[DebuggerDisplay("{2+B630A78B.A2A5AFA5()}")]
public class FE89F52E
{
	private readonly Dictionary<string, uint> dictionary_0;

	public FE89F52E()
	{
		new GClass128().AD84C1A2(new object[1] { this }, 20803876);
	}

	private unsafe byte[] D53EE91F(IntPtr intptr_0, int DA9FEF39, uint AD3B5AA4)
	{
		short num = 0;
		byte b = default(byte);
		while (true)
		{
			byte[] array = new byte[DA9FEF39];
			byte* ptr = (byte*)(void*)intptr_0;
			uint num2 = (uint)((-1450712400 >>> (int)num) | num);
			int num3 = (int)((uint)(num >> 0) / 4162590373u);
			if (((438362134u > (uint)(1158021666 - (1345219627 >> (-334591450 >>> (int)num2)))) ? 1u : 0u) != (2562314628u % ~((uint)num % 1562023478u) >> 1604039076 + ~num) % (uint)((0x578F59A0 ^ num) >>> 7))
			{
				num = (short)(-15954 ^ (((num ^ num) << (int)num) | -73));
				goto IL_002d;
			}
			goto IL_008c;
			IL_002d:
			while (true)
			{
				switch ((uint)num % 4u)
				{
				case 2u:
					break;
				case 1u:
					goto IL_004a;
				default:
					goto end_IL_002d;
				case 3u:
					num = (short)(-143 ^ ~(b + 116));
					return array;
				}
				num = (short)(b * 713 / 156306903);
				if (num3 >= DA9FEF39)
				{
					if (num2 != 0)
					{
						num = (short)((b | -1451995198) ^ -1452012551);
						continue;
					}
				}
				else
				{
					array[num3] = (byte)(ptr[num3] ^ (((AD3B5AA4 << num3) | (AD3B5AA4 >> 32 - num3)) + num3));
					b = 1;
					num3++;
					num2 = 2844254896u;
					num = 0;
				}
				goto IL_008c;
				IL_004a:
				num = (short)(-1450712444 ^ (-44 + (int)num2));
				goto IL_008c;
				continue;
				end_IL_002d:
				break;
			}
			continue;
			IL_008c:
			b = (byte)(1016263450 + (int)num2 % ~num);
			num = (short)((int)((uint)(-((int)b + (((int)num2 < 311631037) ? 1 : 0))) / ~((68151300 + num == 511706171) ? 1u : 0u)) - -25122);
			goto IL_002d;
		}
	}
}
