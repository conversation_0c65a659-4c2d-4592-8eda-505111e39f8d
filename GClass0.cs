using System;
using System.Collections;
using System.Drawing;
using System.Windows.Forms;

[ToolboxBitmap(typeof(DataGridViewCheckBoxColumn))]
public class GClass0 : DataGridViewCheckBoxColumn
{
	public GClass0()
	{
		Class607.B630A78B.object_0[508](this);
		GClass1 gClass = new GClass1();
		Class607.B630A78B.object_0[1199](this, gClass);
		Class607.B630A78B.object_0[93](this, 50);
		gClass.Event_0 += method_0;
	}

	private void method_0(int int_0, bool ABB6C103)
	{
		Class607.B630A78B.object_0[940](Class607.B630A78B.object_0[854](this));
		IEnumerator object_ = Class607.B630A78B.object_0[728](Class607.B630A78B.object_0[317](Class607.B630A78B.object_0[854](this)));
		try
		{
			while (Class607.B630A78B.object_0[212](object_))
			{
				DataGridViewRow object_2 = (DataGridViewRow)Class607.B630A78B.object_0[107](object_);
				if (!Class607.B630A78B.object_0[780](Class607.B630A78B.object_0[1138](Class607.B630A78B.object_0[907](object_2), int_0)))
				{
					Class607.B630A78B.object_0[213](Class607.B630A78B.object_0[1138](Class607.B630A78B.object_0[907](object_2), int_0), ABB6C103);
				}
			}
		}
		finally
		{
			IDisposable disposable = object_ as IDisposable;
			if (disposable != null)
			{
				disposable.Dispose();
			}
		}
		Class607.B630A78B.object_0[940](Class607.B630A78B.object_0[854](this));
	}
}
