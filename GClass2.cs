using System;
using System.Collections;
using System.Windows.Forms;

public class GClass2
{
	private DataGridView BCA11407 = null;

	private CheckedListBox AE079C00;

	private ToolStripDropDown B90EBCAE;

	public int int_0 = 300;

	public int DFB8D7B5 = 200;

	public unsafe DataGridView DataGridView_0
	{
		get
		{
			return BCA11407;
		}
		set
		{
			if (BCA11407 != null)
			{
				Class607.B630A78B.object_0[822](BCA11407, Class607.B630A78B.object_0[346](this, (nint)__ldftn(GClass2.method_0)));
			}
			BCA11407 = value;
			if (BCA11407 != null)
			{
				Class607.B630A78B.object_0[822](BCA11407, Class607.B630A78B.object_0[346](this, (nint)__ldftn(GClass2.method_0)));
			}
		}
	}

	private void method_0(object sender, DataGridViewCellMouseEventArgs e)
	{
		if (Class607.B630A78B.object_0[944](e) != MouseButtons.Right || Class607.B630A78B.object_0[1139](e) != -1 || Class607.B630A78B.object_0[307](e) != 0)
		{
			return;
		}
		Class607.B630A78B.object_0[1174](AE079C00).Clear();
		IEnumerator object_ = Class607.B630A78B.object_0[1167](Class607.B630A78B.object_0[182](BCA11407));
		try
		{
			while (Class607.B630A78B.object_0[212](object_))
			{
				DataGridViewColumn dataGridViewColumn = (DataGridViewColumn)Class607.B630A78B.object_0[107](object_);
				Class607.B630A78B.object_0[1174](AE079C00).Add(Class607.B630A78B.object_0[671](dataGridViewColumn), Class607.B630A78B.object_0[202](dataGridViewColumn));
			}
		}
		finally
		{
			IDisposable disposable = object_ as IDisposable;
			if (disposable != null)
			{
				disposable.Dispose();
			}
		}
		int num = Class607.B630A78B.object_0[1174](AE079C00).Count * 16 + 7;
		DFAB3B3B.F1A30BB7(AE079C00, (num < int_0) ? num : int_0);
		Class607.B630A78B.object_0[18](AE079C00, DFB8D7B5);
		Class607.B630A78B.object_0[292](B90EBCAE, Class607.B630A78B.object_0[312](BCA11407, Class607.B630A78B.object_0[760](Class607.B630A78B.object_0[841](e), Class607.B630A78B.object_0[913](e))));
	}

	public unsafe GClass2()
	{
		Class607.B630A78B.object_0[571](this);
		AE079C00 = Class607.B630A78B.object_0[642]();
		Class607.B630A78B.object_0[71](AE079C00, F9806A25: true);
		Class607.B630A78B.object_0[282](AE079C00, Class607.B630A78B.object_0[1162](this, (nint)__ldftn(GClass2.C50F0428)));
		ToolStripControlHost toolStripControlHost = Class607.B630A78B.object_0[755](AE079C00);
		Class607.B630A78B.object_0[158](toolStripControlHost, Class607.B630A78B.object_0[1039]());
		Class607.B630A78B.object_0[773](toolStripControlHost, Class607.B630A78B.object_0[1039]());
		Class607.B630A78B.object_0[929](toolStripControlHost, bool_0: false);
		B90EBCAE = Class607.B630A78B.object_0[154]();
		Class607.B630A78B.object_0[356](B90EBCAE, Class607.B630A78B.object_0[1039]());
		Class607.B630A78B.object_0[859](Class607.B630A78B.object_0[189](B90EBCAE), toolStripControlHost);
	}

	public GClass2(DataGridView F1B97FBB)
		: this()
	{
		DataGridView_0 = F1B97FBB;
	}

	private void C50F0428(object sender, ItemCheckEventArgs e)
	{
		Class607.B630A78B.object_0[675](Class607.B630A78B.object_0[596](Class607.B630A78B.object_0[182](BCA11407), Class607.B630A78B.object_0[1011](e)), Class607.B630A78B.object_0[1014](e) == CheckState.Checked);
	}
}
